"""
基础测试类和混入类

提供 ManagedMultiProcess 测试的基础设施，包括：
- BaseTestCase: 基础测试类，提供通用的测试设置和清理
- EventTestMixin: 事件测试混入类，提供事件测试的通用方法
"""

import sys
import os
import unittest
import threading
import time
import traceback
from typing import List, Dict, Any, Callable, Optional
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入被测试的类
from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager
from global_tools.utils import Logger, LogLevel, ClassInstanceManager


class BaseTestCase(unittest.TestCase):
    """
    ManagedMultiProcess 测试的基础类
    
    提供通用的测试设置、清理和工具方法。
    """
    
    def setUp(self):
        """测试前的设置"""
        # 获取日志实例
        self.logger = ClassInstanceManager.get_instance(key="Logger")
        
        # 设置测试超时时间
        self.test_timeout = 30.0  # 30秒超时
        
        # 初始化测试数据
        self.test_data = []
        self.mp_instance = None
        self.triggered_events = []
        self.event_data = {}
        self.event_lock = threading.RLock()
        
        # 测试开始时间
        self.start_time = time.time()
        
        self.logger.info(f"开始测试: {self._testMethodName}")
    
    def tearDown(self):
        """测试后的清理"""
        try:
            # 清理 ManagedMultiProcess 实例
            if self.mp_instance is not None:
                try:
                    if hasattr(self.mp_instance, 'is_running') and self.mp_instance.is_running():
                        self.mp_instance.stop_all(immediate=True)
                    
                    # 清理共享数据资源
                    if hasattr(self.mp_instance, 'close_shared_data_resources'):
                        self.mp_instance.close_shared_data_resources()
                        
                except Exception as e:
                    self.logger.warning(f"清理 ManagedMultiProcess 实例时出错: {e}")
                
                self.mp_instance = None
            
            # 计算测试耗时
            elapsed_time = time.time() - self.start_time
            self.logger.info(f"测试完成: {self._testMethodName}, 耗时: {elapsed_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"测试清理时出错: {e}\n{traceback.format_exc()}")
    
    def create_test_data(self, count: int = 10) -> List[int]:
        """
        创建测试数据
        
        Args:
            count: 数据项数量
            
        Returns:
            测试数据列表
        """
        return list(range(count))
    
    def create_simple_worker(self, delay: float = 0.1) -> Callable:
        """
        创建简单的工作函数
        
        Args:
            delay: 处理延迟时间（秒）
            
        Returns:
            工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """简单的工作函数，处理数据项并更新共享数据"""
            time.sleep(delay)  # 模拟处理时间
            
            # 更新共享数据
            shared_data_manager.set_shared_value("processed_count", 
                shared_data_manager.get_shared_value("processed_count", 0) + 1)
            
            # 添加处理结果
            results = shared_data_manager.get_shared_list("results", [])
            results.append(f"processed_{item}")
            shared_data_manager.set_shared_list("results", results)
            
            return f"result_{item}"
        
        return worker
    
    def create_mp_instance(self, 
                          data: List[Any] = None, 
                          worker: Callable = None,
                          num_processes: int = 2) -> ManagedMultiProcess:
        """
        创建 ManagedMultiProcess 实例
        
        Args:
            data: 输入数据，默认创建测试数据
            worker: 工作函数，默认创建简单工作函数
            num_processes: 进程数量
            
        Returns:
            ManagedMultiProcess 实例
        """
        if data is None:
            data = self.create_test_data()
        
        if worker is None:
            worker = self.create_simple_worker()
        
        self.mp_instance = ManagedMultiProcess(
            input_data=data,
            callback_func=worker,
            num_processes=num_processes
        )
        
        return self.mp_instance
    
    def wait_for_condition(self, 
                          condition: Callable[[], bool], 
                          timeout: float = None,
                          check_interval: float = 0.1) -> bool:
        """
        等待条件满足
        
        Args:
            condition: 条件检查函数
            timeout: 超时时间，默认使用 test_timeout
            check_interval: 检查间隔
            
        Returns:
            条件是否在超时前满足
        """
        if timeout is None:
            timeout = self.test_timeout
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition():
                return True
            time.sleep(check_interval)
        
        return False
    
    def assert_timeout(self, func: Callable, timeout: float = None, *args, **kwargs):
        """
        断言函数在指定时间内完成
        
        Args:
            func: 要执行的函数
            timeout: 超时时间
            *args, **kwargs: 函数参数
        """
        if timeout is None:
            timeout = self.test_timeout
        
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        
        self.assertLess(elapsed, timeout, 
                       f"函数执行超时: {elapsed:.2f}s > {timeout}s")
        
        return result


class EventTestMixin:
    """
    事件测试混入类
    
    提供事件测试的通用方法和工具。
    """
    
    def setup_event_tracking(self):
        """设置事件跟踪"""
        self.triggered_events = []
        self.event_data = {}
        self.event_lock = threading.RLock()
        self.event_timestamps = {}
    
    def create_event_handler(self, event_name: str) -> Callable:
        """
        创建事件处理器
        
        Args:
            event_name: 事件名称
            
        Returns:
            事件处理器函数
        """
        def handler(mp_instance, *args, **kwargs):
            """事件处理器"""
            with self.event_lock:
                self.triggered_events.append(event_name)
                self.event_data[event_name] = {
                    'mp_instance': mp_instance,
                    'args': args,
                    'kwargs': kwargs,
                    'timestamp': time.time()
                }
                self.event_timestamps[event_name] = time.time()
                
            self.logger.debug(f"事件触发: {event_name}, args: {args}, kwargs: {kwargs}")
        
        return handler
    
    def register_all_events(self, mp_instance: ManagedMultiProcess):
        """
        注册所有事件的处理器
        
        Args:
            mp_instance: ManagedMultiProcess 实例
        """
        events = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
            ProcessEventManager.PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP,
            ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP
        ]
        
        for event in events:
            handler = self.create_event_handler(event)
            mp_instance.listen_event(event, handler)
    
    def assert_event_triggered(self, event_name: str, timeout: float = 10.0):
        """
        断言事件被触发
        
        Args:
            event_name: 事件名称
            timeout: 超时时间
        """
        def check_event():
            with self.event_lock:
                return event_name in self.triggered_events
        
        success = self.wait_for_condition(check_event, timeout)
        self.assertTrue(success, f"事件 {event_name} 在 {timeout}s 内未被触发")
    
    def assert_event_not_triggered(self, event_name: str, wait_time: float = 2.0):
        """
        断言事件未被触发
        
        Args:
            event_name: 事件名称
            wait_time: 等待时间
        """
        time.sleep(wait_time)  # 等待一段时间
        with self.event_lock:
            self.assertNotIn(event_name, self.triggered_events, 
                           f"事件 {event_name} 不应该被触发")
    
    def assert_event_order(self, expected_order: List[str], timeout: float = 10.0):
        """
        断言事件触发顺序
        
        Args:
            expected_order: 期望的事件顺序
            timeout: 超时时间
        """
        def check_order():
            with self.event_lock:
                if len(self.triggered_events) < len(expected_order):
                    return False
                
                # 检查顺序
                for i, expected_event in enumerate(expected_order):
                    if i >= len(self.triggered_events) or self.triggered_events[i] != expected_event:
                        return False
                return True
        
        success = self.wait_for_condition(check_order, timeout)
        
        with self.event_lock:
            actual_order = self.triggered_events[:len(expected_order)]
        
        self.assertTrue(success, 
                       f"事件顺序不匹配。期望: {expected_order}, 实际: {actual_order}")
    
    def get_event_count(self, event_name: str) -> int:
        """
        获取事件触发次数
        
        Args:
            event_name: 事件名称
            
        Returns:
            触发次数
        """
        with self.event_lock:
            return self.triggered_events.count(event_name)
    
    def clear_event_tracking(self):
        """清除事件跟踪数据"""
        with self.event_lock:
            self.triggered_events.clear()
            self.event_data.clear()
            self.event_timestamps.clear()
