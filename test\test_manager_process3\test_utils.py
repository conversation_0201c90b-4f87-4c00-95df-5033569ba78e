"""
测试工具函数

提供 ManagedMultiProcess 测试所需的各种工具函数和辅助类。
"""

import sys
import os
import time
import threading
import random
import traceback
from typing import List, Dict, Any, Callable, Optional, Tuple
from unittest.mock import Mock

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils import Logger, ClassInstanceManager


class TestUtils:
    """
    测试工具类
    
    提供各种测试辅助方法和工具函数。
    """
    
    @staticmethod
    def create_cpu_intensive_worker(duration: float = 0.5) -> Callable:
        """
        创建CPU密集型工作函数
        
        Args:
            duration: 工作持续时间（秒）
            
        Returns:
            CPU密集型工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """CPU密集型工作函数"""
            start_time = time.time()
            
            # CPU密集型计算
            result = 0
            while time.time() - start_time < duration:
                result += sum(range(1000))
            
            # 更新共享数据
            shared_data_manager.set_shared_value(f"cpu_result_{item}", result)
            
            return f"cpu_processed_{item}"
        
        return worker
    
    @staticmethod
    def create_io_intensive_worker(delay: float = 0.3) -> Callable:
        """
        创建IO密集型工作函数
        
        Args:
            delay: IO延迟时间（秒）
            
        Returns:
            IO密集型工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """IO密集型工作函数"""
            # 模拟IO操作
            time.sleep(delay)
            
            # 更新共享数据
            shared_data_manager.set_shared_value(f"io_result_{item}", f"io_data_{item}")
            
            return f"io_processed_{item}"
        
        return worker
    
    @staticmethod
    def create_error_worker(error_rate: float = 0.3) -> Callable:
        """
        创建会产生错误的工作函数
        
        Args:
            error_rate: 错误率（0.0-1.0）
            
        Returns:
            可能产生错误的工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """可能产生错误的工作函数"""
            if random.random() < error_rate:
                raise ValueError(f"模拟错误: item {item}")
            
            # 正常处理
            time.sleep(0.1)
            shared_data_manager.set_shared_value(f"success_{item}", True)
            
            return f"success_{item}"
        
        return worker
    
    @staticmethod
    def create_data_sharing_worker() -> Callable:
        """
        创建数据共享测试工作函数
        
        Returns:
            数据共享工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """数据共享工作函数"""
            # 读取共享计数器
            counter = shared_data_manager.get_shared_value("counter", 0)
            
            # 增加计数器
            shared_data_manager.set_shared_value("counter", counter + 1)
            
            # 添加到共享列表
            items = shared_data_manager.get_shared_list("processed_items", [])
            items.append(item)
            shared_data_manager.set_shared_list("processed_items", items)
            
            # 添加到共享集合
            item_set = shared_data_manager.get_shared_set("item_set", set())
            item_set.add(item)
            shared_data_manager.set_shared_set("item_set", item_set)
            
            return f"shared_{item}"
        
        return worker
    
    @staticmethod
    def create_callback_worker(callback: Callable = None) -> Callable:
        """
        创建带回调的工作函数
        
        Args:
            callback: 回调函数
            
        Returns:
            带回调的工作函数
        """
        def worker(item, shared_data_manager, *args, **kwargs):
            """带回调的工作函数"""
            time.sleep(0.1)
            
            # 执行回调
            if callback:
                callback(item, shared_data_manager)
            
            return f"callback_{item}"
        
        return worker
    
    @staticmethod
    def measure_execution_time(func: Callable, *args, **kwargs) -> Tuple[Any, float]:
        """
        测量函数执行时间
        
        Args:
            func: 要测量的函数
            *args, **kwargs: 函数参数
            
        Returns:
            (函数返回值, 执行时间)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        return result, execution_time
    
    @staticmethod
    def wait_for_processes_completion(mp_instance, timeout: float = 30.0) -> bool:
        """
        等待进程完成
        
        Args:
            mp_instance: ManagedMultiProcess 实例
            timeout: 超时时间
            
        Returns:
            是否在超时前完成
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if not mp_instance.is_running():
                return True
            time.sleep(0.1)
        
        return False
    
    @staticmethod
    def verify_shared_data_consistency(mp_instance, expected_data: Dict[str, Any]) -> bool:
        """
        验证共享数据一致性
        
        Args:
            mp_instance: ManagedMultiProcess 实例
            expected_data: 期望的数据
            
        Returns:
            数据是否一致
        """
        try:
            results = mp_instance.get_results()
            
            for key, expected_value in expected_data.items():
                if key not in results:
                    return False
                
                actual_value = results[key]
                if actual_value != expected_value:
                    return False
            
            return True
            
        except Exception:
            return False


class MockDataGenerator:
    """
    模拟数据生成器
    
    生成各种类型的测试数据。
    """
    
    @staticmethod
    def generate_simple_data(count: int = 10) -> List[int]:
        """
        生成简单的整数数据
        
        Args:
            count: 数据数量
            
        Returns:
            整数列表
        """
        return list(range(count))
    
    @staticmethod
    def generate_string_data(count: int = 10, prefix: str = "item") -> List[str]:
        """
        生成字符串数据
        
        Args:
            count: 数据数量
            prefix: 字符串前缀
            
        Returns:
            字符串列表
        """
        return [f"{prefix}_{i}" for i in range(count)]
    
    @staticmethod
    def generate_dict_data(count: int = 10) -> List[Dict[str, Any]]:
        """
        生成字典数据
        
        Args:
            count: 数据数量
            
        Returns:
            字典列表
        """
        return [
            {
                'id': i,
                'name': f'item_{i}',
                'value': random.randint(1, 100),
                'timestamp': time.time()
            }
            for i in range(count)
        ]
    
    @staticmethod
    def generate_large_data(count: int = 1000) -> List[Dict[str, Any]]:
        """
        生成大量数据
        
        Args:
            count: 数据数量
            
        Returns:
            大量数据列表
        """
        return MockDataGenerator.generate_dict_data(count)
    
    @staticmethod
    def generate_empty_data() -> List[Any]:
        """
        生成空数据
        
        Returns:
            空列表
        """
        return []
    
    @staticmethod
    def generate_single_item_data() -> List[str]:
        """
        生成单项数据
        
        Returns:
            单项数据列表
        """
        return ["single_item"]


class PerformanceMonitor:
    """
    性能监控器
    
    监控测试过程中的性能指标。
    """
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.metrics = {}
        self.logger = ClassInstanceManager.get_instance(key="Logger")
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.metrics.clear()
        self.logger.debug("开始性能监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()
        if self.start_time:
            self.metrics['total_time'] = self.end_time - self.start_time
        self.logger.debug(f"停止性能监控，总耗时: {self.metrics.get('total_time', 0):.2f}秒")
    
    def record_metric(self, name: str, value: Any):
        """
        记录指标
        
        Args:
            name: 指标名称
            value: 指标值
        """
        self.metrics[name] = value
        self.logger.debug(f"记录指标: {name} = {value}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取所有指标
        
        Returns:
            指标字典
        """
        return self.metrics.copy()
    
    def get_summary(self) -> str:
        """
        获取性能摘要
        
        Returns:
            性能摘要字符串
        """
        if not self.metrics:
            return "无性能数据"
        
        summary = ["性能监控摘要:"]
        for name, value in self.metrics.items():
            if isinstance(value, float):
                summary.append(f"  {name}: {value:.2f}")
            else:
                summary.append(f"  {name}: {value}")
        
        return "\n".join(summary)
