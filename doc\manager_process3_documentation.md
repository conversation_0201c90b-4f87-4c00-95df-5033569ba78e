[TOC]

# manager_process3 模块详细文档

## 模块概述

`manager_process3` 是一个功能强大的Python多进程管理框架，旨在简化和增强Python多进程编程。该模块提供了安全的数据共享、事件管理、进程状态监控和异步回调等高级功能，使开发者能够轻松实现复杂的并行计算任务。

## 目录

1. [模块组件概览](#1-模块组件概览)
2. [主要类详解](#2-主要类详解)
   - [ManagedMultiProcess](#21-managedmultiprocess)
   - [SharedDataManager](#22-shareddatamanager)
   - [ProcessEventManager](#23-processeventmanager)
   - [EventManager](#24-eventmanager)
3. [核心工具函数](#3-核心工具函数)
4. [使用示例](#4-使用示例)
   - [基本使用](#41-基本使用)
   - [数据共享与监控](#42-数据共享与监控)
   - [事件监听](#43-事件监听)
   - [高级功能](#44-高级功能)
5. [最佳实践](#5-最佳实践)
6. [常见问题](#6-常见问题)
7. [API参考](#7-api参考)

## 1. 模块组件概览

`manager_process3` 模块由以下主要组件构成：

### 1.1 导出的类

- **ManagedMultiProcess**: 核心类，负责管理多进程任务处理，提供高级功能如数据共享和事件监听
- **SharedDataManager**: 进程间共享数据管理器，提供线程安全的数据存取接口
- **ProcessEventManager**: 进程事件管理器，负责处理多进程间的事件通知和回调
- **EventManager**: 事件管理器，提供线程级别的事件创建、监听和触发机制

### 1.2 内部组件

- **core.py**: 包含底层工作进程循环和回调处理功能的核心实现
- **managed_helper.py**: 提供进程监控和资源管理的辅助功能

### 1.3 组件关系

```
ManagedMultiProcess
    ├── SharedDataManager (管理共享数据)
    ├── ProcessEventManager (管理进程事件)
    ├── EventManager (管理线程事件)
    └── ProcessMonitor (监控进程状态)
```

## 2. 主要类详解

### 2.1 ManagedMultiProcess

`ManagedMultiProcess` 是整个模块的核心类，它封装了多进程任务处理的复杂性，提供了简洁的接口来创建、管理和监控工作进程。

#### 2.1.1 主要特性

- **自动任务分配**: 将输入数据自动分配给工作进程处理
- **共享数据管理**: 提供进程间安全的数据共享机制
- **状态监控**: 实时监控工作进程的状态和进度
- **错误处理**: 捕获和记录工作进程中的错误，避免单个错误导致整个任务失败
- **资源管理**: 自动管理进程资源，防止资源泄漏
- **数据变化监听**: 支持监听共享数据的变化，并在主进程中执行回调
- **事件机制**: 提供事件监听接口，允许对不同阶段的处理进行干预
- **上下文管理**: 支持使用 `with` 语句进行资源自动管理

#### 2.1.2 构造函数

```python
def __init__(
    self,
    input_data: Iterable[Any],
    callback_func: Callable,
    num_processes: int = 3,
    *custom_args,
    **kwargs,
):
```

**参数说明:**

- **input_data** (Iterable[Any]): 要处理的输入数据集合（列表、集合或任何可迭代对象）
- **callback_func** (Callable): 工作函数，每个工作进程将调用此函数处理输入数据
  - 函数签名: `callback_func(shared_data_manager, item, *custom_args, **kwargs)`
  - `shared_data_manager`: 共享数据管理器的代理对象
  - `item`: 从 `input_data` 中获取的单个数据项
  - `*custom_args`, `**kwargs`: 传递给构造函数的额外参数
- **num_processes** (int, 可选): 工作进程数量，默认为 3
- ***custom_args**: 传递给 `callback_func` 的附加位置参数
- ***\*kwargs**: 传递给 `callback_func` 的附加关键字参数

**工作函数示例:**

```python
def process_task(shared_data_manager, task_item, extra_arg1="", extra_arg2=""):
    """工作函数示例"""
    # 获取共享锁
    lock = shared_data_manager.get_lock()
    
    # 安全地访问共享数据
    with lock:
        try:
            # 处理任务
            result = f"Processed: {task_item}"
            
            # 将结果存入共享数据
            current_results = shared_data_manager.get_value("results", default=[])
            shared_data_manager.append_to_list("results", result)
            
            # 更新状态信息
            shared_data_manager.add_value("status", f"Processing: {task_item}")
            
        except Exception as e:
            # 记录错误
            shared_data_manager.record_error({
                "item": task_item,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
```

#### 2.1.3 主要方法

##### 运行与控制

**run()**: 启动多进程处理任务
```python
def run(self):
```
- **说明**: 启动所有工作进程并开始处理任务。此方法是非阻塞的，会立即返回。
- **返回值**: 无

**wait_all(timeout=None)**: 等待所有任务完成
```python
def wait_all(self, timeout=None):
```
- **说明**: 阻塞直到所有工作进程完成任务处理或超时
- **参数**: 
  - **timeout** (float, 可选): 最大等待时间（秒）。如果为None，则无限期等待。
- **返回值**: 
  - **bool**: 如果所有任务在超时前完成则返回True，否则返回False

**stop_all(immediate=False, force_timeout=None, callback=None, block=False)**: 停止所有工作进程
```python
def stop_all(self, immediate=False, force_timeout=None, callback=None, block=False):
```
- **说明**: 停止所有工作进程，可选择立即强制终止或优雅停止
- **参数**: 
  - **immediate** (bool, 可选): 如果为True，立即终止进程；否则尝试优雅停止。默认为False
  - **force_timeout** (float, 可选): 优雅停止的超时时间（秒）。超过此时间将强制终止。默认为None
  - **callback** (callable, 可选): 停止完成后要执行的回调函数。默认为None
  - **block** (bool, 可选): 如果为True，则阻塞直到停止完成；否则在后台线程中执行停止操作。默认为False
- **返回值**: 
  - 如果block=True，返回一个包含每个进程停止状态的字典
  - 如果block=False，立即返回None

**is_running()**: 检查处理任务是否正在进行
```python
def is_running(self) -> bool:
```
- **说明**: 检查是否有工作进程正在运行
- **返回值**: 
  - **bool**: 如果有工作进程在运行，返回True；否则返回False

##### 数据访问与结果获取

**get_results()**: 获取所有共享数据
```python
def get_results(self) -> Dict[str, Any]:
```
- **说明**: 获取所有共享数据的副本
- **返回值**: 
  - **Dict[str, Any]**: 包含所有共享数据的字典

**get_all_errors()**: 获取所有错误信息
```python
def get_all_errors(self) -> List[Dict[str, Any]]:
```
- **说明**: 获取所有工作进程中记录的错误信息
- **返回值**: 
  - **List[Dict[str, Any]]**: 包含错误信息的列表

**get_shared_value(key, default=None)**: 获取特定键的共享值
```python
def get_shared_value(self, key: str, default: Any = None) -> Any:
```
- **说明**: 获取共享数据中特定键对应的值
- **参数**: 
  - **key** (str): 要获取的共享数据的键名
  - **default** (Any, 可选): 如果键不存在则返回此默认值。默认为None
- **返回值**: 
  - **Any**: 键对应的值，或如果键不存在则返回默认值

**get_shared_list(key, default=None)**: 获取特定键的共享列表
```python
def get_shared_list(self, key: str, default: Optional[List[Any]] = None) -> Optional[List[Any]]:
```
- **说明**: 获取共享数据中特定键对应的列表
- **参数**: 
  - **key** (str): 要获取的共享列表的键名
  - **default** (Optional[List[Any]], 可选): 如果键不存在则返回此默认值。默认为None
- **返回值**: 
  - **Optional[List[Any]]**: 键对应的列表，或如果键不存在则返回默认值

**get_shared_set(key, default=None)**: 获取特定键的共享集合
```python
def get_shared_set(self, key: str, default: Optional[Set[Any]] = None) -> Optional[Set[Any]]:
```
- **说明**: 获取共享数据中特定键对应的集合
- **参数**: 
  - **key** (str): 要获取的共享集合的键名
  - **default** (Optional[Set[Any]], 可选): 如果键不存在则返回此默认值。默认为None
- **返回值**: 
  - **Optional[Set[Any]]**: 键对应的集合，或如果键不存在则返回默认值

**get_status()**: 获取所有工作进程的状态
```python
def get_status(self) -> Dict[int, str]:
```
- **说明**: 获取每个工作进程的当前状态
- **返回值**: 
  - **Dict[int, str]**: 一个字典，键是进程ID，值是状态字符串

**get_process_status_summary()**: 获取进程状态摘要
```python
def get_process_status_summary(self) -> Dict[str, Any]:
```
- **说明**: 获取所有工作进程状态的详细摘要
- **返回值**: 
  - **Dict[str, Any]**: 包含状态摘要的字典，包括总数、各状态数量、最近状态变化等信息

##### 数据监控

**watch_shared_data(keys, callback, *args, **kwargs)**: 监听共享数据变化
```python
def watch_shared_data(self, keys: Union[str, List[str]], callback: Callable[..., None], *args, **kwargs):
```
- **说明**: 注册一个回调函数，监听共享数据中特定键的值变化
- **参数**: 
  - **keys** (Union[str, List[str]]): 要监听的共享数据键名，可以是单个键或键的列表
  - **callback** (Callable[..., None]): 当数据变化时要调用的回调函数
    - 回调函数签名: `callback(key, old_value, new_value, lock, shared_data_manager, *args, **kwargs)`
    - `key`: 发生变化的键名
    - `old_value`: 变化前的旧值
    - `new_value`: 变化后的新值
    - `lock`: 共享锁对象，用于在回调中安全访问共享数据
    - `shared_data_manager`: 共享数据管理器代理对象
    - `*args`, `**kwargs`: 调用 `watch_shared_data` 时传入的附加参数
  - ***args**: 传递给回调函数的附加位置参数
  - ***\*kwargs**: 传递给回调函数的附加关键字参数
- **返回值**: 无

**unwatch_shared_data(keys, callback=None)**: 取消共享数据监听
```python
def unwatch_shared_data(self, keys: Union[str, List[str]], callback: Optional[Callable] = None) -> bool:
```
- **说明**: 取消对共享数据变化的监听
- **参数**: 
  - **keys** (Union[str, List[str]]): 要取消监听的键名，可以是单个键或键的列表
  - **callback** (Optional[Callable], 可选): 要取消的特定回调函数。如果为None，取消所有与键关联的回调。默认为None
- **返回值**: 
  - **bool**: 如果成功取消至少一个监听，返回True；否则返回False

**watch_data_path(paths, callback, *args, **kwargs)**: 监听共享数据中的特定路径
```python
def watch_data_path(self, paths: Union[str, List[str]], callback: Callable[..., None], *args, **kwargs):
```
- **说明**: 注册回调函数，监听共享数据中特定路径的变化（适用于嵌套数据结构）
- **参数**: 
  - **paths** (Union[str, List[str]]): 要监听的数据路径，格式为"key.subkey[0].property"，可以是单个路径或路径列表
  - **callback** (Callable[..., None]): 当路径数据变化时要调用的回调函数
    - 回调函数签名: 与 `watch_shared_data` 相同，但提供的是路径中的数据变化
  - ***args**, ***\*kwargs**: 传递给回调函数的附加参数
- **返回值**: 无

**unwatch_data_path(paths, callback=None)**: 取消数据路径监听
```python
def unwatch_data_path(self, paths: Union[str, List[str]], callback: Optional[Callable] = None) -> bool:
```
- **说明**: 取消对特定数据路径的监听
- **参数**: 
  - **paths** (Union[str, List[str]]): 要取消监听的路径，可以是单个路径或路径列表
  - **callback** (Optional[Callable], 可选): 要取消的特定回调函数。如果为None，取消所有与路径关联的回调。默认为None
- **返回值**: 
  - **bool**: 如果成功取消至少一个监听，返回True；否则返回False

##### 事件监听

**listen_event(event_key, callback, *args, **kwargs)**: 监听进程事件
```python
def listen_event(self, event_key: str, callback: Callable[..., None], *args, **kwargs) -> bool:
```
- **说明**: 注册一个回调函数以监听特定事件
- **参数**: 
  - **event_key** (str): 事件键名，可以是预定义事件（如"PROCESS_COMPLETED"）或自定义事件名
  - **callback** (Callable[..., None]): 当事件触发时要调用的回调函数
    - 回调函数签名: `callback(mp_instance, *args, **kwargs)`
    - `mp_instance`: 当前的 `ManagedMultiProcess` 实例
    - `*args`, `**kwargs`: 调用 `listen_event` 时提供的附加参数
  - ***args**, ***\*kwargs**: 传递给回调函数的附加参数
- **返回值**: 
  - **bool**: 如果成功注册事件监听，返回True；否则返回False

**unlisten_event(event_key, callback=None)**: 取消事件监听
```python
def unlisten_event(self, event_key: str, callback: Optional[Callable] = None) -> bool:
```
- **说明**: 取消对特定事件的监听
- **参数**: 
  - **event_key** (str): 要取消监听的事件键名
  - **callback** (Optional[Callable], 可选): 要取消的特定回调函数。如果为None，取消所有与事件关联的回调。默认为None
- **返回值**: 
  - **bool**: 如果成功取消至少一个事件监听，返回True；否则返回False

##### 事件管理

**create_event(key)**: 创建线程事件
```python
def create_event(self, key: str) -> threading.Event:
```
- **说明**: 创建一个新的线程事件并与键名关联
- **参数**: 
  - **key** (str): 事件的键名
- **返回值**: 
  - **threading.Event**: 创建的事件对象

**get_event(key)**: 获取事件
```python
def get_event(self, key: str) -> Optional[threading.Event]:
```
- **说明**: 获取指定键名的事件对象
- **参数**: 
  - **key** (str): 事件的键名
- **返回值**: 
  - **Optional[threading.Event]**: 事件对象，如果键名不存在则返回None

**set_event(key)**: 设置事件状态为已触发
```python
def set_event(self, key: str) -> bool:
```
- **说明**: 设置指定事件的状态为已触发
- **参数**: 
  - **key** (str): 事件的键名
- **返回值**: 
  - **bool**: 如果成功设置事件，返回True；否则返回False

**clear_event(key)**: 清除事件的触发状态
```python
def clear_event(self, key: str) -> bool:
```
- **说明**: 清除指定事件的触发状态
- **参数**: 
  - **key** (str): 事件的键名
- **返回值**: 
  - **bool**: 如果成功清除事件状态，返回True；否则返回False

**is_event_set(key)**: 检查事件是否已触发
```python
def is_event_set(self, key: str) -> Optional[bool]:
```
- **说明**: 检查指定事件是否处于已触发状态
- **参数**: 
  - **key** (str): 事件的键名
- **返回值**: 
  - **Optional[bool]**: 如果事件存在，返回其触发状态；如果事件不存在，返回None

**wait_event(key, timeout=None)**: 等待事件触发
```python
def wait_event(self, key: str, timeout: Optional[float] = None) -> bool:
```
- **说明**: 阻塞等待指定事件被触发或超时
- **参数**: 
  - **key** (str): 事件的键名
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为None，表示无限期等待
- **返回值**: 
  - **bool**: 如果事件在超时前被触发，返回True；否则返回False

**wait_any_event(keys, timeout=None)**: 等待任意一个事件触发
```python
def wait_any_event(self, keys: List[str], timeout: Optional[float] = None) -> Optional[str]:
```
- **说明**: 等待多个事件中的任意一个被触发
- **参数**: 
  - **keys** (List[str]): 要等待的事件键名列表
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为None，表示无限期等待
- **返回值**: 
  - **Optional[str]**: 第一个被触发的事件键名，如果超时则返回None

**wait_all_events(keys, timeout=None)**: 等待所有事件触发
```python
def wait_all_events(self, keys: List[str], timeout: Optional[float] = None) -> bool:
```
- **说明**: 等待所有指定的事件都被触发
- **参数**: 
  - **keys** (List[str]): 要等待的事件键名列表
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为None，表示无限期等待
- **返回值**: 
  - **bool**: 如果所有事件在超时前都被触发，返回True；否则返回False

##### 资源管理

**close_shared_data_resources()**: 关闭共享数据资源
```python
def close_shared_data_resources(self):
```
- **说明**: 释放所有与共享数据相关的资源
- **返回值**: 无

**check_and_release_lock(timeout=0.5, force=False)**: 检查并释放可能被阻塞的共享锁
```python
def check_and_release_lock(self, timeout=0.5, force=False) -> dict:
```
- **说明**: 检测并尝试释放可能被阻塞的共享锁
- **参数**: 
  - **timeout** (float, 可选): 尝试获取锁的超时时间（秒）。默认为0.5
  - **force** (bool, 可选): 是否强制释放锁。默认为False
- **返回值**: 
  - **dict**: 包含检查结果的字典

#### 2.1.4 上下文管理器支持

`ManagedMultiProcess` 支持使用 `with` 语句进行资源管理：

```python
with ManagedMultiProcess(input_data, worker_func, num_processes=4) as mp:
    mp.run()
    # 执行其他操作...
    results = mp.get_results()
# 退出with语句块时自动调用 stop_all() 释放资源
```

#### 2.1.5 预定义事件

`ManagedMultiProcess` 提供了一系列预定义事件，可用于监听处理过程的不同阶段：

- **PROCESS_CREATED**: 所有子进程创建完成后触发
- **PROCESS_COMPLETED**: 所有子进程执行完成后立即触发
- **PROCESS_COMPLETED_WITH_DATA**: 所有子进程执行完成且数据更新队列为空后触发
- **PROCESS_STOPPED**: 所有子进程停止后立即触发
- **PROCESS_STOPPED_WITH_DATA**: 所有子进程停止且数据更新队列为空后触发
- **PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP**: 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
- **PROCESS_ALL_COMPLETED_BEFORE_STOP**: 调用stop_all后，所有子进程正常执行完成(不等待数据更新队列为空)，在__perform_stop_all前触发 

### 2.2 SharedDataManager

`SharedDataManager` 是一个专门设计用于多进程环境中安全共享数据的类。它封装了 Python 原生的 `multiprocessing.Manager` 的功能，提供了一个更简洁、更安全的接口来在工作进程之间共享和同步数据。

#### 2.2.1 主要特性

- **多进程安全**: 提供线程/进程安全的数据访问机制
- **统一数据接口**: 提供一致的API访问不同类型的共享数据结构（字典、列表、集合等）
- **锁机制**: 内置锁对象，确保数据一致性和完整性
- **错误处理**: 提供专门的错误记录功能
- **数据通知**: 支持数据变化的回调通知机制
- **适配常见数据结构**: 支持字典、列表、集合等Python常用数据结构

#### 2.2.2 构造函数

```python
def __init__(self):
```

**说明:**
- 初始化 `SharedDataManager` 管理器
- 创建并启动一个 `multiprocessing.Manager`
- 初始化共享字典、共享锁和数据变更通知机制
- 不需要传入参数

#### 2.2.3 基本数据操作

##### 获取共享锁

```python
def get_lock(self) -> AcquirerProxy:
```

- **说明**: 获取共享锁对象，用于控制对共享数据的访问
- **返回值**: 
  - **AcquirerProxy**: `Manager` 创建的锁对象，可用于 `with` 语句

##### 添加/更新值

```python
def add_value(self, key: str, value: Any):
```

- **说明**: 将值添加或更新到共享字典中
- **参数**: 
  - **key** (str): 键名
  - **value** (Any): 要存储的值
- **注意**: 调用此方法前应已获取锁

##### 获取值

```python
def get_value(self, key: str, default: Any = None) -> Any:
```

- **说明**: 从共享字典中获取特定键的值
- **参数**: 
  - **key** (str): 要获取的值的键名
  - **default** (Any, 可选): 如果键不存在，返回此默认值。默认为None
- **返回值**: 
  - **Any**: 键对应的值，或如果键不存在则返回默认值
- **注意**: 调用此方法前应已获取锁

##### 批量更新值

```python
def update_values(self, update_dict: Dict[str, Any]):
```

- **说明**: 使用字典批量更新共享字典中的多个值
- **参数**: 
  - **update_dict** (Dict[str, Any]): 包含要更新的键值对的字典
- **注意**: 调用此方法前应已获取锁

#### 2.2.4 列表操作

##### 追加到列表

```python
def append_to_list(self, key: str, value: Any):
```

- **说明**: 将值追加到共享列表中
- **参数**: 
  - **key** (str): 列表的键名
  - **value** (Any): 要追加的值
- **注意**: 
  - 调用此方法前应已获取锁
  - 如果键不存在，会自动创建新的共享列表
  - 如果键存在但不是列表，会抛出异常

##### 获取列表

```python
def get_list(self, key: str, default: Optional[List[Any]] = None) -> Optional[List[Any]]:
```

- **说明**: 获取共享列表的副本
- **参数**: 
  - **key** (str): 要获取的列表的键名
  - **default** (Optional[List[Any]], 可选): 如果键不存在，返回此默认值。默认为None
- **返回值**: 
  - **Optional[List[Any]]**: 列表的副本，或如果键不存在则返回默认值
- **注意**: 
  - 调用此方法前应已获取锁
  - 返回的是普通Python列表，不是Manager代理对象

#### 2.2.5 集合操作

##### 添加到集合

```python
def add_to_set(self, key: str, value: Any):
```

- **说明**: 将值添加到共享集合中
- **参数**: 
  - **key** (str): 集合的键名
  - **value** (Any): 要添加的值
- **注意**: 
  - 调用此方法前应已获取锁
  - 如果键不存在，会自动创建新的共享集合
  - 如果键存在但不是列表（内部表示为列表），会抛出异常
  - 由于 `multiprocessing.Manager` 不直接支持集合，集合是用列表模拟的

##### 获取集合

```python
def get_set(self, key: str, default: Optional[Set[Any]] = None) -> Optional[Set[Any]]:
```

- **说明**: 获取共享集合的副本
- **参数**: 
  - **key** (str): 要获取的集合的键名
  - **default** (Optional[Set[Any]], 可选): 如果键不存在，返回此默认值。默认为None
- **返回值**: 
  - **Optional[Set[Any]]**: 集合的副本，或如果键不存在则返回默认值
- **注意**: 
  - 调用此方法前应已获取锁
  - 返回的是普通Python集合，不是Manager代理对象

#### 2.2.6 错误处理

##### 记录错误

```python
def record_error(self, error_info: Dict[str, Any]):
```

- **说明**: 将错误信息记录到共享错误列表中
- **参数**: 
  - **error_info** (Dict[str, Any]): 包含错误详情的字典
- **注意**: 
  - 调用此方法前应已获取锁
  - 错误信息将被追加到 "__errors" 键对应的共享列表中

##### 获取错误

```python
def get_errors(self) -> List[Dict[str, Any]]:
```

- **说明**: 获取所有记录的错误信息
- **返回值**: 
  - **List[Dict[str, Any]]**: 包含所有错误信息的列表
- **注意**: 
  - 调用此方法前应已获取锁
  - 返回的是普通Python列表，不是Manager代理对象 

#### 2.2.7 数据通知机制

##### 设置实时通知

```python
def set_enable_realtime_notify(self, enable: bool = True):
```

- **说明**: 设置是否启用实时数据变化通知功能
- **参数**: 
  - **enable** (bool, 可选): 是否启用实时通知，默认为True
- **注意**: 
  - 启用时，所有数据变更都会被放入实时通知队列，供 `ManagedMultiProcess` 监听
  - 禁用时，数据变更不会被放入实时通知队列，减少资源消耗

##### 注册变更回调

```python
def register_change_callback(self, key: str, callback_func: Callable[[str, Any, AcquirerProxy], None]):
```

- **说明**: 注册一个回调函数，监听特定键的值变化
- **参数**: 
  - **key** (str): 要监听的键名
  - **callback_func** (Callable[[str, Any, AcquirerProxy], None]): 当键值变化时要调用的回调函数
    - 回调函数签名: `callback_func(key, value, lock)`
    - `key`: 发生变化的键名
    - `value`: 变化后的新值
    - `lock`: 共享锁对象
- **注意**: 
  - 回调函数会在独立线程中执行，不会阻塞数据操作
  - 回调执行时会自动获取锁

##### 取消变更回调

```python
def unregister_change_callback(self, key: str, callback_func: Optional[Callable] = None):
```

- **说明**: 取消之前注册的变更回调
- **参数**: 
  - **key** (str): 要取消监听的键名
  - **callback_func** (Optional[Callable], 可选): 要取消的特定回调函数。如果为None，取消所有与键关联的回调。默认为None

##### 获取实时通知队列

```python
def get_realtime_notify_queue(self) -> Queue:
```

- **说明**: 获取用于实时数据变化通知的队列
- **返回值**: 
  - **Queue**: 实时通知队列对象
- **注意**: 
  - 此队列由 `ManagedMultiProcess` 内部使用，通常不需要直接访问

#### 2.2.8 管理与清理

##### 获取所有数据

```python
def get_all_data_as_dict(self) -> Dict[str, Any]:
```

- **说明**: 获取共享字典中所有数据的副本
- **返回值**: 
  - **Dict[str, Any]**: 包含所有共享数据的字典
- **注意**: 
  - 返回的是普通Python字典，不是Manager代理对象
  - 调用此方法前应已获取锁

##### 转换代理对象

```python
def __convert_manager_proxy(self, proxy_obj):
```

- **说明**: 将Manager代理对象转换为标准Python对象
- **参数**: 
  - **proxy_obj**: 要转换的Manager代理对象
- **返回值**: 
  - 转换后的普通Python对象
- **注意**: 
  - 这是一个内部方法，不应直接调用
  - 支持递归转换嵌套的代理对象

##### 重置共享锁

```python
def reset_shared_lock(self) -> bool:
```

- **说明**: 尝试重置可能被阻塞的共享锁
- **返回值**: 
  - **bool**: 如果成功重置锁，返回True；否则返回False
- **注意**: 
  - 这是一个紧急方法，只有在怀疑锁被阻塞时才应使用
  - 可能导致数据不一致，应谨慎使用

##### 关闭资源

```python
def shutdown(self):
```

- **说明**: 关闭Manager和相关资源
- **注意**: 
  - 关闭后，所有共享数据将不可访问
  - 通常由 `ManagedMultiProcess` 在 `stop_all` 或析构函数中调用

#### 2.2.9 使用示例

```python
# 在多进程工作函数中使用 SharedDataManager
def worker_function(shared_data_manager, task_item):
    # 获取共享锁
    lock = shared_data_manager.get_lock()
    
    # 安全地访问共享数据
    with lock:
        # 获取现有数据 (如果不存在，使用空列表作为默认值)
        results = shared_data_manager.get_list("results", default=[])
        
        # 处理任务
        result = f"Processed: {task_item}"
        
        # 更新共享数据
        shared_data_manager.append_to_list("results", result)
        
        # 记录状态
        shared_data_manager.add_value("status", "processing")
        
        # 使用集合记录唯一值
        shared_data_manager.add_to_set("processed_items", task_item)
    
    try:
        # 执行任务处理逻辑
        process_result = complex_processing(task_item)
        
        # 再次获取锁，更新最终结果
        with lock:
            shared_data_manager.append_to_list("final_results", process_result)
    except Exception as e:
        # 记录错误
        with lock:
            shared_data_manager.record_error({
                "task": task_item,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
```

### 2.3 ProcessEventManager

`ProcessEventManager` 是一个专门设计用于管理和处理多进程事件的类。它提供了一个统一的机制，允许在多进程程序中的不同阶段触发事件并执行回调函数。

#### 2.3.1 主要特性

- **事件注册**: 允许注册回调函数以响应特定事件
- **事件触发**: 在特定条件下触发事件并执行关联的回调函数
- **异步执行**: 在独立线程中执行回调，不阻塞主线程
- **参数传递**: 支持向回调函数传递额外参数
- **预定义事件**: 包含一系列与多进程生命周期相关的预定义事件

#### 2.3.2 预定义事件常量

- **PROCESS_CREATED**: 所有子进程创建完成后触发
- **PROCESS_COMPLETED**: 所有子进程执行完成后立即触发
- **PROCESS_COMPLETED_WITH_DATA**: 所有子进程执行完成且数据更新队列为空后触发
- **PROCESS_STOPPED**: 所有子进程停止后立即触发
- **PROCESS_STOPPED_WITH_DATA**: 所有子进程停止且数据更新队列为空后触发
- **PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP**: 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
- **PROCESS_ALL_COMPLETED_BEFORE_STOP**: 调用stop_all后，所有子进程正常执行完成(不等待数据更新队列为空)，在__perform_stop_all前触发

#### 2.3.3 构造函数

```python
def __init__(self, mp_instance=None):
```

**参数说明:**
- **mp_instance**: 关联的ManagedMultiProcess实例，可选。如果不在初始化时提供，可以稍后通过`set_mp_instance`方法设置。

#### 2.3.4 主要方法

##### 设置ManagedMultiProcess实例

```python
def set_mp_instance(self, mp_instance):
```

- **说明**: 设置关联的ManagedMultiProcess实例
- **参数**: 
  - **mp_instance**: ManagedMultiProcess实例

##### 注册事件处理函数

```python
def register_event_handler(self, event_key: str, handler: Callable, *args, **kwargs) -> bool:
```

- **说明**: 注册一个事件处理函数，以响应特定事件
- **参数**: 
  - **event_key** (str): 事件键名，可以是预定义事件或自定义事件名
  - **handler** (Callable): 回调函数，第一个参数会接收ManagedMultiProcess实例
  - ***args**: 传递给回调函数的额外位置参数
  - ***\*kwargs**: 传递给回调函数的额外关键字参数
- **返回值**: 
  - **bool**: 是否成功注册事件处理函数
- **注意**:
  - 如果事件已触发，会立即执行新注册的处理函数

##### 取消注册事件处理函数

```python
def unregister_event_handler(self, event_key: str, handler: Optional[Callable] = None) -> bool:
```

- **说明**: 取消注册事件处理函数
- **参数**: 
  - **event_key** (str): 事件键名
  - **handler** (Optional[Callable], 可选): 要取消的特定回调函数。如果为None，则移除所有关联该事件的处理函数。默认为None
- **返回值**: 
  - **bool**: 是否成功取消注册

##### 触发事件

```python
def trigger_event(self, event_key: str, mp_instance=None) -> bool:
```

- **说明**: 触发特定事件，执行所有关联的处理函数
- **参数**: 
  - **event_key** (str): 要触发的事件键名
  - **mp_instance**: 要传递给处理函数的ManagedMultiProcess实例。如果为None，使用构造时提供的实例。
- **返回值**: 
  - **bool**: 是否成功触发事件
- **注意**:
  - 处理函数会在独立线程中异步执行
  - 事件只会被触发一次，重复触发相同事件不会再次执行处理函数

##### 重置事件

```python
def reset_event(self, event_key: str) -> bool:
```

- **说明**: 重置事件的触发状态，使其可以再次被触发
- **参数**: 
  - **event_key** (str): 要重置的事件键名
- **返回值**: 
  - **bool**: 是否成功重置事件

##### 重置所有事件

```python
def reset_all_events(self) -> None:
```

- **说明**: 重置所有事件的触发状态

##### 检查事件是否已触发

```python
def is_event_triggered(self, event_key: str) -> bool:
```

- **说明**: 检查特定事件是否已被触发
- **参数**: 
  - **event_key** (str): 要检查的事件键名
- **返回值**: 
  - **bool**: 如果事件已触发，返回True；否则返回False

##### 获取所有事件

```python
def get_all_events(self) -> List[str]:
```

- **说明**: 获取所有已注册的事件键名
- **返回值**: 
  - **List[str]**: 包含所有事件键名的列表

##### 获取已触发事件

```python
def get_triggered_events(self) -> List[str]:
```

- **说明**: 获取所有已触发的事件键名
- **返回值**: 
  - **List[str]**: 包含所有已触发事件键名的列表

##### 检查事件是否有处理函数

```python
def has_handlers(self, event_key: str) -> bool:
```

- **说明**: 检查特定事件是否有关联的处理函数
- **参数**: 
  - **event_key** (str): 要检查的事件键名
- **返回值**: 
  - **bool**: 如果事件有处理函数，返回True；否则返回False

#### 2.3.5 使用示例

```python
# 创建ManagedMultiProcess实例
mp = ManagedMultiProcess(input_data, worker_function, num_processes=4)

# 定义事件处理函数
def on_all_completed(mp_instance, *args, **kwargs):
    print(f"所有进程已完成！参数: {args}, {kwargs}")
    results = mp_instance.get_results()
    print(f"处理结果: {results}")

# 通过ManagedMultiProcess的接口注册事件处理函数
mp.listen_event("PROCESS_COMPLETED", on_all_completed, "extra_info", tag="demo")

# 启动处理
mp.run()

# 等待处理完成
mp.wait_all()

# 也可以直接使用ProcessEventManager (通常不需要)
event_manager = mp.get_process_event_manager()
event_manager.register_event_handler("PROCESS_STOPPED", 
                                    lambda mp_inst: print("进程已停止"),
                                    additional_param="value")
``` 

### 2.4 EventManager

`EventManager` 是一个多线程事件管理器，它提供了统一的接口来创建、管理和同步线程事件。它是对 Python 标准库中 `threading.Event` 对象的高级封装，提供了更丰富的功能和更便捷的接口。

#### 2.4.1 主要特性

- **事件命名**: 通过键名标识事件，使事件管理更加直观
- **线程安全**: 所有操作都是线程安全的
- **等待策略**: 提供灵活的单个事件等待和多事件等待策略
- **批量操作**: 支持同时操作多个事件
- **状态查询**: 提供丰富的事件状态查询方法

#### 2.4.2 构造函数

```python
def __init__(self):
```

**说明:**
- 初始化 `EventManager` 实例
- 创建存储事件的字典和线程锁

#### 2.4.3 事件创建和获取

##### 创建事件

```python
def create_event(self, key: str) -> threading.Event:
```

- **说明**: 创建一个多线程 Event 事件，并用指定的键值存储
- **参数**: 
  - **key** (str): Event 事件的唯一标识键值
- **返回值**: 
  - **threading.Event**: 创建的事件对象

##### 获取事件

```python
def get_event(self, key: str) -> Optional[threading.Event]:
```

- **说明**: 获取指定键值的 Event 事件
- **参数**: 
  - **key** (str): 要获取的事件的键值
- **返回值**: 
  - **Optional[threading.Event]**: 如果存在，返回事件对象；否则返回 None

##### 检查事件是否存在

```python
def has_event(self, key: str) -> bool:
```

- **说明**: 检查指定键值的事件是否存在
- **参数**: 
  - **key** (str): 要检查的事件键值
- **返回值**: 
  - **bool**: 如果事件存在，返回 True；否则返回 False

##### 获取或创建事件

```python
def get_or_create_event(self, key: str) -> threading.Event:
```

- **说明**: 获取指定键值的事件，如果不存在则创建
- **参数**: 
  - **key** (str): 事件的键值
- **返回值**: 
  - **threading.Event**: 已存在或新创建的事件对象

#### 2.4.4 事件操作

##### 设置事件

```python
def set_event(self, key: str) -> bool:
```

- **说明**: 设置指定事件的状态为已触发
- **参数**: 
  - **key** (str): 要设置的事件键值
- **返回值**: 
  - **bool**: 如果事件存在且被成功设置，返回 True；否则返回 False

##### 清除事件

```python
def clear_event(self, key: str) -> bool:
```

- **说明**: 清除指定事件的触发状态
- **参数**: 
  - **key** (str): 要清除的事件键值
- **返回值**: 
  - **bool**: 如果事件存在且被成功清除，返回 True；否则返回 False

##### 检查事件是否已触发

```python
def is_event_set(self, key: str) -> Optional[bool]:
```

- **说明**: 检查指定事件是否已触发
- **参数**: 
  - **key** (str): 要检查的事件键值
- **返回值**: 
  - **Optional[bool]**: 如果事件存在，返回其触发状态；否则返回 None

##### 等待事件

```python
def wait_event(self, key: str, timeout: Optional[float] = None) -> bool:
```

- **说明**: 等待指定事件被触发
- **参数**: 
  - **key** (str): 要等待的事件键值
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为 None，表示无限期等待
- **返回值**: 
  - **bool**: 如果事件在超时前被触发，返回 True；否则返回 False

##### 删除事件

```python
def delete_event(self, key: str) -> bool:
```

- **说明**: 删除指定事件
- **参数**: 
  - **key** (str): 要删除的事件键值
- **返回值**: 
  - **bool**: 如果事件存在且被成功删除，返回 True；否则返回 False

#### 2.4.5 批量操作

##### 设置所有事件

```python
def set_all_events(self) -> None:
```

- **说明**: 触发所有已注册的事件
- **注意**:
  - 会遍历所有事件并逐个调用 `set()` 方法

##### 清除所有事件

```python
def clear_all_events(self) -> None:
```

- **说明**: 清除所有事件的触发状态
- **注意**:
  - 会遍历所有事件并逐个调用 `clear()` 方法

##### 等待任意事件

```python
def wait_any_event(self, keys: List[str], timeout: Optional[float] = None) -> Optional[str]:
```

- **说明**: 等待多个事件中的任意一个被触发
- **参数**: 
  - **keys** (List[str]): 要等待的事件键值列表
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为 None，表示无限期等待
- **返回值**: 
  - **Optional[str]**: 第一个被触发的事件键值，如果超时则返回 None

##### 等待所有事件

```python
def wait_all_events(self, keys: List[str], timeout: Optional[float] = None) -> bool:
```

- **说明**: 等待所有指定的事件都被触发
- **参数**: 
  - **keys** (List[str]): 要等待的事件键值列表
  - **timeout** (Optional[float], 可选): 等待超时时间（秒）。默认为 None，表示无限期等待
- **返回值**: 
  - **bool**: 如果所有事件在超时前都被触发，返回 True；否则返回 False

#### 2.4.6 状态查询

##### 获取所有事件

```python
def get_all_events(self) -> Dict[str, threading.Event]:
```

- **说明**: 获取所有已注册的事件
- **返回值**: 
  - **Dict[str, threading.Event]**: 键为事件键值，值为事件对象

##### 获取所有事件状态

```python
def get_all_event_states(self) -> Dict[str, bool]:
```

- **说明**: 获取所有事件的触发状态
- **返回值**: 
  - **Dict[str, bool]**: 键为事件键值，值为事件的触发状态

##### 计数事件

```python
def count_events(self) -> int:
```

- **说明**: 计算已注册的事件数量
- **返回值**: 
  - **int**: 事件数量

##### 获取事件键值列表

```python
def get_event_keys(self) -> List[str]:
```

- **说明**: 获取所有已注册的事件键值
- **返回值**: 
  - **List[str]**: 事件键值列表

#### 2.4.7 清理

##### 清理所有

```python
def clear_all(self) -> None:
```

- **说明**: 删除所有事件，完全重置事件管理器

#### 2.4.8 特殊方法

##### `__len__`

```python
def __len__(self) -> int:
```

- **说明**: 获取事件数量，支持使用 `len(event_manager)` 语法
- **返回值**: 
  - **int**: 事件数量

##### `__contains__`

```python
def __contains__(self, key: str) -> bool:
```

- **说明**: 检查事件是否存在，支持使用 `key in event_manager` 语法
- **参数**: 
  - **key** (str): 要检查的事件键值
- **返回值**: 
  - **bool**: 如果事件存在，返回 True；否则返回 False

#### 2.4.9 使用示例

```python
import time
import threading
from global_tools.utils.manager_process3 import EventManager

# 创建事件管理器
event_manager = EventManager()

# 创建事件
event_manager.create_event("task_started")
event_manager.create_event("task_completed")
event_manager.create_event("task_failed")

# 在工作线程中等待和触发事件
def worker_thread():
    print("工作线程启动")
    
    # 设置开始事件
    event_manager.set_event("task_started")
    
    try:
        # 模拟工作
        time.sleep(2)
        
        # 模拟50%概率的成功/失败
        if time.time() % 2 < 1:
            raise Exception("模拟任务失败")
        
        # 设置完成事件
        event_manager.set_event("task_completed")
        print("任务成功完成")
    except Exception as e:
        # 设置失败事件
        event_manager.set_event("task_failed")
        print(f"任务失败: {e}")

# 启动工作线程
thread = threading.Thread(target=worker_thread)
thread.start()

# 在主线程等待任务开始
if event_manager.wait_event("task_started", timeout=1.0):
    print("任务已开始")
else:
    print("等待任务开始超时")

# 等待任务完成或失败
result = event_manager.wait_any_event(["task_completed", "task_failed"], timeout=3.0)
if result == "task_completed":
    print("主线程: 任务成功完成")
elif result == "task_failed":
    print("主线程: 任务失败")
else:
    print("主线程: 等待任务结果超时")

# 确保线程结束
thread.join()

# 获取事件状态
all_states = event_manager.get_all_event_states()
print(f"最终事件状态: {all_states}")
```

## 4. 使用示例

本章节提供了一系列完整的、可复制的示例，展示如何在实际应用中使用 `manager_process3` 模块。

### 4.1 基本使用

这个示例展示了使用 `ManagedMultiProcess` 的基本流程：创建实例、定义工作函数、运行任务和处理结果。

```python
import time
import traceback
from global_tools.utils.manager_process3 import ManagedMultiProcess

# 步骤1：定义工作函数
def process_item(shared_data_manager, item, multiply_factor=1):
    """
    每个工作进程调用此函数来处理单个任务项
    
    Args:
        shared_data_manager: 共享数据管理器代理
        item: 要处理的项目
        multiply_factor: 可选的乘数因子
    """
    # 获取锁，确保数据访问安全
    lock = shared_data_manager.get_lock()
    
    try:
        # 模拟处理时间
        time.sleep(0.5)
        
        # 计算结果
        result = item * multiply_factor
        
        # 安全地更新共享数据
        with lock:
            # 获取现有结果列表或创建新列表
            results = shared_data_manager.get_list("results", default=[])
            # 追加新结果
            shared_data_manager.append_to_list("results", result)
            # 记录处理的项目
            shared_data_manager.add_to_set("processed_items", item)
            # 更新计数
            count = shared_data_manager.get_value("count", default=0)
            shared_data_manager.add_value("count", count + 1)
            
    except Exception as e:
        # 记录任何处理错误
        with lock:
            shared_data_manager.record_error({
                "item": item,
                "error": str(e),
                "traceback": traceback.format_exc()
            })

# 步骤2：准备输入数据
items_to_process = list(range(1, 11))  # 1到10的数字

# 步骤3：创建和配置ManagedMultiProcess实例
mp = ManagedMultiProcess(
    input_data=items_to_process,  # 输入数据
    callback_func=process_item,   # 工作函数
    num_processes=3,              # 使用3个工作进程
    multiply_factor=2             # 额外参数传递给工作函数
)

# 步骤4：启动处理（非阻塞）
mp.run()

# 步骤5：等待所有任务完成
print("等待任务完成...")
mp.wait_all()
print("所有任务已完成")

# 步骤6：获取和处理结果
results = mp.get_results()

# 打印所有共享数据
print(f"处理结果: {results.get('results')}")
print(f"处理项目: {results.get('processed_items')}")
print(f"处理计数: {results.get('count')}")

# 检查是否有错误
errors = mp.get_all_errors()
if errors:
    print(f"处理过程中发生了 {len(errors)} 个错误:")
    for error in errors:
        print(f"  项目 {error['item']}: {error['error']}")

# 步骤7：清理资源
mp.stop_all()
```

### 4.2 数据共享与监控

这个示例展示了如何使用数据监控功能，实时响应共享数据的变化。

```python
import time
from global_tools.utils.manager_process3 import ManagedMultiProcess

# 工作函数
def slow_counter(shared_data_manager, item):
    lock = shared_data_manager.get_lock()
    
    # 初始化计数器（如果尚未初始化）
    with lock:
        if not shared_data_manager.get_value("counter", None):
            shared_data_manager.add_value("counter", 0)
    
    # 每隔1秒增加计数
    for i in range(5):
        time.sleep(1)
        with lock:
            current = shared_data_manager.get_value("counter")
            new_value = current + 1
            shared_data_manager.add_value("counter", new_value)
            # 更新进度
            shared_data_manager.add_value("progress", f"项目 {item}: {(i+1)*20}%")

# 数据变化回调函数
def on_counter_change(key, old_value, new_value, lock, shared_data_manager, *args, **kwargs):
    print(f"计数器变化: {old_value} -> {new_value}")
    
    # 安全地访问其他共享数据
    with lock:
        progress = shared_data_manager.get_value("progress", "未知")
        print(f"当前进度: {progress}")
    
    # 使用传递的额外参数
    if "threshold" in kwargs and new_value >= kwargs["threshold"]:
        print(f"警告：计数器达到阈值 {kwargs['threshold']}！")

# 路径监控回调函数
def on_nested_data_change(key, old_value, new_value, path, lock, shared_data_manager, *args, **kwargs):
    print(f"检测到路径 '{path}' 的变化: {new_value}")

# 创建ManagedMultiProcess实例
mp = ManagedMultiProcess(
    input_data=[1, 2],  # 只处理两个项目
    callback_func=slow_counter,
    num_processes=1      # 使用单个进程以便于观察
)

# 监听"counter"键的变化
mp.watch_shared_data("counter", on_counter_change, threshold=5)

# 添加一些嵌套数据结构（将在工作函数外手动更新）
with mp.get_shared_data_manager().get_lock():
    mp.get_shared_data_manager().add_value("stats", {
        "system": {
            "cpu": 0,
            "memory": 0
        }
    })

# 监听嵌套数据路径
mp.watch_data_path("stats.system.cpu", on_nested_data_change)

# 启动处理
mp.run()

# 主线程模拟更新嵌套数据
time.sleep(2)
with mp.get_shared_data_manager().get_lock():
    stats = mp.get_shared_data_manager().get_value("stats")
    stats["system"]["cpu"] = 75
    mp.get_shared_data_manager().add_value("stats", stats)

# 等待所有任务完成
mp.wait_all()

# 获取最终结果
results = mp.get_results()
print(f"最终计数: {results.get('counter')}")
print(f"最终统计: {results.get('stats')}")

# 清理资源
mp.stop_all()
```

### 4.3 事件监听

这个示例展示了如何使用事件监听机制，在多进程处理的关键阶段执行回调函数。

```python
import time
from global_tools.utils.manager_process3 import ManagedMultiProcess

# 工作函数
def process_task(shared_data_manager, task_id):
    lock = shared_data_manager.get_lock()
    
    with lock:
        shared_data_manager.append_to_list("task_log", f"开始任务 {task_id}")
    
    # 模拟工作
    time.sleep(task_id * 0.5)  # 任务ID越大，处理时间越长
    
    with lock:
        shared_data_manager.append_to_list("task_log", f"完成任务 {task_id}")
        shared_data_manager.append_to_list("completed_tasks", task_id)

# 定义事件回调函数
def on_process_created(mp_instance, tag=None):
    print(f"[{tag}] 所有进程已创建")

def on_process_completed(mp_instance, tag=None):
    print(f"[{tag}] 所有进程已完成执行")
    task_log = mp_instance.get_shared_list("task_log", [])
    print(f"任务日志: {task_log}")

def on_process_completed_with_data(mp_instance, tag=None):
    print(f"[{tag}] 所有进程已完成且数据处理完毕")
    completed = mp_instance.get_shared_list("completed_tasks", [])
    print(f"已完成的任务: {completed}")
    print(f"总完成数量: {len(completed)}")

# 创建任务列表
tasks = list(range(1, 6))  # 5个任务

# 创建ManagedMultiProcess实例
mp = ManagedMultiProcess(
    input_data=tasks,
    callback_func=process_task,
    num_processes=2
)

# 注册事件监听
mp.listen_event("PROCESS_CREATED", on_process_created, tag="事件监听")
mp.listen_event("PROCESS_COMPLETED", on_process_completed, tag="事件监听")
mp.listen_event("PROCESS_COMPLETED_WITH_DATA", on_process_completed_with_data, tag="事件监听")

# 启动处理
mp.run()

# 可以在主线程做其他工作
for i in range(5):
    print(f"主线程工作中... {i+1}/5")
    time.sleep(1)

# 等待所有任务完成
mp.wait_all()

# 清理资源
mp.stop_all()
```

### 4.4 高级功能

这个示例展示了一些更高级的功能，包括自定义线程事件、进程状态监控和错误处理。

```python
import time
import random
import traceback
from global_tools.utils.manager_process3 import ManagedMultiProcess

# 工作函数，可能会随机失败
def unreliable_worker(shared_data_manager, task_id):
    lock = shared_data_manager.get_lock()
    
    try:
        # 获取或创建暂停事件
        pause_event = shared_data_manager.get_event("pause_processing")
        
        # 检查是否应该暂停
        if pause_event and pause_event.is_set():
            with lock:
                shared_data_manager.append_to_list("logs", f"任务 {task_id} 已跳过 (暂停模式)")
            return
        
        # 模拟随机失败
        if random.random() < 0.3:  # 30%概率失败
            raise RuntimeError(f"任务 {task_id} 随机失败")
        
        # 模拟工作
        time.sleep(1)
        
        # 更新结果
        with lock:
            shared_data_manager.append_to_list("successful_tasks", task_id)
            shared_data_manager.append_to_list("logs", f"任务 {task_id} 成功完成")
    
    except Exception as e:
        with lock:
            shared_data_manager.record_error({
                "task_id": task_id,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            shared_data_manager.append_to_list("logs", f"任务 {task_id} 失败: {e}")

# 进程状态监控回调
def on_monitoring_complete(monitor):
    summary = monitor.get_process_status_summary()
    print("进程监控摘要:")
    for status, count in summary["summary"].items():
        if count > 0:
            print(f"  {status}: {count}个进程")

# 创建任务列表
tasks = list(range(1, 11))

# 创建ManagedMultiProcess实例
mp = ManagedMultiProcess(
    input_data=tasks,
    callback_func=unreliable_worker,
    num_processes=3
)

# 创建自定义事件
pause_event = mp.create_event("pause_processing")

# 注册进程状态监控回调
mp.get_process_monitor().set_completion_callback(on_monitoring_complete)

# 启动处理
mp.run()

# 主线程操作
print("等待2秒...")
time.sleep(2)

# 中途暂停新任务处理
print("暂停处理新任务")
pause_event.set()

# 等待所有任务完成
mp.wait_all()

# 获取结果
results = mp.get_results()
print(f"成功任务: {results.get('successful_tasks', [])}")
print(f"日志: {results.get('logs', [])}")

# 检查错误
errors = mp.get_all_errors()
print(f"错误数量: {len(errors)}")
for error in errors:
    print(f"任务 {error['task_id']} 错误: {error['error']}")

# 获取进程状态摘要
status_summary = mp.get_process_status_summary()
print(f"最终进程状态: {status_summary['summary']}")

# 清理资源
mp.stop_all()
```

## 5. 最佳实践

本章节提供了使用 `manager_process3` 模块时的一些最佳实践和建议，帮助您避免常见的陷阱并获得最佳性能。

### 5.1 共享数据访问

- **始终使用锁保护共享数据**: 无论何时访问或修改共享数据，都应获取锁以确保线程和进程安全。
  ```python
  with shared_data_manager.get_lock():
      # 安全地访问/修改共享数据
      shared_data_manager.append_to_list("results", result)
  ```

- **最小化锁持有时间**: 只在必要的操作周围使用锁，避免在持有锁时执行耗时操作。
  ```python
  # 不好的做法
  with lock:
      compute_expensive_result()  # 耗时操作，阻塞其他进程
      shared_data_manager.add_value("result", result)
  
  # 好的做法
  result = compute_expensive_result()  # 锁外执行耗时操作
  with lock:
      shared_data_manager.add_value("result", result)
  ```

- **批量更新数据**: 尽量批量更新数据，减少获取锁的次数。
  ```python
  # 不好的做法
  for item in items:
      with lock:
          shared_data_manager.append_to_list("results", item)
  
  # 好的做法
  results = []
  for item in items:
      results.append(process_item(item))
  
  with lock:
      for result in results:
          shared_data_manager.append_to_list("results", result)
  ```

- **使用集合记录唯一项**: 对于需要跟踪唯一项的场景，使用共享集合而非列表。
  ```python
  # 使用集合记录已处理的项目ID
  with lock:
      shared_data_manager.add_to_set("processed_items", item_id)
  ```

### 5.2 错误处理

- **记录错误细节**: 使用 `record_error` 方法记录详细的错误信息，包括任务标识符、异常信息和堆栈跟踪。
  ```python
  try:
      process_task(item)
  except Exception as e:
      with lock:
          shared_data_manager.record_error({
              "item": item,
              "error": str(e),
              "traceback": traceback.format_exc(),
              "timestamp": time.time()
          })
  ```

- **定期检查错误**: 在主进程中定期检查错误，而不是仅在所有任务完成后。可以使用数据监听功能。
  ```python
  def on_errors_change(key, old_value, new_value, lock, shared_data_manager):
      if key == "errors" and len(new_value) > 0:
          print(f"检测到 {len(new_value)} 个错误!")
          
  mp.watch_shared_data("errors", on_errors_change)
  ```

### 5.3 性能优化

- **合理设置进程数量**: 根据任务特性和系统资源设置合适的进程数，通常为CPU核心数或略多一点。
  ```python
  import multiprocessing
  
  optimal_processes = min(multiprocessing.cpu_count() + 1, 8)  # 最多8个进程
  mp = ManagedMultiProcess(input_data, worker_func, num_processes=optimal_processes)
  ```

- **数据批处理**: 对于大量小任务，考虑将它们按批次分组。
  ```python
  # 假设有10000个小项目需要处理
  # 按每批100个分组
  batch_size = 100
  batches = [items[i:i+batch_size] for i in range(0, len(items), batch_size)]
  
  # 每个工作进程处理一批项目
  def process_batch(shared_data_manager, batch):
      results = []
      for item in batch:
          results.append(process_item(item))
      
      # 批量更新共享数据
      with shared_data_manager.get_lock():
          for result in results:
              shared_data_manager.append_to_list("results", result)
  
  mp = ManagedMultiProcess(batches, process_batch, num_processes=4)
  ```

- **仅监听必要的数据变化**: 避免监听频繁变化的数据，这可能导致大量回调执行，影响性能。

### 5.4 资源管理

- **使用上下文管理器**: 利用 `with` 语句自动管理资源。
  ```python
  with ManagedMultiProcess(items, process_func, num_processes=4) as mp:
      mp.run()
      mp.wait_all()
      results = mp.get_results()
  # 自动调用 mp.stop_all()
  ```

- **显式清理资源**: 如果不使用上下文管理器，确保手动调用 `stop_all()` 清理资源。
  ```python
  try:
      mp = ManagedMultiProcess(items, process_func)
      mp.run()
      results = mp.get_results()
  finally:
      if mp:
          mp.stop_all()
  ```

### 5.5 事件使用

- **避免过早监听事件**: 在启动处理前注册事件监听器，避免错过事件。
  ```python
  mp = ManagedMultiProcess(data, func)
  
  # 先注册事件监听
  mp.listen_event("PROCESS_COMPLETED", on_complete)
  
  # 然后启动处理
  mp.run()
  ```

- **区分同步和异步等待**: 根据需要选择同步等待或异步回调。
  ```python
  # 同步等待所有任务完成
  mp.run()
  mp.wait_all()
  print("所有任务已完成")
  
  # 或者使用异步回调
  mp.listen_event("PROCESS_COMPLETED_WITH_DATA", lambda mp_inst: print("所有任务已完成"))
  mp.run()
  # 主线程可以做其他工作
  ```

## 6. 常见问题

本章节列出了使用 `manager_process3` 模块时可能遇到的常见问题及其解决方案。

### 6.1 进程间通信问题

#### 问题: 获取锁时出现 `EOFError` 或 `BrokenPipeError`

**症状**: 工作进程在尝试获取锁时抛出 `EOFError` 或 `BrokenPipeError` 异常。

**原因**: 这通常表示与Manager进程的连接已断开，可能是因为Manager进程意外终止或主进程已退出。

**解决方案**: 
1. 确保在所有工作进程退出前不要终止Manager进程。
2. 使用 `try-except` 块捕获这些异常，并优雅地处理。
3. 如果频繁出现，可能需要增加 `multiprocessing.connection.BUFSIZE` 的值。

```python
try:
    with shared_data_manager.get_lock():
        # 操作共享数据
except (EOFError, BrokenPipeError) as e:
    print(f"与Manager的连接已断开: {e}")
    # 执行清理操作
```

#### 问题: 锁被阻塞

**症状**: 某个进程似乎永久持有锁，导致其他进程无法进行。

**原因**: 通常是因为持有锁的进程在锁内部出现了未捕获的异常，或执行了阻塞操作。

**解决方案**: 
1. 确保所有异常都在锁内部被捕获。
2. 使用 `check_and_release_lock` 方法检测并尝试释放被阻塞的锁。
3. 在锁内部避免执行可能阻塞的操作。

```python
# 检测并尝试释放被阻塞的锁
lock_status = mp.check_and_release_lock(timeout=1.0, force=True)
print(f"锁状态: {lock_status}")
```

### 6.2 数据共享问题

#### 问题: 数据更新没有反映在其他进程中

**症状**: 在一个进程中更新的数据在其他进程中不可见。

**原因**: 可能是直接修改了代理对象内部的可变对象，而不是重新分配值。

**解决方案**: 
1. 总是使用 `add_value` 来更新共享字典中的值，而不是直接修改。
2. 对于嵌套的数据结构，获取副本，修改后再更新整个结构。

```python
# 不好的做法 (可能不会跨进程更新)
with lock:
    stats = shared_data_manager.get_value("stats")
    stats["counter"] += 1  # 直接修改可变对象

# 好的做法
with lock:
    stats = shared_data_manager.get_value("stats")
    stats = dict(stats)  # 创建副本
    stats["counter"] += 1
    shared_data_manager.add_value("stats", stats)  # 更新整个结构
```

#### 问题: 共享数据类型限制

**症状**: 某些复杂的Python对象无法存储在共享数据中。

**原因**: `multiprocessing.Manager` 只支持少数几种可通过网络安全传输的对象类型。

**解决方案**: 
1. 使用基本类型（数字、字符串、列表、字典）存储数据。
2. 对于复杂对象，考虑序列化（如使用JSON或pickle）。
3. 为特殊需求实现自定义 `BaseProxy` 子类。

```python
import json

# 序列化复杂对象
complex_obj = MyComplexClass()
serialized = json.dumps(complex_obj.to_dict())

with lock:
    shared_data_manager.add_value("complex_data", serialized)

# 另一个进程中反序列化
with lock:
    serialized = shared_data_manager.get_value("complex_data")
    obj_dict = json.loads(serialized)
    reconstructed = MyComplexClass.from_dict(obj_dict)
```

### 6.3 性能问题

#### 问题: 进程创建缓慢

**症状**: 启动大量进程时创建过程非常缓慢。

**原因**: 进程创建是资源密集型操作，特别是在Windows上。

**解决方案**: 
1. 减少进程数量，使用更合理的值。
2. 考虑使用进程池而不是创建新进程。
3. 批处理任务以减少所需的进程数。

#### 问题: 内存使用过高

**症状**: 应用程序使用的内存远远超过预期。

**原因**: 每个进程有自己的内存空间，共享数据可能被复制多次。

**解决方案**: 
1. 限制进程数量。
2. 减少共享数据的大小，只共享必要的数据。
3. 对于大型数据，考虑使用 `multiprocessing.shared_memory` 或内存映射文件。
4. 在工作函数中处理数据批次，而不是一次性加载所有数据。

### 6.4 其他常见问题

#### 问题: 子进程无法导入自定义模块

**症状**: 子进程抛出 `ImportError`，无法找到在主进程中可用的模块。

**原因**: 在Windows上，子进程不会继承主进程的导入上下文。

**解决方案**: 
1. 确保所有必要的导入都放在被子进程使用的函数内部。
2. 考虑使用 `if __name__ == "__main__":` 模式组织代码。
3. 使用绝对导入而不是相对导入。

```python
def worker_function(shared_data_manager, item):
    # 在函数内部导入，确保子进程可以访问
    import my_custom_module
    
    # 处理逻辑...
```

#### 问题: 回调函数中的异常被吞噬

**症状**: 在数据变化或事件回调中的异常不会导致程序崩溃，但可能导致功能异常。

**原因**: 回调是在单独的线程中执行的，其中的异常不会传播到主线程。

**解决方案**: 
1. 在回调函数中使用 `try-except` 块捕获并记录所有异常。
2. 考虑使用共享事件向主线程发出错误信号。

```python
def on_data_change(key, value, lock, shared_data_manager):
    try:
        # 处理逻辑...
    except Exception as e:
        # 记录异常
        print(f"回调函数中的异常: {e}")
        traceback.print_exc()
        
        # 可选：设置错误事件，通知主线程
        with lock:
            error_event = shared_data_manager.get_or_create_event("callback_error")
            shared_data_manager.add_value("callback_error_details", {
                "callback": "on_data_change",
                "key": key,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            error_event.set()
```