"""
模拟数据工厂

提供各种测试场景所需的模拟数据和工作函数。
"""

import sys
import os
import time
import random
import threading
from typing import List, Dict, Any, Callable, Optional
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils import Logger, ClassInstanceManager
from worker_functions import get_worker_function


class WorkerType(Enum):
    """工作函数类型枚举"""
    SIMPLE = "simple"
    CPU_INTENSIVE = "cpu_intensive"
    IO_INTENSIVE = "io_intensive"
    ERROR_PRONE = "error_prone"
    DATA_SHARING = "data_sharing"
    LONG_RUNNING = "long_running"
    QUICK = "quick"


@dataclass
class TestScenario:
    """测试场景配置"""
    name: str
    description: str
    data_count: int
    worker_type: WorkerType
    num_processes: int
    expected_duration: float
    worker_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.worker_params is None:
            self.worker_params = {}


class MockDataFactory:
    """
    模拟数据工厂
    
    提供各种测试场景的数据和工作函数。
    """
    
    def __init__(self):
        self.logger = ClassInstanceManager.get_instance(key="Logger")
        self._worker_registry = {}
        self._register_workers()
    
    def _register_workers(self):
        """注册所有工作函数"""
        self._worker_registry = {
            WorkerType.SIMPLE: "simple",
            WorkerType.CPU_INTENSIVE: "cpu_intensive",
            WorkerType.IO_INTENSIVE: "io_intensive",
            WorkerType.ERROR_PRONE: "error_prone",
            WorkerType.DATA_SHARING: "data_sharing",
            WorkerType.LONG_RUNNING: "long_running",
            WorkerType.QUICK: "quick"
        }
    
    def create_test_scenario(self, scenario_name: str) -> TestScenario:
        """
        创建预定义的测试场景
        
        Args:
            scenario_name: 场景名称
            
        Returns:
            测试场景配置
        """
        scenarios = {
            "basic_event_test": TestScenario(
                name="基础事件测试",
                description="测试基本事件触发机制",
                data_count=5,
                worker_type=WorkerType.SIMPLE,
                num_processes=2,
                expected_duration=2.0,
                worker_params={"delay": 0.2}
            ),
            "quick_completion": TestScenario(
                name="快速完成测试",
                description="测试快速完成场景",
                data_count=3,
                worker_type=WorkerType.QUICK,
                num_processes=2,
                expected_duration=0.5,
                worker_params={"delay": 0.05}
            ),
            "cpu_intensive": TestScenario(
                name="CPU密集型测试",
                description="测试CPU密集型工作负载",
                data_count=4,
                worker_type=WorkerType.CPU_INTENSIVE,
                num_processes=2,
                expected_duration=3.0,
                worker_params={"duration": 0.5}
            ),
            "io_intensive": TestScenario(
                name="IO密集型测试",
                description="测试IO密集型工作负载",
                data_count=6,
                worker_type=WorkerType.IO_INTENSIVE,
                num_processes=3,
                expected_duration=2.0,
                worker_params={"delay": 0.3}
            ),
            "error_handling": TestScenario(
                name="错误处理测试",
                description="测试错误处理机制",
                data_count=8,
                worker_type=WorkerType.ERROR_PRONE,
                num_processes=2,
                expected_duration=3.0,
                worker_params={"error_rate": 0.3}
            ),
            "data_sharing": TestScenario(
                name="数据共享测试",
                description="测试数据共享机制",
                data_count=10,
                worker_type=WorkerType.DATA_SHARING,
                num_processes=3,
                expected_duration=2.0
            ),
            "long_running": TestScenario(
                name="长时间运行测试",
                description="测试长时间运行场景",
                data_count=3,
                worker_type=WorkerType.LONG_RUNNING,
                num_processes=2,
                expected_duration=5.0,
                worker_params={"duration": 1.5}
            ),
            "single_process": TestScenario(
                name="单进程测试",
                description="测试单进程场景",
                data_count=5,
                worker_type=WorkerType.SIMPLE,
                num_processes=1,
                expected_duration=2.0,
                worker_params={"delay": 0.2}
            ),
            "many_processes": TestScenario(
                name="多进程测试",
                description="测试多进程场景",
                data_count=20,
                worker_type=WorkerType.SIMPLE,
                num_processes=5,
                expected_duration=3.0,
                worker_params={"delay": 0.1}
            ),
            "empty_data": TestScenario(
                name="空数据测试",
                description="测试空数据场景",
                data_count=0,
                worker_type=WorkerType.SIMPLE,
                num_processes=2,
                expected_duration=0.5
            )
        }
        
        if scenario_name not in scenarios:
            raise ValueError(f"未知的测试场景: {scenario_name}")
        
        return scenarios[scenario_name]
    
    def create_data_for_scenario(self, scenario: TestScenario) -> List[Any]:
        """
        为测试场景创建数据
        
        Args:
            scenario: 测试场景
            
        Returns:
            测试数据列表
        """
        if scenario.data_count == 0:
            return []
        
        # 根据场景类型生成不同的数据
        if scenario.worker_type == WorkerType.DATA_SHARING:
            return [f"shared_item_{i}" for i in range(scenario.data_count)]
        elif scenario.worker_type == WorkerType.ERROR_PRONE:
            return [f"error_test_{i}" for i in range(scenario.data_count)]
        else:
            return list(range(scenario.data_count))
    
    def create_worker_for_scenario(self, scenario: TestScenario) -> Callable:
        """
        为测试场景创建工作函数

        Args:
            scenario: 测试场景

        Returns:
            工作函数
        """
        worker_type_name = self._worker_registry.get(scenario.worker_type)
        if not worker_type_name:
            raise ValueError(f"未知的工作函数类型: {scenario.worker_type}")

        return get_worker_function(worker_type_name, **scenario.worker_params)
    

    
    def get_available_scenarios(self) -> List[str]:
        """
        获取可用的测试场景列表
        
        Returns:
            场景名称列表
        """
        return [
            "basic_event_test",
            "quick_completion", 
            "cpu_intensive",
            "io_intensive",
            "error_handling",
            "data_sharing",
            "long_running",
            "single_process",
            "many_processes",
            "empty_data"
        ]
