from collections import deque
from typing import List, Type, TypeVar

from PyQt5.QtWidgets import (QApplication, QGroupBox, QHBoxLayout, QLabel,
                           QLineEdit, QLayout, QPushButton, QVBoxLayout, QWidget)

T = TypeVar('T', bound=QWidget)


def get_widgets_from_layout(layout: QLayout, widget_type: Type[T]) -> List[T]:
    """
    递归地在指定的 PyQt5 布局容器中查找所有特定类型的控件。

    此函数经过性能优化，使用 collections.deque 进行高效的广度优先搜索。
    它会遍历布局中的所有项目，包括嵌套的子布局和容器控件（如 QGroupBox），
    以确保找到所有符合条件的控件实例。

    Args:
        layout (QLayout): 要搜索的顶层 PyQt5 布局容器，例如 QHBoxLayout, QVBoxLayout。
        widget_type (Type[T]): 要查找的控件类，例如 QPushButton, QLabel, QLineEdit。

    Returns:
        List[T]: 一个包含所有找到的、指定类型控件实例的列表。返回的列表中没有重复项。
    
    使用示例:
    ```python
    import sys
    
    # 创建一个 QApplication 实例
    app = QApplication(sys.argv)

    # 创建主窗口和主布局
    window = QWidget()
    main_layout = QVBoxLayout(window)

    # 添加一些直接的控件
    main_layout.addWidget(QPushButton("Button 1"))
    main_layout.addWidget(QLabel("Label 1"))

    # 创建一个水平布局并添加控件
    h_layout = QHBoxLayout()
    h_layout.addWidget(QPushButton("Button 2"))
    h_layout.addWidget(QLineEdit("LineEdit 1"))
    
    # 将水平布局添加到主布局
    main_layout.addLayout(h_layout)

    # 创建一个 QGroupBox (容器控件)
    group_box = QGroupBox("Group Box")
    group_box_layout = QVBoxLayout(group_box)
    group_box_layout.addWidget(QPushButton("Button 3 (in Group)"))
    group_box_layout.addWidget(QLabel("Label 2 (in Group)"))
    
    # 将 GroupBox 添加到主布局
    main_layout.addWidget(group_box)

    # 使用函数查找所有的 QPushButton
    all_buttons = get_widgets_from_layout(main_layout, QPushButton)
    print(f"找到 {len(all_buttons)} 个 QPushButton:")
    for button in all_buttons:
        print(f" - {button.text()}")
    # 预期输出:
    # 找到 3 个 QPushButton:
    # - Button 1
    # - Button 2
    # - Button 3 (in Group)

    # 使用函数查找所有的 QLabel
    all_labels = get_widgets_from_layout(main_layout, QLabel)
    print(f"\\n找到 {len(all_labels)} 个 QLabel:")
    for label in all_labels:
        print(f" - {label.text()}")
    # 预期输出:
    # 找到 2 个 QLabel:
    # - Label 1
    # - Label 2 (in Group)

    # window.show()  # 取消注释以显示窗口
    # sys.exit(app.exec_())
    ```
    """
    # 使用集合来存储找到的控件，以自动处理和避免重复项。
    found_widgets = set()

    # 使用 collections.deque 作为高效队列（O(1)的append和popleft操作），
    # 初始化时放入顶层布局，用于广度优先遍历所有布局。
    layouts_to_process = deque([layout])

    while layouts_to_process:
        # 从队列左侧弹出一个布局进行处理
        current_layout = layouts_to_process.popleft()

        for i in range(current_layout.count()):
            item = current_layout.itemAt(i)

            if not item:
                continue

            # 情况 1: 当前项是一个嵌套的子布局。
            # 如果该项本身是一个布局(layout)，则将其添加到处理队列的末尾。
            if item.layout():
                layouts_to_process.append(item.layout())
            
            # 情况 2: 当前项是一个控件(widget)。
            # 如果该项包含一个控件(widget)，我们需要检查它以及它的所有后代控件。
            elif item.widget():
                widget = item.widget()

                # 2.1: 检查这个控件本身是否是我们要寻找的类型。
                if isinstance(widget, widget_type):
                    found_widgets.add(widget)

                # 2.2: (关键步骤) 使用高效的`findChildren`方法递归地查找所有
                #      符合条件的后代控件。这优雅地解决了控件嵌套在其他
                #      容器控件内的情况 (如 QGroupBox)。
                found_widgets.update(widget.findChildren(widget_type))

    return list(found_widgets)
