"""
PostgreSQL数据库连接池管理模块

提供高效的数据库连接池管理功能，支持：
1. 自动创建和维护连接池
2. 连接健康检查和自动重连
3. 连接回收和超时处理
4. 连接池状态监控和统计

通过连接池可以减少频繁创建和销毁连接的开销，提高数据库操作性能。
"""

import time
import threading
import contextlib
from typing import Dict, Any, Optional, Tuple, List, Set, Union, Generator, ContextManager
import traceback

import psycopg2
from psycopg2 import pool

from .exceptions import ConnectionError, PoolError
from .logger import get_logger, log_method_call


class ConnectionPool:
    """PostgreSQL数据库连接池管理类"""
    
    def __init__(
        self,
        host: str,
        port: int,
        database: str,
        user: str,
        password: str,
        min_connections: int = 1,
        max_connections: int = 10,
        connection_timeout: int = 30,
        pool_recycle: int = 3600,
        application_name: Optional[str] = None,
        **kwargs
    ):
        """
        初始化连接池管理器
        
        Args:
            host: 数据库主机
            port: 数据库端口
            database: 数据库名称
            user: 用户名
            password: 密码
            min_connections: 最小连接数
            max_connections: 最大连接数
            connection_timeout: 连接超时时间(秒)
            pool_recycle: 连接回收时间(秒)
            application_name: 应用名称
            **kwargs: 其他连接参数
        """
        self.logger = get_logger("PostgreSQLConnectionPool")
        
        # 连接参数
        self.connection_params = {
            'host': host,
            'port': port,
            'database': database,
            'user': user,
            'password': password,
        }
        
        # 如果提供了应用名称，添加到连接参数
        if application_name:
            self.connection_params['application_name'] = application_name
            
        # 添加其他连接参数
        self.connection_params.update(kwargs)
        
        # 连接池配置
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.pool_recycle = pool_recycle
        
        # 连接池
        self.pool = None
        
        # 连接池状态
        self.active_connections = 0
        self.total_connections_created = 0
        self.total_connections_closed = 0
        self.total_connections_reused = 0
        self.total_connection_errors = 0
        
        # 连接跟踪
        self.connection_timestamps = {}  # 记录连接创建时间
        self.connection_usage_count = {}  # 记录连接使用次数
        self.connection_last_used = {}  # 记录连接最后使用时间
        self.active_connection_ids = set()  # 当前活动连接ID集合
        
        # 连接池锁
        self.lock = threading.RLock()
        
        # 创建连接池
        self._create_pool()
        
    @log_method_call()
    def _create_pool(self) -> None:
        """
        创建数据库连接池

        Raises:
            PoolError: 如果创建连接池失败
        """
        try:
            # ========== 修复UTF-8编码问题：强制设置环境变量 ==========
            import os
            import sys

            # 强制设置PostgreSQL客户端编码
            os.environ['PGCLIENTENCODING'] = 'UTF8'
            os.environ['PGDATESTYLE'] = 'ISO'

            # 在Windows上强制设置编码
            if sys.platform == 'win32':
                os.environ['PYTHONIOENCODING'] = 'utf-8'

            self.logger.info(f"创建PostgreSQL连接池: {self.connection_params['database']}@{self.connection_params['host']}:{self.connection_params['port']}, "
                            f"连接数范围: {self.min_connections}-{self.max_connections}")
            # ========== 关键修复：移除多余参数，防止ThreadedConnectionPool参数冲突 ==========
            connection_params_copy = self.connection_params.copy()
            connection_params_copy.pop('min_connections', None)
            connection_params_copy.pop('max_connections', None)

            # 创建线程安全的连接池
            self.pool = pool.ThreadedConnectionPool(
                minconn=self.min_connections,
                maxconn=self.max_connections,
                **connection_params_copy
            )


            
            # 初始化连接池状态
            self.active_connections = 0
            
            self.logger.info("PostgreSQL连接池创建成功")
            
        except Exception as e:
            self.total_connection_errors += 1
            error_msg = f"创建PostgreSQL连接池失败: {str(e)}"

            # ========== 修复：对于UTF-8编码错误（通常是数据库不存在），使用警告级别而不是错误级别 ==========
            if isinstance(e, UnicodeDecodeError):
                self.logger.warning(error_msg)  # 使用警告级别，不打印堆栈
                raise e  # 直接抛出UnicodeDecodeError，便于上层识别
            else:
                self.logger.error(error_msg)
                traceback.print_exc()  # 打印详细异常堆栈
                raise PoolError(error_msg) from e
            
    @log_method_call()
    def get_connection(self) -> psycopg2.extensions.connection:
        """
        从连接池获取连接
        
        Returns:
            数据库连接对象
            
        Raises:
            ConnectionError: 如果获取连接失败
        """
        conn = None
        start_time = time.time()
        
        try:
            with self.lock:
                # 检查连接池是否存在
                if self.pool is None:
                    self._create_pool()
                    
                # 从连接池获取连接
                conn = self.pool.getconn()
                
                # 检查连接是否有效
                if conn is None or conn.closed:
                    raise ConnectionError("获取到的连接无效或已关闭")
                    
                # 检查连接是否需要回收
                conn_id = id(conn)
                if conn_id in self.connection_timestamps:
                    # 检查连接是否过期
                    create_time = self.connection_timestamps[conn_id]
                    if time.time() - create_time > self.pool_recycle:
                        self.logger.debug(f"连接 {conn_id} 已过期，关闭并重新创建")
                        try:
                            self.pool.putconn(conn, close=True)
                        except Exception as e:
                            self.logger.warning(f"关闭过期连接失败: {str(e)}")
                            
                        # 重新获取连接
                        conn = self.pool.getconn()
                        conn_id = id(conn)
                        self.connection_timestamps[conn_id] = time.time()
                        self.connection_usage_count[conn_id] = 0
                        self.total_connections_created += 1
                    else:
                        # 更新连接使用计数
                        self.connection_usage_count[conn_id] = self.connection_usage_count.get(conn_id, 0) + 1
                        self.total_connections_reused += 1
                else:
                    # 新连接，记录创建时间
                    self.connection_timestamps[conn_id] = time.time()
                    self.connection_usage_count[conn_id] = 0
                    self.total_connections_created += 1
                    
                # 更新连接最后使用时间
                self.connection_last_used[conn_id] = time.time()
                
                # 添加到活动连接集合
                self.active_connection_ids.add(conn_id)
                self.active_connections += 1
                
        except (psycopg2.Error, Exception) as e:
            self.total_connection_errors += 1
            error_msg = f"从连接池获取连接失败: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈
            
            # 如果获取到了连接但后续处理失败，确保归还连接
            if conn is not None:
                try:
                    self.pool.putconn(conn, close=True)
                except Exception:
                    pass
                    
            # 如果连接池出现问题，尝试重新创建
            if isinstance(e, psycopg2.pool.PoolError):
                try:
                    self._create_pool()
                except Exception:
                    pass
                    
            raise ConnectionError(error_msg) from e
            
        # 记录获取连接的耗时
        elapsed = time.time() - start_time
        self.logger.debug(f"获取连接耗时: {elapsed:.6f}秒")
        
        return conn
        
    @log_method_call()
    def return_connection(self, conn: psycopg2.extensions.connection, close: bool = False) -> None:
        """
        将连接归还到连接池
        
        Args:
            conn: 要归还的连接
            close: 是否关闭连接而不是归还到池中
            
        Raises:
            PoolError: 如果归还连接失败
        """
        if conn is None:
            return
            
        conn_id = id(conn)
        
        try:
            with self.lock:
                # 检查连接池是否存在
                if self.pool is None:
                    if not conn.closed:
                        conn.close()
                    return
                    
                # 从活动连接集合中移除
                if conn_id in self.active_connection_ids:
                    self.active_connection_ids.remove(conn_id)
                    self.active_connections -= 1
                    
                # 归还连接到连接池
                self.pool.putconn(conn, close=close)
                
                # 如果关闭连接，更新统计信息
                if close:
                    self.total_connections_closed += 1
                    # 清理连接跟踪信息
                    if conn_id in self.connection_timestamps:
                        del self.connection_timestamps[conn_id]
                    if conn_id in self.connection_usage_count:
                        del self.connection_usage_count[conn_id]
                    if conn_id in self.connection_last_used:
                        del self.connection_last_used[conn_id]
                        
        except Exception as e:
            error_msg = f"归还连接到连接池失败: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈
            
            # 如果归还失败，尝试关闭连接
            try:
                if not conn.closed:
                    conn.close()
            except Exception:
                pass
                
            raise PoolError(error_msg) from e
            
    @log_method_call()
    def close_all(self) -> None:
        """
        关闭所有连接并销毁连接池
        
        Raises:
            PoolError: 如果关闭连接池失败
        """
        try:
            with self.lock:
                if self.pool is not None:
                    self.logger.info("关闭所有数据库连接并销毁连接池")
                    self.pool.closeall()
                    self.pool = None
                    self.active_connections = 0
                    self.active_connection_ids.clear()
                    
        except Exception as e:
            error_msg = f"关闭连接池失败: {str(e)}"
            self.logger.error(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈
            raise PoolError(error_msg) from e
            
    @log_method_call()
    def check_and_recycle_connections(self) -> int:
        """
        检查并回收过期连接
        
        Returns:
            回收的连接数量
        """
        recycled_count = 0
        current_time = time.time()
        
        try:
            with self.lock:
                # 检查连接池是否存在
                if self.pool is None:
                    return 0
                    
                # 检查所有连接
                for conn_id, last_used in list(self.connection_last_used.items()):
                    # 如果连接已经很久没有使用，关闭它
                    if current_time - last_used > self.pool_recycle:
                        # 注意：这里我们无法直接获取到连接对象，
                        # 所以只能在下次获取连接时进行回收
                        self.logger.debug(f"标记连接 {conn_id} 为过期，将在下次获取时回收")
                        recycled_count += 1
                        
        except Exception as e:
            self.logger.error(f"检查和回收连接失败: {str(e)}")
            traceback.print_exc()  # 打印详细异常堆栈
            
        return recycled_count
        
    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取连接池状态
        
        Returns:
            包含连接池状态信息的字典
        """
        with self.lock:
            return {
                "active_connections": self.active_connections,
                "min_connections": self.min_connections,
                "max_connections": self.max_connections,
                "total_created": self.total_connections_created,
                "total_closed": self.total_connections_closed,
                "total_reused": self.total_connections_reused,
                "total_errors": self.total_connection_errors,
                "pool_exists": self.pool is not None,
                "connection_recycle_time": self.pool_recycle,
                "connection_timeout": self.connection_timeout,
                "database": self.connection_params.get("database", ""),
                "host": self.connection_params.get("host", ""),
                "port": self.connection_params.get("port", ""),
            }
            
    @contextlib.contextmanager
    def connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        """
        获取连接的上下文管理器
        
        Yields:
            数据库连接对象
            
        Example:
            ```python
            with pool.connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
            ```
        """
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        finally:
            if conn is not None:
                self.return_connection(conn)
                
    def __del__(self) -> None:
        """析构函数，确保连接池被正确关闭"""
        try:
            self.close_all()
        except Exception:
            pass


# 连接池管理器，存储所有创建的连接池
class ConnectionPoolManager:
    """连接池管理器，管理多个数据库连接池"""
    
    # 单例模式
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConnectionPoolManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
        
    def __init__(self):
        if self._initialized:
            return
            
        self.logger = get_logger("PostgreSQLConnectionPoolManager")
        self.pools = {}  # 存储连接池的字典，键为数据库标识
        self.lock = threading.RLock()  # 线程锁
        self._initialized = True
        
    def _get_pool_key(self, **kwargs) -> str:
        """
        生成连接池的唯一标识
        
        Args:
            **kwargs: 连接参数
            
        Returns:
            连接池标识
        """
        # 使用数据库连接信息作为键
        return f"{kwargs.get('host', 'localhost')}:{kwargs.get('port', 5432)}:{kwargs.get('database', 'postgres')}:{kwargs.get('user', 'postgres')}"
        
    @log_method_call()
    def get_pool(self, **kwargs) -> ConnectionPool:
        """
        获取或创建连接池
        
        Args:
            **kwargs: 连接参数
            
        Returns:
            连接池对象
            
        Raises:
            PoolError: 如果创建连接池失败
        """
        pool_key = self._get_pool_key(**kwargs)
        
        with self.lock:
            if pool_key not in self.pools:
                try:
                    self.logger.info(f"连接池 {pool_key} 不存在，尝试创建...")
                    new_pool = ConnectionPool(**kwargs)
                    self.pools[pool_key] = new_pool
                    self.logger.info(f"连接池 {pool_key} 创建成功并已缓存。")
                except UnicodeDecodeError as e:
                    self.logger.warning(f"在 ConnectionPoolManager 中创建连接池 {pool_key} 失败: {e}")
                    raise  # 直接抛出UnicodeDecodeError，保持原始异常类型
                except PoolError as e:
                    self.logger.error(f"在 ConnectionPoolManager 中创建连接池 {pool_key} 失败: {e}")
                    raise
            
            if pool_key not in self.pools or self.pools[pool_key] is None:
                err_msg = f"连接池 {pool_key} 在获取时发现为 None 或不存在于缓存中，即使它应该已被创建。"
                self.logger.critical(err_msg)
                raise PoolError(err_msg)

            return self.pools[pool_key]
                
    @log_method_call()
    def close_pool(self, **kwargs) -> bool:
        """
        关闭指定的连接池
        
        Args:
            **kwargs: 连接参数
            
        Returns:
            是否成功关闭连接池
        """
        pool_key = self._get_pool_key(**kwargs)
        
        with self.lock:
            if pool_key in self.pools:
                try:
                    self.pools[pool_key].close_all()
                    del self.pools[pool_key]
                    self.logger.info(f"关闭并移除连接池: {pool_key}")
                    return True
                except Exception as e:
                    self.logger.error(f"关闭连接池失败: {str(e)}")
                    traceback.print_exc()  # 打印详细异常堆栈
                    return False
            return False
            
    @log_method_call()
    def close_all_pools(self) -> None:
        """关闭所有连接池"""
        with self.lock:
            for pool_key, pool_instance in list(self.pools.items()):
                try:
                    pool_instance.close_all()
                    self.logger.info(f"关闭连接池: {pool_key}")
                except Exception as e:
                    self.logger.error(f"关闭连接池 {pool_key} 失败: {str(e)}")
                    traceback.print_exc()  # 打印详细异常堆栈
                    
            self.pools.clear()
            
    def get_all_pools_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有连接池的状态
        
        Returns:
            包含所有连接池状态的字典
        """
        result = {}
        with self.lock:
            for pool_key, pool_instance in self.pools.items():
                try:
                    result[pool_key] = pool_instance.get_pool_status()
                except Exception as e:
                    self.logger.error(f"获取连接池 {pool_key} 状态失败: {str(e)}")
                    result[pool_key] = {"error": str(e)}
                    
        return result
        
    def __del__(self) -> None:
        """析构函数，确保所有连接池被正确关闭"""
        try:
            self.close_all_pools()
        except Exception:
            pass


# 创建全局连接池管理器
pool_manager = ConnectionPoolManager()


def get_connection_pool(**kwargs) -> ConnectionPool:
    """
    获取或创建连接池
    
    Args:
        **kwargs: 连接参数
        
    Returns:
        连接池对象
    """
    return pool_manager.get_pool(**kwargs)


def close_connection_pool(**kwargs) -> bool:
    """
    关闭指定的连接池
    
    Args:
        **kwargs: 连接参数
        
    Returns:
        是否成功关闭连接池
    """
    return pool_manager.close_pool(**kwargs)


def close_all_connection_pools() -> None:
    """关闭所有连接池"""
    pool_manager.close_all_pools()


def get_all_pools_status() -> Dict[str, Dict[str, Any]]:
    """
    获取所有连接池的状态
    
    Returns:
        包含所有连接池状态的字典
    """
    return pool_manager.get_all_pools_status() 