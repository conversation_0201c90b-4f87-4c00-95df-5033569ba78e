import os
import random
import sys
import threading
import time
import logging
import traceback
import multiprocessing
import queue  # 添加queue模块导入，用于处理队列异常
from multiprocessing import Pool, Manager, Process, JoinableQueue, Lock as ProcessLock, Event  # 重命名 Lock 避免冲突
from multiprocessing.managers import BaseManager, DictProxy, ListProxy, AcquirerProxy  # 确保 AcquirerProxy 被导入
from queue import Empty  # 导入 Empty 异常
from typing import Any, Dict, Callable, Iterable, List, Optional, Tuple, Union, cast, Set, TypeVar, Generic, Type  # 添加 Type 的导入
import functools
from queue import Queue  # 添加Queue导入
from global_tools.utils import Logger, LogLevel, ClassInstanceManager

# 日志
logger: Logger = ClassInstanceManager.get_instance(key="Logger")


class EventManager:
    """
    EventManager - 多线程 Event 事件管理器类

    该类负责创建、管理和访问多线程 Event 事件，提供统一的接口进行事件管理。

    核心功能:
    ----------
    - **事件创建**: 创建并存储具有唯一标识键的 threading.Event 事件。
    - **事件操作**: 提供获取、设置、清除、删除事件的方法。
    - **状态管理**: 可以查询事件的状态，以及等待事件被触发。
    - **批量操作**: 提供对多个事件进行操作的方法，如等待任一事件、等待所有事件等。
    - **线程安全**: 所有操作都是线程安全的，使用线程锁保护对事件存储器的访问。

    使用示例:
    ----------

    ```python
    # 创建事件管理器
    event_manager = EventManager()

    # 创建事件
    event_manager.create_event("task_started")
    event_manager.create_event("task_completed")

    # 在某个线程中设置事件
    event_manager.set_event("task_started")

    # 在另一个线程中等待事件
    if event_manager.wait_event("task_completed", timeout=10):
        print("任务已完成")
    else:
        print("等待任务完成超时")

    # 等待多个事件中的任意一个
    triggered_event = event_manager.wait_any_event(["process_completed", "process_error"], timeout=5)
    if triggered_event:
        if triggered_event == "process_completed":
            print("处理成功完成")
        else:
            print("处理出错")
    else:
        print("等待超时")
    ```
    """

    def __init__(self):
        """
        初始化 EventManager 实例。

        创建存储事件的字典和用于线程安全访问的锁。
        """
        self.__events = {}  # 事件存储器，格式: {key: threading.Event对象}
        self.__lock = threading.RLock()  # 线程锁，用于保护事件存储器
        logger.debug("EventManager 初始化完成")

    def create_event(self, key: str) -> threading.Event:
        """
        创建一个多线程 Event 事件，并用指定的 key 键值存储在事件存储器中。

        Args:
            key: Event 事件的唯一标识键值。

        Returns:
            创建的 threading.Event 对象。

        Example:
            ```python
            # 创建一个名为 "task_completed" 的事件
            event = event_manager.create_event("task_completed")

            # 在其他地方设置事件
            event_manager.set_event("task_completed")

            # 在另一个线程中等待事件
            event_manager.wait_event("task_completed")
            ```
        """
        with self.__lock:
            if key in self.__events:
                logger.debug(f"Event '{key}' 已存在，返回现有事件")
                return self.__events[key]

            event = threading.Event()
            self.__events[key] = event
            logger.debug(f"已创建新的 Event '{key}'")
            return event

    def get_event(self, key: str) -> Optional[threading.Event]:
        """
        获取指定 key 键值的多线程 Event 事件。

        Args:
            key: 要获取的 Event 事件的键值。

        Returns:
            如果存在，则返回对应的 threading.Event 对象；否则返回 None。

        Example:
            ```python
            # 获取名为 "task_completed" 的事件
            event = event_manager.get_event("task_completed")
            if event:
                # 使用该事件
                event.wait(timeout=10)
            ```
        """
        with self.__lock:
            event = self.__events.get(key)
            if event is None:
                logger.debug(f"尝试获取不存在的 Event '{key}'")
            return event

    def set_event(self, key: str) -> bool:
        """
        设置指定 key 键值的多线程 Event 事件状态为已触发。

        Args:
            key: 要设置的 Event 事件的键值。

        Returns:
            如果事件存在且被成功设置，则返回 True；否则返回 False。

        Example:
            ```python
            # 设置名为 "task_completed" 的事件
            success = event_manager.set_event("task_completed")
            if success:
                print("事件已成功设置")
            else:
                print("事件不存在或设置失败")
            ```
        """
        with self.__lock:
            event = self.__events.get(key)
            if event is None:
                logger.debug(f"尝试设置不存在的 Event '{key}'")
                return False

            event.set()
            logger.debug(f"Event '{key}' 已设置")
            return True

    def clear_event(self, key: str) -> bool:
        """
        清除指定 key 键值的多线程 Event 事件状态为未触发。

        Args:
            key: 要清除的 Event 事件的键值。

        Returns:
            如果事件存在且被成功清除，则返回 True；否则返回 False。

        Example:
            ```python
            # 清除名为 "task_completed" 的事件
            success = event_manager.clear_event("task_completed")
            if success:
                print("事件已成功清除")
            else:
                print("事件不存在或清除失败")
            ```
        """
        with self.__lock:
            event = self.__events.get(key)
            if event is None:
                logger.debug(f"尝试清除不存在的 Event '{key}'")
                return False

            event.clear()
            logger.debug(f"Event '{key}' 已清除")
            return True

    def is_event_set(self, key: str) -> Optional[bool]:
        """
        检查指定 key 键值的多线程 Event 事件是否已触发。

        Args:
            key: 要检查的 Event 事件的键值。

        Returns:
            如果事件存在，则返回其是否已触发的布尔值；如果事件不存在，则返回 None。

        Example:
            ```python
            # 检查名为 "task_completed" 的事件是否已触发
            is_set = event_manager.is_event_set("task_completed")
            if is_set is None:
                print("事件不存在")
            elif is_set:
                print("事件已触发")
            else:
                print("事件未触发")
            ```
        """
        with self.__lock:
            event = self.__events.get(key)
            if event is None:
                logger.debug(f"尝试检查不存在的 Event '{key}'")
                return None

            return event.is_set()

    def wait_event(self, key: str, timeout: Optional[float] = None) -> bool:
        """
        等待指定 key 键值的多线程 Event 事件被触发。

        Args:
            key: 要等待的 Event 事件的键值。
            timeout: 等待超时时间（秒），如果为 None 则无限等待。

        Returns:
            如果事件存在且在超时前被触发，则返回 True；否则返回 False。

        Example:
            ```python
            # 等待名为 "task_completed" 的事件，最多等待 10 秒
            if event_manager.wait_event("task_completed", 10):
                print("事件已触发")
            else:
                print("等待超时或事件不存在")
            ```
        """
        with self.__lock:
            event = self.__events.get(key)
            if event is None:
                logger.debug(f"尝试等待不存在的 Event '{key}'")
                return False

        # 释放锁后再等待，避免死锁
        result = event.wait(timeout=timeout)
        logger.debug(f"等待 Event '{key}' {'超时' if not result else '成功'}")
        return result

    def delete_event(self, key: str) -> bool:
        """
        从事件存储器中删除指定 key 键值的多线程 Event 事件。

        Args:
            key: 要删除的 Event 事件的键值。

        Returns:
            如果事件存在且被成功删除，则返回 True；否则返回 False。

        Example:
            ```python
            # 删除名为 "task_completed" 的事件
            success = event_manager.delete_event("task_completed")
            if success:
                print("事件已成功删除")
            else:
                print("事件不存在或删除失败")
            ```
        """
        with self.__lock:
            if key not in self.__events:
                logger.debug(f"尝试删除不存在的 Event '{key}'")
                return False

            del self.__events[key]
            logger.debug(f"Event '{key}' 已删除")
            return True

    def get_all_events(self) -> Dict[str, threading.Event]:
        """
        获取所有多线程 Event 事件。

        Returns:
            包含所有 Event 事件的字典，键为事件的键值，值为 threading.Event 对象。

        Example:
            ```python
            # 获取所有事件
            events = event_manager.get_all_events()
            for key, event in events.items():
                print(f"事件 '{key}' 的状态: {'已触发' if event.is_set() else '未触发'}")
            ```
        """
        with self.__lock:
            # 返回字典的副本，避免外部修改
            return self.__events.copy()

    def get_all_event_states(self) -> Dict[str, bool]:
        """
        获取所有多线程 Event 事件的状态。

        Returns:
            包含所有 Event 事件状态的字典，键为事件的键值，值为布尔值表示事件是否已触发。

        Example:
            ```python
            # 获取所有事件的状态
            states = event_manager.get_all_event_states()
            for key, is_set in states.items():
                print(f"事件 '{key}' 的状态: {'已触发' if is_set else '未触发'}")
            ```
        """
        with self.__lock:
            return {key: event.is_set() for key, event in self.__events.items()}

    def has_event(self, key: str) -> bool:
        """
        检查指定 key 键值的多线程 Event 事件是否存在。

        Args:
            key: 要检查的 Event 事件的键值。

        Returns:
            如果事件存在，则返回 True；否则返回 False。

        Example:
            ```python
            # 检查名为 "task_completed" 的事件是否存在
            if event_manager.has_event("task_completed"):
                print("事件存在")
            else:
                print("事件不存在")
            ```
        """
        with self.__lock:
            return key in self.__events

    def get_or_create_event(self, key: str) -> threading.Event:
        """
        获取指定 key 键值的多线程 Event 事件，如果不存在则创建。

        Args:
            key: 要获取或创建的 Event 事件的键值。

        Returns:
            现有的或新创建的 threading.Event 对象。

        Example:
            ```python
            # 获取或创建名为 "task_completed" 的事件
            event = event_manager.get_or_create_event("task_completed")
            ```
        """
        with self.__lock:
            if key in self.__events:
                return self.__events[key]

            event = threading.Event()
            self.__events[key] = event
            logger.debug(f"已创建新的 Event '{key}'")
            return event

    def set_all_events(self) -> None:
        """
        设置所有多线程 Event 事件状态为已触发。

        Example:
            ```python
            # 设置所有事件
            event_manager.set_all_events()
            ```
        """
        with self.__lock:
            for key, event in self.__events.items():
                event.set()
                logger.debug(f"Event '{key}' 已设置")

    def clear_all_events(self) -> None:
        """
        清除所有多线程 Event 事件状态为未触发。

        Example:
            ```python
            # 清除所有事件
            event_manager.clear_all_events()
            ```
        """
        with self.__lock:
            for key, event in self.__events.items():
                event.clear()
                logger.debug(f"Event '{key}' 已清除")

    def wait_any_event(self, keys: List[str], timeout: Optional[float] = None) -> Optional[str]:
        """
        等待指定键值列表中的任意一个多线程 Event 事件被触发。

        Args:
            keys: 要等待的 Event 事件键值列表。
            timeout: 等待超时时间（秒），如果为 None 则无限等待。

        Returns:
            如果有事件被触发，则返回被触发的事件键值；如果超时或所有事件都不存在，则返回 None。

        Example:
            ```python
            # 等待 "task_completed" 或 "process_aborted" 事件被触发，最多等待 10 秒
            triggered_event = event_manager.wait_any_event(["task_completed", "process_aborted"], 10)
            if triggered_event:
                print(f"事件 '{triggered_event}' 已触发")
            else:
                print("等待超时或所有事件都不存在")
            ```
        """
        if not keys:
            return None

        # 复制一份事件列表，避免在等待过程中被修改
        events_to_wait = {}
        with self.__lock:
            for key in keys:
                if key in self.__events:
                    events_to_wait[key] = self.__events[key]

        if not events_to_wait:
            logger.debug("尝试等待的事件列表中所有事件都不存在")
            return None

        # 使用多线程等待任意事件
        result_key = [None]  # 使用列表存储结果，以便在线程间共享
        wait_threads = []

        def wait_for_event(key, event):
            if event.wait(timeout=timeout):
                # 事件被触发
                if result_key[0] is None:  # 只记录第一个触发的事件
                    result_key[0] = key

        # 为每个事件创建等待线程
        for key, event in events_to_wait.items():
            thread = threading.Thread(target=wait_for_event, args=(key, event), daemon=True)
            wait_threads.append(thread)
            thread.start()

        # 设置总体超时
        end_time = None if timeout is None else time.time() + timeout

        # 等待任意线程完成或超时
        for thread in wait_threads:
            if end_time is not None:
                remaining = max(0, end_time - time.time())
                thread.join(timeout=remaining)
            else:
                thread.join()

            # 如果已有事件触发，不再等待其他线程
            if result_key[0] is not None:
                break

        logger.debug(f"等待任意事件结果: {result_key[0] or '超时或无事件触发'}")
        return result_key[0]

    def wait_all_events(self, keys: List[str], timeout: Optional[float] = None) -> bool:
        """
        等待指定键值列表中的所有多线程 Event 事件被触发。

        Args:
            keys: 要等待的 Event 事件键值列表。
            timeout: 每个事件的等待超时时间（秒），如果为 None 则无限等待。

        Returns:
            如果所有存在的事件都在超时前被触发，则返回 True；否则返回 False。

        Example:
            ```python
            # 等待 "stage1_completed" 和 "stage2_completed" 事件都被触发，每个最多等待 10 秒
            if event_manager.wait_all_events(["stage1_completed", "stage2_completed"], 10):
                print("所有事件都已触发")
            else:
                print("部分事件超时未触发或不存在")
            ```
        """
        if not keys:
            return True

        # 复制一份事件列表，避免在等待过程中被修改
        events_to_wait = {}
        with self.__lock:
            for key in keys:
                if key in self.__events:
                    events_to_wait[key] = self.__events[key]

        if not events_to_wait:
            logger.debug("尝试等待的事件列表中所有事件都不存在")
            return False

        # 依次等待每个事件
        for key, event in events_to_wait.items():
            if not event.wait(timeout=timeout):
                logger.debug(f"等待事件 '{key}' 超时")
                return False

        logger.debug("所有指定事件都已触发")
        return True

    def count_events(self) -> int:
        """
        获取当前事件存储器中事件的总数。

        Returns:
            事件总数。

        Example:
            ```python
            # 获取事件总数
            event_count = event_manager.count_events()
            print(f"当前共有 {event_count} 个事件")
            ```
        """
        with self.__lock:
            return len(self.__events)

    def get_event_keys(self) -> List[str]:
        """
        获取所有事件的键值列表。

        Returns:
            包含所有事件键值的列表。

        Example:
            ```python
            # 获取所有事件的键值
            event_keys = event_manager.get_event_keys()
            print(f"当前事件: {', '.join(event_keys)}")
            ```
        """
        with self.__lock:
            return list(self.__events.keys())

    def clear_all(self) -> None:
        """
        清空事件存储器，删除所有事件。

        Example:
            ```python
            # 清空所有事件
            event_manager.clear_all()
            ```
        """
        with self.__lock:
            self.__events.clear()
            logger.debug("已清空所有事件")

    def __len__(self) -> int:
        """
        获取事件总数，使对象可以直接用于 len() 函数。

        Returns:
            事件总数。

        Example:
            ```python
            # 获取事件总数
            event_count = len(event_manager)
            print(f"当前共有 {event_count} 个事件")
            ```
        """
        return self.count_events()

    def __contains__(self, key: str) -> bool:
        """
        检查是否包含指定键值的事件，使对象可以直接用于 in 运算符。

        Args:
            key: 要检查的事件键值。

        Returns:
            如果事件存在，则返回 True；否则返回 False。

        Example:
            ```python
            # 检查事件是否存在
            if "task_completed" in event_manager:
                print("事件存在")
            else:
                print("事件不存在")
            ```
        """
        return self.has_event(key)
