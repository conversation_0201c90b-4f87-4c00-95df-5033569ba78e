[TOC]

# PostgreSQL 数据库操作模块

这个模块是对原始单文件实现 `auto_annotation/src/core/postgre_sql.py` 的重构版本，将单一文件拆分为多个功能模块，提高代码的可维护性和可扩展性，同时保持与原始模块相同的功能和接口。

## 主要特性

-   **连接池管理**：高效管理数据库连接，提高性能和可扩展性
-   **表操作**：创建、修改、删除表，支持丰富的字段类型和约束
-   **数据操作**：插入、更新、查询、删除数据，支持复杂的条件和批量操作
-   **事务管理**：支持事务操作，确保数据一致性
-   **错误处理**：提供详细的错误信息和日志记录
-   **类型转换**：自动处理 Python 和 PostgreSQL 之间的类型转换
-   **实例管理**：支持按数据库名称复用实例，避免重复连接
-   **批量操作**：支持批量更新和批量查询，提高处理效率
-   **命令行接口**：提供命令行工具，方便快速使用

## 安装

```bash
# 克隆仓库
git clone <repository_url>

# 安装依赖
pip install psycopg2-binary
```

## 快速开始

### 基本使用

```python
from automatic_dimension.postgre_sql import PostgreSQLClient

# 创建客户端实例
client = PostgreSQLClient(
    host="localhost",
    port=5432,
    database="your_database",
    user="your_username",
    password="your_password"
)

# 创建表
table_schema = {
    "columns": {
        "id": {"type": "SERIAL", "primary_key": True},
        "name": {"type": "VARCHAR(100)", "nullable": False},
        "age": {"type": "INTEGER", "default": 0},
        "email": {"type": "VARCHAR(255)", "unique": True},
        "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
    },
    "table_comment": "用户信息表"
}
client.create_table("users", table_schema)

# 插入数据
data = [
    {"name": "张三", "age": 25, "email": "<EMAIL>"},
    {"name": "李四", "age": 30, "email": "<EMAIL>"}
]
client.insert_data("users", data)

# 查询数据
result = client.fetch_data("users", condition_str="age > 20")
print(result)

# 更新数据
client.update_data("users", condition_str="name == '张三'", data_json={"age": 26})

# 删除数据
client.delete_data("users", "name == '李四'")

# 删除表
client.drop_table("users")

# 关闭客户端
client.close()
```

### 事务操作

```python
# 使用上下文管理器进行事务操作
with PostgreSQLClient(host="localhost", database="your_database", user="your_username", password="your_password") as client:
    # 所有操作在一个事务中执行
    client.insert_data("users", {"name": "王五", "age": 35})
    client.update_data("users", "name == '张三'", {"age": 27})
    # 事务自动提交

# 手动控制事务
client = PostgreSQLClient(host="localhost", database="your_database", user="your_username", password="your_password")
try:
    client.begin()  # 开始事务
    client.insert_data("users", {"name": "赵六", "age": 40})
    client.update_data("users", "name == '王五'", {"age": 36})
    client.commit()  # 提交事务
except Exception as e:
    client.rollback()  # 回滚事务
    print(f"事务失败: {e}")
finally:
    client.close()  # 关闭客户端
```

### 批量操作

#### 批量更新

```python
# 批量更新数据
batch_updates = [
    ["id == 1", {"name": "张三", "age": 26}],
    ["id == 2", {"name": "李四", "age": 31}],
    ["id == 3", {"name": "王五", "age": 36}]
]
result = client.update_data(table_name="users", batch_updates=batch_updates)
print(f"批量更新结果: {result}")
```

#### 批量查询

```python
# 批量条件查询
batch_conditions = ["age == 25", "age == 30", "age == 35"]
results = client.fetch_data("users", batch_conditions=batch_conditions)
print(f"批量查询结果: {results}")
```

### 复杂表操作

```python
# 创建具有复杂约束的表
complex_schema = {
    "columns": {
        "id": {"type": "SERIAL", "primary_key": True},
        "name": {"type": "VARCHAR(100)", "nullable": False},
        "email": {"type": "VARCHAR(255)", "unique": True},
        "department_id": {"type": "INTEGER", "references": "departments(id)", "on_delete": "CASCADE"},
        "status": {"type": "VARCHAR(20)", "check": "status IN ('active', 'inactive', 'pending')"},
        "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
    },
    "constraints": [
        {"type": "UNIQUE", "columns": ["name", "department_id"]},
        {"type": "CHECK", "condition": "length(name) > 2"}
    ],
    "indexes": [
        {"columns": ["department_id"], "method": "BTREE"},
        {"columns": ["created_at"], "method": "BRIN"}
    ],
    "table_comment": "员工信息表"
}
client.create_table("employees", complex_schema)

# 向表中添加新列
new_column = {
    "name": "salary",
    "type": "NUMERIC(10,2)",
    "nullable": False,
    "default": 0,
    "check": "salary >= 0",
    "comment": "员工薪资"
}
client.add_column("employees", new_column)
```

### 命令行使用

```bash
# 执行SQL命令
python -m automatic_dimension.postgre_sql -d your_database -u your_username --password your_password sql "SELECT * FROM users"

# 创建表
python -m automatic_dimension.postgre_sql -d your_database -u your_username create-table users schema.json

# 插入数据
python -m automatic_dimension.postgre_sql -d your_database -u your_username insert users data.json

# 查询数据
python -m automatic_dimension.postgre_sql -d your_database -u your_username fetch users --condition "age > 20"

# 更新数据
python -m automatic_dimension.postgre_sql -d your_database -u your_username update users "name == '张三'" update_data.json

# 删除数据
python -m automatic_dimension.postgre_sql -d your_database -u your_username delete users "name == '李四'"

# 删除表
python -m automatic_dimension.postgre_sql -d your_database -u your_username drop-table users
```

## 模块结构

-   `core_client.py`: 核心客户端类，提供完整的数据库操作功能
-   `connection_pool.py`: 连接池管理，负责创建和维护数据库连接池
-   `sql_condition_parser.py`: SQL 条件解析，将 Python 风格的条件字符串转换为 SQL WHERE 子句
-   `db_type_converter.py`: 数据类型转换，处理 Python 和 PostgreSQL 之间的类型转换
-   `exceptions.py`: 异常处理，定义模块特定的异常类
-   `logger.py`: 日志记录，提供统一的日志记录功能
-   `config.py`: 配置管理，处理数据库配置参数
-   `data_operations_1.py`: 数据操作功能(第 1 部分)，包含插入数据等基础操作
-   `data_operations_2.py`: 数据操作功能(第 2 部分)，包含更新和删除数据等操作
-   `data_operations_batch.py`: 批量数据操作功能，处理批量更新等高效操作
-   `data_operations_fetch.py`: 数据查询功能，处理各种复杂的查询操作
-   `db_operations.py`: 数据库操作功能，处理表创建、修改等 DDL 操作
-   `__main__.py`: 命令行接口，提供命令行工具入口

## 详细模块说明

### 1. core_client.py

-   **功能**：PostgreSQL 数据库客户端主入口，聚合所有功能模块，提供统一的数据库操作接口。
-   **主要类**：`PostgreSQLClient`
    -   **初始化参数**：host, port, database, user, password, min_connections, max_connections, log_level, config_file, application_name 等。
    -   **主要属性**：连接池、日志、配置、当前连接、事务状态。
    -   **主要方法**：
        -   `get_instance`：获取已存在的客户端实例。
        -   `execute_query`：执行 SQL 语句，支持参数化和自动提交/回滚。
        -   `begin/commit/rollback`：事务管理。
        -   `close`：关闭连接和连接池。
        -   `create_table/add_column/drop_table`：表结构操作。
        -   `insert_data/update_data/delete_data/fetch_data`：数据操作。
        -   `get_all_columns`：获取表所有列名。
        -   **异常**：ConnectionError, ExecutionError, ConfigurationError, ConditionParseError。
    -   **典型用法**：见"快速开始"与"API 参考"。
    -   **注意事项**：实例管理采用单例模式，参数变更需注意实例唯一性。

### 2. connection_pool.py

-   **功能**：数据库连接池管理，支持多数据库、多用户的高效连接复用。
-   **主要类**：
    -   `ConnectionPool`：单个数据库连接池，支持获取/归还/回收连接，状态监控。
    -   `ConnectionPoolManager`：全局连接池管理器，支持多数据库多用户的连接池统一管理。
-   **主要方法**：
    -   `get_connection/return_connection/close_all/check_and_recycle_connections/get_pool_status`。
    -   `get_pool/close_pool/close_all_pools/get_all_pools_status`。
-   **典型用法**：内部由`PostgreSQLClient`自动调用。
-   **注意事项**：连接池参数需与数据库参数一致，否则可能导致连接泄漏。

### 3. config.py

-   **功能**：数据库配置管理，支持默认配置、环境变量、配置文件、动态参数合并。
-   **主要类**：`DBConfig`
    -   **方法**：
        -   `get_config/get/get_connection_params/get_pool_params/get_log_level`。
        -   支持从文件、环境变量加载配置。
-   **典型用法**：由`PostgreSQLClient`自动加载。
-   **注意事项**：优先级：直接参数 > kwargs > config > 默认。

### 4. logger.py

-   **功能**：统一日志管理，支持多级别、彩色输出、SQL 日志过滤、方法调用自动记录。
-   **主要类**：`LoggerManager`
    -   **方法**：`get_logger/set_level/enable_sql_logging/log_method_call`。
    -   **辅助函数**：`configure_logger`。
-   **典型用法**：由`PostgreSQLClient`自动初始化。
-   **注意事项**：可通过参数控制日志级别和 SQL 日志开关。

### 5. exceptions.py

-   **功能**：模块专用异常体系，细分连接、执行、配置、条件解析、数据等多种错误类型。
-   **主要类**：
    -   `PostgreSQLError`（基类）、`ConnectionError`、`ExecutionError`、`ConfigurationError`、`ConditionParseError`等。
-   **典型用法**：所有核心方法均抛出结构化异常，便于上层捕获和调试。

### 6. sql_condition_parser.py

-   **功能**：将 Python 风格的条件表达式解析为 SQL WHERE 子句，支持复杂嵌套、各种操作符、括号、字符串转义。
-   **主要类/函数**：
    -   `SQLConditionParser`：主解析器。
    -   `parse_condition`：便捷函数，推荐直接调用。
-   **典型用法**：内部由`fetch_data/update_data/delete_data`等自动调用。
-   **注意事项**：支持丰富的语法，详见"条件语法参考"。

### 7. db_type_converter.py

-   **功能**：Python 与 PostgreSQL 数据类型双向转换，支持基本类型、复杂类型、特殊类型（如 UUID、Decimal、JSON、二进制等）。
-   **主要类/函数**：
    -   `DBTypeConverter`：类型转换器。
    -   `adapt_value_for_db/convert_to_pg_type`：便捷函数。
-   **典型用法**：数据插入、更新时自动调用。
-   **注意事项**：支持递归和自定义类型注册。

### 8. data_operations_1.py

-   **功能**：数据插入相关操作。
-   **主要类**：`DataOperations1`
    -   **方法**：
        -   `filter_insert_data_by_schema`：过滤无效字段。
        -   `insert_data`：支持多种 JSON 格式的批量/单条插入。
-   **典型用法**：由`PostgreSQLClient.insert_data`自动调用。
-   **注意事项**：自动处理字段过滤、类型转换、异常捕获。

### 9. data_operations_2.py

-   **功能**：数据更新、删除相关操作。
-   **主要类**：`DataOperations2`
    -   **方法**：
        -   `update_data`：支持单条和批量更新，自动过滤无效字段。
        -   `delete_data`：安全删除，防止误删。
-   **典型用法**：由`PostgreSQLClient.update_data/delete_data`自动调用。
-   **注意事项**：所有条件均通过`sql_condition_parser`解析，异常结构化返回。

### 10. data_operations_batch.py

-   **功能**：批量数据操作，支持高效批量更新。
-   **主要类**：`DataOperationsBatch`
    -   **方法**：
        -   `_batch_update_data`：批量更新，事务保证，详细结果反馈。
-   **典型用法**：由`update_data(batch_updates=...)`自动调用。
-   **注意事项**：每条更新独立反馈，整体事务一致性。

### 11. data_operations_fetch.py

-   **功能**：数据查询，支持单条和批量条件查询、复杂列处理、自动类型反序列化。
-   **主要类**：`DataOperationsFetch`
    -   **方法**：
        -   `fetch_data`：支持多种参数、批量模式、自动 JSON 反序列化。
-   **典型用法**：由`PostgreSQLClient.fetch_data`自动调用。
-   **注意事项**：所有异常结构化返回，支持复杂条件和分页。

### 12. db_operations.py

-   **功能**：表结构操作，支持创建表、添加列、删除表、执行 SQL 脚本。
-   **主要类**：`DBOperations`
    -   **方法**：
        -   `create_table/add_column/drop_table/execute_sql_script`。
-   **典型用法**：由`PostgreSQLClient`相关方法自动调用。
-   **注意事项**：支持复杂约束、索引、注释、枚举类型等。

## API 参考

### PostgreSQLClient

#### 初始化

```python
client = PostgreSQLClient(
    host="localhost",          # 数据库主机
    port=5432,                 # 数据库端口
    database="your_database",  # 数据库名称
    user="your_username",      # 用户名
    password="your_password",  # 密码
    min_connections=1,         # 连接池最小连接数
    max_connections=10,        # 连接池最大连接数
    log_level=logging.INFO,    # 日志级别
    config_file=None,          # 配置文件路径(可选)
    application_name="PyPgClient" # 应用名称(可选)
)
```

**内部实现**:

-   使用单例模式管理实例，以 `(host, database, user)` 为键存储已创建的实例
-   自动创建连接池，管理数据库连接
-   支持从配置文件、环境变量和直接参数加载配置，优先级为：直接参数 > kwargs > 配置文件 > 环境变量 > 默认配置
-   如果相同键的实例已存在但关键参数(如密码、端口等)不同，会强制重新创建

**异常**:

-   连接数据库失败时会记录错误日志，但构造函数不会抛出异常

#### 实例管理

-   `get_instance(database, host=None, user=None)`: 获取已存在的客户端实例

    **参数**:

    -   `database` (str): 数据库名称
    -   `host` (str, optional): 数据库主机名，默认为 None
    -   `user` (str, optional): 用户名，默认为 None

    **返回**:

    -   `PostgreSQLClient`: 如果存在匹配的实例，则返回该实例
    -   `None`: 如果不存在匹配的实例

    **功能**:

    -   根据数据库名称、主机名和用户名查找现有实例
    -   如果只提供数据库名，则查找任何匹配该数据库的实例
    -   如果提供全部参数，则查找精确匹配的实例

    **内部实现**:

    -   从类的 `_instances` 字典中查找匹配的实例
    -   不会创建新实例，只返回已存在的实例或 None

#### 表操作

-   `create_table(table_name, table_schema_json)`: 创建表，支持丰富的字段属性和约束

    **参数**:

    -   `table_name` (str): 表名
    -   `table_schema_json` (dict 或 str): 表结构定义，支持 JSON 字符串或字典对象

    **返回**:

    -   `Dict[str, Any]`: 操作结果字典，包含以下字段：
        -   成功时: `{"success": True, "message": str, "table_name": str, "execution_time_ms": int, "sql": str, "schema": Dict}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "table_name": str, "execution_time_ms": int}`

    **功能**:

    -   根据 JSON 格式的表结构描述创建 PostgreSQL 表
    -   支持主键、外键、唯一约束、检查约束、默认值、注释等 PostgreSQL 特性
    -   支持创建索引、表注释和枚举类型

    **内部实现**:

    -   解析表结构 JSON，生成 CREATE TABLE SQL 语句
    -   自动处理字段属性、约束和索引
    -   通过事务执行多个 SQL 语句以创建表及相关对象

    **异常**:

    -   `ExecutionError`: SQL 执行错误，如语法错误、权限不足等
    -   `ValueError`: JSON 解析失败或表结构定义无效

-   `drop_table(table_name, force=False)`: 删除表

    **参数**:

    -   `table_name` (str): 表名，可以是 schema.table 格式
    -   `force` (bool, optional): 是否强制删除，默认 False。启用时会先 VACUUM 表以处理不可见元组，并使用 CASCADE 删除依赖对象

    **返回**:

    -   `Dict[str, Any]`: 操作结果字典，包含以下字段：
        -   成功时: `{"success": True, "message": str, "table_name": str, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "table_name": str, "execution_time_ms": int}`

    **功能**:

    -   删除指定的数据库表
    -   可以选择强制删除，处理不可见元组和依赖对象

    **内部实现**:

    -   检查表是否存在，不存在则返回成功
    -   如果 force=True，先执行 VACUUM，再执行 DROP TABLE CASCADE
    -   如果 force=False，执行标准 DROP TABLE

    **异常**:

    -   `ExecutionError`: 删除表失败，可能是因为权限不足或存在依赖对象

-   `add_column(table_name, column_json)`: 向表中添加新列

    **参数**:

    -   `table_name` (str): 表名
    -   `column_json` (dict 或 str): 列定义，支持 JSON 字符串或字典对象

    **返回**:

    -   `Dict[str, Any]`: 操作结果字典，包含以下字段：
        -   成功时: `{"success": True, "message": str, "table_name": str, "column_name": str, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "table_name": str, "execution_time_ms": int}`

    **功能**:

    -   向现有表中添加新列
    -   支持列类型、约束、默认值、注释等 PostgreSQL 特性

    **内部实现**:

    -   解析列定义 JSON，生成 ALTER TABLE ADD COLUMN SQL 语句
    -   支持自动添加相关约束和注释

    **异常**:

    -   `ExecutionError`: 添加列失败，如表不存在、列名重复等
    -   `ValueError`: JSON 解析失败或列定义无效

-   `get_all_columns(table_name)`: 获取表的所有列名

    **参数**:

    -   `table_name` (str): 表名

    **返回**:

    -   `str`: 所有列名的逗号分隔字符串，每个列名用双引号包围

    **功能**:

    -   查询指定表的所有列名，并对每个列名进行安全引号处理
    -   用于构建安全的 SQL 语句，避免 SQL 注入和保留字冲突

    **内部实现**:

    -   查询 information_schema.columns 获取表的列信息
    -   对每个列名进行处理：替换内部双引号为两个双引号，然后用双引号包裹
    -   返回安全引用的列名字符串

    **异常**:

    -   `ValueError`: 表不存在时抛出

#### 数据操作

-   `insert_data(table_name, data_json)`: 向表中插入数据

    **参数**:

    -   `table_name` (str): 表名
    -   `data_json` (dict, list 或 str): 要插入的数据，支持多种格式：
        -   `{"rows": [{...}, {...}]}`: 包含 rows 键的字典
        -   `[{...}, {...}]`: 字典列表
        -   `{...}`: 单个字典(插入单行)
        -   JSON 字符串: 上述任意格式的 JSON 字符串

    **返回**:

    -   `Dict[str, Union[bool, int, str, float, List]]`: 操作结果字典，包含以下字段：
        -   成功时: `{"success": True, "message": str, "inserted_count": int, "expected_count": int, "transaction_status": str, "table_name": str, "timestamp": float, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "error_code": str, "transaction_status": str, "table_name": str, "inserted_count": int, "expected_count": int, "timestamp": float, "execution_time_ms": int}`

    **功能**:

    -   向指定表插入单条或多条数据
    -   自动处理数据类型转换，支持复杂类型
    -   自动过滤表中不存在的字段

    **内部实现**:

    -   统一数据格式为行列表
    -   获取表列信息，过滤无效字段
    -   使用参数化查询构建安全的 INSERT 语句
    -   使用 executemany 批量插入多行数据
    -   自动处理事务提交/回滚

    **异常**:

    -   `ExecutionError`: 插入失败，包含详细的数据库错误信息
        -   不同类型的错误会有不同的 error_type：UniqueViolation、ForeignKeyViolation 等

-   `update_data(table_name, condition_str=None, data_json=None, batch_updates=None)`: 更新数据

    **参数**:

    -   `table_name` (str): 表名
    -   `condition_str` (str, optional): 更新条件字符串，如"id == 1"
    -   `data_json` (dict 或 str, optional): 要更新的数据，字典或 JSON 字符串
    -   `batch_updates` (list, optional): 批量更新数据列表，格式为[[condition_str, data_json], ...]

    **返回**:

    -   单条更新: `Dict[str, Any]` 包含以下字段：
        -   成功时: `{"success": True, "updated_count": int, "updated_data": Dict, "removed_fields": List[str], "transaction_status": str, "table_name": str, "condition": str, "timestamp": float, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "transaction_status": str, "table_name": str, "condition": str, "timestamp": float, "execution_time_ms": int}`
    -   批量更新: `Dict[str, Union[bool, int, List[Dict[str, Any]], str, float]]` 包含以下字段：
        -   `{"success": bool, "total_updates": int, "successful_updates": int, "failed_updates": int, "results": List[Dict], "transaction_status": str, "table_name": str, "timestamp": float, "execution_time_ms": int}`
        -   每个结果项: `{"index": int, "condition": str, "success": bool, "updated_count": int, "error": str, "error_type": str, "execution_time_ms": int}`

    **功能**:

    -   根据条件更新表中的数据
    -   支持单条更新或批量更新
    -   自动过滤表中不存在的字段

    **内部实现**:

    -   解析条件字符串为 SQL WHERE 子句
    -   过滤数据中不存在于表中的字段
    -   构建参数化的 UPDATE 语句
    -   批量更新时，在一个事务中处理所有更新，单独跟踪每个更新的结果
    -   自动处理事务提交/回滚

    **异常**:

    -   `ValueError`: 条件字符串解析错误
    -   `ExecutionError`: 更新失败，包含详细的数据库错误信息

-   `delete_data(table_name, condition_str)`: 删除数据

    **参数**:

    -   `table_name` (str): 表名
    -   `condition_str` (str): 删除条件字符串，如"id == 1"

    **返回**:

    -   `Dict[str, Any]`: 操作结果字典，包含以下字段：
        -   成功时: `{"success": True, "deleted_count": int, "condition": str, "transaction_status": str, "table_name": str, "timestamp": float, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "transaction_status": str, "table_name": str, "condition": str, "timestamp": float, "execution_time_ms": int}`

    **功能**:

    -   根据条件删除表中的数据
    -   自动处理事务提交/回滚

    **内部实现**:

    -   解析条件字符串为 SQL WHERE 子句
    -   构建参数化的 DELETE 语句
    -   执行 SQL 并返回受影响行数

    **异常**:

    -   `ValueError`: 条件字符串解析错误或表不存在
    -   `ExecutionError`: 删除失败，包含详细的数据库错误信息

-   `fetch_data(table_name, condition_str=None, columns="*", order_by=None, limit=None, offset=None, batch_conditions=None)`: 查询数据

    **参数**:

    -   `table_name` (str): 表名
    -   `condition_str` (str, optional): 查询条件字符串，如"age > 25"
    -   `columns` (str 或 list, optional): 要查询的列，可以是"\*"、列名字符串或列名列表
    -   `order_by` (str 或 list, optional): 排序规则，如"age DESC"或["name ASC", "age DESC"]
    -   `limit` (int, optional): 限制返回行数
    -   `offset` (int, optional): 偏移量，用于分页
    -   `batch_conditions` (list, optional): 批量查询条件列表，如["id == 1", "id == 2"]

    **返回**:

    -   单条件查询: `Dict[str, Any]` 包含以下字段：
        -   成功时: `{"success": True, "data": List[Dict[str, Any]], "count": int, "message": str, "table_name": str, "condition": str, "columns": List[str], "timestamp": float, "execution_time_ms": int}`
        -   失败时: `{"success": False, "error": str, "error_type": str, "message": str, "table_name": str, "condition": str, "timestamp": float, "execution_time_ms": int}`
    -   批量查询: `List[Dict[str, Any]]` 每项结构同上

    **功能**:

    -   根据条件查询表中的数据
    -   支持列选择、排序、分页和批量查询
    -   自动处理 JSON 字段的反序列化

    **内部实现**:

    -   解析条件字符串为 SQL WHERE 子句
    -   构建参数化的 SELECT 语句
    -   将查询结果转换为字典列表，每个字段名为键
    -   对 JSON/JSONB 类型字段自动进行反序列化
    -   批量查询时，并发处理多个条件查询

    **异常**:

    -   `ValueError`: 条件字符串解析错误或表不存在
    -   `ExecutionError`: 查询失败

-   `filter_insert_data_by_schema(insert_data, table_schema)`: 过滤不存在的字段

    **参数**:

    -   `insert_data` (dict 或 list): 要插入的数据，可以是单个字典或字典列表
    -   `table_schema` (dict): 表结构定义，支持完整格式和简化格式

    **返回**:

    -   `Dict[str, Any]`: 过滤结果，包含以下字段：
        -   成功时: `{"filtered_data": Any, "removed_fields": List[str], "success": True, "message": str}`
        -   失败时: `{"filtered_data": None, "removed_fields": [], "success": False, "message": str}`

    **功能**:

    -   检查插入数据的字段是否存在于表结构中
    -   过滤掉表中不存在的字段
    -   返回过滤后的数据和被删除的字段列表

    **内部实现**:

    -   从表结构中提取有效列名
    -   遍历数据，移除无效字段
    -   保持原始数据结构（单个字典或列表）

    **异常**:

    -   `ValueError`: 数据格式无效

#### 事务管理

-   `begin()`: 开始事务

    **功能**:

    -   显式开始一个新事务
    -   设置 `in_transaction` 标志为 True
    -   如果已经在事务中，则忽略此调用

    **内部实现**:

    -   获取数据库连接并保持连接打开
    -   不执行实际的 BEGIN 语句，依赖 psycopg2 的自动事务管理

    **异常**:

    -   `ExecutionError`: 开始事务失败，例如连接问题

-   `commit()`: 提交事务

    **功能**:

    -   提交当前事务中的所有操作
    -   重置 `in_transaction` 标志为 False
    -   如果不在事务中，则忽略此调用

    **内部实现**:

    -   调用连接的 commit()方法提交事务
    -   提交后归还连接到连接池

    **异常**:

    -   `ExecutionError`: 提交事务失败

-   `rollback()`: 回滚事务

    **功能**:

    -   回滚当前事务中的所有操作
    -   重置 `in_transaction` 标志为 False
    -   如果不在事务中，则忽略此调用

    **内部实现**:

    -   调用连接的 rollback()方法回滚事务
    -   回滚后归还连接到连接池

    **异常**:

    -   `ExecutionError`: 回滚事务失败

-   `__enter__()`/`__exit__()`: 上下文管理器

    **功能**:

    -   支持使用 `with` 语句自动管理事务
    -   进入 `with` 块时自动开始事务
    -   退出 `with` 块时，如果没有异常则提交事务，有异常则回滚事务

    **用法**:

    ```python
    with PostgreSQLClient(...) as client:
        # 在一个事务中执行操作
        client.insert_data(...)
        client.update_data(...)
        # with块结束时自动提交或回滚
    ```

#### 其他操作

-   `execute_query(sql, params=None, fetch=True)`: 执行 SQL 查询

    **参数**:

    -   `sql` (str): SQL 查询语句
    -   `params` (Any, optional): 查询参数，可以是元组、列表或字典，用于参数化查询防止 SQL 注入
    -   `fetch` (bool, optional): 是否获取查询结果，默认 True

    **返回**:

    -   `Optional[List[Tuple]]`:
        -   当 fetch=True 且是 SELECT 查询时，返回查询结果列表，每行是一个元组
        -   当 fetch=False 或不是 SELECT 查询时，返回 None

    **功能**:

    -   执行任意 SQL 语句
    -   支持参数化查询，防止 SQL 注入
    -   自动识别是否需要获取结果
    -   自动处理事务提交/回滚

    **内部实现**:

    -   自动判断 SQL 类型（SELECT/INSERT/UPDATE 等）
    -   对非 SELECT 语句，自动将 fetch 设为 False
    -   使用 psycopg2 参数化查询
    -   非事务操作自动提交或回滚
    -   返回 fetchall()结果或 None

    **异常**:

    -   `ExecutionError`: 执行失败，包含原始 SQL 和参数

-   `execute_sql_script(sql_script_path)`: 执行 SQL 脚本文件

    **参数**:

    -   `sql_script_path` (str): SQL 脚本文件路径

    **返回**:

    -   `Dict[str, Any]`: 操作结果字典，包含执行状态、脚本路径和执行时间等信息

    **功能**:

    -   执行 SQL 脚本文件中的所有语句
    -   可用于批量执行初始化脚本、模式迁移等

    **内部实现**:

    -   读取脚本文件内容
    -   按分号分割为多条 SQL 语句
    -   在一个事务中执行所有语句

    **异常**:

    -   `IOError`: 文件读取失败
    -   `ExecutionError`: SQL 执行失败

-   `close()`: 关闭客户端

    **功能**:

    -   归还当前连接到连接池
    -   关闭连接池，释放所有资源
    -   建议在程序结束时调用

    **内部实现**:

    -   如果在事务中，尝试回滚事务
    -   归还当前连接到连接池
    -   关闭整个连接池

    **异常**:

    -   即使发生异常也会继续尝试释放资源，不抛出异常

## 条件语法参考

本模块支持使用 Python 风格的条件字符串，会被自动转换为 SQL WHERE 子句。

### 基本比较操作符

-   `==`: 等于，如 `"name == '张三'"`
-   `!=`: 不等于，如 `"age != 30"`
-   `>`: 大于，如 `"age > 25"`
-   `<`: 小于，如 `"age < 40"`
-   `>=`: 大于等于，如 `"age >= 18"`
-   `<=`: 小于等于，如 `"age <= 60"`

### 逻辑操作符

-   `and`: 逻辑与，如 `"age > 20 and age < 30"`
-   `or`: 逻辑或，如 `"name == '张三' or name == '李四'"`
-   `not`: 逻辑非，如 `"not (age < 18)"`

### 特殊操作符

-   `is`: 判断是否为 NULL，如 `"email is null"`
-   `like`: 模糊匹配，如 `"name like '%张%'"`
-   `in`: 包含，如 `"age in (25, 30, 35)"`
-   `between`: 范围，如 `"age between 20 and 30"`

### 组合使用

-   可以使用括号组合多个条件，如 `"(age > 20 and age < 30) or (name == '张三')"`
-   可以嵌套使用，如 `"not (age < 18 or (name == '张三' and city == '北京'))"`

## 条件字符串语法详解（高级用法与全覆盖示例）

本节详细说明 `PostgreSQLClient.fetch_data`、`update_data` 等方法中 `condition_str` 条件字符串的所有支持语法、用法、注意事项与丰富示例，适用于所有基于条件字符串的查询、更新、删除等操作。

### 1. 字段存在性判断

-   判断字段是否为 NULL：
    -   `field is null`  
        示例：`email is null`  
        作用：查询 email 字段值为 NULL 的记录
    -   `field is not null`  
        示例：`email is not null`  
        作用：查询 email 字段有值的记录

### 2. 字段等值/比较操作

-   等于/不等于/大于/小于/大于等于/小于等于：
    -   `field == value`  
        示例：`age == 18`  
        作用：查询 age 等于 18 的记录
    -   `field != value` 或 `field <> value`  
        示例：`name != '张三'`  
        作用：查询 name 不等于"张三"的记录
    -   `field > value`、`field < value`、`field >= value`、`field <= value`  
        示例：`score >= 60`  
        作用：查询 score 大于等于 60 的记录

### 3. 范围与集合操作

-   IN/NOT IN：
    -   `field in (v1, v2, v3)`  
        示例：`status in ('active', 'pending')`  
        作用：查询 status 为 active 或 pending 的记录
    -   `field not in (v1, v2, v3)`  
        示例：`id not in (1, 2, 3)`
-   BETWEEN：
    -   `field between v1 and v2`  
        示例：`age between 18 and 30`  
        作用：查询 age 在 18 到 30 之间的记录

### 4. 模糊匹配

-   LIKE/NOT LIKE：
    -   `field like '%abc%'`  
        示例：`name like '%三%'`  
        作用：查询 name 包含"三"的记录
    -   `field not like '%abc%'`  
        示例：`email not like '%@gmail.com'`

### 5. 逻辑组合与嵌套

-   AND/OR/NOT/括号：
    -   `cond1 and cond2`  
        示例：`age > 18 and city == '北京'`
    -   `cond1 or cond2`  
        示例：`name == '张三' or name == '李四'`
    -   `not cond`  
        示例：`not (status == 'disabled')`
    -   括号嵌套：
        示例：`(age > 18 and age < 30) or (name == '张三')`

### 6. NULL 判断与特殊操作符

-   IS/IS NOT：
    -   `field is null`、`field is not null`
-   其他特殊操作符：
    -   `field like ...`、`field in (...)`、`field between ... and ...`

### 7. JSON 字段操作

-   判断 JSON 字段 key 是否存在：
    -   `json_field->'key' is not null`  
        示例：`profile->'hobby' is not null`  
        作用：profile 字段（jsonb）中存在 hobby 键且不为 null
-   判断 JSON 字段 key 的值：
    -   `json_field->'key' = 'value'`  
        示例：`profile->'gender' = 'male'`
-   JSON 字段数值比较：
    -   `json_field->'score'::int > 80`  
        示例：`profile->'score'::int > 80`
-   JSON 多级嵌套：
    -   `json_field->'outer'->'inner' = 'xxx'`  
        示例：`profile->'address'->'city' = '北京'`
-   json_field->'key'
-   json_field->>'key'
-   json_field->'key'::int
-   多级嵌套：json_field->'a'->'b'
-   链式组合：json_field->'a'->>'b'
-   括号表达式：(json_field->'a'->>'b' = 'x')
-   与比较/逻辑操作符结合：json_field->'key' is not null、json_field->>'key' = 'value'
-   类型转换：json_field->'key'::int > 10
-   数组下标：如 data->'arr'->>0，目前解析器会把 0 当作字符串 '0'，而 PostgreSQL 允许数字下标。若需严格支持数字下标，可进一步增强。
-   类型转换：目前仅支持 ::type，如 ::int、::float，与 PostgreSQL 兼容。
-   取 JSON 对象字段，返回 JSON（如 data->'a'）
-   取 JSON 对象字段，返回文本（如 data->>'a'）
-   多级嵌套：data->'a'->'b'
-   多级文本：data->'a'->>'b'
-   类型转换：(data->>'a')::int
-   支持与比较、IS NULL、LIKE、IN、BETWEEN、AND/OR/NOT 等 SQL 组合
-   支持数组下标：data->'arr'->>0（目前解析器未专门支持数字下标，但可作为字符串处理）

> 注意：JSON 字段操作依赖于 PostgreSQL 的 jsonb 操作符，字段类型需为 jsonb，且建议在表结构中声明为 jsonb 类型。

### 8. 数组字段操作

-   判断数组包含：
    -   `array_field @> ARRAY[1,2]`  
        示例：`tags @> ARRAY['python']`  
        作用：tags 字段（数组）包含 'python'
-   判断数组长度：
    -   `array_length(array_field, 1) > 2`

### 9. 日期/时间字段操作

-   日期区间：
    -   `date_field between '2023-01-01' and '2023-12-31'`
-   日期比较：
    -   `created_at >= '2024-01-01'`

### 10. 复杂嵌套与组合

-   支持任意层级的括号嵌套、复杂逻辑组合：
    -   `((name == '张三' or name == '李四') and age > 18) or (vip_level > 3 and (points > 1000 or register_days > 365))`

### 11. 字段类型与自动转换

-   字符串需用单引号包裹：`name == '张三'`
-   数字直接写：`age > 18`
-   布尔值：`is_active == true`
-   NULL：`field is null`

### 12. 错误与注意事项

-   字段名区分大小写（如有大写/特殊字符需用双引号包裹）
-   JSON 字段操作仅支持 jsonb 类型字段
-   SQL 注入风险已自动防控，条件字符串会被安全解析
-   不支持的语法会抛出结构化异常，建议捕获并处理

### 13. 典型用法示例

```python
# 查询 name 为"张三"且年龄大于 20 的用户
data = client.fetch_data('users', condition_str="name == '张三' and age > 20")

# 查询 email 字段为 null 的用户
data = client.fetch_data('users', condition_str="email is null")

# 查询 profile JSON 字段中 hobby 存在且为"足球"
data = client.fetch_data('users', condition_str="profile->'hobby' = '足球'")

# 查询 tags 数组字段包含 'python'
data = client.fetch_data('users', condition_str="tags @> ARRAY['python']")

# 查询注册日期在 2023 年的用户
data = client.fetch_data('users', condition_str="register_date between '2023-01-01' and '2023-12-31'")

# 复杂嵌套条件
data = client.fetch_data('users', condition_str="(age > 18 and (city == '北京' or city == '上海')) or (vip_level > 3)")
```

> **建议：如需更复杂的条件，可直接书写 PostgreSQL 支持的表达式，或参考 `sql_condition_parser.py` 的实现与测试用例。所有条件字符串均会被自动安全解析为 SQL WHERE 子句。**

## 测试

模块包含完整的测试用例，可以通过以下方式运行：

```python
from automatic_dimension.postgre_sql.test_client import run_all_tests

# 运行所有测试
run_all_tests()
```

### SQL 条件解析器测试

本模块提供了三个测试脚本，用于验证 SQL 条件解析器的功能：

#### 1. 简单测试 (simple_test.py)

用于测试 SQL 条件解析器的基本功能，包括基本比较操作、逻辑操作符、复合条件、特殊操作符等。

```python
# 运行简单测试
python automatic_dimension/simple_test.py
```

示例测试用例：

-   基本比较操作：`"name == '张三'"`, `"age > 25"`, `"age >= 18"`, `"age <= 65"`, `"age != 30"`
-   逻辑操作符：`"name == '张三' and age > 25"`, `"name == '张三' or name == '李四'"`, `"not age < 18"`
-   复合条件：`"name == '张三' and (age > 25 or city == '北京')"`
-   特殊操作符：`"name is null"`, `"city like '%京%'"`, `"city in ('北京', '上海', '广州')"`, `"age between 18 and 65"`

#### 2. 复杂测试 (complex_test.py)

用于测试 SQL 条件解析器处理复杂表达式的能力，包括嵌套条件、多级嵌套、组合操作符等。

```python
# 运行复杂测试
python automatic_dimension/complex_test.py
```

示例测试用例：

-   嵌套条件：`"((name == '张三' or name == '李四') and age > 18) or (vip_level > 3 and (points > 1000 or register_days > 365))"`
-   多级嵌套：`"(status == 'active' and (role == 'admin' or (role == 'user' and permissions in ('read', 'write', 'delete'))))"`
-   组合操作符：`"age >= 18 and age <= 65 and not (status == 'disabled' or status == 'suspended')"`
-   极端嵌套：`"(((a == 1 and b == 2) or (c == 3 and d == 4)) and ((e == 5 or f == 6) and (g == 7 or h == 8)))"`

#### 3. 通用测试 (test_postgre_sql.py)

综合测试脚本，可以验证 SQL 条件解析器的各种功能，并支持预期结果验证。

```python
# 运行通用测试
python automatic_dimension/test_postgre_sql.py
```

使用方法：

```python
# 测试条件解析，不指定预期结果
test_condition("name == '张三'")

# 测试条件解析，指定预期结果
test_condition("age > 25", expected='("age" > 25)')
```

### 自定义测试

您可以基于这些测试脚本创建自己的测试用例：

```python
from automatic_dimension.postgre_sql.sql_condition_parser import parse_condition

# 测试自定义条件
condition = "user_id == 100 and (status == 'active' or last_login > '2023-01-01')"
sql_where = parse_condition(condition)
print(f"条件: {condition}")
print(f"SQL WHERE子句: {sql_where}")
```

### 测试脚本使用技巧

1. **预期结果验证**：使用 `test_condition` 函数的 `expected` 参数可以验证解析结果是否符合预期
2. **批量测试**：可以定义测试用例列表，批量运行测试
3. **错误处理测试**：可以测试不正确的条件表达式，验证解析器的错误处理能力
4. **性能测试**：可以使用大量复杂条件测试解析器的性能

通过这些测试脚本，您可以确保 SQL 条件解析器能够正确处理各种复杂的条件表达式，为数据库操作提供可靠的条件转换功能。

## 贡献

欢迎贡献代码、报告问题或提出改进建议。

## 许可证

[MIT](LICENSE)

## 详细示例（强烈推荐阅读）

### 1. 连接池与配置高级用法

```python
from automatic_dimension.postgre_sql import PostgreSQLClient

# 通过配置文件初始化（支持json/ini/yaml）
client = PostgreSQLClient(
    host="localhost",
    port=5432,
    database="test_db",
    user="test_user",
    password="test_pwd",
    min_connections=2,
    max_connections=20,
    config_file="./db_config.json",  # 可选，自动合并优先级
    log_level="DEBUG",               # 日志级别可选
    application_name="MyApp"         # 可选，便于数据库端追踪
)

# 获取连接池状态
print(client.pool.get_pool_status())
```

### 2. 复杂表结构创建（含约束、索引、注释、枚举）

```python
# 定义复杂表结构，含多主键、外键、唯一、检查、索引、注释、枚举
schema = {
    "columns": {
        "id": {"type": "SERIAL", "primary_key": True, "comment": "主键ID"},
        "name": {"type": "VARCHAR(100)", "nullable": False, "unique": True, "comment": "用户名"},
        "role": {"type": "user_role_enum", "enum_values": ["admin", "user", "guest"], "create_type": True, "default": "'user'", "comment": "角色"},
        "dept_id": {"type": "INTEGER", "references": "departments(id)", "on_delete": "CASCADE", "comment": "部门ID"},
        "salary": {"type": "NUMERIC(10,2)", "default": 0, "check": "salary >= 0", "comment": "薪资"},
        "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
    },
    "constraints": [
        {"type": "UNIQUE", "columns": ["name", "dept_id"]},
        {"type": "CHECK", "condition": "length(name) > 2"}
    ],
    "indexes": [
        {"columns": ["dept_id"], "method": "BTREE"},
        {"columns": ["created_at"], "method": "BRIN"}
    ],
    "table_comment": "员工信息表"
}
client.create_table("employees", schema)
```

### 3. 批量插入与字段过滤

```python
# 批量插入数据，自动过滤不存在的字段
rows = [
    {"name": "张三", "role": "admin", "salary": 10000, "extra": "will be ignored"},
    {"name": "李四", "role": "user", "salary": 8000, "unknown": 123}
]
# 先过滤
filter_result = client.filter_insert_data_by_schema(rows, schema)
print(filter_result)
# 插入
insert_result = client.insert_data("employees", rows)
print(insert_result)
```

### 4. 复杂条件查询与分页

```python
# 查询所有薪资大于5000且角色为admin的员工，按创建时间倒序，分页
result = client.fetch_data(
    table_name="employees",
    condition_str="salary > 5000 and role == 'admin'",
    columns=["id", "name", "salary", "role", "created_at"],
    order_by="created_at DESC",
    limit=10,
    offset=0
)
print(result)

# 批量条件查询
batch_conditions = [
    "role == 'admin'",
    "salary > 9000",
    "name like '%四%'"
]
batch_result = client.fetch_data("employees", batch_conditions=batch_conditions)
print(batch_result)
```

### 5. 批量更新与事务

```python
# 批量更新，所有操作在一个事务中，部分失败不会影响其他
batch_updates = [
    ["id == 1", {"salary": 12000}],
    ["id == 2", {"salary": 9000, "role": "user"}],
    ["id == 999", {"salary": 5000}]  # 不存在的id，返回失败但不影响其他
]
update_result = client.update_data(table_name="employees", batch_updates=batch_updates)
print(update_result)
```

### 6. 日志与异常处理

```python
import logging
from automatic_dimension.postgre_sql import PostgreSQLClient, ExecutionError

# 设置日志级别为DEBUG，输出所有SQL和详细信息
client = PostgreSQLClient(
    host="localhost", database="test_db", user="test_user", password="test_pwd", log_level=logging.DEBUG
)

try:
    # 故意插入重复唯一字段，触发异常
    client.insert_data("employees", {"name": "张三", "role": "admin"})
except ExecutionError as e:
    print("捕获到数据库执行异常：", e)
    print("详细信息：", e.to_dict())
```

### 7. 事务管理与回滚

```python
# 手动事务控制，出现异常自动回滚
client = PostgreSQLClient(host="localhost", database="test_db", user="test_user", password="test_pwd")
try:
    client.begin()
    client.insert_data("employees", {"name": "王五", "role": "user"})
    # 故意触发错误
    client.update_data("employees", "id == 9999", {"salary": 10000})
    client.commit()
except Exception as e:
    client.rollback()
    print("事务失败，已回滚：", e)
finally:
    client.close()
```

### 8. JSON/数组/特殊类型字段的插入与查询

```python
# 假设有jsonb类型字段
schema = {
    "columns": {
        "id": {"type": "SERIAL", "primary_key": True},
        "profile": {"type": "jsonb", "nullable": True, "comment": "用户扩展信息"}
    }
}
client.create_table("user_profiles", schema)

# 插入带json字段的数据
client.insert_data("user_profiles", {"profile": {"hobby": ["足球", "编程"], "age": 28}})

# 查询并自动反序列化json字段
result = client.fetch_data("user_profiles", columns=["id", "profile"])
print(result)
```

### 9. 命令行工具用法

```bash
# 查询表数据
python -m automatic_dimension.postgre_sql -d test_db -u test_user --password test_pwd fetch employees --condition "salary > 5000"

# 批量插入
python -m automatic_dimension.postgre_sql -d test_db -u test_user insert employees data.json

# 创建表
python -m automatic_dimension.postgre_sql -d test_db -u test_user create-table employees schema.json
```

---

## 详细示例进阶（全场景覆盖）

### 1. 数据类型全覆盖插入与查询

```python
import uuid, decimal, datetime
from automatic_dimension.postgre_sql import PostgreSQLClient

schema = {
    "columns": {
        "id": {"type": "SERIAL", "primary_key": True},
        "str_col": {"type": "VARCHAR(50)"},
        "int_col": {"type": "INTEGER"},
        "float_col": {"type": "FLOAT"},
        "bool_col": {"type": "BOOLEAN"},
        "date_col": {"type": "DATE"},
        "dt_col": {"type": "TIMESTAMP"},
        "arr_col": {"type": "INTEGER[]"},
        "json_col": {"type": "jsonb"},
        "bin_col": {"type": "BYTEA"},
        "uuid_col": {"type": "UUID"},
        "dec_col": {"type": "NUMERIC(10,2)"}
    }
}
client.create_table("type_test", schema)

row = {
    "str_col": "hello",
    "int_col": 42,
    "float_col": 3.14,
    "bool_col": True,
    "date_col": datetime.date.today(),
    "dt_col": datetime.datetime.now(),
    "arr_col": [1,2,3],
    "json_col": {"a": 1, "b": [1,2]},
    "bin_col": b"binarydata",
    "uuid_col": str(uuid.uuid4()),
    "dec_col": decimal.Decimal("12.34")
}
client.insert_data("type_test", row)
result = client.fetch_data("type_test")
print(result)
```

### 2. 字段缺失/多余/类型不匹配处理

```python
# 多余字段会被自动过滤，缺失字段如有默认值则自动补齐
row = {"str_col": "abc", "extra": 123}
client.insert_data("type_test", row)  # extra字段被忽略
# 类型不匹配会抛出异常
try:
    client.insert_data("type_test", {"int_col": "not_an_int"})
except Exception as e:
    print("类型不匹配异常：", e)
```

### 3. 主键/唯一/外键/检查约束冲突

```python
# 主键冲突
try:
    client.insert_data("type_test", {"id": 1, "str_col": "dup"})
except Exception as e:
    print("主键冲突异常：", e)
# 唯一约束冲突
schema2 = {"columns": {"id": {"type": "SERIAL", "primary_key": True}, "email": {"type": "VARCHAR(100)", "unique": True}}}
client.create_table("unique_test", schema2)
client.insert_data("unique_test", {"email": "<EMAIL>"})
try:
    client.insert_data("unique_test", {"email": "<EMAIL>"})
except Exception as e:
    print("唯一约束异常：", e)
# 外键约束冲突
schema3 = {"columns": {"id": {"type": "SERIAL", "primary_key": True}, "ref_id": {"type": "INTEGER", "references": "unique_test(id)"}}}
client.create_table("fk_test", schema3)
try:
    client.insert_data("fk_test", {"ref_id": 9999})
except Exception as e:
    print("外键约束异常：", e)
# 检查约束冲突
schema4 = {"columns": {"id": {"type": "SERIAL", "primary_key": True}, "age": {"type": "INTEGER", "check": "age >= 0"}}}
client.create_table("check_test", schema4)
try:
    client.insert_data("check_test", {"age": -1})
except Exception as e:
    print("检查约束异常：", e)
```

### 4. 复杂条件与嵌套查询

```python
# 支持括号、not、in、between、like、is null等
result = client.fetch_data(
    "type_test",
    condition_str="(int_col > 10 and float_col < 10) or (str_col like '%he%') and not bool_col == false"
)
print(result)
# in/between/is null
result = client.fetch_data("type_test", condition_str="int_col in (1,2,42) and date_col between '2020-01-01' and '2099-01-01'")
print(result)
result = client.fetch_data("type_test", condition_str="bin_col is null")
print(result)
```

### 5. 事务嵌套与回滚

```python
# 嵌套事务（推荐用with语法）
try:
    with PostgreSQLClient(host="localhost", database="test_db", user="test_user", password="test_pwd") as client2:
        client2.insert_data("type_test", {"str_col": "in_tx"})
        raise Exception("模拟异常")
except Exception as e:
    print("自动回滚：", e)
# 手动begin/commit/rollback
client.begin()
try:
    client.insert_data("type_test", {"str_col": "manual_tx"})
    client.commit()
except Exception as e:
    client.rollback()
    print("手动回滚：", e)
```

### 6. 配置优先级与环境变量

```python
import os
os.environ["PGDB_HOST"] = "127.0.0.1"
os.environ["PGDB_PORT"] = "5433"
client = PostgreSQLClient(database="test_db", user="test_user", password="test_pwd")
print(client.config.get_config())  # 环境变量优先于默认配置
```

### 7. 日志与异常结构化处理

```python
import logging
client = PostgreSQLClient(host="localhost", database="test_db", user="test_user", password="test_pwd", log_level=logging.DEBUG)
try:
    client.insert_data("type_test", {"int_col": "bad"})
except Exception as e:
    if hasattr(e, 'to_dict'):
        print("结构化异常：", e.to_dict())
    else:
        print("普通异常：", e)
```

### 8. 连接池极限与回收

```python
# 模拟高并发获取连接，超过max_connections会等待或报错
import threading
results = []
def worker():
    try:
        c = PostgreSQLClient(host="localhost", database="test_db", user="test_user", password="test_pwd", min_connections=1, max_connections=2)
        c.fetch_data("type_test")
        results.append("ok")
    except Exception as e:
        results.append(str(e))
threads = [threading.Thread(target=worker) for _ in range(5)]
for t in threads: t.start()
for t in threads: t.join()
print(results)
# 手动回收所有连接
client.pool.close_all()
```

### 9. 表结构变更与动态 DDL

```python
# 添加新列
client.add_column("type_test", {"name": "new_col", "type": "VARCHAR(20)", "default": "'abc'", "comment": "新加列"})
# 删除表
client.drop_table("type_test")
```

### 10. 命令行工具全参数用法

```bash
# 查询所有参数
python -m automatic_dimension.postgre_sql --help
# 执行SQL
python -m automatic_dimension.postgre_sql -d test_db -u test_user sql "SELECT version()"
# 批量更新
python -m automatic_dimension.postgre_sql -d test_db -u test_user update employees batch_updates.json
```

### 11. 性能与批量操作

```python
# 批量插入1万条数据
rows = [{"str_col": f"row{i}", "int_col": i} for i in range(10000)]
client.insert_data("type_test", rows)
# 批量查询
conds = [f"int_col == {i}" for i in range(100, 110)]
results = client.fetch_data("type_test", batch_conditions=conds)
print(results)
```

### 12. 常见错误与调试技巧

```python
# 1. 查询无结果时返回空列表而不是异常
result = client.fetch_data("type_test", condition_str="int_col == -9999")
print(result)
# 2. 条件语法错误自动结构化返回
try:
    client.fetch_data("type_test", condition_str="id === 1")
except ValueError as e:
    print("条件语法错误：", e)
# 3. 日志调试SQL
client = PostgreSQLClient(host="localhost", database="test_db", user="test_user", password="test_pwd", log_level="DEBUG")
client.fetch_data("type_test")
```

> **建议：所有示例均可直接运行，遇到任何异常，建议先查看日志输出，或捕获异常后打印`to_dict()`结构化信息，便于调试和定位问题。**

# 其他

-   官方推荐方法：
    重建所有使用默认排序规则的对象（如表、索引等）。
    刷新数据库排序规则版本：
    ```sql
    ALTER DATABASE postgres REFRESH COLLATION VERSION;
    ```
