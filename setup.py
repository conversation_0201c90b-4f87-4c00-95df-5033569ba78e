from setuptools import setup, find_packages

setup(
    # 包的名称，这是你的包在 PyPI 上的唯一标识符
    name="global-tools",
    
    # 包的版本号，遵循语义化版本规范 (major.minor.patch)
    version="1.0.0",
    
    # 使用 find_packages() 自动发现所有包和子包
    packages=find_packages(),
    
    # 包含所有包数据文件
    include_package_data=True,
    
    # 指定每个包中要包含的数据文件
    package_data={
        'global_tools': ['*', '*/*', '*/*/*', '*/*/*/*'],  # 递归包含所有文件
    },
    
    # 指定依赖包列表，安装此包时会自动安装这些依赖
    # 目前为空列表，表示没有依赖
    install_requires=[],
    
    # 包的作者信息
    author="Your Name",
    
    # 定义命令行入口点，安装后可以在命令行直接使用的命令
    entry_points={          
       
    },
)
