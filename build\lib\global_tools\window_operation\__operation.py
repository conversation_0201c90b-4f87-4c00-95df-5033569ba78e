import asyncio
import os
import re
import threading
import time
from typing import Any, Dict, List, Optional
import win32gui
import numpy as np
import cv2
from .helper import *
import logging
import traceback
from global_tools.utils import Singleton
import random
from ctypes import byref, wintypes

""" ====================================================================== PC 端窗口句柄截图 ================================================================== """


def capture_window_handle(
    hwnd: int,
    region: list = None,
    save: bool = False,
    save_path: str = "screenshots",
    preview: bool = False,
    grayscale: bool = False,
    binary: bool = False
) -> dict:
    """终极优化版窗口截图函数"""
    result = {"status": "error", "message": "", "image": None, "path": ""}

    if not user32.IsWindow(hwnd):
        result["message"] = "无效窗口句柄"
        return result

    hdc_main = hdc_mem = hbitmap = old_hbitmap = None
    try:
        # 获取客户区尺寸
        client_rect = win32gui.GetClientRect(hwnd)
        client_width = client_rect[2] - client_rect[0]
        client_height = client_rect[3] - client_rect[1]
        if client_width <= 0 or client_height <= 0:
            raise ValueError("无效窗口尺寸")

        # 创建设备上下文
        hdc_main = user32.GetDC(hwnd)
        if not hdc_main:
            raise WindowCaptureError("获取设备上下文失败")

        hdc_mem = gdi32.CreateCompatibleDC(hdc_main)
        if not hdc_mem:
            raise WindowCaptureError("创建兼容设备上下文失败")

        # 初始化BITMAPINFO
        bmi = BITMAPINFO()
        bmi.bmiHeader.biSize = ctypes.sizeof(BITMAPINFOHEADER)
        bmi.bmiHeader.biWidth = client_width
        bmi.bmiHeader.biHeight = -client_height  # 顶到底扫描
        bmi.bmiHeader.biPlanes = 1
        bmi.bmiHeader.biBitCount = 32
        bmi.bmiHeader.biCompression = BI_RGB

        # 创建DIB段
        ppv_bits = ctypes.c_void_p()
        hbitmap = gdi32.CreateDIBSection(
            hdc_mem,
            ctypes.byref(bmi),
            DIB_RGB_COLORS,
            ctypes.byref(ppv_bits),
            None,
            0
        )

        if not hbitmap:
            raise WindowCaptureError("创建DIB段失败")

        old_hbitmap = gdi32.SelectObject(hdc_mem, hbitmap)
        if not old_hbitmap:
            raise WindowCaptureError("选择位图对象失败")

        # 执行截图
        if not user32.PrintWindow(hwnd, hdc_mem, PW_RENDERFULLCONTENT):
            raise WindowCaptureError("PrintWindow调用失败")

        # 直接访问内存
        buffer = (ctypes.c_ubyte * (client_width * client_height * 4)).from_address(ppv_bits.value)
        img_np = np.frombuffer(buffer, dtype=np.uint8).reshape((client_height, client_width, 4))
        img_cv = img_np[..., :3].copy()  # 转换为BGR

        # 区域处理
        if region:
            if len(region) != 4 or any(x < 0 for x in region) or region[2] <= region[0] or region[3] <= region[1]:
                raise ValueError("区域参数错误")
            img_cv = img_cv[region[1]:region[3], region[0]:region[2]]

        # 图像处理
        if grayscale:
            img_cv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            if binary:
                cv2.threshold(img_cv, 200, 255, cv2.THRESH_BINARY, dst=img_cv)

        # 保存和预览
        if save:
            cv2.imwrite(save_path, img_cv)
            result["path"] = save_path

        if preview:
            cv2.imshow('Preview', img_cv)
            cv2.waitKey(1)

        result.update({"status": "success", "image": img_cv})

    except Exception as e:
        logging.error(f"截图异常: {str(e)}", exc_info=True)
        traceback.print_exc()
        result["message"] = str(e)
    finally:
        # 释放资源
        if old_hbitmap and hdc_mem:
            gdi32.SelectObject(hdc_mem, old_hbitmap)
        if hbitmap:
            gdi32.DeleteObject(hbitmap)
        if hdc_mem:
            gdi32.DeleteDC(hdc_mem)
        if hdc_main:
            user32.ReleaseDC(hwnd, hdc_main)

    return result


""" ====================================================================== 获取指定窗口句柄 ================================================================== """

@Singleton
class GetSpecifiedWindowHandle:
    """
    根据窗口类名和标题获取窗口句柄的类
    支持精确匹配和模糊匹配（使用正则表达式）
    """

    class_name = None
    title = None

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化窗口句柄获取器
        支持正则表达式的模糊匹配

        Args:
            class_name: 窗口类名，支持正则表达式
            title: 窗口标题，支持正则表达式
            timeout: 查找超时时间（秒）
        """
        self._class_name = class_name
        self._title = title
        self._timeout = timeout
        self._handle = None
        self._last_find_time = 0
        self._cache_duration = 30  # 缓存有效期（秒）

        # 初始化时立即查找一次
        self._find_window_handle_with_timeout()

    def __call__(self) -> Optional[int]:
        """
        获取窗口句柄，如果缓存失效则重新查找

        Returns:
            Optional[int]: 窗口句柄，如果未找到则返回 None
        """
        if not self.is_valid or (time.time() - self._last_find_time > self._cache_duration):
            self._find_window_handle_with_timeout()
        return self._handle

    @property
    def handle(self) -> Optional[int]:
        """获取当前缓存的窗口句柄"""
        return self._handle

    @property
    def is_valid(self) -> bool:
        """
        检查当前句柄是否有效
        """
        if self._handle is None:
            return False
        try:
            # 尝试获取窗口位置，验证句柄有效性
            win32gui.GetWindowRect(self._handle)
            return True
        except Exception:
            return False

    def refresh(self) -> Optional[int]:
        """
        强制刷新窗口句柄

        Returns:
            Optional[int]: 新的窗口句柄
        """
        self._find_window_handle_with_timeout()
        return self._handle

    def _find_window_handle_with_timeout(self) -> None:
        """带超时的窗口句柄查找"""
        start_time = time.time()
        while time.time() - start_time < self._timeout:
            try:
                handle = self._find_window_handle()
                if handle is not None:
                    self._handle = handle
                    self._last_find_time = time.time()
                    return
            except Exception as _:
                traceback.print_exc()
            time.sleep(0.1)  # 短暂休眠避免CPU占用过高

        # 超时后，如果没找到句柄，设置为None
        self._handle = None
        print(f"在 {self._timeout} 秒内未找到符合条件的窗口句柄")

    def _find_window_handle(self) -> Optional[int]:
        """
        查找符合条件的窗口句柄
        支持正则表达式匹配
        """
        try:
            def enum_windows_callback(hwnd: int, handles: List[int]) -> bool:
                try:
                    # 获取窗口类名和标题
                    window_class = win32gui.GetClassName(hwnd)
                    window_title = win32gui.GetWindowText(hwnd)

                    # 检查窗口是否可见
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    # 根据类名和标题进行匹配
                    class_match = True
                    title_match = True

                    if self._class_name:
                        class_match = bool(re.match(self._class_name, window_class))
                    if self._title:
                        title_match = bool(re.match(self._title, window_title))

                    if class_match and title_match:
                        handles.append(hwnd)

                except Exception:
                    pass  # 忽略单个窗口的错误，继续枚举
                return True

            # 存储找到的所有匹配句柄
            matching_handles = []
            win32gui.EnumWindows(enum_windows_callback, matching_handles)

            if not matching_handles:
                return None

            # 如果找到多个匹配的窗口，记录日志
            if len(matching_handles) > 1:
                print(f"找到多个匹配的窗口: {len(matching_handles)} 个")
                for hwnd in matching_handles:
                    try:
                        window_class = win32gui.GetClassName(hwnd)
                        window_title = win32gui.GetWindowText(hwnd)
                        print(f"句柄: {hwnd}, 类名: {window_class}, 标题: {window_title}")
                    except Exception:
                        continue

            # 返回第一个匹配的句柄
            return matching_handles[0]

        except Exception as _:
            traceback.print_exc()
            return None

    def get_window_info(self) -> Optional[Dict[str, Any]]:
        """
        获取窗口的详细信息

        Returns:
            Optional[Dict[str, Any]]: 包含窗口信息的字典，如果获取失败则返回 None
        """
        if not self.is_valid:
            return None

        try:
            info = {
                "handle": self._handle,
                "class_name": win32gui.GetClassName(self._handle),
                "title": win32gui.GetWindowText(self._handle),
                "rect": win32gui.GetWindowRect(self._handle),
                "is_visible": win32gui.IsWindowVisible(self._handle),
                "is_enabled": win32gui.IsWindowEnabled(self._handle),
                "parent": win32gui.GetParent(self._handle)
            }
            return info
        except Exception as _:
            traceback.print_exc()
            return None

    def wait_for_valid(self, timeout: float = None) -> bool:
        """
        等待直到获取到有效的窗口句柄或超时

        Args:
            timeout: 超时时间（秒），None 则使用默认超时时间

        Returns:
            bool: 是否成功获取到有效句柄
        """
        timeout = timeout or self._timeout
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.refresh() is not None and self.is_valid:
                return True
            time.sleep(0.1)

        return False

    @staticmethod
    def find_window(class_name: str = None, title: str = None) -> Optional[int]:
        """
        静态方法：快速查找单个窗口句柄

        Args:
            class_name: 窗口类名，支持正则表达式
            title: 窗口标题，支持正则表达式

        Returns:
            Optional[int]: 窗口句柄，如果未找到则返回 None
        """
        finder = GetSpecifiedWindowHandle(class_name, title)
        return finder.handle

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出时清理资源"""
        self._handle = None
        
""" ==================================================================== 窗口句柄管理器 ====================================================================== """

class WindowHandleManager:
    """
    窗口句柄管理器 - 提供全面的窗口操作和信息获取功能

    功能列表:
    1. 窗口初始化和管理
        - 自动获取并维护窗口句柄
        - 窗口状态监控和自动恢复
        - 详细的错误处理和日志记录

    2. 窗口信息获取
        - 获取窗口完整信息
        - 获取窗口标题和类名
        - 获取窗口位置和大小
        - 支持进程ID、线程ID获取
        - 支持Z序查询
        - 支持窗口样式查询

    3. 窗口操作
        - 设置前台窗口
        - 修改窗口标题
        - 设置窗口位置和大小
        - 支持窗口最小化/恢复
        - 支持窗口位置调整

    使用示例:
    ```python
    try:
        # 创建窗口管理器实例
        window_mgr = WindowHandleManager(class_name="Notepad", title="无标题 - 记事本")

        # 获取窗口信息
        info = window_mgr.get_window_info()
        print(f"窗口信息: {info}")

        # 设置为前台窗口
        window_mgr.set_foreground()

        # 移动窗口位置
        window_mgr.set_window_pos(100, 100, 800, 600)

        # 修改窗口标题
        window_mgr.set_window_text("新标题")
    except WindowNotFoundError as e:
        print(f"找不到指定窗口: {e}")
    except WindowOperationError as e:
        print(f"窗口操作失败: {e}")
    ```
    """

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化窗口管理器

        Args:
            class_name: 窗口类名，支持正则表达式
            title: 窗口标题，支持正则表达式
            timeout: 查找超时时间（秒）

        Raises:
            WindowNotFoundError: 未找到指定窗口
            WindowOperationError: 窗口操作失败
        """
        self.__logger = logging.getLogger(self.__class__.__name__)
        self.__init_time = time.time()
        self.__class_name = class_name
        self.__title = title
        self.__timeout = timeout
        self.__handle = None
        self.__window_finder = None
        self.__last_operation = {}
        self.__cache = {}
        self.__cache_lock = threading.Lock()
        self.__monitor_thread = None
        self.__stop_monitor = threading.Event()

        # 初始化窗口查找器
        self.__initialize_window_finder()

        # 启动窗口监控
        self.__start_window_monitor()

        self.__logger.info(f"WindowHandleManager initialized with class_name='{class_name}', title='{title}'")

    def __initialize_window_finder(self):
        """初始化窗口查找器并获取窗口句柄"""
        try:
            self.__window_finder = GetSpecifiedWindowHandle(self.__class_name, self.__title, self.__timeout)
            if not self.__window_finder.wait_for_valid(self.__timeout):
                error_msg = f"无法找到指定窗口 (class_name='{self.__class_name}', title='{self.__title}')"
                self.__logger.error(error_msg)
                raise WindowNotFoundError(error_msg)

            self.__handle = self.__window_finder.handle
            self.__logger.info(f"Successfully found window handle: {self.__handle}")
        except Exception as e:
            error_msg = f"初始化窗口查找器失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def __start_window_monitor(self):
        """启动窗口监控线程"""
        def monitor_window():
            while not self.__stop_monitor.is_set():
                try:
                    if not self.is_valid_window():
                        self.__logger.warning("Window handle became invalid, attempting to refresh...")
                        self.__initialize_window_finder()
                except Exception as e:
                    self.__logger.error(f"Window monitor error: {str(e)}")
                time.sleep(1)  # 监控间隔

        self.__monitor_thread = threading.Thread(target=monitor_window, daemon=True)
        self.__monitor_thread.start()

    def __del__(self):
        """清理资源"""
        self.__stop_monitor.set()
        if self.__monitor_thread:
            self.__monitor_thread.join(timeout=1.0)

    @property
    def handle(self) -> int | None:
        """获取当前窗口句柄"""
        return self.__handle

    @property
    def is_alive(self) -> bool:
        """检查窗口管理器是否正常运行"""
        return self.__handle is not None and self.is_valid_window()

    def is_valid_window(self) -> bool:
        """
        检查窗口句柄是否有效

        Returns:
            bool: 窗口是否有效

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            return bool(win32gui.IsWindow(self.__handle))
        except Exception as e:
            error_msg = f"检查窗口有效性失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def get_window_info(self) -> Dict[str, Any]:
        """
        获取窗口的详细信息

        Returns:
            Dict[str, Any]: 窗口信息字典

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")

            thread_id, process_id = win32process.GetWindowThreadProcessId(self.__handle)
            info = {
                'handle': self.__handle,
                'title': self.get_window_text(),
                'class_name': self.__get_window_class(),
                'rect': win32gui.GetWindowRect(self.__handle),
                'is_visible': win32gui.IsWindowVisible(self.__handle),
                'is_enabled': win32gui.IsWindowEnabled(self.__handle),
                'is_unicode': bool(user32.IsWindowUnicode(self.__handle)),
                'parent': win32gui.GetParent(self.__handle),
                'process_id': process_id,
                'thread_id': thread_id,
                'style': win32api.GetWindowLong(self.__handle, win32con.GWL_STYLE),
                'ex_style': win32api.GetWindowLong(self.__handle, win32con.GWL_EXSTYLE),
                'last_active': self.__last_operation.get('time', 0),
                'z_order': self.__get_window_z_order()
            }
            return info
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"获取窗口信息失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def set_foreground(self) -> bool:
        """
        将窗口设置为前台窗口

        Returns:
            bool: 操作是否成功

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")

            # 记录操作
            self.__record_operation('set_foreground')

            # 如果窗口被最小化，先恢复
            if win32gui.IsIconic(self.__handle):
                win32gui.ShowWindow(self.__handle, win32con.SW_RESTORE)

            # 尝试设置前台窗口
            win32gui.SetForegroundWindow(self.__handle)

            # 验证操作是否成功
            success = win32gui.GetForegroundWindow() == self.__handle
            if not success:
                raise WindowOperationError("设置前台窗口失败")
            return success
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"设置前台窗口失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def get_window_text(self) -> str:
        """
        获取窗口标题文本

        Returns:
            str: 窗口标题

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")
            return win32gui.GetWindowText(self.__handle)
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"获取窗口标题失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def set_window_text(self, text: str) -> bool:
        """
        设置窗口标题文本

        Args:
            text: 新标题

        Returns:
            bool: 操作是否成功

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")

            win32gui.SetWindowText(self.__handle, text)
            return True
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"设置窗口标题失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def get_window_rect(self) -> Dict[str, Any]:
        """
        获取窗口位置和大小信息

        Returns:
            Dict[str, Any]: 包含窗口位置和大小信息的字典

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")

            # 获取窗口位置和大小
            left, top, right, bottom = win32gui.GetWindowRect(self.__handle)
            width = right - left
            height = bottom - top

            # 获取客户区位置和大小
            client_rect = win32gui.GetClientRect(self.__handle)
            client_left, client_top, client_right, client_bottom = client_rect
            client_width = client_right - client_left
            client_height = client_bottom - client_top

            # 计算窗口边框大小
            frame_width = width - client_width
            frame_height = height - client_height

            return {
                'position': (left, top, right, bottom),
                'size': (width, height),
                'client_rect': client_rect,
                'client_size': (client_width, client_height),
                'frame_size': (frame_width, frame_height),
                'center': (left + width // 2, top + height // 2),
                'is_minimized': bool(win32gui.IsIconic(self.__handle)),
                'is_maximized': bool(user32.IsZoomed(self.__handle))
            }
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"获取窗口位置信息失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def set_window_pos(self, x: int, y: int, width: int, height: int,
                       flags: int = win32con.SWP_SHOWWINDOW) -> bool:
        """
        设置窗口位置和大小

        Args:
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            flags: 窗口位置标志

        Returns:
            bool: 操作是否成功

        Raises:
            WindowOperationError: 窗口操作失败
        """
        try:
            if not self.is_valid_window():
                raise WindowOperationError("窗口句柄无效")

            win32gui.SetWindowPos(self.__handle, win32con.HWND_TOP, x, y, width, height, flags)
            return True
        except WindowOperationError:
            raise
        except Exception as e:
            error_msg = f"设置窗口位置失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def __get_window_class(self) -> str:
        """获取窗口类名"""
        try:
            return win32gui.GetClassName(self.__handle)
        except Exception as e:
            error_msg = f"获取窗口类名失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def __get_window_z_order(self) -> int:
        """获取窗口Z序"""
        try:
            order = 0
            current = self.__handle
            while current := win32gui.GetWindow(current, win32con.GW_HWNDPREV):
                order += 1
            return order
        except Exception as e:
            error_msg = f"获取窗口Z序失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowOperationError(error_msg)

    def __record_operation(self, operation: str) -> None:
        """记录窗口操作"""
        self.__last_operation = {
            'operation': operation,
            'time': time.time()
        }

    def __str__(self) -> str:
        """返回管理器状态的字符串表示"""
        return (f"WindowHandleManager(handle={self.__handle}, "
                f"class_name='{self.__class_name}', "
                f"title='{self.__title}', "
                f"uptime={time.time()-self.__init_time:.2f}s)")

    def __repr__(self) -> str:
        """返回管理器的详细表示"""
        return self.__str__()
    
    
    
""" ==================================================================== 窗口截图类 ====================================================================== """

    
class WindowCapture:
    """
    窗口截图类 - 提供高级的窗口截图功能

    功能特性：
    1. 中心点截图
        - 自动计算窗口中心点
        - 支持自定义截图大小
        - 智能边界处理

    2. 图像处理
        - 支持多种图像格式
        - 支持灰度图转换
        - 支持二值化处理
        - 支持图像缩放

    3. 文件管理
        - 自动创建保存目录
        - 文件名自动生成
        - 支持多种文件格式

    4. 错误处理
        - 详细的异常信息
        - 自动重试机制
        - 资源自动释放

    使用示例：
    ```python
    try:
        # 创建窗口截图实例
        window_capture = WindowCapture(
            class_name="Notepad",
            title="无标题 - 记事本"
        )

        # 执行截图
        image = window_capture.capture(
            width=800,
            height=600,
            save_path="screenshots",
            filename="screenshot",
            file_type="png"
        )

        # 处理截图
        print(f"截图大小: {image.shape}")
    except WindowCaptureError as e:
        print(f"截图失败: {e}")
    ```
    """

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化窗口截图器

        Args:
            class_name: 窗口类名，支持正则表达式
            title: 窗口标题，支持正则表达式
            timeout: 查找超时时间（秒）

        Raises:
            WindowCaptureError: 初始化失败
        """
        # 添加 matplotlib 相关的导入
        try:
            import matplotlib
            matplotlib.use('TkAgg')  # 使用 TkAgg 后端，更稳定
            import matplotlib.pyplot as plt
            self.__plt = plt
            self.__matplotlib_available = True
        except Exception as e:
            self.__logger.warning(f"matplotlib 初始化失败，预览功能将不可用: {str(e)}")
            self.__matplotlib_available = False

        # 现有的初始化代码
        self.__logger = logging.getLogger(self.__class__.__name__)
        self.__window_mgr = None
        self.__last_capture = None
        self.__cache = {}
        self.__cache_lock = threading.Lock()
        self.__init_time = time.time()
        self.__preview_figure = None

        try:
            self.__window_mgr = WindowHandleManager(class_name, title, timeout)
            self.__logger.info(f"WindowCapture initialized with class_name='{class_name}', title='{title}'")
        except Exception as e:
            error_msg = f"初始化窗口截图器失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowCaptureError(error_msg)

    def capture(self, width: int, height: int, save_path: str = None,
                filename: str = None, file_type: str = "png") -> np.ndarray:
        """
        执行窗口截图

        Args:
            width: 截图宽度
            height: 截图高度
            save_path: 保存路径（可选）
            filename: 文件名（可选）
            file_type: 文件类型（默认为png）

        Returns:
            np.ndarray: 截图图像数据

        Raises:
            WindowCaptureError: 截图失败
        """
        try:
            # 验证参数
            if width <= 0 or height <= 0:
                raise ValueError("截图尺寸必须大于0")

            # 获取窗口位置信息
            window_rect = self.__window_mgr.get_window_rect()
            if not window_rect:
                raise WindowCaptureError("无法获取窗口位置信息")

            # 获取窗口的客户区大小
            client_width, client_height = window_rect['client_size']

            # 计算截图区域（相对于客户区的中心点）
            center_x = client_width // 2
            center_y = client_height // 2

            # 计算截图区域的边界（相对于客户区）
            left = max(0, center_x - width // 2)
            top = max(0, center_y - height // 2)
            right = min(client_width, left + width)
            bottom = min(client_height, top + height)

            # 如果截图区域超出客户区，调整位置保持在客户区内
            if right > client_width:
                left = max(0, client_width - width)
                right = client_width
            if bottom > client_height:
                top = max(0, client_height - height)
                bottom = client_height

            # 计算最终的截图区域
            region = [left, top, right, bottom]

            # 准备保存路径
            save_file_path = None
            if save_path:
                if not filename:
                    filename = f"screenshot_{int(time.time())}"
                if not file_type.startswith('.'):
                    file_type = f".{file_type}"
                os.makedirs(save_path, exist_ok=True)
                save_file_path = os.path.join(save_path, f"{filename}{file_type}")

            # 执行截图
            result = capture_window_handle(
                hwnd=self.__window_mgr.handle,
                region=region,
                save=bool(save_file_path),
                save_path=save_file_path
            )

            if result["status"] != "success":
                raise WindowCaptureError(f"截图失败: {result['message']}")

            # 更新缓存
            self.__last_capture = {
                'time': time.time(),
                'image': result['image'],
                'path': result.get('path', None),
                'region': region,  # 保存截图区域信息
                'window_size': (client_width, client_height)  # 保存窗口大小信息
            }

            return result['image']

        except Exception as e:
            error_msg = f"执行截图失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            raise WindowCaptureError(error_msg)

    def capture_async(self, width: int, height: int, save_path: str = None,
                      filename: str = None, file_type: str = "png") -> asyncio.Future:
        """
        异步执行窗口截图

        Args:
            width: 截图宽度
            height: 截图高度
            save_path: 保存路径（可选）
            filename: 文件名（可选）
            file_type: 文件类型（默认为png）

        Returns:
            asyncio.Future: 异步任务对象

        Note:
            使用示例:
            ```python
            async def main():
                capture_task = window_capture.capture_async(800, 600)
                image = await capture_task
                print(f"截图大小: {image.shape}")
            ```
        """
        async def _capture():
            return self.capture(width, height, save_path, filename, file_type)

        return asyncio.create_task(_capture())

    @property
    def last_capture(self) -> Optional[Dict[str, Any]]:
        """获取最后一次截图的信息"""
        return self.__last_capture

    @property
    def window_handle(self) -> Optional[int]:
        """获取当前窗口句柄"""
        return self.__window_mgr.handle if self.__window_mgr else None

    def is_valid(self) -> bool:
        """检查窗口截图器是否有效"""
        return self.__window_mgr is not None and self.__window_mgr.is_alive

    def __str__(self) -> str:
        """返回截图器状态的字符串表示"""
        return (f"WindowCapture(window_handle={self.window_handle}, "
                f"last_capture_time={self.__last_capture['time'] if self.__last_capture else None}, "
                f"uptime={time.time()-self.__init_time:.2f}s)")

    def __repr__(self) -> str:
        """返回截图器的详细表示"""
        return self.__str__()

    def preview_capture(self, width: int, height: int, title: str = None,
                        dpi: int = 100, window_size: tuple = None,
                        block: bool = True, timeout: float = None) -> bool:
        """
        预览窗口截图

        Args:
            width: 截图宽度
            height: 截图高度
            title: 预览窗口标题（可选）
            dpi: 显示分辨率（默认100）
            window_size: 预览窗口大小 (宽, 高)（可选）
            block: 是否阻塞等待窗口关闭（默认True）
            timeout: 预览超时时间（秒）（可选）

        Returns:
            bool: 预览是否成功

        Raises:
            WindowCaptureError: 预览失败
        """
        if not self.__matplotlib_available:
            error_msg = "matplotlib 未正确初始化，无法使用预览功能"
            self.__logger.error(error_msg)
            raise WindowCaptureError(error_msg)

        try:
            # 关闭可能存在的旧预览窗口
            self.__close_preview()

            # 捕获新的截图
            image = self.capture(width, height)
            if image is None:
                raise WindowCaptureError("获取截图失败")

            # 创建新的预览窗口
            self.__preview_figure = self.__plt.figure(
                figsize =window_size or (width / 100, height / 100),
                dpi=dpi
            )

            # 设置窗口标题
            if title is None:
                title = f"Screenshot Preview - {self.__window_mgr.get_window_text()}"
            self.__preview_figure.canvas.manager.set_window_title(title)

            # 显示图像
            ax = self.__preview_figure.add_subplot(111)
            ax.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))  # 转换颜色空间
            ax.axis('off')  # 隐藏坐标轴

            # 优化显示
            self.__preview_figure.tight_layout(pad=0)

            # 设置关闭事件处理
            def on_close(event):
                self.__preview_figure = None
            self.__preview_figure.canvas.mpl_connect('close_event', on_close)

            # 显示窗口
            self.__plt.show(block=block)

            # 如果设置了超时，启动超时处理
            if timeout is not None and timeout > 0:
                def close_after_timeout():
                    time.sleep(timeout)
                    self.__close_preview()
                threading.Thread(target=close_after_timeout, daemon=True).start()

            return True

        except Exception as e:
            error_msg = f"预览截图失败: {str(e)}\n{traceback.format_exc()}"
            self.__logger.error(error_msg)
            self.__close_preview()  # 清理资源
            raise WindowCaptureError(error_msg)

    def __close_preview(self):
        """关闭预览窗口并清理资源"""
        try:
            if self.__preview_figure is not None:
                self.__plt.close(self.__preview_figure)
                self.__preview_figure = None
        except Exception as e:
            self.__logger.warning(f"关闭预览窗口失败: {str(e)}")

    def __del__(self):
        """清理资源"""
        self.__close_preview()
        
        
""" ====================================================================== 窗口绘制工具类 ================================================================== """

class DrawUtils:
    """
    绘制工具类，用于在目标窗口上创建透明覆盖层并绘制文本和边框。

    主要功能：
    1. 创建透明覆盖窗口：
       - 覆盖窗口位于目标窗口之上，但不总是置顶。
       - 背景透明，允许透过覆盖层看到目标窗口内容。
       - 覆盖窗口可以穿透鼠标事件，不影响目标窗口的交互。
       - 覆盖窗口的大小和位置与目标窗口保持一致。
    2. 在覆盖窗口上绘制文本和边框：
       - 使用双缓冲技术，避免绘制过程中的闪烁。
       - 绘制的文本和边框相对于目标窗口坐标系。
       - 可以自定义文本内容、字体大小、颜色，以及边框颜色和粗细。
    3. 处理Windows消息循环：
       - 包含一个简单的消息循环处理函数，确保覆盖窗口能够响应系统消息。
    4. 管理覆盖窗口的生命周期：
       - 提供创建、清除和销毁覆盖窗口的方法。

    性能优化：
    - 缓存目标窗口信息（位置、大小），减少重复计算。
    - 使用高效的GDI绘图函数（MoveToEx、LineTo、ExtTextOut）。
    - 减少GDI对象创建次数，缓存字体对象。
    - 使用双缓冲技术，避免闪烁。
    - 统一资源清理，避免内存泄漏。

    使用方法：
    1. 创建 DrawUtils 实例，传入目标窗口句柄。
    2. （可选）调用 create_overlay_window 创建覆盖窗口。
       如果不手动创建，draw_text_with_box_overlay 会自动创建。
    3. 调用 draw_text_with_box_overlay 方法绘制文本和边框。
    4. （可选）调用 clear_canvas 清除画布内容。
    5. （可选）调用 process_messages 处理窗口消息。
    6. （可选）调用 wait 等待一段时间并处理消息。
    7. 调用 destroy_overlay 销毁覆盖窗口。
    """

    def __init__(self, target_hwnd):
        """
        初始化 DrawUtils 类。

        Args:
            target_hwnd: 目标窗口句柄，用于确定覆盖窗口的位置和大小。
        """
        self.target_hwnd = target_hwnd  # 目标窗口句柄
        self.overlay_hwnd = None  # 覆盖窗口句柄，初始为 None
        self.target_rect = None  # 目标窗口客户区矩形
        self.target_x = 0  # 目标窗口左上角 x 坐标（屏幕坐标系）
        self.target_y = 0  # 目标窗口左上角 y 坐标（屏幕坐标系）
        self.target_width = 0  # 目标窗口宽度
        self.target_height = 0  # 目标窗口高度
        self.cached_font = None  # 缓存的字体对象
        self.black_brush = win32gui.CreateSolidBrush(win32api.RGB(0, 0, 0))  # 用于清除画布的黑色画刷

    def generate_dark_color(self):
        """
        生成一个深色的随机颜色。

        Returns:
            tuple: RGB 格式的颜色元组 (R, G, B)，每个分量范围 0-128。
        """
        color = (
            random.randint(0, 128),  # R 分量
            random.randint(0, 128),  # G 分量
            random.randint(0, 128),  # B 分量
        )
        return color

    def create_overlay_window(self):
        """
        创建透明覆盖窗口作为绘制画布。

        特性：
        1. 窗口置于目标窗口之上，但不会始终置顶。
        2. 背景透明，使用黑色作为透明色键。
        3. 可以穿透鼠标事件。
        4. 大小和位置与目标窗口一致。

        Returns:
            int: 创建的覆盖窗口句柄，失败返回 None。
        """
        try:
            if not win32gui.IsWindow(self.target_hwnd):
                raise ValueError("无效的目标窗口句柄")

            # 使用 GetClientRect 获取客户区的大小, 并缓存
            self.target_rect = win32gui.GetClientRect(self.target_hwnd)
            self.target_x, self.target_y = win32gui.ClientToScreen(self.target_hwnd, (0, 0))
            self.target_width = self.target_rect[2] - self.target_rect[0]
            self.target_height = self.target_rect[3] - self.target_rect[1]

            # 创建覆盖窗口，设置窗口样式
            overlay_hwnd = win32gui.CreateWindowEx(
                win32con.WS_EX_TRANSPARENT  # 窗口透明
                | win32con.WS_EX_LAYERED  # 使用分层窗口
                | win32con.WS_EX_NOACTIVATE,  # 窗口不激活
                "STATIC",  # 使用静态窗口类
                "",  # 窗口标题为空
                win32con.WS_POPUP | win32con.WS_VISIBLE,  # 无边框且可见
                self.target_x,  # 窗口位置 x
                self.target_y,  # 窗口位置 y
                self.target_width,  # 窗口宽度
                self.target_height,  # 窗口高度
                self.target_hwnd,  # 设置目标窗口为父窗口
                0,  # 菜单句柄
                0,  # 应用程序实例句柄
                None,  # 窗口创建数据
            )

            if not overlay_hwnd:
                raise WindowCaptureError("创建覆盖窗口失败")

            # 设置窗口透明属性
            win32gui.SetLayeredWindowAttributes(
                overlay_hwnd,
                win32api.RGB(0, 0, 0),  # 透明色改为黑色
                255,  # 不透明度，应保持为 255
                win32con.LWA_COLORKEY,  # 使用颜色键透明
            )

            # 设置窗口位置，使用 HWND_TOP 而不是 HWND_BOTTOM
            win32gui.SetWindowPos(
                overlay_hwnd,
                win32con.HWND_TOP,  # 置于普通窗口之上
                self.target_x,
                self.target_y,
                self.target_width,
                self.target_height,
                win32con.SWP_SHOWWINDOW | win32con.SWP_NOACTIVATE,  # 显示窗口但不激活
            )

            # 强制立即更新窗口
            win32gui.UpdateWindow(overlay_hwnd)
            self.overlay_hwnd = overlay_hwnd  # 将句柄赋值给属性
            return overlay_hwnd

        except Exception as e:
            traceback.print_exc()
            print(f"创建覆盖窗口失败: {str(e)}")
            return None
    
    def update_target_window_info(self):
        """
        更新目标窗口信息（位置和大小）。

        Returns:
            bool: 更新是否成功。如果目标窗口无效，返回 False。
        """
        if not win32gui.IsWindow(self.target_hwnd):
            return False

        self.target_rect = win32gui.GetClientRect(self.target_hwnd)
        self.target_x, self.target_y = win32gui.ClientToScreen(self.target_hwnd, (0, 0))
        self.target_width = self.target_rect[2] - self.target_rect[0]
        self.target_height = self.target_rect[3] - self.target_rect[1]
        return True

    def draw_content(
        self,
        overlay_hwnd,
        coords,
        text,
        box_color,
        box_thickness,
        text_color,
        font_size,
    ):
        """
        在覆盖窗口上绘制内容（文本和边框）。
        使用双缓冲技术避免闪烁。

        Args:
            overlay_hwnd: 覆盖窗口句柄。
            coords: 边框坐标元组 (x1, x2, y1, y2)，相对于目标窗口客户区。
            text: 要绘制的文本。
            box_color: 边框颜色 (R, G, B)。
            box_thickness: 边框粗细。
            text_color: 文本颜色 (R, G, B)。
            font_size: 字体大小。

        Returns:
            bool: 绘制是否成功。
        """
        try:
            # 验证窗口句柄的有效性
            if not win32gui.IsWindow(overlay_hwnd) or not win32gui.IsWindow(
                self.target_hwnd
            ):
                return False

            # 更新目标窗口信息
            if not self.update_target_window_info():
                return False

            # 计算绘制坐标（相对于屏幕）
            x1, x2, y1, y2 = map(int, coords)
            x1 += self.target_x  # 转换为屏幕坐标
            x2 += self.target_x
            y1 += self.target_y
            y2 += self.target_y

            # 获取设备上下文
            hdc = win32gui.GetDC(overlay_hwnd)
            if not hdc:
                raise WindowCaptureError("获取覆盖窗口设备上下文失败")

            # 创建内存 DC 用于双缓冲绘制
            memdc = win32gui.CreateCompatibleDC(hdc)
            bitmap = win32gui.CreateCompatibleBitmap(hdc, self.target_width, self.target_height)
            old_bitmap = win32gui.SelectObject(memdc, bitmap)

            # 使用预先创建的黑色画刷填充背景，用于清除
            win32gui.FillRect(memdc, (0, 0, self.target_width, self.target_height), self.black_brush)

            # 创建并设置画笔用于绘制边框
            pen = win32gui.CreatePen(
                win32con.PS_SOLID, box_thickness, win32api.RGB(*box_color)
            )
            old_pen = win32gui.SelectObject(memdc, pen)

            # 绘制矩形边框 (使用 GDI 绘制，优化性能)
            # 注意：这里绘制的坐标是相对于覆盖窗口的，而不是相对于屏幕的
            win32gui.MoveToEx(memdc, x1 - self.target_x, y1 - self.target_y)
            win32gui.LineTo(memdc, x2 - self.target_x, y1 - self.target_y)
            win32gui.LineTo(memdc, x2 - self.target_x, y2 - self.target_y)
            win32gui.LineTo(memdc, x1 - self.target_x, y2 - self.target_y)
            win32gui.LineTo(memdc, x1 - self.target_x, y1 - self.target_y)

            # 设置文本颜色和背景模式
            win32gui.SetTextColor(memdc, win32api.RGB(*text_color))
            win32gui.SetBkMode(memdc, win32con.TRANSPARENT)  # 设置透明背景模式

            # 创建或使用缓存的字体对象
            if self.cached_font is None:
                lf = win32gui.LOGFONT()
                lf.lfFaceName = "Consolas"  # 使用 Consolas 字体
                lf.lfHeight = font_size  # 设置字体大小
                lf.lfWeight = win32con.FW_NORMAL  # 设置字体粗细
                self.cached_font = win32gui.CreateFontIndirect(lf)
            old_font = win32gui.SelectObject(memdc, self.cached_font)

            # 计算文本尺寸以实现居中对齐
            text_size = win32gui.GetTextExtentPoint32(memdc, text)
            text_width = text_size[0]

            # 计算文本位置（在边框上方居中）
            text_x = x1 - self.target_x + (x2 - x1 - text_width) // 2
            text_y = y1 - self.target_y - font_size - 2

            # 绘制文本 (优化绘制文本)
            win32gui.ExtTextOut(memdc, text_x, text_y, win32con.ETO_OPAQUE, None, text, None)

            # 将内存 DC 中的内容复制到窗口
            win32gui.BitBlt(
                hdc, 0, 0, self.target_width, self.target_height, memdc, 0, 0, win32con.SRCCOPY
            )

            # 清理资源 (只删除一次对象)
            win32gui.SelectObject(memdc, old_bitmap)  # 恢复旧位图
            win32gui.SelectObject(memdc, old_pen)  # 恢复旧画笔
            win32gui.SelectObject(memdc, old_font)  # 恢复旧字体
            win32gui.DeleteObject(bitmap)  # 删除位图
            win32gui.DeleteObject(pen)  # 删除画笔
            # 注意：不需要删除 self.cached_font，因为它是缓存的
            win32gui.DeleteDC(memdc)  # 删除内存 DC
            win32gui.ReleaseDC(overlay_hwnd, hdc)  # 释放设备上下文

            return True

        except Exception as e:
            traceback.print_exc()
            print(f"绘制内容失败: {str(e)}")
            return False

    def draw_text_with_box_overlay(
        self,
        coords,
        text,
        box_color=None,
        box_thickness=2,
        text_color=None,
        font_size=14,
        overlay_hwnd=None,
    ):
        """
        在目标窗口上绘制带有边框的文本。

        这是一个高级接口，整合了创建窗口、清除画布和绘制内容的功能。

        Args:
            coords: 边框坐标元组 (x1, x2, y1, y2)，相对于目标窗口客户区。
            text: 要绘制的文本。
            box_color: 边框颜色 (R, G, B)，默认 None 将使用随机深色。
            box_thickness: 边框粗细，默认 2 像素。
            text_color: 文本颜色 (R, G, B)，默认 None 将使用随机深色。
            font_size: 字体大小，默认 14。
            overlay_hwnd: 现有的覆盖窗口句柄，默认 None 将创建新窗口。

        Returns:
            tuple: (是否成功, 覆盖窗口句柄)。
        """
        try:
            # 验证参数
            if not isinstance(coords, (tuple, list)) or len(coords) != 4:
                raise ValueError("coords 必须是包含 4 个元素的元组或列表 (x1, x2, y1, y2)")

            if not isinstance(text, str):
                raise TypeError("text 必须是字符串类型")

            # 如果没有有效的覆盖窗口，创建新窗口
            if not self.overlay_hwnd or not win32gui.IsWindow(self.overlay_hwnd):
                overlay_hwnd = self.create_overlay_window()
                print(f"创建覆盖窗口: {overlay_hwnd}")
                if not overlay_hwnd:
                    return False, None

            # 清空画布
            self.clear_canvas(self.overlay_hwnd)

            # 如果 box_color 或 text_color 为 None，则生成随机深色
            if box_color is None:
                box_color = self.generate_dark_color()
            if text_color is None:
                text_color = self.generate_dark_color()

            # 绘制内容
            if self.draw_content(
                self.overlay_hwnd,
                coords,
                text,
                box_color,
                box_thickness,
                text_color,
                font_size,
            ):
                return True, self.overlay_hwnd
            return False, None

        except Exception as e:
            traceback.print_exc()
            return False, None

    def clear_canvas(self, overlay_hwnd):
        """
        清除覆盖窗口的画布内容。

        Args:
            overlay_hwnd: 覆盖窗口句柄。
        """
        try:
            if not win32gui.IsWindow(overlay_hwnd) or not win32gui.IsWindow(
                self.target_hwnd
            ):
                return

            # 更新目标窗口信息
            if not self.update_target_window_info():
                return

            hdc = win32gui.GetDC(overlay_hwnd)
            if not hdc:
                raise WindowCaptureError("获取覆盖窗口设备上下文失败")

            memdc = win32gui.CreateCompatibleDC(hdc)
            bitmap = win32gui.CreateCompatibleBitmap(hdc, self.target_width, self.target_height)
            old_bitmap = win32gui.SelectObject(memdc, bitmap)

            # 使用预先创建的黑色画刷清除内容
            win32gui.FillRect(memdc, (0, 0, self.target_width, self.target_height), self.black_brush)

            # 将清除后的内容复制到窗口
            win32gui.BitBlt(
                hdc, 0, 0, self.target_width, self.target_height, memdc, 0, 0, win32con.SRCCOPY
            )

            # 清理资源
            win32gui.SelectObject(memdc, old_bitmap)  # 恢复旧位图
            win32gui.DeleteObject(bitmap)  # 删除位图
            win32gui.DeleteDC(memdc)  # 删除内存 DC
            win32gui.ReleaseDC(overlay_hwnd, hdc)  # 释放设备上下文

        except Exception as e:
            traceback.print_exc()
            print(f"清除画布失败: {str(e)}")

    def destroy_overlay(self, overlay_hwnd):
        """
        销毁覆盖窗口，释放资源。

        Args:
            overlay_hwnd: 要销毁的覆盖窗口句柄。
        """
        if overlay_hwnd:
            try:
                win32gui.DestroyWindow(overlay_hwnd)
                self.overlay_hwnd = None  # 将 overlay_hwnd 设置为 None
            except Exception:
                pass

    def process_messages(self):
        """
        处理 Windows 消息循环。

        确保窗口能够正常响应系统消息，避免出现"未响应"状态。
        """
        try:
            message = wintypes.MSG()
            while user32.PeekMessageA(byref(message), None, 0, 0, win32con.PM_REMOVE):
                user32.TranslateMessage(byref(message))
                user32.DispatchMessageA(byref(message))
        except Exception:
            pass

    def wait(self, duration=0.1):
        """
        等待一段时间并处理消息。

        Args:
            duration: 等待时间，单位秒，默认 0.1 秒。
        """
        start_time = time.time()
        while time.time() - start_time < duration:
            if not win32gui.IsWindow(self.target_hwnd):
                break
            self.process_messages()
            time.sleep(0.001)