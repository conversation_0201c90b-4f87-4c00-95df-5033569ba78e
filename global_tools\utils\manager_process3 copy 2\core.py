import os
import random
import sys
import threading
import time
import logging
import traceback
import multiprocessing
import queue  # 添加queue模块导入，用于处理队列异常
from multiprocessing import Pool, Manager, Process, JoinableQueue, Lock as ProcessLock, Event  # 重命名 Lock 避免冲突
from multiprocessing.managers import BaseManager, DictProxy, ListProxy, AcquirerProxy  # 确保 AcquirerProxy 被导入
from queue import Empty  # 导入 Empty 异常
from typing import Any, Dict, Callable, Iterable, List, Optional, Tuple, Union, cast, Set, TypeVar, Generic, Type  # 添加 Type 的导入
import functools
from queue import Queue  # 添加Queue导入
from global_tools.utils import Logger, LogLevel, ClassInstanceManager

# 日志输出
logger: Logger = ClassInstanceManager.get_instance(key="Logger")


def _proxy_callback_for_mp(key: str, value: Any, lock: AcquirerProxy, callback_queue: Queue):
    """
    代理回调函数，在工作进程中被调用，将数据变化通知转发到主进程队列。
    由于实例方法无法被pickle序列化传递给多进程Manager，必须使用独立的顶层函数。

    Args:
        key: 发生变化的键名
        value: 变化后的新值
        lock: 共享锁对象
        callback_queue: 回调队列，用于将消息传递回主进程
    """
    try:
        # 在工作进程中获取值的副本
        if hasattr(value, '_getvalue'):
            # 如果是Manager代理对象，获取其值的副本
            copied_value = value._getvalue()
        else:
            # 否则直接使用值
            copied_value = value

        # 将数据变化放入主进程队列
        callback_queue.put((key, copied_value, time.time()))
        logger.debug(f"已将键 '{key}' 的数据变化放入主进程队列")
    except Exception as e:
        logger.error(f"代理回调函数处理键 '{key}' 时出错: {e}", exc_info=True)



def _top_level_worker_loop(
    worker_id,           # 工作进程编号
    queue,               # 任务队列 (JoinableQueue)
    # shared_data_manager: SharedDataManager,  # 不再直接传递实例
    shared_data_manager_proxy,  # 替换: 共享数据管理器代理对象
    stop_flag,           # 停止事件 (Event)
    status_dict,         # 进程状态字典代理 (Manager.dict)
    callback,            # 用户定义的回调函数
    custom_init_args,    # 传递给回调的位置参数元组
    extra_kwargs,        # 传递给回调的关键字参数字典
):
    """
    工作进程的主循环 (顶层函数) - 已更新以使用 SharedDataManager 代理

    Args:
        worker_id: 工作进程的标识符/编号
        queue: 任务队列 (JoinableQueue)
        shared_data_manager_proxy: 共享数据管理器代理对象
        stop_flag: 停止事件 (Event)
        status_dict: 进程状态字典代理 (Manager.dict) - 注意：这个仍然来自外部 Manager
        callback: 用户定义的回调函数, 签名应为: callback(shared_data_manager_proxy, item, *custom_args, **kwargs)
        custom_init_args: 传递给回调的位置参数元组
        extra_kwargs: 传递给回调的关键字参数字典
    """
    pid = os.getpid()
    process_name = f"Worker-{worker_id}(PID:{pid})"
    logger.info(f"{process_name}: 开始运行...")

    # 防止字典访问错误
    try:
        status_dict[pid] = "running"  # 更新自己的状态
    except Exception as e:
        logger.error(f"{process_name}: 无法更新状态字典: {e}")
        # 继续执行，即使状态更新失败

    # 获取锁 (通过代理获取锁的代理)
    lock_proxy: Optional[AcquirerProxy] = None
    try:
        # 注意：现在调用代理的方法
        lock_proxy = shared_data_manager_proxy.get_lock()
        if lock_proxy is None:
            raise RuntimeError("获取到的锁代理对象为 None")
        logger.debug(f"{process_name}: 已获取共享数据锁代理")
    except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
        logger.error(f"{process_name}: 与 Manager 的连接已中断: {conn_err}")
        try:
            status_dict[pid] = "error_manager_disconnected"
        except Exception:
            pass
        return  # Manager连接已断开，无法继续
    except Exception as e:
        logger.error(f"{process_name}: 无法从 SharedDataManager 代理获取锁: {e}\n{traceback.format_exc()}")
        try:
            status_dict[pid] = "error_getting_lock"
        except Exception:
            pass
        return  # 没有锁无法安全工作

    # 为了更快地响应停止信号，设置较短的队列获取超时时间
    queue_timeout = 0.1  # 秒
    task_count = 0  # 记录本进程处理的任务数
    empty_queue_count = 0  # 记录连续获取到空队列的次数
    max_empty_count = 5  # 当连续5次获取到空队列时，认为所有任务已处理完毕，退出循环

    # 启动时检查停止标志
    if stop_flag.is_set():
        logger.info(f"{process_name}: 启动时检测到停止信号，不开始处理")
        try:
            status_dict[pid] = "stopped_before_start"
        except Exception:
            pass
        return

    while not stop_flag.is_set():
        task_element = None  # 重置任务元素
        try:
            # 1. 从队列获取任务
            # logger.debug(f"{process_name}: 等待任务 (超时 {queue_timeout}s)...") # Debug 日志
            try:
                task_element = queue.get(block=True, timeout=queue_timeout)
                # 重置空队列计数
                empty_queue_count = 0
                # logger.debug(f"{process_name}: 获取到任务: {task_element}") # Debug 日志
            except Empty:  # 直接使用导入的Empty异常，而不是queue.Empty
                # 队列暂时为空，不是错误，继续循环检查停止标志
                empty_queue_count += 1
                if empty_queue_count >= max_empty_count:
                    logger.info(f"{process_name}: 连续 {max_empty_count} 次获取到空队列，认为所有任务已处理完毕，退出循环")
                    try:
                        status_dict[pid] = "completed_all_tasks"
                    except Exception:
                        pass
                    break  # 退出主循环
                # logger.debug(f"{process_name}: 队列为空 ({empty_queue_count}/{max_empty_count})，继续检查停止标志") # Debug 日志
                continue
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as e:
                # Manager 可能已关闭，或者队列已损坏
                logger.error(f"{process_name}: 从队列获取任务时连接错误 (可能Manager已关闭): {str(e)}")
                try:
                    status_dict[pid] = "error_queue_get"
                except Exception:
                    pass
                break
            except Exception as e:
                # 获取任务时发生其他意外错误
                logger.error(f"{process_name}: 从队列获取任务失败: {str(e)}\n{traceback.format_exc()}")
                try:
                    status_dict[pid] = "error_queue_get"
                except Exception:
                    pass
                break

            # 2. 再次检查停止标志 (获取任务后，处理任务前)
            # 如果在等待任务时收到了停止信号，则不处理刚获取的任务
            if stop_flag.is_set():
                logger.info(f"{process_name}: 收到停止信号，将任务放回队列: {task_element}")
                try:
                    # 尝试将任务放回队列，以便其他进程（如果还在运行）或下次运行处理
                    queue.put(task_element)
                    # 注意：放回后，需要调用 task_done() 来平衡 get() 操作
                    # 否则 JoinableQueue 的计数会出错
                    queue.task_done()
                except (EOFError, BrokenPipeError, ConnectionRefusedError) as e:
                    logger.warning(f"{process_name}: 连接已断开，无法将任务放回队列: {e}")
                    try:
                        queue.task_done()  # 仍然尝试标记任务完成
                    except Exception:
                        pass
                except Exception as put_err:
                    logger.error(f"{process_name}: 将任务放回队列失败: {put_err}")
                    # 即使放回失败，也需要标记任务完成（从本进程角度）
                    try:
                        queue.task_done()  # 仍然需要调用
                    except Exception:
                        pass
                break

            # 3. 处理任务
            task_start_time = time.time()
            try:
                status_dict[pid] = f"processing: {task_element}"
            except Exception:
                pass
            logger.info(f"{process_name}: 开始处理任务: {task_element}")
            try:
                # 调用用户提供的回调函数，传递 SharedDataManager 代理实例
                # 回调签名: callback(shared_data_manager_proxy, item, *custom_args, **kwargs)
                # 注意：回调函数内部现在通过代理对象调用方法，例如:
                # lock = shared_data_manager_proxy.get_lock()
                # with lock:
                #     shared_data_manager_proxy.add_value(...)
                callback(
                    shared_data_manager_proxy, task_element, *custom_init_args, **extra_kwargs
                )
                task_duration = time.time() - task_start_time
                logger.info(f"{process_name}: 完成处理任务: {task_element} (耗时: {task_duration:.4f}s)")
                task_count += 1
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                task_duration = time.time() - task_start_time
                logger.error(f"{process_name}: Manager连接中断，无法继续处理任务: {conn_err}")
                try:
                    status_dict[pid] = "error_manager_disconnected"
                except Exception:
                    pass
                try:
                    queue.task_done()
                except Exception:
                    pass
                return  # 连接断开，无法继续执行
            except Exception as e:
                # 回调函数执行出错
                task_duration = time.time() - task_start_time
                logger.error(f"{process_name}: 处理任务 {task_element} 时回调函数出错 (耗时: {task_duration:.4f}s): {str(e)}\n{traceback.format_exc()}")
                # 尝试通过代理记录错误
                try:
                    # 注意：这里也需要获取锁代理
                    with lock_proxy:
                        error_detail = {
                            "item": task_element,
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                            "pid": pid,
                            "worker_id": worker_id,
                            "timestamp": time.time()
                        }
                        shared_data_manager_proxy.record_error(error_detail)
                except Exception as record_err:
                    logger.error(f"{process_name}: 记录错误信息时失败: {record_err}")
                try:
                    status_dict[pid] = "callback_error"
                except Exception:
                    pass

            finally:
                # 4. 标记任务完成 (无论成功还是失败)
                # logger.debug(f"{process_name}: 标记任务完成: {task_element}") # Debug 日志
                try:
                    queue.task_done()
                except (EOFError, BrokenPipeError, ConnectionRefusedError):
                    # 连接已断开，无法标记任务完成，但这不影响任务的实际完成情况
                    logger.warning(f"{process_name}: 连接已断开，无法标记任务完成")
                    break  # 连接断开，退出循环
                except Exception as e:
                    logger.error(f"{process_name}: 标记任务完成时出错: {e}")
                    # 继续执行，避免因标记失败而影响其他任务

                try:
                    status_dict[pid] = "running"  # 处理完一个任务后，状态改回 running
                except Exception:
                    pass

                # 5. 再次检查停止标志 (处理完一个任务后)
                if stop_flag.is_set():
                    logger.info(f"{process_name}: 在任务处理后检测到停止信号，准备退出")
                    break

        except KeyboardInterrupt:
            # 允许 Ctrl+C 中断工作进程
            logger.warning(f"{process_name}: 收到 KeyboardInterrupt，准备退出...")
            try:
                status_dict[pid] = "interrupted"
            except Exception:
                pass
            try:
                stop_flag.set()  # 尝试通知其他进程也停止
            except Exception as e:
                logger.error(f"{process_name}: 设置停止标志时出错: {e}")
            break
        except Exception as loop_err:
            # 捕获工作循环本身可能出现的其他异常
            logger.error(f"{process_name}: 工作循环中发生意外严重错误: {str(loop_err)}\n{traceback.format_exc()}")
            try:
                status_dict[pid] = "loop_error"
            except Exception:
                pass
            # 尝试记录循环错误
            try:
                with lock_proxy:
                    error_detail = {
                        "item": task_element,  # 可能为 None
                        "error": f"Loop error: {str(loop_err)}",
                        "traceback": traceback.format_exc(),
                        "pid": pid,
                        "worker_id": worker_id,
                        "timestamp": time.time()
                    }
                    shared_data_manager_proxy.record_error(error_detail)
            except Exception as record_err:
                logger.error(f"{process_name}: 记录循环错误时失败: {record_err}")

            break  # 发生严重循环错误，退出

    # --- 循环结束后的清理 ---
    try:
        final_status = status_dict.get(pid, "unknown")
        logger.info(f"{process_name}: 退出工作循环。处理任务总数: {task_count}。最终状态: {final_status}")

        # 根据退出原因更新最终状态
        if stop_flag.is_set() and final_status not in ["error", "loop_error", "callback_error", "interrupted",
                                                       "error_queue_get", "error_getting_lock", "error_manager_disconnected", "stopped_before_start"]:
            status_dict[pid] = "stopped_signal"  # 因信号停止
        elif final_status == "running":  # 如果是正常处理完队列所有任务退出
            status_dict[pid] = "completed"  # 正常完成
    except Exception as e:
        logger.error(f"{process_name}: 更新最终状态时出错: {e}")

    # 显式断开与Manager的连接
    shared_data_manager_proxy = None
    lock_proxy = None

    logger.info(f"{process_name}: 工作进程完全退出")