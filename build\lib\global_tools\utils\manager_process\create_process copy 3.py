#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import random
import sys
import threading
import time
import logging
import traceback
import multiprocessing
import queue  # 添加queue模块导入，用于处理队列异常
from multiprocessing import Pool, Manager, Process, JoinableQueue, Lock as ProcessLock, Event  # 重命名 Lock 避免冲突
from multiprocessing.managers import BaseManager, DictProxy, ListProxy, AcquirerProxy  # 确保 AcquirerProxy 被导入
from queue import Empty  # 导入 Empty 异常
from typing import Any, Dict, Callable, Iterable, List, Optional, Tuple, Union, cast, Set, TypeVar, Generic
import functools
from queue import Queue  # 添加Queue导入

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    force=True,
)


logger = logging.getLogger(__name__)
# logger.setLevel(logging.DEBUG) # 如果需要默认看到 debug 日志，取消此行注释

# 顶层代理回调函数


def _proxy_callback_for_mp(key: str, value: Any, lock: AcquirerProxy, callback_queue: Queue):
    """
    代理回调函数，在工作进程中被调用，将数据变化通知转发到主进程队列。
    由于实例方法无法被pickle序列化传递给多进程Manager，必须使用独立的顶层函数。

    Args:
        key: 发生变化的键名
        value: 变化后的新值
        lock: 共享锁对象
        callback_queue: 回调队列，用于将消息传递回主进程
    """
    try:
        # 在工作进程中获取值的副本
        if hasattr(value, '_getvalue'):
            # 如果是Manager代理对象，获取其值的副本
            copied_value = value._getvalue()
        else:
            # 否则直接使用值
            copied_value = value

        # 将数据变化放入主进程队列
        callback_queue.put((key, copied_value, time.time()))
        logger.debug(f"已将键 '{key}' 的数据变化放入主进程队列")
    except Exception as e:
        logger.error(f"代理回调函数处理键 '{key}' 时出错: {e}", exc_info=True)

# =============== Shared Data Manager Class ===============


class SharedDataManager:
    """
    SharedDataManager - 共享数据管理类

    该类负责创建、管理和访问基于 `multiprocessing.Manager` 的共享数据结构，
    为 `ManagedMultiProcess` 提供统一、线程/进程安全的数据共享接口。

    核心功能:
    ----------
    - **封装 Manager**: 管理 `multiprocessing.Manager` 的生命周期。
    - **共享数据容器**: 提供 Manager 创建的共享字典 (`dict`) 和列表 (`list`)。
    - **同步控制**: 提供进程锁 (`Lock`)，用于保护对共享数据的并发访问。
    - **安全访问方法**: 提供实例方法，用于安全地读写共享数据 (需要外部加锁)。
    - **数据转换**: 提供方法将 Manager 代理对象转换为普通的 Python 对象。
    - **错误记录**: 内建对错误信息的记录支持。
    - **数据变化通知**: 支持注册回调函数监听数据变化，并在数据变更时通知。

    使用示例:
    ----------

    ```python
    # (在 ManagedMultiProcess 内部或类似场景下使用)
    # 假设 manager_instance 是 SharedDataManager 的一个实例

    # 获取锁
    lock = manager_instance.get_lock()

    # 安全地向共享字典添加/更新值
        with lock:
        # 使用实例方法添加/更新 (推荐)
        manager_instance.add_value('my_key', 'my_value')

    # 安全地获取共享字典中的值
    with lock:
        value = manager_instance.get_value('my_key', default='default_value')
        print(f"获取到的值: {value}")

    # 安全地向共享列表添加值
    with lock:
        manager_instance.append_to_list('my_list', 'list_item_1')

    # 获取整个共享列表 (返回普通 Python 列表)
    with lock:
        my_list_copy = manager_instance.get_list('my_list')
        print(f"获取到的列表: {my_list_copy}")

    # 记录错误信息
    error_info = {"item": "bad_data", "error": "处理失败", "pid": 1234}
    with lock:
        manager_instance.record_error(error_info)

    # 获取所有结果 (普通 Python 字典)
    all_results = manager_instance.get_all_data_as_dict() # 需要加锁
    print(f"所有结果: {all_results}")

    # 获取错误列表 (普通 Python 列表)
    all_errors = manager_instance.get_errors() # 需要加锁
    print(f"所有错误: {all_errors}")

    # 注册数据变化监听
    def on_data_change(key, value, lock):
        with lock:
            print(f"数据 {key} 已变更为: {value}")

    manager_instance.register_change_callback('my_key', on_data_change)

    # 关闭 Manager (在 ManagedMultiProcess 的 stop_all 或 __del__ 中调用)
    # manager_instance.shutdown()
    """

    __DEFAULT_ERRORS_KEY = "errors"  # 存储错误的默认键名
    __CHANGE_CALLBACKS_KEY = "__callbacks"  # 存储回调函数的键名
    __CHANGE_QUEUE_KEY = "__change_queue"  # 存储变更队列的键名
    __LAST_VALUES_KEY = "__last_values"  # 存储上次值的键名
    __REALTIME_NOTIFY_QUEUE_KEY = "__realtime_notify_queue"  # 存储实时通知队列的键名
    __ENABLE_REALTIME_NOTIFY_KEY = "__enable_realtime_notify"  # 控制是否启用实时通知的标志

    def __init__(self):
        """
        初始化 SharedDataManager 管理器

        创建并启动一个 multiprocessing.Manager，初始化共享字典和列表，
        以及锁对象用于控制对共享数据的访问。

        同时初始化数据变更的回调注册和通知机制。
        """
        # 创建 Manager 对象
        self.__manager = Manager()

        # 创建共享字典和列表
        self.__shared_dict = self.__manager.dict()

        # 创建锁对象，用于控制对共享数据的访问
        self.__shared_lock = self.__manager.Lock()

        # 初始化空的错误列表
        self.__shared_dict[self.__DEFAULT_ERRORS_KEY] = self.__manager.list()

        # 初始化变更回调和跟踪机制
        self.__shared_dict[self.__CHANGE_CALLBACKS_KEY] = self.__manager.dict()
        self.__shared_dict[self.__LAST_VALUES_KEY] = self.__manager.dict()
        self.__shared_dict[self.__CHANGE_QUEUE_KEY] = self.__manager.Queue()

        # 初始化实时通知相关资源
        self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY] = self.__manager.Queue()
        # 默认不启用实时通知
        self.__shared_dict[self.__ENABLE_REALTIME_NOTIFY_KEY] = False

        # 启动变更通知处理线程
        self.__notification_thread = threading.Thread(
            target=self.__process_change_notifications,
            daemon=True  # 守护线程，随主线程退出
        )
        self.__notification_running = threading.Event()
        self.__notification_running.set()  # 初始设置为运行状态
        self.__notification_thread.start()

        logger.debug("SharedDataManager 初始化完成")

    def set_enable_realtime_notify(self, enable: bool = True):
        """
        设置是否启用实时通知功能

        当启用时，所有数据变更都会被放入实时通知队列，可供ManagedMultiProcess监听
        当禁用时，数据变更不会被放入实时通知队列，减少不必要的资源消耗

        Args:
            enable: 是否启用实时通知，默认为True
        """
        with self.__shared_lock:
            old_value = self.__shared_dict.get(self.__ENABLE_REALTIME_NOTIFY_KEY, False)
            self.__shared_dict[self.__ENABLE_REALTIME_NOTIFY_KEY] = enable
            if old_value != enable:
                logger.debug(f"实时通知功能已{'启用' if enable else '禁用'}")

    def is_realtime_notify_enabled(self) -> bool:
        """
        检查实时通知功能是否已启用

        Returns:
            bool: 如果实时通知功能已启用，返回True；否则返回False
        """
        return self.__shared_dict.get(self.__ENABLE_REALTIME_NOTIFY_KEY, False)

    def get_lock(self) -> AcquirerProxy:
        """
        获取共享锁对象，用于控制对共享数据的访问

        Returns:
            AcquirerProxy: Manager创建的锁对象
        """
        return self.__shared_lock

    def add_value(self, key: str, value: Any):
        """
        将值添加或更新到共享字典

        注意：调用此方法前应已获取锁

        Args:
            key: 键名
            value: 要存储的值
        """
        # 记录旧值（如果存在）
        old_value = None
        if key in self.__shared_dict:
            old_value = self.__convert_manager_proxy(self.__shared_dict[key])

        # 更新值
        self.__shared_dict[key] = value

        # 更新最后的值记录
        if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
            self.__shared_dict[self.__LAST_VALUES_KEY][key] = self.__convert_manager_proxy(value)

        # 如果有回调，添加到变更队列
        if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
            # 放入变更队列
            self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, value, old_value))

        # 新增：只有在启用实时通知时才将变更放入实时通知队列
        if self.is_realtime_notify_enabled():
            try:
                self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, value, time.time()))
                logger.debug(f"已将键 '{key}' 的变更放入实时通知队列")
            except Exception as e:
                logger.error(f"将键 '{key}' 的变更放入实时通知队列时出错: {e}", exc_info=True)

    def get_value(self, key: str, default: Any = None):
        """
        从共享字典获取指定键的值

        注意：调用此方法前应已获取锁

        Args:
            key: 要获取的键名
            default: 如果键不存在，返回的默认值

        Returns:
            Any: 如果键存在，返回对应的值；否则返回默认值
        """
        if key in self.__shared_dict:
            return self.__shared_dict[key]
        return default

    def update_values(self, update_dict: Dict[str, Any]):
        """
        批量更新共享字典中的多个键值对

        注意：调用此方法前应已获取锁

        Args:
            update_dict: 包含要更新的键值对的字典
        """
        # 检查并记录变更前的值
        changes = []
        realtime_changes = []  # 新增：记录所有变更用于实时通知
        for key, new_value in update_dict.items():
            if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                old_value = None
                if key in self.__shared_dict:
                    old_value = self.__shared_dict[key]

                # 如果有回调，记录变更
                if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                    changes.append((key, new_value, old_value))

                # 新增：只有在启用实时通知时才记录实时变更
                if self.is_realtime_notify_enabled():
                    realtime_changes.append((key, new_value, time.time()))

        # 执行更新
        for key, value in update_dict.items():
            self.__shared_dict[key] = value

            # 更新最后的值记录
            if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                self.__shared_dict[self.__LAST_VALUES_KEY][key] = self.__convert_manager_proxy(value)

        # 将所有变更添加到队列
        for key, new_value, old_value in changes:
            self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

        # 新增：只有在启用实时通知时才将变更放入实时通知队列
        if self.is_realtime_notify_enabled() and realtime_changes:
            for key, new_value, timestamp in realtime_changes:
                try:
                    self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, timestamp))
                    logger.debug(f"已将键 '{key}' 的变更放入实时通知队列")
                except Exception as e:
                    logger.error(f"将键 '{key}' 的变更放入实时通知队列时出错: {e}", exc_info=True)

    def append_to_list(self, key: str, value: Any):
        """
        向共享字典中指定键的列表添加值

        如果键不存在，先创建一个空列表；
        如果键存在但值不是列表，先将其替换为包含该值的新列表，然后再添加新值。

        注意：调用此方法前应已获取锁

        Args:
            key: 列表的键名
            value: 要添加到列表的值
        """
        old_value = None
        if key in self.__shared_dict:
            old_value = self.__convert_manager_proxy(self.__shared_dict[key])

        # 处理键不存在的情况
        if key not in self.__shared_dict:
            # 创建新列表
            new_list = self.__manager.list()
            new_list.append(value)
            self.__shared_dict[key] = new_list

            # 更新最后的值记录并通知变更
            if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                new_value = [value]  # 为简单起见，只返回一个普通列表副本
                self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                # 如果有回调，添加到变更队列
                if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                    self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                # 新增：只有在启用实时通知时才将变更放入实时通知队列
                if self.is_realtime_notify_enabled():
                    try:
                        self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                        logger.debug(f"已将键 '{key}' 的列表创建放入实时通知队列")
                    except Exception as e:
                        logger.error(f"将键 '{key}' 的列表创建放入实时通知队列时出错: {e}", exc_info=True)

        else:
            # 处理键存在但值不是列表的情况
            current = self.__shared_dict[key]

            if not isinstance(current, ListProxy):
                # 当前值不是列表，创建新列表包含当前值和新值
                new_list = self.__manager.list()
                try:
                    # 尝试添加当前值（如果可迭代）
                    if isinstance(current, (list, tuple, set)):
                        for item in current:
                            new_list.append(item)
                    else:
                        # 非迭代类型，直接添加
                        new_list.append(current)
                except TypeError:
                    # 不可迭代，作为单个元素添加
                    new_list.append(current)

                # 添加新值
                new_list.append(value)
                self.__shared_dict[key] = new_list

                # 更新最后的值记录并通知变更
                if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                    new_value = list(new_list)  # 为简单起见，只返回一个普通列表副本
                    self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                    # 如果有回调，添加到变更队列
                    if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                        self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                    # 新增：只有在启用实时通知时才将变更放入实时通知队列
                    if self.is_realtime_notify_enabled():
                        try:
                            self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                            logger.debug(f"已将键 '{key}' 的列表更新放入实时通知队列")
                        except Exception as e:
                            logger.error(f"将键 '{key}' 的列表更新放入实时通知队列时出错: {e}", exc_info=True)
            else:
                # 当前值已经是列表，直接添加新值
                current.append(value)

                # 更新最后的值记录并通知变更
                if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                    new_value = list(current)  # 为简单起见，只返回一个普通列表副本
                    self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                    # 如果有回调，添加到变更队列
                    if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                        self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                    # 新增：只有在启用实时通知时才将变更放入实时通知队列
                    if self.is_realtime_notify_enabled():
                        try:
                            self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                            logger.debug(f"已将键 '{key}' 的列表追加放入实时通知队列")
                        except Exception as e:
                            logger.error(f"将键 '{key}' 的列表追加放入实时通知队列时出错: {e}", exc_info=True)

    def get_list(self, key: str, default: Optional[List[Any]] = None) -> Optional[List[Any]]:
        """
        获取共享字典中指定键关联的列表的拷贝（普通 Python 列表）（需要外部加锁）。

        **警告:** 此方法本身不是线程/进程安全的，必须在获取锁 (`with self.get_lock():`) 的上下文中使用。

        Args:
            key (str): 共享字典中的键名。
            default (Optional[List[Any]], optional): 如果键不存在或值不是列表，返回的默认值。
                                                     默认为 None。

        Returns:
            Optional[List[Any]]: 如果键存在且值为列表，则返回列表的普通 Python 拷贝；
                               否则返回 `default`。

        示例:
            ```python
            with shared_manager.get_lock():
                log_messages = shared_manager.get_list('log_messages', default=[])
                print(f"获取到 {len(log_messages)} 条日志。")
            ```
        """
        if key not in self.__shared_dict:
            logger.debug(f"SharedDataManager.get_list (未加锁): 键 '{key}' 不存在，返回默认值")
            return default

        proxy_list = self.__shared_dict[key]
        if not isinstance(proxy_list, ListProxy):
            logger.warning(f"SharedDataManager.get_list (未加锁): 键 '{key}' 的值不是 ListProxy (类型: {type(proxy_list)})，返回默认值")
            return default

        # 将 ListProxy 转换为普通 list
        try:
            # 递归转换列表项
            return [self.__convert_manager_proxy(item) for item in proxy_list]
        except Exception as e:
            logger.error(f"SharedDataManager.get_list (未加锁): 转换 ListProxy 时出错: {str(e)}")
            return default  # 转换失败返回默认值

    def add_to_set(self, key: str, value: Any):
        """
        向共享字典中指定键的集合添加值

        如果键不存在，先创建一个空集合；
        如果键存在但值不是集合，先将其替换为包含该值的新集合，然后再添加新值。

        注意：调用此方法前应已获取锁

        Args:
            key: 集合的键名
            value: 要添加到集合的值
        """
        old_value = None
        if key in self.__shared_dict:
            old_value = self.__convert_manager_proxy(self.__shared_dict[key])

        # 处理键不存在的情况
        if key not in self.__shared_dict:
            # 创建新集合（使用列表实现，因为Manager不直接支持集合）
            new_set = self.__manager.list()
            new_set.append(value)
            self.__shared_dict[key] = new_set

            # 更新最后的值记录并通知变更
            if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                new_value = {value}  # 转换为普通集合
                self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                # 如果有回调，添加到变更队列
                if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                    self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                # 新增：只有在启用实时通知时才将变更放入实时通知队列
                if self.is_realtime_notify_enabled():
                    try:
                        self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                        logger.debug(f"已将键 '{key}' 的集合创建放入实时通知队列")
                    except Exception as e:
                        logger.error(f"将键 '{key}' 的集合创建放入实时通知队列时出错: {e}", exc_info=True)

        else:
            # 处理键存在但值不是列表（我们用列表表示集合）的情况
            current = self.__shared_dict[key]

            if not isinstance(current, ListProxy):
                # 当前值不是列表，创建新列表包含当前值和新值
                new_set = self.__manager.list()
                try:
                    # 尝试将当前值转换为集合，然后添加元素
                    if isinstance(current, (list, tuple, set)):
                        unique_values = set(current)
                        for item in unique_values:
                            new_set.append(item)
                    else:
                        # 非集合类型，作为单个元素添加
                        new_set.append(current)
                except TypeError:
                    # 不可迭代或不可哈希，作为单个元素添加
                    new_set.append(current)

                # 添加新值（如果已存在则不添加）
                if value not in new_set:
                    new_set.append(value)
                self.__shared_dict[key] = new_set

                # 更新最后的值记录并通知变更
                if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                    new_value = set(new_set)  # 转换为普通集合
                    self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                    # 如果有回调，添加到变更队列
                    if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                        self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                    # 新增：只有在启用实时通知时才将变更放入实时通知队列
                    if self.is_realtime_notify_enabled():
                        try:
                            self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                            logger.debug(f"已将键 '{key}' 的集合更新放入实时通知队列")
                        except Exception as e:
                            logger.error(f"将键 '{key}' 的集合更新放入实时通知队列时出错: {e}", exc_info=True)
            else:
                # 当前值已经是列表（我们用列表表示集合），只添加新值
                if value not in current:
                    current.append(value)

                    # 更新最后的值记录并通知变更
                    if key not in self.__CHANGE_CALLBACKS_KEY and key not in self.__LAST_VALUES_KEY and key not in self.__CHANGE_QUEUE_KEY and key not in self.__REALTIME_NOTIFY_QUEUE_KEY and key not in self.__ENABLE_REALTIME_NOTIFY_KEY:
                        new_value = set(current)  # 转换为普通集合
                        self.__shared_dict[self.__LAST_VALUES_KEY][key] = new_value

                        # 如果有回调，添加到变更队列
                        if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                            self.__shared_dict[self.__CHANGE_QUEUE_KEY].put((key, new_value, old_value))

                        # 新增：只有在启用实时通知时才将变更放入实时通知队列
                        if self.is_realtime_notify_enabled():
                            try:
                                self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY].put((key, new_value, time.time()))
                                logger.debug(f"已将键 '{key}' 的集合添加放入实时通知队列")
                            except Exception as e:
                                logger.error(f"将键 '{key}' 的集合添加放入实时通知队列时出错: {e}", exc_info=True)

    def get_set(self, key: str, default: Optional[Set[Any]] = None) -> Optional[Set[Any]]:
        """
        获取共享字典中指定键关联的集合的拷贝（普通 Python set）。
        集合在内部通常由 Manager 列表模拟。

        **警告:** 此方法本身不是线程/进程安全的，必须在获取锁 (`with self.get_lock():`) 的上下文中使用。

        Args:
            key (str): 共享字典中的键名。
            default (Optional[Set[Any]], optional): 如果键不存在或值不是列表（用于模拟集合），返回的默认值。
                                                  默认为 None。

        Returns:
            Optional[Set[Any]]: 如果键存在且值为列表，则返回包含列表元素的普通 Python set；
                              否则返回 `default`。
                              如果 SharedDataManager 未初始化或已关闭，则返回 `default`。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例)
            # 假设工作进程执行了:
            # with lock:
            #     shared_manager.add_to_set('unique_items', 'apple')
            #     shared_manager.add_to_set('unique_items', 'banana')
            #     shared_manager.add_to_set('unique_items', 'apple') # 重复添加无效

            # 获取集合拷贝
            item_set = process_manager.get_shared_set('unique_items', default=set())
            if item_set is not None:
                print(f"获取到的 unique_items 集合: {item_set}") # 输出: {'apple', 'banana'} (顺序不定)
                print(f"集合类型: {type(item_set)}") # 输出: <class 'set'>
            ```
        """
        proxy_list_copy = self.get_list(key, default=None)  # 先尝试获取列表的拷贝

        if proxy_list_copy is None:
            logger.debug(f"SharedDataManager.get_set (未加锁): 键 '{key}' 无法获取列表，返回默认值")
            return default
        elif not isinstance(proxy_list_copy, list):
            logger.warning(f"SharedDataManager.get_set (未加锁): 从 get_list 获取的不是列表 (类型: {type(proxy_list_copy)})，返回默认值")
            return default
        else:
            # 将获取到的普通列表转换为集合
            try:
                return set(proxy_list_copy)
            except TypeError as e:
                logger.error(f"SharedDataManager.get_set (未加锁): 转换列表 '{key}' 为集合时出错 (可能包含不可哈希的元素): {e}")
                return default  # 转换失败返回默认值

    def record_error(self, error_info: Dict[str, Any]):
        """
        将错误信息记录到共享的错误列表中（需要外部加锁）。

        **警告:** 此方法本身不是线程/进程安全的，必须在获取锁 (`with self.get_lock():`) 的上下文中使用。

        Args:
            error_info (Dict[str, Any]): 包含错误详情的字典。

        示例:
            ```python
            error_details = {
                'timestamp': time.time(),
                'task_id': 123,
                'message': 'File not found',
                'traceback': '...'
            }
            with shared_manager.get_lock():
                shared_manager.record_error(error_details)
            ```
        """
        try:
            # 直接使用 append_to_list 方法
            self.append_to_list(self.__DEFAULT_ERRORS_KEY, error_info)
            logger.debug("SharedDataManager.record_error (未加锁): 错误信息已记录")
        except Exception as e:
            logger.error(f"SharedDataManager.record_error(未加锁): 记录错误时失败: {str(e)}{traceback.format_exc()}")

    def get_errors(self) -> List[Dict[str, Any]]:
        """
        获取所有记录的错误信息的拷贝（普通 Python 列表）（需要外部加锁）。

        **警告:** 此方法本身不是线程/进程安全的，必须在获取锁 (`with self.get_lock():`) 的上下文中使用。

        Returns:
            List[Dict[str, Any]]: 包含所有错误信息字典的列表，如果无错误则为空列表。

        示例:
            ```python
            with shared_manager.get_lock():
                all_errors = shared_manager.get_errors()
                if all_errors:
                    print(f"共记录了 {len(all_errors)} 个错误。")
            ```
        """
        # 使用 get_list 获取错误列表，若不存在或类型错误则返回空列表
        errors_list = self.get_list(self.__DEFAULT_ERRORS_KEY, default=[])
        # 确保返回的是 List[Dict[str, Any]] 类型
        return errors_list if isinstance(errors_list, list) else []

    def get_all_data_as_dict(self) -> Dict[str, Any]:
        """
        获取共享字典中所有数据的拷贝（普通 Python 字典）（需要外部加锁）。
        会自动将 Manager 代理对象（如 ListProxy）转换为普通 Python 对象。

        **警告:** 此方法本身不是线程/进程安全的，必须在获取锁 (`with self.get_lock():`) 的上下文中使用。

        Returns:
            Dict[str, Any]: 包含所有共享数据的普通 Python 字典。

        示例:
            ```python
            with shared_manager.get_lock():
                snapshot = shared_manager.get_all_data_as_dict()
                print("当前所有共享数据快照:", snapshot)
            ```
        """
        logger.debug("SharedDataManager.get_all_data_as_dict (未加锁): 开始转换整个共享字典...")
        try:
            # 使用私有转换方法进行递归转换
            converted_data = self.__convert_manager_proxy(self.__shared_dict)
            logger.debug("SharedDataManager.get_all_data_as_dict (未加锁): 转换完成")
            return converted_data if isinstance(converted_data, dict) else {}
        except Exception as e:
            logger.error(f"SharedDataManager.get_all_data_as_dict(未加锁): 转换共享数据时出错: {str(e)}{traceback.format_exc()}")
            return {}  # 出错时返回空字典

    def __convert_manager_proxy(self, proxy_obj):
        """
        递归地将 Manager 代理对象转换为普通的 Python 对象 (私有方法)。
        处理 DictProxy, ListProxy，并递归处理嵌套结构。

        Args:
            proxy_obj: Manager 返回的代理对象 或 普通 Python 对象。

        Returns:
            转换后的普通 Python 对象 (dict, list, or primitive type)。
        """
        # logger.debug(f"  __convert_manager_proxy: 转换对象类型: {type(proxy_obj)}") # 调试日志，可按需开启

        # 基础类型或非代理对象直接返回
        if not isinstance(proxy_obj, (DictProxy, ListProxy, dict, list)):
            # logger.debug(f"  __convert_manager_proxy: 对象类型 {type(proxy_obj)} 非代理或容器，直接返回。")
            return proxy_obj

        # 处理 Manager dict 代理
        if isinstance(proxy_obj, DictProxy):
            # logger.debug("  __convert_manager_proxy: 检测到 DictProxy，转换为 dict...")
            normal_dict = {}
            try:
                # 使用 list(proxy_obj.items()) 创建副本防止迭代过程中修改
                for key, value in list(proxy_obj.items()):
                    normal_dict[key] = self.__convert_manager_proxy(value)  # 递归转换值
                return normal_dict
            except Exception as e:
                logger.error(f"  __convert_manager_proxy: 转换 DictProxy 时出错 (key: {key}): {str(e)}")
                return normal_dict  # 返回部分转换的字典

        # 处理 Manager list 代理
        elif isinstance(proxy_obj, ListProxy):
            # logger.debug("  __convert_manager_proxy: 检测到 ListProxy，转换为 list...")
            normal_list = []
            try:
                # 使用 list(proxy_obj) 创建副本防止迭代过程中修改
                for item in list(proxy_obj):
                    normal_list.append(self.__convert_manager_proxy(item))  # 递归转换列表项
                return normal_list
            except Exception as e:
                logger.error(f"  __convert_manager_proxy: 转换 ListProxy 时出错: {str(e)}")
                return normal_list  # 返回部分转换的列表

        # 处理普通 dict (递归转换其值)
        elif isinstance(proxy_obj, dict):
            # logger.debug("  __convert_manager_proxy: 检测到普通 dict，递归转换其值...")
            return {k: self.__convert_manager_proxy(v) for k, v in proxy_obj.items()}

        # 处理普通 list (递归转换其元素)
        elif isinstance(proxy_obj, list):
            # logger.debug("  __convert_manager_proxy: 检测到普通 list，递归转换其元素...")
            return [self.__convert_manager_proxy(item) for item in proxy_obj]

        logger.warning(f"  __convert_manager_proxy: 未知可处理类型 {type(proxy_obj)}，直接返回原始对象。")
        return proxy_obj

    def register_change_callback(self, key: str, callback_func: Callable[[str, Any, AcquirerProxy], None]):
        """
        注册一个回调函数，当指定键的值发生变化时被调用。

        注意：调用此方法前应已获取锁

        Args:
            key: 要监听变化的键名
            callback_func: 回调函数，接收参数(key, value, lock)，其中value是变化后的新值
        """
        callbacks_dict = self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]
        if key not in callbacks_dict:
            callbacks_dict[key] = []

        # 注册回调
        callbacks = callbacks_dict[key]
        if callback_func not in callbacks:
            callbacks.append(callback_func)
            callbacks_dict[key] = callbacks

            # 记录当前值作为基准
            if key in self.__shared_dict and key not in self.__LAST_VALUES_KEY:
                self.__shared_dict[self.__LAST_VALUES_KEY][key] = self.__convert_manager_proxy(self.__shared_dict[key])

            logger.debug(f"为键 '{key}' 注册了变化回调")

    def unregister_change_callback(self, key: str, callback_func: Optional[Callable] = None):
        """
        取消注册指定键的变化回调函数。

        注意：调用此方法前应已获取锁

        Args:
            key: 要取消监听的键名
            callback_func: 要取消的特定回调函数。如果为None，则移除该键的所有回调。
        """
        callbacks_dict = self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]
        if key in callbacks_dict:
            if callback_func is None:
                # 移除所有回调
                del callbacks_dict[key]
                logger.debug(f"已移除键 '{key}' 的所有变化回调")
            else:
                # 移除特定回调
                callbacks = callbacks_dict[key]
                if callback_func in callbacks:
                    callbacks.remove(callback_func)
                    if not callbacks:  # 如果没有回调了，删除键
                        del callbacks_dict[key]
                    else:
                        callbacks_dict[key] = callbacks
                    logger.debug(f"已移除键 '{key}' 的指定变化回调")

    def __process_change_notifications(self):
        """
        私有方法：处理数据变化通知的后台线程。

        持续监控变更队列，当有数据变化时，调用对应的回调函数。
        """
        logger.debug("启动数据变化通知处理线程")
        while self.__notification_running.is_set():
            try:
                # 尝试从队列获取变更通知，设置超时以便定期检查运行状态
                try:
                    key, new_value, old_value = self.__shared_dict[self.__CHANGE_QUEUE_KEY].get(timeout=0.5)
                except (queue.Empty, EOFError):
                    # 队列为空或连接断开，继续循环
                    continue

                # 获取该键的回调函数列表
                callbacks = []
                with self.__shared_lock:
                    if key in self.__shared_dict[self.__CHANGE_CALLBACKS_KEY]:
                        callbacks = list(self.__shared_dict[self.__CHANGE_CALLBACKS_KEY][key])

                # 执行所有回调
                for callback in callbacks:
                    try:
                        # 传递键名、新值和锁对象
                        callback(key, new_value, self.__shared_lock)
                    except Exception as e:
                        logger.error(f"执行键 '{key}' 的变化回调时出错: {e}", exc_info=True)

            except Exception as e:
                # 捕获通知处理过程中的任何异常
                logger.error(f"数据变化通知处理线程发生错误: {e}", exc_info=True)
                # 短暂休眠避免CPU空转
                time.sleep(0.1)

        logger.debug("数据变化通知处理线程已停止")

    def shutdown(self):
        """
        关闭 Manager 和相关资源。

        此方法应在不再需要共享数据管理器时调用，
        通常在 ManagedMultiProcess 的 stop_all 或 __del__ 中调用。
        """
        logger.debug("SharedDataManager.shutdown - 开始关闭...")

        # 停止通知线程
        if hasattr(self, '_SharedDataManager__notification_running'):
            self.__notification_running.clear()

            # 等待通知线程结束
            if hasattr(self, '_SharedDataManager__notification_thread') and self.__notification_thread.is_alive():
                self.__notification_thread.join(timeout=1.0)
                if self.__notification_thread.is_alive():
                    logger.warning("通知处理线程未能在超时时间内停止")

        # 关闭 Manager
        if hasattr(self, '_SharedDataManager__manager') and self.__manager:
            try:
                logger.debug("  关闭 Manager...")
                self.__manager.shutdown()
                logger.debug("  Manager 已关闭。")
            except Exception as e:
                logger.error(f"  关闭 Manager 时出错: {e}", exc_info=True)
            finally:
                self.__manager = None

        logger.debug("SharedDataManager.shutdown - 完成。")

    def __del__(self):
        """析构函数，确保资源被释放"""
        self.shutdown()

    def get_realtime_notify_queue(self) -> Queue:
        """
        获取实时通知队列

        此队列用于子进程实时通知主进程数据变化。
        主进程可以监听此队列获取数据变化，而不需要轮询。

        Returns:
            Queue: 实时通知队列对象
        """
        return self.__shared_dict[self.__REALTIME_NOTIFY_QUEUE_KEY]


# =============== Helper Function for Worker Process ===============

# 修改 _top_level_worker_loop 函数签名和内部逻辑
def _top_level_worker_loop(
    worker_id,           # 工作进程编号
    queue,               # 任务队列 (JoinableQueue)
    # shared_data_manager: SharedDataManager,  # 不再直接传递实例
    shared_data_manager_proxy,  # 替换: 共享数据管理器代理对象
    stop_flag,           # 停止事件 (Event)
    status_dict,         # 进程状态字典代理 (Manager.dict)
    callback,            # 用户定义的回调函数
    custom_init_args,    # 传递给回调的位置参数元组
    extra_kwargs,        # 传递给回调的关键字参数字典
):
    """
    工作进程的主循环 (顶层函数) - 已更新以使用 SharedDataManager 代理

    Args:
        worker_id: 工作进程的标识符/编号
        queue: 任务队列 (JoinableQueue)
        shared_data_manager_proxy: 共享数据管理器代理对象
        stop_flag: 停止事件 (Event)
        status_dict: 进程状态字典代理 (Manager.dict) - 注意：这个仍然来自外部 Manager
        callback: 用户定义的回调函数, 签名应为: callback(shared_data_manager_proxy, item, *custom_args, **kwargs)
        custom_init_args: 传递给回调的位置参数元组
        extra_kwargs: 传递给回调的关键字参数字典
    """
    pid = os.getpid()
    process_name = f"Worker-{worker_id}(PID:{pid})"
    logger.info(f"{process_name}: 开始运行...")

    # 防止字典访问错误
    try:
        status_dict[pid] = "running"  # 更新自己的状态
    except Exception as e:
        logger.error(f"{process_name}: 无法更新状态字典: {e}")
        # 继续执行，即使状态更新失败

    # 获取锁 (通过代理获取锁的代理)
    lock_proxy: Optional[AcquirerProxy] = None
    try:
        # 注意：现在调用代理的方法
        lock_proxy = shared_data_manager_proxy.get_lock()
        if lock_proxy is None:
            raise RuntimeError("获取到的锁代理对象为 None")
        logger.debug(f"{process_name}: 已获取共享数据锁代理")
    except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
        logger.error(f"{process_name}: 与 Manager 的连接已中断: {conn_err}")
        try:
            status_dict[pid] = "error_manager_disconnected"
        except Exception:
            pass
        return  # Manager连接已断开，无法继续
    except Exception as e:
        logger.error(f"{process_name}: 无法从 SharedDataManager 代理获取锁: {e}\n{traceback.format_exc()}")
        try:
            status_dict[pid] = "error_getting_lock"
        except Exception:
            pass
        return  # 没有锁无法安全工作

    # 为了更快地响应停止信号，设置较短的队列获取超时时间
    queue_timeout = 0.1  # 秒
    task_count = 0  # 记录本进程处理的任务数
    empty_queue_count = 0  # 记录连续获取到空队列的次数
    max_empty_count = 30  # 当连续30次获取到空队列时，认为所有任务已处理完毕，退出循环

    # 启动时检查停止标志
    if stop_flag.is_set():
        logger.info(f"{process_name}: 启动时检测到停止信号，不开始处理")
        try:
            status_dict[pid] = "stopped_before_start"
        except Exception:
            pass
        return

    while not stop_flag.is_set():
        task_element = None  # 重置任务元素
        try:
            # 1. 从队列获取任务
            # logger.debug(f"{process_name}: 等待任务 (超时 {queue_timeout}s)...") # Debug 日志
            try:
                task_element = queue.get(block=True, timeout=queue_timeout)
                # 重置空队列计数
                empty_queue_count = 0
                # logger.debug(f"{process_name}: 获取到任务: {task_element}") # Debug 日志
            except Empty:  # 直接使用导入的Empty异常，而不是queue.Empty
                # 队列暂时为空，不是错误，继续循环检查停止标志
                empty_queue_count += 1
                if empty_queue_count >= max_empty_count:
                    logger.info(f"{process_name}: 连续 {max_empty_count} 次获取到空队列，认为所有任务已处理完毕，退出循环")
                    try:
                        status_dict[pid] = "completed_all_tasks"
                    except Exception:
                        pass
                    break  # 退出主循环
                # logger.debug(f"{process_name}: 队列为空 ({empty_queue_count}/{max_empty_count})，继续检查停止标志") # Debug 日志
                continue
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as e:
                # Manager 可能已关闭，或者队列已损坏
                logger.error(f"{process_name}: 从队列获取任务时连接错误 (可能Manager已关闭): {str(e)}")
                try:
                    status_dict[pid] = "error_queue_get"
                except Exception:
                    pass
                break
            except Exception as e:
                # 获取任务时发生其他意外错误
                logger.error(f"{process_name}: 从队列获取任务失败: {str(e)}\n{traceback.format_exc()}")
                try:
                    status_dict[pid] = "error_queue_get"
                except Exception:
                    pass
                break

            # 2. 再次检查停止标志 (获取任务后，处理任务前)
            # 如果在等待任务时收到了停止信号，则不处理刚获取的任务
            if stop_flag.is_set():
                logger.info(f"{process_name}: 收到停止信号，将任务放回队列: {task_element}")
                try:
                    # 尝试将任务放回队列，以便其他进程（如果还在运行）或下次运行处理
                    queue.put(task_element)
                    # 注意：放回后，需要调用 task_done() 来平衡 get() 操作
                    # 否则 JoinableQueue 的计数会出错
                    queue.task_done()
                except (EOFError, BrokenPipeError, ConnectionRefusedError) as e:
                    logger.warning(f"{process_name}: 连接已断开，无法将任务放回队列: {e}")
                    try:
                        queue.task_done()  # 仍然尝试标记任务完成
                    except Exception:
                        pass
                except Exception as put_err:
                    logger.error(f"{process_name}: 将任务放回队列失败: {put_err}")
                    # 即使放回失败，也需要标记任务完成（从本进程角度）
                    try:
                        queue.task_done()  # 仍然需要调用
                    except Exception:
                        pass
                break

            # 3. 处理任务
            task_start_time = time.time()
            try:
                status_dict[pid] = f"processing: {task_element}"
            except Exception:
                pass
            logger.info(f"{process_name}: 开始处理任务: {task_element}")
            try:
                # 调用用户提供的回调函数，传递 SharedDataManager 代理实例
                # 回调签名: callback(shared_data_manager_proxy, item, *custom_args, **kwargs)
                # 注意：回调函数内部现在通过代理对象调用方法，例如:
                # lock = shared_data_manager_proxy.get_lock()
                # with lock:
                #     shared_data_manager_proxy.add_value(...)
                callback(
                    shared_data_manager_proxy, task_element, *custom_init_args, **extra_kwargs
                )
                task_duration = time.time() - task_start_time
                logger.info(f"{process_name}: 完成处理任务: {task_element} (耗时: {task_duration:.4f}s)")
                task_count += 1
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                task_duration = time.time() - task_start_time
                logger.error(f"{process_name}: Manager连接中断，无法继续处理任务: {conn_err}")
                try:
                    status_dict[pid] = "error_manager_disconnected"
                except Exception:
                    pass
                try:
                    queue.task_done()
                except Exception:
                    pass
                return  # 连接断开，无法继续执行
            except Exception as e:
                # 回调函数执行出错
                task_duration = time.time() - task_start_time
                logger.error(f"{process_name}: 处理任务 {task_element} 时回调函数出错 (耗时: {task_duration:.4f}s): {str(e)}\n{traceback.format_exc()}")
                # 尝试通过代理记录错误
                try:
                    # 注意：这里也需要获取锁代理
                    with lock_proxy:
                        error_detail = {
                            "item": task_element,
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                            "pid": pid,
                            "worker_id": worker_id,
                            "timestamp": time.time()
                        }
                        shared_data_manager_proxy.record_error(error_detail)
                except Exception as record_err:
                    logger.error(f"{process_name}: 记录错误信息时失败: {record_err}")
                try:
                    status_dict[pid] = "callback_error"
                except Exception:
                    pass

            finally:
                # 4. 标记任务完成 (无论成功还是失败)
                # logger.debug(f"{process_name}: 标记任务完成: {task_element}") # Debug 日志
                try:
                    queue.task_done()
                except (EOFError, BrokenPipeError, ConnectionRefusedError):
                    # 连接已断开，无法标记任务完成，但这不影响任务的实际完成情况
                    logger.warning(f"{process_name}: 连接已断开，无法标记任务完成")
                    break  # 连接断开，退出循环
                except Exception as e:
                    logger.error(f"{process_name}: 标记任务完成时出错: {e}")
                    # 继续执行，避免因标记失败而影响其他任务

                try:
                    status_dict[pid] = "running"  # 处理完一个任务后，状态改回 running
                except Exception:
                    pass

                # 5. 再次检查停止标志 (处理完一个任务后)
                if stop_flag.is_set():
                    logger.info(f"{process_name}: 在任务处理后检测到停止信号，准备退出")
                    break

        except KeyboardInterrupt:
            # 允许 Ctrl+C 中断工作进程
            logger.warning(f"{process_name}: 收到 KeyboardInterrupt，准备退出...")
            try:
                status_dict[pid] = "interrupted"
            except Exception:
                pass
            try:
                stop_flag.set()  # 尝试通知其他进程也停止
            except Exception as e:
                logger.error(f"{process_name}: 设置停止标志时出错: {e}")
            break
        except Exception as loop_err:
            # 捕获工作循环本身可能出现的其他异常
            logger.error(f"{process_name}: 工作循环中发生意外严重错误: {str(loop_err)}\n{traceback.format_exc()}")
            try:
                status_dict[pid] = "loop_error"
            except Exception:
                pass
            # 尝试记录循环错误
            try:
                with lock_proxy:
                    error_detail = {
                        "item": task_element,  # 可能为 None
                        "error": f"Loop error: {str(loop_err)}",
                        "traceback": traceback.format_exc(),
                        "pid": pid,
                        "worker_id": worker_id,
                        "timestamp": time.time()
                    }
                    shared_data_manager_proxy.record_error(error_detail)
            except Exception as record_err:
                logger.error(f"{process_name}: 记录循环错误时失败: {record_err}")

            break  # 发生严重循环错误，退出

    # --- 循环结束后的清理 ---
    try:
        final_status = status_dict.get(pid, "unknown")
        logger.info(f"{process_name}: 退出工作循环。处理任务总数: {task_count}。最终状态: {final_status}")

        # 根据退出原因更新最终状态
        if stop_flag.is_set() and final_status not in ["error", "loop_error", "callback_error", "interrupted",
                                                       "error_queue_get", "error_getting_lock", "error_manager_disconnected", "stopped_before_start"]:
            status_dict[pid] = "stopped_signal"  # 因信号停止
        elif final_status == "running":  # 如果是正常处理完队列所有任务退出
            status_dict[pid] = "completed"  # 正常完成
    except Exception as e:
        logger.error(f"{process_name}: 更新最终状态时出错: {e}")

    # 显式断开与Manager的连接
    shared_data_manager_proxy = None
    lock_proxy = None

    logger.info(f"{process_name}: 工作进程完全退出")


# =============== 主类实现 ===============

# 定义一个自定义 Manager 用于注册 SharedDataManager
class DataManagerManager(BaseManager):
    """
    自定义Manager类，用于注册和管理SharedDataManager。

    作为BaseManager的子类，此类负责在一个独立的进程中创建和管理SharedDataManager实例，
    使其能够被多个子进程共享访问。
    """

    def __init__(self, *args, **kwargs):
        """
        初始化DataManagerManager实例。

        Args:
            *args: 传递给BaseManager的位置参数
            **kwargs: 传递给BaseManager的关键字参数，常见参数包括：
                - authkey: 用于进程间认证的密钥
                - address: 监听地址元组 (host, port)
        """
        # 设置默认参数
        filtered_kwargs = {}
        filtered_kwargs['authkey'] = kwargs.get('authkey', b'ManagedMultiProcess')
        filtered_kwargs['address'] = kwargs.get('address', ('127.0.0.1', 0))  # 使用随机端口

        # 移除不应传递给BaseManager.__init__的参数
        # daemon参数将在start方法中设置，而不是在__init__中传递
        if 'daemon' in kwargs:
            logger.debug("从kwargs中移除daemon参数，将在start方法中设置")

        logger.debug(f"初始化DataManagerManager，参数: {filtered_kwargs}")
        super().__init__(*args, **filtered_kwargs)

        # 注册SharedDataManager
        self.register('SharedDataManager', SharedDataManager)

    def start(self):
        """
        启动管理器进程并将其设为daemon模式。

        daemon模式确保当主进程退出时，管理器进程也会自动终止，
        避免孤立进程导致主程序无法退出的问题。

        Returns:
            返回启动的管理器进程对象
        """
        # 先调用父类的start方法
        process = super().start()
        # 将进程设置为daemon模式
        if hasattr(process, '_process') and process._process:
            process._process.daemon = True
            logger.debug("  DataManagerManager进程已设置为daemon模式")
        else:
            logger.warning("  无法设置DataManagerManager为daemon模式：_process属性不存在")
        return process


class ManagedMultiProcess:
    """
    ManagedMultiProcess - 高级多进程任务处理管理器

    该类封装了一套完整的多进程任务处理系统，提供了安全、便捷的方式来处理大规模并行任务。
    核心特性包括进程间安全的数据共享、错误处理、状态监控以及实时数据变化通知等。

    核心功能:
    ----------
    - **多进程任务处理**: 自动将输入数据分配到多个工作进程中并行处理。
    - **进程间数据共享**: 通过 SharedDataManager 提供安全的进程间数据共享。
    - **错误处理与记录**: 自动捕获和记录工作进程中的错误，避免整个任务失败。
    - **状态监控**: 提供各工作进程的状态信息。
    - **资源管理**: 自动管理进程资源，避免资源泄漏。
    - **实时数据变化通知**: 支持监听共享数据的变化，并在主进程中执行回调函数。

    数据监听机制:
    ----------
    本类实现了一套高效的数据变化监听机制。当共享数据发生变化时，相关回调函数会被自动调用。
    主要特点包括:

    - **实时通知**: 通过共享队列实现数据变化的实时通知，而非轮询检测，减少延迟，提高效率。
    - **异步回调**: 回调函数在单独的线程中异步执行，不会阻塞主线程或数据处理线程。
    - **顺序执行**: 回调函数按数据变化的顺序依次执行，确保不会丢失任何数据变化事件。
    - **主进程访问**: 回调函数在主进程中执行，可以安全地访问主进程的变量和资源。
    - **多键监听**: 支持同时监听多个键的变化，使用同一个或不同的回调函数。
    - **路径监听**: 支持监听嵌套数据结构中特定路径的变化。

    使用示例:
    ----------

    ```python
    # 1. 创建并配置 ManagedMultiProcess 实例
    mp = ManagedMultiProcess(
        input_data=tasks,  # 要处理的任务列表
        callback_func=worker_function,  # 工作函数
        num_processes=3,  # 使用3个工作进程
        extra_arg1="value1",  # 额外参数会传递给工作函数
        extra_arg2="value2"
    )

    # 2. 注册数据变化监听
    def on_status_change(key, value, lock):
        with lock:  # 使用锁确保线程安全
            print(f"状态变为: {value}")
            # 可以安全访问主进程变量
            # 可以使用锁安全访问/修改其他共享数据

    mp.watch_shared_data("status", on_status_change)

    # 3. 启动处理
    mp.run()  # 非阻塞，立即返回
    # ... 做其他事情 ...
    mp.wait_all()  # 等待所有任务完成

    # 4. 获取结果
    results = mp.get_results()  # 获取所有共享数据
    status = mp.get_shared_value("status")  # 获取特定键的值
    errors = mp.get_all_errors()  # 获取所有错误

    # 5. 清理资源
    mp.stop_all()
    ```

    工作函数示例:
    ----------

    ```python
    def worker_function(shared_manager, task_item, extra_arg1, extra_arg2):
        # 获取共享锁
        lock = shared_manager.get_lock()

        try:
            # 处理任务...
            result = process(task_item)

            # 安全地更新共享数据
            with lock:
                # 读取当前值
                current = shared_manager.get_value("results", default=[])
                # 添加结果
                shared_manager.append_to_list("results", result)
                # 更新状态
                shared_manager.add_value("status", "processing")

        except Exception as e:
            # 记录错误
            with lock:
                shared_manager.record_error({
                    "task": task_item,
                    "error": str(e),
                    "traceback": traceback.format_exc()
                })
    ```
    """

    def __init__(
        self,
        input_data: Iterable[Any],
        callback_func: Callable,
        num_processes: int = 3,
        *custom_args,
        **kwargs,
    ):
        """
        初始化 ManagedMultiProcess 实例。

        Args:
            input_data: 要处理的数据项迭代器，每个数据项会传递给工作函数。
            callback_func: 工作函数，将在子进程中调用，处理每个输入数据项。
            num_processes: 要创建的工作进程数量，默认为3。
            *custom_args: 传递给每个工作函数的位置参数。
            **kwargs: 额外的关键字参数，会传递给每个工作函数。
        """
        # 记录初始化参数
        self.input_data = input_data
        self.callback_func = callback_func
        self.num_processes = num_processes
        self.custom_args = custom_args
        self.kwargs = kwargs

        # 多进程数据容器
        self.queue = None           # 任务队列 (JoinableQueue)
        self.stop_flag = None       # 停止信号事件 (Event)
        self.status_dict = None     # 进程状态字典 (Manager.dict)
        self.processes = {}         # 工作进程字典 {pid: Process对象}

        # Managers
        self.__mp_manager = None               # multiprocessing.Manager
        self.__data_manager = None             # 自定义的DataManagerManager
        self.__shared_data_manager_proxy = None  # SharedDataManager代理对象

        # 内部工作标志
        self.__is_started = False      # 是否已启动
        self.__is_running = False      # 是否正在运行
        self.__is_stopping = False     # 是否正在停止
        self.__status = "初始化中"      # 处理器状态

        # 内部数据缓存
        self.__cached_shared_values = {}  # 共享数据的本地缓存

        # 数据变化监听
        self.__monitored_keys = set()            # 被监控的键集合
        self.__main_process_callbacks = {}       # 每个键的回调函数列表
        self.__last_values = {}                  # 每个键的最近值
        self.__callback_thread = None            # 轮询线程
        self.__callback_running = threading.Event()  # 轮询线程控制事件
        self.__callback_running.set()  # 初始设置为运行状态

        # 添加异步回调队列和工作线程
        self.__callback_queue = queue.Queue()             # 回调函数执行队列
        self.__callback_worker_thread = None               # 回调处理工作线程
        self.__callback_worker_running = threading.Event()  # 工作线程控制事件
        self.__callback_worker_running.set()              # 初始设置为运行状态

        # 回调函数注册表和路径映射
        self.__callback_registry = {}  # 格式: {key: 绑定的顶层回调函数}
        self.__key_paths = {}          # 每个键的路径映射，用于路径监听

        # 新增：实时通知处理相关
        self.__realtime_notify_thread = None        # 实时通知处理线程
        self.__realtime_notify_running = threading.Event()  # 线程控制事件
        self.__realtime_notify_running.set()        # 初始设置为运行状态

        # 初始化单进程模式标志
        self.is_single_process_mode = False  # 默认为多进程模式

        logger.debug(f"  输入数据项数: {len(self.input_data)}")
        logger.debug(f"  请求进程数: {num_processes}")
        logger.debug(f"  实际使用的进程数 (self.num_processes): {self.num_processes}")
        logger.debug(f"  传递给回调的位置参数 (self.custom_args): {self.custom_args}")
        logger.debug(f"  传递给回调的关键字参数 (self.kwargs): {self.kwargs}")

        # 初始化标记和资源 (确保即使失败也能清理)
        # self.__shared_data_manager: Optional[SharedDataManager] = None # 不再直接持有实例
        self.__data_manager_manager: Optional[DataManagerManager] = None  # 持有自定义 Manager
        self.__shared_data_manager_proxy = None  # 持有 SharedDataManager 的代理
        self.__status_manager: Optional[BaseManager] = None
        self.__process_status_dict: Optional[DictProxy] = None
        self.task_queue: Optional[JoinableQueue] = None
        self.stop_event = None  # 使用 multiprocessing.Event
        self.worker_processes: List[Process] = []

        try:
            # 1. 创建并启动自定义 Manager 用于 SharedDataManager
            logger.debug("  创建并启动 DataManagerManager...")
            self.__data_manager_manager = DataManagerManager()
            self.__data_manager_manager.start()
            logger.debug("  DataManagerManager 已启动。")

            # 2. 通过 Manager 创建 SharedDataManager 的代理实例
            logger.debug("  创建 SharedDataManager 代理...")
            # 这会调用在 Manager 进程中运行的 SharedDataManager 的 __init__
            self.__shared_data_manager_proxy = self.__data_manager_manager.SharedDataManager()
            logger.debug(f"  SharedDataManager 代理已创建 (类型: {type(self.__shared_data_manager_proxy)})。")

            # 3. 创建用于进程状态的 Manager 和字典
            logger.debug("  创建状态 Manager 和字典...")
            self.__status_manager = Manager()  # 使用标准的 Manager

            # 确保状态字典Manager也是守护进程
            if isinstance(self.__status_manager, BaseManager):
                # 如果status_manager是BaseManager的实例，尝试设置daemon属性
                try:
                    if hasattr(self.__status_manager, '_process') and self.__status_manager._process:
                        self.__status_manager._process.daemon = True
                        logger.debug("  状态Manager进程设置为守护进程模式。")
                except Exception as e:
                    logger.warning(f"  无法将状态Manager设置为守护进程: {e}")

            self.__process_status_dict = self.__status_manager.dict()
            logger.debug("  状态字典 Manager 和代理已创建。")

            # 4. 创建任务队列和停止事件
            logger.debug("  创建任务队列和停止事件...")
            self.task_queue = JoinableQueue()
            self.stop_event = multiprocessing.Event()  # 直接创建 Event
            logger.debug("  任务队列和停止事件已创建。")

        except Exception as e:
            logger.error(f"初始化多进程资源时出错: {e}\n{traceback.format_exc()}")
            self.stop_all(immediate=True)  # 尝试清理已创建的部分资源
            raise RuntimeError(f"无法初始化多进程资源: {e}") from e

        # 检查是否应进入单进程模式
        self.is_single_process_mode = (
            not self.input_data or self.num_processes <= 0  # 处理 num_processes=0 或空数据
        )
        # 如果用户请求了 1 个进程，也认为是单进程模式
        if num_processes == 1 and self.input_data:
            self.is_single_process_mode = True

        # 将输入数据放入任务队列 (仅在多进程模式下需要)
        if not self.is_single_process_mode and self.input_data:
            logger.debug(f"  填充任务队列 ({len(self.input_data)} 项)...")
            if self.task_queue:
                for item in self.input_data:
                    self.task_queue.put(item)
                logger.debug("  任务队列填充完成。")
            else:
                # 这个错误理论上不应发生，因为上面已初始化
                logger.error("任务队列未初始化，无法填充！")
                self.stop_all(immediate=True)
                raise RuntimeError("Task queue not initialized after creation attempt.")
        elif not self.input_data:
            logger.info("  输入数据为空，不填充任务队列。")
        else:  # 单进程模式且有数据
            logger.info("  单进程模式，任务将在 run() 中直接处理，不填充队列。")

        # 如果是单进程模式，不创建工作进程
        if self.is_single_process_mode:
            logger.info("将使用单进程模式执行，不启动额外的工作进程。")
            # 在单进程模式下，num_processes 应视为 0 个外部进程
            self.num_processes = 0  # 重置外部进程计数
            logger.info(f"ManagedMultiProcess 初始化完成，模式: 单进程")
            return  # 单进程模式初始化结束

        # --- 多进程模式：创建并启动工作进程 ---
        logger.info(f"准备启动 {self.num_processes} 个工作进程...")
        try:
            # 检查多进程所需的核心组件
            if self.__shared_data_manager_proxy is None or self.task_queue is None or self.stop_event is None or self.__process_status_dict is None:
                raise RuntimeError("多进程所需的核心组件未完全初始化！")

            for i in range(self.num_processes):
                process_name = f"Worker-{i}"  # 用于日志
                logger.debug(f"  准备启动进程 {process_name}...")
                p = Process(
                    target=_top_level_worker_loop,
                    args=(
                        i,
                        self.task_queue,
                        self.__shared_data_manager_proxy,  # 传递 SharedDataManager 代理
                        self.stop_event,
                        self.__process_status_dict,
                        self.callback_func,
                        self.custom_args,
                        self.kwargs,
                    ),
                    name=process_name,  # 设置进程名
                    daemon=True,
                )
                self.worker_processes.append(p)
                p.start()
                logger.info(f"  工作进程 {i} (PID: {p.pid}, Name: {p.name}) 已启动")

        except Exception as e:
            logger.error(f"启动工作进程时发生错误: {str(e)}\n{traceback.format_exc()}")
            self.stop_all(immediate=True)
            raise RuntimeError(f"无法启动工作进程: {e}") from e

        logger.info(
            f"ManagedMultiProcess 初始化完成，模式: 多进程 ({len(self.worker_processes)} 个工作进程)"
        )

        # --- 内部状态：完成与结果 ---
        self.all_tasks_submitted = False  # 标记是否所有任务已提交
        self.__cached_data = None  # 缓存的共享数据，在 stop_all 时保存

        # 初始化共享数据
        self.__shared_data_cache = {}  # 数据缓存
        self.__last_cache_time = 0  # 缓存时间戳

        # 新增：在主进程中执行回调的必要组件
        self.__main_process_callbacks = {}  # 格式: {key: [callback1, callback2, ...]}
        self.__callback_thread = None  # 主进程回调处理线程
        self.__callback_running.set()  # 初始设置为运行状态
        self.__monitored_keys = set()  # 监控的键集合
        self.__last_values = {}  # 上次的值

        # 缓存创建的绑定回调函数，避免重复创建
        self.__callback_registry = {}  # 格式: {key: 绑定的顶层回调函数}

        # 初始化属性
        self.custom_args = custom_args
        self.kwargs = kwargs

        # 内部状态和控制标志
        self.__status = "初始化中"  # 当前状态
        self.__is_running = False   # 运行标志
        self.__is_started = False   # 是否已启动
        self.__is_stopping = False  # 是否正在停止

        # 监听数据变化的属性
        self.__monitored_keys = set()  # 要监听的键集合
        self.__main_process_callbacks = {}  # 每个键对应的回调函数集
        self.__last_values = {}  # 每个键的最近值
        self.__callback_thread = None  # 回调轮询线程
        self.__callback_running = threading.Event()  # 控制回调线程的事件

        # 添加异步回调队列和处理线程
        self.__callback_queue = queue.Queue()  # 回调任务队列
        self.__callback_worker_thread = None  # 回调工作线程
        self.__callback_worker_running = threading.Event()  # 控制回调工作线程的事件

        # 添加深度监听标志
        self.__use_deep_compare = False  # 是否使用深度比较
        self.__key_paths = {}  # 每个键的路径映射，用于深度监听

        self.__completion_callbacks = []  # 存储完成回调函数
        self.__completion_thread = None   # 完成回调执行线程
        self.__completion_event = threading.Event()  # 完成事件标志

    def __enter__(self):
        """进入上下文管理器时调用，返回实例自身。"""
        logger.debug("ManagedMultiProcess.__enter__ - 进入上下文")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        退出上下文管理器时调用，执行清理操作。

        Args:
            exc_type: 异常类型 (如果 with 块内发生异常)
            exc_val: 异常值
            exc_tb: 异常追踪信息
        """
        logger.debug(f"ManagedMultiProcess.__exit__ - 退出上下文 (异常: {exc_type})")
        # 无论是否发生异常，都尝试优雅地停止所有进程和资源
        # 如果需要根据异常类型决定是否立即停止，可以在此添加逻辑
        self.stop_all(immediate=False)  # 默认进行优雅停止
        logger.debug("ManagedMultiProcess.__exit__ - 清理完成")
        # 返回 False 表示如果发生异常，不抑制异常，让其正常抛出
        # 如果希望抑制异常，可以返回 True
        return False

    def stop_all(self, immediate=False, force_timeout=1.0):
        """
        停止所有工作进程、任务队列和管理器。

        这是首选的停止方法，应在不再需要处理器时调用。

        Args:
            immediate: 如果为True，则立即停止正在进行的处理。
                      如果为False（默认），则等待当前正在处理的任务完成。
            force_timeout: 强制终止前等待的秒数(如果immediate=True)，默认为1.0秒。

        Returns:
            None
        """
        logger.debug(f"ManagedMultiProcess.stop_all - 开始停止处理器 (immediate={immediate})...")

        if not self.__is_started:
            logger.info("ManagedMultiProcess.stop_all - 未启动，无需停止。")
            return

        # 设置停止标志
        self.__is_stopping = True
        self.__is_running = False
        self.__status = "正在停止"

        # 记录开始时间，用于性能分析
        stop_start_time = time.time()

        try:
            # 尝试缓存共享数据以便后续使用
            self.__cache_shared_data()

            # 停止实时通知处理线程
            if hasattr(self, '_ManagedMultiProcess__realtime_notify_running') and self.__realtime_notify_running.is_set():
                logger.debug("停止实时通知处理线程...")
                self.__realtime_notify_running.clear()
                if hasattr(self, '_ManagedMultiProcess__realtime_notify_thread') and self.__realtime_notify_thread and self.__realtime_notify_thread.is_alive():
                    self.__realtime_notify_thread.join(timeout=0.5)
                    if self.__realtime_notify_thread.is_alive():
                        logger.warning("实时通知处理线程未能在超时时间内停止")

            # 停止回调轮询线程 (保留兼容性)
            if hasattr(self, '_ManagedMultiProcess__callback_running') and self.__callback_running.is_set():
                logger.debug("停止回调轮询线程...")
                self.__callback_running.clear()
                if hasattr(self, '_ManagedMultiProcess__callback_thread') and self.__callback_thread and self.__callback_thread.is_alive():
                    self.__callback_thread.join(timeout=0.5)
                    if self.__callback_thread.is_alive():
                        logger.warning("回调轮询线程未能在超时时间内停止")

            # 停止回调工作线程
            if hasattr(self, '_ManagedMultiProcess__callback_worker_running') and self.__callback_worker_running.is_set():
                logger.debug("停止回调工作线程...")
                self.__callback_worker_running.clear()
                if hasattr(self, '_ManagedMultiProcess__callback_worker_thread') and self.__callback_worker_thread and self.__callback_worker_thread.is_alive():
                    # 清空回调队列
                    try:
                        while not self.__callback_queue.empty():
                            self.__callback_queue.get(block=False)
                            self.__callback_queue.task_done()
                    except queue.Empty:
                        pass

                    # 等待线程结束
                    self.__callback_worker_thread.join(timeout=0.5)
                    if self.__callback_worker_thread.is_alive():
                        logger.warning("回调工作线程未能在超时时间内停止")

            # 停止工作进程
            self.__stop_worker_processes(immediate, force_timeout)

            # 关闭管理器
            self.__shutdown_managers()

            # 清理资源
            self.__cleanup_resources()

            # 记录完成时间和总耗时
            stop_end_time = time.time()
            stop_duration = stop_end_time - stop_start_time
            logger.info(f"ManagedMultiProcess.stop_all - 处理器已成功停止，耗时: {stop_duration:.4f}秒")

        except Exception as e:
            logger.error(f"ManagedMultiProcess.stop_all - 停止处理器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

        finally:
            # 确保状态被正确设置，无论是否出现异常
            self.__is_stopping = False
            self.__is_running = False
            self.__status = "已停止"

            # 清理监听数据
            self.__monitored_keys.clear()
            self.__main_process_callbacks.clear()
            self.__last_values.clear()
            self.__key_paths.clear()
            self.__use_deep_compare = False

        # 完成后触发回调
        self.__trigger_completion_callbacks()

    def run(self):
        """
        开始处理所有任务 (已更新以处理 Manager 代理)。

        - **多进程模式**: 非阻塞。确保工作进程已启动。返回 None。
                        **需要手动调用 `wait_all()` 等待完成。**
        - **单进程模式**: 阻塞。在主进程中顺序执行任务。返回最终结果字典。

        Returns:
            Optional[Dict]:
                - 多进程模式: 返回 None (表示任务已提交)。
                - 单进程模式: 最终结果字典 (调用 get_results() 获取)。

        Raises:
            RuntimeError: 如果 SharedDataManager 代理不可用 (单/多进程模式) 或其他初始化问题。
        """
        mode_str = '单进程' if self.is_single_process_mode else '多进程'
        logger.info(f"ManagedMultiProcess.run - 开始执行任务 (模式: {mode_str})...")
        input_len = len(self.input_data) if hasattr(self, 'input_data') else 0
        logger.info(f"  总任务数: {input_len}")

        if not hasattr(self, 'input_data') or not self.input_data:
            logger.info("没有输入任务需要处理。")
            # 即使没有任务，也应该可以获取空结果 (如果是单进程模式)
            if self.is_single_process_mode:
                logger.info("单进程模式无任务，返回空结果字典。")
                # 确保代理存在以便 get_results 工作
                if self.__shared_data_manager_proxy is None:
                    logger.error("SharedDataManager 代理未初始化，无法获取结果。")
                    raise RuntimeError("SharedDataManager proxy not initialized for single-process mode results.")
                return self.get_results()  # 获取空结果
            else:
                logger.info("多进程模式无任务，返回 None。")
                return None  # 多进程无任务直接返回

        # 检查核心组件 (代理) 是否初始化
        if not hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') or self.__shared_data_manager_proxy is None:
            logger.error("SharedDataManager 代理未初始化，无法运行。")
            raise RuntimeError("SharedDataManager proxy not initialized.")

        # --- 单进程模式执行 ---
        if self.is_single_process_mode:
            logger.info("执行单进程模式...")

            # 获取锁代理 (单进程也需要，回调函数可能依赖它)
            lock_proxy = self.__shared_data_manager_proxy.get_lock()
            if lock_proxy is None:
                logger.error("单进程模式错误：无法获取锁代理。")
                raise RuntimeError("Failed to get lock proxy in single process mode.")

            logger.info(f"  开始串行处理 {input_len} 个任务...")

            # 串行处理
            for item_index, item in enumerate(self.input_data):
                task_start_time = time.time()
                logger.info(f"  处理任务 {item_index + 1}/{input_len}: {item}")
                try:
                    # 调用回调，传递 SharedDataManager 代理实例
                    self.callback_func(self.__shared_data_manager_proxy, item, *self.custom_args, **self.kwargs)
                    task_duration = time.time() - task_start_time
                    logger.info(f"  完成任务: {item} (耗时: {task_duration:.4f}s)")
                except Exception as e:
                    task_duration = time.time() - task_start_time
                    error_msg = f"处理任务 {item} 时回调函数出错 (耗时: {task_duration:.4f}s): {str(e)}"
                    logger.error(f"{error_msg}\n{traceback.format_exc()}")

                    # 记录错误到 SharedDataManager (通过代理)
                    try:
                        with lock_proxy:
                            error_detail = {
                                "item": item,
                                "error": str(e),
                                "traceback": traceback.format_exc(),
                                "pid": os.getpid(),  # 主进程 PID
                                "worker_id": "main",  # 标记为主进程
                                "timestamp": time.time()
                            }
                            self.__shared_data_manager_proxy.record_error(error_detail)
                    except Exception as log_err:
                        logger.error(f"单进程模式记录错误时失败: {log_err}\n{traceback.format_exc()}")
            # 获取最终结果
            logger.info("单进程任务处理完成。获取最终结果...")
            final_results = self.get_results()  # get_results 会通过代理获取并转换

            # 单进程模式结束后，管理器仍需关闭，由调用者调用 stop_all() 或依赖 __del__
            logger.info("单进程模式 run() 执行完毕。")
            return final_results

        # --- 多进程模式执行 ---
        else:
            # 检查工作进程是否已创建并可能在运行
            if not hasattr(self, 'worker_processes') or not self.worker_processes:
                logger.error("多进程模式错误：工作进程列表为空或未创建。")
                raise RuntimeError("Worker processes list is empty or not created.")
            # 检查是否所有进程都已意外退出
            if not any(isinstance(p, Process) and p.is_alive() for p in self.worker_processes):
                logger.warning("多进程模式警告：没有活动的子进程！ run() 可能无效。请检查初始化日志。")
                # 仍然可以继续，但任务可能不会被处理

            logger.info("执行多进程模式，任务已提交到工作进程。")
            logger.info("此 run() 调用是非阻塞的。请调用 wait_all() 等待任务完成。")
            return None  # 多进程模式 run 不返回结果

    def wait_all(self, timeout=None):
        """
        等待所有任务完成 (仅在多进程模式下有效)。

        Args:
            timeout (float, optional): 等待的最大时间（秒）。如果为 None，则等待无限长时间。

        Raises:
            TimeoutError: 如果等待队列或进程退出超时。
            RuntimeError: 如果等待过程中发生其他错误，或者在单进程模式下调用。
            ValueError: 如果 timeout 值无效。
        """
        if self.is_single_process_mode:
            logger.warning("wait_all() 在单进程模式下无效，因为 run() 是阻塞的。")
            # 或者可以选择 raise RuntimeError
            # raise RuntimeError("wait_all() cannot be called in single-process mode.")
            return  # 无操作

        if timeout is not None and not isinstance(timeout, (int, float)):
            raise ValueError(f"timeout 必须是数字或 None，而不是 {type(timeout)}")
        if timeout is not None and timeout < 0:
            raise ValueError("timeout 不能为负数")

        logger.info(f"开始等待所有任务处理完成 (超时时间: {timeout if timeout else '无限'})...")
        start_time = time.time()
        original_timeout = timeout

        # 确保停止事件未被设置
        if hasattr(self, 'stop_event') and self.stop_event and self.stop_event.is_set():
            logger.warning("停止事件已被设置，可能无法正常等待任务完成。")

        # 1. 等待任务队列清空 (JoinableQueue.join)
        queue_wait_success = False
        try:
            logger.debug("  等待任务队列 (JoinableQueue.join())...")
            if not hasattr(self, 'task_queue') or self.task_queue is None:
                logger.error("  任务队列不存在，无法等待！")
                raise RuntimeError("Task queue is not available.")

            # 检查队列是否已经为空
            if self.task_queue.empty():
                logger.debug("  任务队列已经为空，无需等待。")
                queue_wait_success = True
            else:
                # 计算此步骤的超时
                queue_timeout = None
                if original_timeout is not None:
                    # 留出一点时间给进程 join
                    time_for_process_join = min(1.0, original_timeout * 0.1) if original_timeout > 0 else 0.1
                    queue_timeout = max(0.01, original_timeout - time_for_process_join)

                # 使用辅助线程执行 join，以便我们可以处理超时
                join_thread = threading.Thread(target=self._ManagedMultiProcess__wait_queue_join, args=(self.task_queue,), daemon=True)
                join_thread.start()
                logger.debug(f"  启动辅助线程 (ID: {join_thread.ident}) 等待队列 join (超时: {queue_timeout})...")

                join_thread.join(timeout=queue_timeout)

                if join_thread.is_alive():
                    # 如果 join 超时
                    logger.warning(f"等待任务队列清空超时 ({queue_timeout} 秒)! 队列可能未处理完。")
                    raise TimeoutError(f"等待任务队列完成超时 ({queue_timeout}s)")
                else:
                    # join 成功返回
                    logger.debug("  任务队列已清空 (JoinableQueue.join 返回)。")
                    queue_wait_success = True

        except TimeoutError:
            # 超时，直接重新抛出
            raise
        except Exception as e:
            logger.error(f"等待任务队列时发生未知错误: {e}\n{traceback.format_exc()}")
            raise RuntimeError(f"等待队列时出错: {e}") from e

        # 2. 当队列清空后，设置停止标志，通知工作进程可以退出了
        if queue_wait_success and hasattr(self, 'stop_event') and self.stop_event and not self.stop_event.is_set():
            try:
                logger.info("任务队列已清空，设置停止标志通知工作进程退出...")
                self.stop_event.set()
            except Exception as e:
                logger.error(f"设置停止标志时出错: {e}")

        # 3. 等待所有工作进程退出 (仅当队列清空后，或无限等待时)
        logger.info("所有任务已在队列层面完成。现在等待所有工作进程自然退出...")

        # 计算剩余时间用于进程 join
        remaining_process_join_timeout = None
        if original_timeout is not None:
            elapsed_time = time.time() - start_time
            remaining_process_join_timeout = max(0.01, original_timeout - elapsed_time)

        # 修复：确保 self.worker_processes 存在且是列表
        if not hasattr(self, 'worker_processes') or not isinstance(self.worker_processes, list):
            logger.warning("worker_processes 属性不存在或不是列表，无法等待进程退出。")
            alive_procs = []
        else:
            # 获取当前仍然存活的进程列表
            alive_procs = [p for p in self.worker_processes if isinstance(p, Process) and p.is_alive()]

        wait_start_time_proc = time.time()  # 记录进程等待开始时间
        while alive_procs:
            # 检查是否整体超时
            if original_timeout is not None:
                current_elapsed_total = time.time() - start_time
                if current_elapsed_total >= original_timeout:
                    logger.warning(f"等待所有工作进程退出超时 ({original_timeout} 秒)! 可能有些进程未完成。")
                    # 在超时情况下设置停止事件，帮助进程能够退出
                    if hasattr(self, 'stop_event') and self.stop_event and not self.stop_event.is_set():
                        try:
                            self.stop_event.set()
                            logger.info("  在超时情况下设置停止事件，帮助进程退出。")
                        except Exception as e:
                            logger.error(f"  设置停止事件时出错: {e}")
                    raise TimeoutError(f"等待所有工作进程退出超时 ({original_timeout}s)")

            # 计算当前轮询的 join 超时
            # 即使总超时是 None，也使用小的轮询超时
            current_join_timeout = 0.1

            # 如果设置了总超时，确保当前 join 不超过剩余时间
            if remaining_process_join_timeout is not None:
                elapsed_proc_wait = time.time() - wait_start_time_proc
                current_remaining = max(0.01, remaining_process_join_timeout - elapsed_proc_wait)
                current_join_timeout = min(current_join_timeout, current_remaining)

            if current_join_timeout <= 0:  # 避免 join 超时为 0 或负数
                current_join_timeout = 0.01

            # logger.debug(f"  轮询等待进程退出，当前 join 超时: {current_join_timeout:.3f}s") # Debug 日志

            next_alive = []
            for p in alive_procs:
                try:
                    p.join(timeout=current_join_timeout)
                    if p.is_alive():
                        next_alive.append(p)
                    else:
                        logger.info(f"    进程 {p.pid} (Name: {p.name}) 已退出 (Exit code: {p.exitcode})。")
                        # 尝试从 self.worker_processes 中移除已退出的进程
                        if hasattr(self, 'worker_processes') and isinstance(self.worker_processes, list):
                            if p in self.worker_processes:
                                self.worker_processes.remove(p)
                except Exception as e:
                    logger.error(f"    等待进程 {p.pid} (Name: {p.name}) 退出时出错: {str(e)}")
                    # 出错也认为它不再活动，不再加入 next_alive
                    if hasattr(self, 'worker_processes') and isinstance(self.worker_processes, list):
                        if p in self.worker_processes:
                            self.worker_processes.remove(p)
            alive_procs = next_alive

            if not alive_procs:
                logger.info("所有工作进程已退出。")
                break

            # 短暂休眠避免CPU空转
            time.sleep(0.05)

        # 4. [新增] 如果使用了watch_shared_data，等待通知队列为空
        all_processes_finished = not self.is_running()
        if all_processes_finished:
            if timeout is None:
                # 无限等待直到通知队列为空
                logger.debug("所有进程已完成，等待实时通知队列清空...")
                while not self.__is_notify_queue_empty():
                    time.sleep(0.1)
            else:
                # 有超时限制，以固定间隔检查通知队列
                remaining_time = 0.01
                if original_timeout is not None:
                    remaining_time = max(0.01, original_timeout - (time.time() - start_time))
                wait_end = time.time() + remaining_time
                while time.time() < wait_end:
                    if self.__is_notify_queue_empty():
                        break
                    time.sleep(0.1)

        # 5. [新增] 触发完成回调
        if all_processes_finished:
            self.__trigger_completion_callbacks()

        logger.info("ManagedMultiProcess.wait_all - 所有任务已完成，所有工作进程已退出。")
        return all_processes_finished

    def _ManagedMultiProcess__wait_queue_join(self, queue_obj):
        """
        私有辅助方法，用于在单独线程中等待队列join完成。

        Args:
            queue_obj: 要等待的JoinableQueue对象
        """
        try:
            queue_obj.join()
            logger.debug("  队列join操作完成。")
        except Exception as e:
            logger.error(f"  队列join过程中出错: {e}\n{traceback.format_exc()}")

    def get_results(self) -> Dict[str, Any]:
        """
        获取所有共享数据的拷贝（普通 Python 字典）。

        此方法是线程/进程安全的。优先尝试从 SharedDataManager 获取最新的实时数据。
        每次成功获取实时数据后，会更新内部缓存。
        如果无法连接到 Manager (例如已通过 stop_all() 关闭或发生连接错误)，则返回最后缓存的数据。

        调用此方法可以实时监控所有共享数据的变化（只要 Manager 处于活动状态）。

        Returns:
            Dict[str, Any]: 包含所有共享数据快照的普通 Python 字典。
                           如果无法获取实时数据且没有缓存，则返回空字典。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例且正在运行)
            while process_manager.is_running():
                current_results = process_manager.get_results()
                print(f"当前实时结果: {current_results}")
                time.sleep(1)

            # 获取最终结果 (可能是来自缓存，如果 stop_all 已被调用)
            final_results = process_manager.get_results()
            print(f"最终结果: {final_results}")
            ```
        """
        logger.debug("ManagedMultiProcess.get_results - 请求获取所有结果...")

        # 1. 检查代理是否有效，尝试获取实时数据
        results = None
        manager_accessible = False  # 重置状态
        if hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') and self.__shared_data_manager_proxy is not None:
            logger.debug("  尝试通过代理获取实时共享数据...")
            try:
                lock_proxy = self.__shared_data_manager_proxy.get_lock()
                if lock_proxy is not None:
                    with lock_proxy:
                        results = self.__shared_data_manager_proxy.get_all_data_as_dict()
                    manager_accessible = True  # 标记成功访问管理器
                    # 获取成功，更新缓存
                    if results is not None:
                        # 总是更新缓存为最新获取的数据
                        self.__cached_data = results  # results 已经是转换后的普通字典
                        logger.debug(f"  成功获取实时数据并更新缓存 (类型: {type(results)})。")
                    else:
                        # get_all_data_as_dict 不应返回 None，但以防万一
                        logger.warning("  通过代理获取的数据为 None，缓存可能未更新。")
                        # 保留旧缓存或初始化为空字典
                        if not hasattr(self, '_ManagedMultiProcess__cached_data') or self.__cached_data is None:
                            self.__cached_data = {}
                else:
                    logger.warning("  无法获取共享锁代理，无法获取实时数据。")
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                logger.warning(f"  获取实时数据时连接管理器失败: {conn_err}。将尝试使用缓存数据。", exc_info=False)
                # 连接失败，manager_accessible 保持 False
            except Exception as e:
                logger.error(f"  获取实时数据时发生意外错误: {e}\n{traceback.format_exc()}")
                # 发生其他错误，manager_accessible 保持 False
        else:
            logger.info("  共享数据代理无效，将尝试使用缓存数据。")

        # 2. 如果无法访问 Manager 获取实时数据 (manager_accessible is False)
        #    或者获取到的实时数据是 None (异常情况)，则尝试返回缓存数据
        if not manager_accessible or results is None:
            logger.debug("  无法获取实时数据或获取结果为None，尝试返回缓存数据...")
            if hasattr(self, '_ManagedMultiProcess__cached_data') and self.__cached_data is not None:
                logger.info("  返回缓存的共享数据结果。")
                # 返回缓存数据的副本，确保内部缓存不被外部修改
                # __cached_data 存储的应该是普通字典
                return self.__cached_data.copy() if isinstance(self.__cached_data, dict) else dict(self.__cached_data)
            else:
                logger.warning("  无法获取实时数据且缓存无效，返回空字典。")
                return {}  # 既无实时数据也无缓存，返回空字典
        else:
            # Manager 可访问且成功获取了 results，直接返回它 (它已经是普通字典)
            return results

    def get_shared_value(self, key: str, default: Any = None) -> Any:
        """
        从共享数据中获取指定键的值。

        此方法是线程/进程安全的。优先尝试从 SharedDataManager 获取最新的实时数据。
        每次成功获取实时数据后，会更新内部缓存中对应键的值。
        如果无法连接到 Manager，则返回最后缓存的数据中对应键的值。
        注意：返回的可能是基础类型或 Manager 代理本身。

        Args:
            key (str): 要获取值的键名。
            default (Any, optional): 如果键不存在于实时数据和缓存中，或无法获取，返回的默认值。默认为 None。

        Returns:
            Any: 如果键存在则返回对应的值，否则返回 default 值。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例且正在运行)
            name = None
            while name != "LXL": # 假设工作进程会设置 name="LXL"
                name = process_manager.get_shared_value('name')
                print(f"当前 name: {name}")
                time.sleep(0.5)

            # 获取最终值 (可能是来自缓存)
            final_name = process_manager.get_shared_value('name')
            print(f"最终 name: {final_name}")
            ```
        """
        logger.debug(f"ManagedMultiProcess.get_shared_value - 请求获取键 '{key}' 的值...")

        # 1. 尝试获取实时数据
        value = default  # 默认为 default 值
        retrieved_realtime = False  # 标记是否成功从 Manager 获取到值（即使键不存在也算成功访问）
        if hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') and self.__shared_data_manager_proxy is not None:
            logger.debug(f"  尝试通过代理获取键 '{key}' 的实时值...")
            try:
                lock_proxy = self.__shared_data_manager_proxy.get_lock()
                if lock_proxy is not None:
                    with lock_proxy:
                        # 使用一个哨兵对象来区分键不存在和值为 None 的情况
                        _sentinel = object()
                        retrieved_value = self.__shared_data_manager_proxy.get_value(key, default=_sentinel)
                        retrieved_realtime = True  # 标记成功访问管理器
                        if retrieved_value is not _sentinel:
                            value = retrieved_value  # 获取到了实际值
                            # 更新缓存
                            if not hasattr(self, '_ManagedMultiProcess__cached_data') or self.__cached_data is None:
                                self.__cached_data = {}
                            self.__cached_data[key] = value  # 直接缓存获取到的值
                            logger.debug(f"  成功获取键 '{key}' 的实时值并更新缓存 (类型: {type(value)})。")
                        else:
                            # 键不存在于实时数据中
                            value = default  # 确认返回 default
                            # 如果缓存中存在这个键，可以选择移除或保留旧值，这里选择保留
                            logger.debug(f"  键 '{key}' 在实时数据中不存在，将返回默认值。缓存中对应的值（如果有）不会被清除。")
                else:
                    logger.warning(f"  无法获取共享锁代理 (获取 '{key}')。")
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                logger.warning(f"  获取键 '{key}' 时连接管理器失败: {conn_err}。将尝试使用缓存。", exc_info=False)
                # 连接失败，retrieved_realtime 保持 False
            except Exception as e:
                logger.error(f"  获取键 '{key}' 时发生意外错误: {e}\n{traceback.format_exc()}")
                # 其他错误，retrieved_realtime 保持 False
        else:
            logger.info(f"  共享数据代理无效 (获取 '{key}')。")

        # 2. 如果无法访问 Manager 获取实时数据，则尝试从缓存获取
        if not retrieved_realtime:
            logger.debug(f"  无法获取键 '{key}' 的实时数据，尝试从缓存获取...")
            if hasattr(self, '_ManagedMultiProcess__cached_data') and self.__cached_data is not None and key in self.__cached_data:
                logger.info(f"  返回缓存中键 '{key}' 的值。")
                # 注意：缓存中的值可能是代理对象，这里直接返回
                return self.__cached_data.get(key, default)
            else:
                logger.warning(f"  无法获取实时数据且缓存中无此键 '{key}'，返回默认值。")
                return default
        else:
            # Manager 可访问，直接返回从 Manager 获取到的值 (或键不存在时的 default)
            # value 在前面已确定
            return value

    def get_shared_list(self, key: str, default: Optional[List[Any]] = None) -> Optional[List[Any]]:
        """
        获取共享数据中指定键关联的列表的拷贝（普通 Python 列表）。

        此方法是线程/进程安全的。优先尝试从 SharedDataManager 获取最新的实时数据。
        每次成功获取实时列表后，会更新内部缓存中对应键的列表（副本）。
        如果无法连接到 Manager，则返回最后缓存的列表（副本）。

        Args:
            key (str): 共享字典中的键名。
            default (Optional[List[Any]], optional): 如果键不存在、值不是列表或无法获取，返回的默认值。
                                                     默认为 None。

        Returns:
            Optional[List[Any]]: 如果键存在且值为列表，则返回列表的普通 Python 拷贝；
                               否则返回 `default`。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例且正在运行)
            processed_count = 0
            while process_manager.is_running():
                current_ids = process_manager.get_shared_list('processed_ids', default=[])
                if len(current_ids) > processed_count:
                    print(f"新增处理的 IDs: {current_ids[processed_count:]}")
                    processed_count = len(current_ids)
                time.sleep(1)

            # 获取最终列表 (可能是来自缓存)
            final_ids = process_manager.get_shared_list('processed_ids', default=[])
            print(f"最终处理的 IDs: {final_ids}")
            ```
        """
        logger.debug(f"ManagedMultiProcess.get_shared_list - 请求获取键 '{key}' 的列表拷贝...")

        # 1. 尝试获取实时数据
        list_copy = None
        retrieved_realtime = False  # 标记是否成功访问 Manager 并获取数据
        if hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') and self.__shared_data_manager_proxy is not None:
            logger.debug(f"  尝试通过代理获取键 '{key}' 的实时列表...")
            try:
                lock_proxy = self.__shared_data_manager_proxy.get_lock()
                if lock_proxy is not None:
                    with lock_proxy:
                        # 直接调用代理的 get_list
                        retrieved_list = self.__shared_data_manager_proxy.get_list(key, default=default)
                        retrieved_realtime = True  # 标记成功访问管理器
                        # 检查返回的是否是列表或者默认值 None
                        if isinstance(retrieved_list, list) or retrieved_list is default:
                            list_copy = retrieved_list  # 获取到了列表或确认应返回默认值
                            # 如果获取到了实际列表 (非默认值)，更新缓存
                            if list_copy is not default and list_copy is not None:
                                # 确保缓存字典存在
                                if not hasattr(self, '_ManagedMultiProcess__cached_data') or self.__cached_data is None:
                                    self.__cached_data = {}
                                # 缓存列表的副本
                                self.__cached_data[key] = list_copy.copy()  # list_copy 已经是普通列表
                                logger.debug(f"  成功获取键 '{key}' 的实时列表并更新缓存。")
                            elif list_copy is default:
                                logger.debug(f"  键 '{key}' 在实时数据中不存在或不是列表，将返回默认值。")
                        else:
                            # get_list 返回了非列表也非默认值，这是异常情况
                            logger.error(f"  代理 get_list 返回了意外类型 {type(retrieved_list)}，期望 list 或 default。将返回默认值。")
                            list_copy = default  # 强制返回默认值
                else:
                    logger.warning(f"  无法获取共享锁代理 (获取 '{key}')。")
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                logger.warning(f"  获取列表 '{key}' 时连接管理器失败: {conn_err}。将尝试使用缓存。", exc_info=False)
                # 连接失败，retrieved_realtime 保持 False
            except Exception as e:
                logger.error(f"  获取列表 '{key}' 时发生意外错误: {e}\n{traceback.format_exc()}")
                # 其他错误，retrieved_realtime 保持 False
        else:
            logger.info(f"  共享数据代理无效 (获取 '{key}')。")

        # 2. 如果无法访问 Manager 获取实时数据，则尝试从缓存获取
        if not retrieved_realtime:
            logger.debug(f"  无法获取键 '{key}' 的实时列表，尝试从缓存获取...")
            if hasattr(self, '_ManagedMultiProcess__cached_data') and self.__cached_data is not None and key in self.__cached_data:
                cached_value = self.__cached_data.get(key)
                if isinstance(cached_value, list):
                    logger.info(f"  返回缓存中键 '{key}' 的列表副本。")
                    return cached_value.copy()  # 返回副本
                else:
                    logger.warning(f"  缓存中的键 '{key}' 不是列表类型，返回默认值。")
                    return default
            else:
                logger.warning(f"  无法获取实时数据且缓存中无此键 '{key}'，返回默认值。")
                return default
        else:
            # Manager 可访问，直接返回从 Manager 获取到的列表拷贝 (或 default)
            # list_copy 在前面已确定
            # 如果 list_copy 不是 None，它已经是普通列表拷贝
            return list_copy

    def get_shared_set(self, key: str, default: Optional[Set[Any]] = None) -> Optional[Set[Any]]:
        """
        获取共享数据中指定键关联的集合的拷贝（普通 Python set）。

        此方法是线程/进程安全的。优先尝试从 SharedDataManager 获取最新的实时数据。
        每次成功获取实时集合后，会更新内部缓存中对应键的集合（作为列表副本存储）。
        如果无法连接到 Manager，则返回最后缓存的数据（转换后的集合副本）。
        集合在内部通常由 Manager 列表模拟。

        Args:
            key (str): 共享字典中的键名。
            default (Optional[Set[Any]], optional): 如果键不存在、值不是列表/集合或无法获取/转换，返回的默认值。
                                                  默认为 None。

        Returns:
            Optional[Set[Any]]: 如果键存在且值为列表/集合，则返回包含列表/集合元素的普通 Python set 拷贝；
                              否则返回 `default`。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例且正在运行)
            unique_users = set()
            while process_manager.is_running():
                current_users = process_manager.get_shared_set('unique_users', default=set())
                new_users = current_users - unique_users
                if new_users:
                    print(f"发现新用户: {new_users}")
                    unique_users.update(new_users)
                time.sleep(1)

            # 获取最终集合 (可能是来自缓存)
            final_users = process_manager.get_shared_set('unique_users', default=set())
            print(f"最终所有用户: {final_users}")
            ```
        """
        logger.debug(f"ManagedMultiProcess.get_shared_set - 请求获取键 '{key}' 的集合拷贝...")

        # 1. 尝试获取实时数据
        set_copy = None
        retrieved_realtime = False  # 标记是否成功访问 Manager 并获取数据
        if hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') and self.__shared_data_manager_proxy is not None:
            logger.debug(f"  尝试通过代理获取键 '{key}' 的实时集合...")
            try:
                lock_proxy = self.__shared_data_manager_proxy.get_lock()
                if lock_proxy is not None:
                    with lock_proxy:
                        # 直接调用代理的 get_set
                        retrieved_set = self.__shared_data_manager_proxy.get_set(key, default=default)
                        retrieved_realtime = True  # 标记成功访问管理器
                        # 检查返回的是否是集合或者默认值 None
                        if isinstance(retrieved_set, set) or retrieved_set is default:
                            set_copy = retrieved_set  # 获取到了集合或确认应返回默认值
                            # 如果获取到了实际集合 (非默认值)，更新缓存（存列表形式）
                            if set_copy is not default and set_copy is not None:
                                # 确保缓存字典存在
                                if not hasattr(self, '_ManagedMultiProcess__cached_data') or self.__cached_data is None:
                                    self.__cached_data = {}
                                # 尝试获取原始列表进行缓存，以保持一致性
                                source_list = self.__shared_data_manager_proxy.get_list(key, default=None)
                                if source_list is not None and isinstance(source_list, list):
                                    self.__cached_data[key] = source_list.copy()  # 缓存列表副本
                                    logger.debug(f"  成功获取键 '{key}' 的实时集合并更新缓存（列表形式）。")
                                else:
                                    # 如果无法获取原始列表，则缓存集合转换后的列表
                                    self.__cached_data[key] = list(set_copy)
                                    logger.debug(f"  成功获取键 '{key}' 的实时集合，但无法获取原始列表，缓存转换后的列表。")
                            elif set_copy is default:
                                logger.debug(f"  键 '{key}' 在实时数据中不存在或不是集合/列表，将返回默认值。")
                        else:
                            # get_set 返回了非集合也非默认值，这是异常情况
                            logger.error(f"  代理 get_set 返回了意外类型 {type(retrieved_set)}，期望 set 或 default。将返回默认值。")
                            set_copy = default  # 强制返回默认值
                else:
                    logger.warning(f"  无法获取共享锁代理 (获取 '{key}')。")
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                logger.warning(f"  获取集合 '{key}' 时连接管理器失败: {conn_err}。将尝试使用缓存。", exc_info=False)
                # 连接失败，retrieved_realtime 保持 False
            except Exception as e:
                logger.error(f"  获取集合 '{key}' 时发生意外错误: {e}\n{traceback.format_exc()}")
                # 其他错误，retrieved_realtime 保持 False
        else:
            logger.info(f"  共享数据代理无效 (获取 '{key}')。")

        # 2. 如果无法访问 Manager 获取实时数据，则尝试从缓存获取
        if not retrieved_realtime:
            logger.debug(f"  无法获取键 '{key}' 的实时集合，尝试从缓存获取...")
            if hasattr(self, '_ManagedMultiProcess__cached_data') and self.__cached_data is not None and key in self.__cached_data:
                cached_value = self.__cached_data.get(key)
                # 缓存中存储的是列表或集合
                if isinstance(cached_value, list):
                    logger.info(f"  返回缓存中键 '{key}' 的集合副本 (从列表转换)。")
                    try:
                        return set(cached_value)  # 转换时可能出错
                    except TypeError:
                        logger.warning(f"  缓存中的列表 '{key}' 包含不可哈希项，无法转为集合，返回默认值。")
                        return default
                elif isinstance(cached_value, set):
                    logger.info(f"  返回缓存中键 '{key}' 的集合副本。")
                    return cached_value.copy()  # 返回集合副本
                else:
                    logger.warning(f"  缓存中的键 '{key}' 不是列表或集合类型，返回默认值。")
                    return default
            else:
                logger.warning(f"  无法获取实时数据且缓存中无此键 '{key}'，返回默认值。")
                return default
        else:
            # Manager 可访问，直接返回从 Manager 获取到的集合拷贝 (或 default)
            # set_copy 在前面已确定
            # 如果 set_copy 不是 None，它已经是普通 set 拷贝
            return set_copy

    def get_all_errors(self) -> List[Dict[str, Any]]:
        """
        获取所有记录的错误信息的拷贝（普通 Python 列表）。

        此方法是线程/进程安全的。优先尝试从 SharedDataManager 获取最新的实时数据。
        每次成功获取实时错误列表后，会更新内部缓存。
        如果无法连接到 Manager，则返回最后缓存的错误列表（副本）。

        Returns:
            List[Dict[str, Any]]: 包含所有错误信息字典的普通 Python 列表。
                                 如果无法获取实时数据且没有缓存，则返回空列表。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例且正在运行)
            known_errors = []
            while process_manager.is_running():
                current_errors = process_manager.get_all_errors()
                if len(current_errors) > len(known_errors):
                     print(f"发现新错误: {current_errors[len(known_errors):]}")
                     known_errors = current_errors # 更新已知错误列表
                time.sleep(2)

            # 获取最终错误列表 (可能是来自缓存)
            final_errors = process_manager.get_all_errors()
            print(f"最终所有错误: {final_errors}")
            ```
        """
        logger.debug("ManagedMultiProcess.get_all_errors - 请求获取所有错误...")

        # 使用 SharedDataManager 的错误列表键名
        errors_key = SharedDataManager._SharedDataManager__DEFAULT_ERRORS_KEY

        # 1. 尝试获取实时数据
        errors_list = None
        retrieved_realtime = False  # 标记是否成功访问 Manager 并获取数据
        if hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') and self.__shared_data_manager_proxy is not None:
            logger.debug("  尝试通过代理获取实时错误列表...")
            try:
                lock_proxy = self.__shared_data_manager_proxy.get_lock()
                if lock_proxy is not None:
                    with lock_proxy:
                        # 直接调用代理的 get_errors
                        retrieved_errors = self.__shared_data_manager_proxy.get_errors()
                        retrieved_realtime = True  # 标记成功访问管理器
                        # get_errors 应该总是返回列表 (可能为空)
                        if isinstance(retrieved_errors, list):
                            errors_list = retrieved_errors  # 获取到了列表
                            # 更新缓存
                            # 确保缓存字典存在
                            if not hasattr(self, '_ManagedMultiProcess__cached_data') or self.__cached_data is None:
                                self.__cached_data = {}
                            # 缓存错误列表的副本
                            self.__cached_data[errors_key] = errors_list.copy()  # errors_list 已经是普通列表
                            logger.debug("  成功获取实时错误列表并更新缓存。")
                        else:
                            # 异常情况: get_errors 未返回列表
                            logger.error(f"  代理 get_errors 返回了意外类型 {type(retrieved_errors)}，期望 list。将返回空列表。")
                            errors_list = []  # 强制返回空列表
                else:
                    logger.warning("  无法获取共享锁代理 (获取错误列表)。")
            except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
                logger.warning(f"  获取错误列表时连接管理器失败: {conn_err}。将尝试使用缓存。", exc_info=False)
                # 连接失败，retrieved_realtime 保持 False
            except Exception as e:
                logger.error(f"  获取错误列表时发生意外错误: {e}\n{traceback.format_exc()}")
                # 其他错误，retrieved_realtime 保持 False
        else:
            logger.info("  共享数据代理无效 (获取错误列表)。")

        # 2. 如果无法访问 Manager 获取实时数据，则尝试从缓存获取
        if not retrieved_realtime:
            logger.debug("  无法获取实时错误列表，尝试从缓存获取...")
            if hasattr(self, '_ManagedMultiProcess__cached_data') and self.__cached_data is not None and errors_key in self.__cached_data:
                cached_errors = self.__cached_data.get(errors_key)
                if isinstance(cached_errors, list):
                    logger.info("  返回缓存中的错误列表副本。")
                    return cached_errors.copy()  # 返回副本
                else:
                    logger.warning("  缓存中的错误记录不是列表类型，返回空列表。")
                    return []
            else:
                logger.warning("  无法获取实时数据且缓存中无错误记录，返回空列表。")
                return []
        else:
            # Manager 可访问，直接返回从 Manager 获取到的错误列表 (或空列表)
            # errors_list 在前面已确定
            return errors_list if isinstance(errors_list, list) else []

    def get_status(self) -> Dict[int, str]:
        """
        获取当前所有工作进程的状态字典的拷贝。

        字典的键是进程ID (PID)，值是表示进程状态的字符串
        （例如 'running', 'processing: task_data', 'stopped_signal', 'error', 'completed'等）。

        注意：状态字典本身是通过另一个 Manager 管理的，此方法访问该 Manager 的代理。
        返回的是普通 Python 字典拷贝。

        Returns:
            Dict[int, str]: 包含进程ID和状态的字典。
                            如果状态管理器未初始化或 Manager 已关闭，返回空字典。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例)
            # 在调用 run() 后，可以轮询状态
            status = process_manager.get_status()
            print("当前进程状态:", status)
            # 可能输出: {12345: 'running', 12346: 'processing: item_3', 12347: 'running'}
            ```
        """
        logger.debug("ManagedMultiProcess.get_status - 请求获取进程状态...")
        if not hasattr(self, '_ManagedMultiProcess__process_status_dict') or self.__process_status_dict is None:
            logger.warning("ManagedMultiProcess.get_status - 进程状态字典代理未初始化或已失效，返回空字典。")
            return {}

        try:
            # Manager 字典代理的访问通常是进程安全的，但获取拷贝以避免迭代问题
            status_copy = dict(self.__process_status_dict)
            logger.debug(f"  成功获取状态字典拷贝 (共 {len(status_copy)} 个进程)。")
            return status_copy
        except (EOFError, BrokenPipeError, ConnectionRefusedError) as conn_err:
            logger.error(f"ManagedMultiProcess.get_status - 与状态 Manager 连接中断: {conn_err}")
            return {}
        except Exception as e:
            # 连接断开等问题可能导致访问代理失败
            logger.error(f"ManagedMultiProcess.get_status - 获取状态时出错: {e}\n{traceback.format_exc()}")
            return {}  # 出错时返回空字典，符合类型签名

    def is_running(self) -> bool:
        """
        检查是否至少有一个工作进程仍在运行。

        Returns:
            bool: 如果有任何工作进程存活则返回 True，否则返回 False。

        示例:
            ```python
            # (假设 process_manager 是 ManagedMultiProcess 实例)
            process_manager.run()
            # ... 等待一段时间 ...
            if process_manager.is_running():
                print("至少有一个工作进程仍在运行。")
            else:
                print("所有工作进程都已停止。")
            ```
        """
        logger.debug("ManagedMultiProcess.is_running - 检查是否有活动进程...")
        # 检查进程列表是否存在且有效
        if not hasattr(self, 'worker_processes') or not isinstance(self.worker_processes, list) or not self.worker_processes:
            logger.debug("  工作进程列表无效或为空，认为没有进程在运行。")
            return False

        try:
            # 遍历检查每个进程的 is_alive() 状态
            for p in self.worker_processes:
                if isinstance(p, Process) and p.is_alive():
                    logger.debug(f"  发现活动进程 (PID: {p.pid}, Name: {p.name})，返回 True。")
                    return True
            # 如果循环完成没有找到活动进程
            logger.debug("  未发现活动进程，返回 False。")
            return False
        except Exception as e:
            # 处理可能的异常，例如进程对象状态异常
            logger.error(f"ManagedMultiProcess.is_running - 检查进程状态时出错: {e}\n{traceback.format_exc()}")
            return False  # 出错时保守返回 False

    # ================== 私有辅助方法 ==================

    def __cache_shared_data(self):
        """
        从共享数据管理器读取当前值并缓存到本地，以减少后续请求。
        """
        logger.debug("ManagedMultiProcess.__cache_shared_data - 开始缓存共享数据...")

        # 如果共享数据管理器不可用，返回
        if not hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') or not self.__shared_data_manager_proxy:
            logger.warning("无法缓存共享数据：共享数据管理器不可用")
            return

        try:
            # 获取锁
            lock = self.__shared_data_manager_proxy.get_lock()
            with lock:
                # 获取所有数据
                all_data = self.__shared_data_manager_proxy.get_all_data_as_dict()

                # 更新缓存
                self.__cached_shared_values = all_data
                logger.debug(f"已缓存 {len(all_data)} 项共享数据")
        except Exception as e:
            logger.error(f"缓存共享数据时出错: {e}", exc_info=True)
            import traceback
            logger.error(traceback.format_exc())

    def __deep_compare(self, value1, value2):
        """
        深度比较两个值，适用于嵌套的数据结构。

        Args:
            value1: 第一个值
            value2: 第二个值

        Returns:
            bool: 如果两个值在深度比较下相等，则返回True；否则返回False
            list: 变化的路径列表，格式为 ['key1', 'key1.subkey1', 'key1.list[0]'] 等
        """
        logger.debug(f"深度比较数据: {type(value1)} vs {type(value2)}")

        # 记录变化的路径
        changes = []

        def _compare(val1, val2, path=""):
            # 类型不同，直接认为不同
            if not isinstance(val1, type(val2)):
                changes.append(path)
                return False

            # 如果是字典，递归比较每一项
            if isinstance(val1, dict):
                if set(val1.keys()) != set(val2.keys()):
                    # 键集合不同
                    changes.append(path)
                    # 记录具体哪些键不同
                    for k in set(val1.keys()) - set(val2.keys()):
                        changes.append(f"{path}.{k}" if path else k)
                    for k in set(val2.keys()) - set(val1.keys()):
                        changes.append(f"{path}.{k}" if path else k)
                    return False

                result = True
                for k in val1:
                    current_path = f"{path}.{k}" if path else k
                    if not _compare(val1[k], val2[k], current_path):
                        result = False
                return result

            # 如果是列表或元组，递归比较每一项
            elif isinstance(val1, (list, tuple)):
                if len(val1) != len(val2):
                    changes.append(path)
                    return False

                result = True
                for i, (item1, item2) in enumerate(zip(val1, val2)):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    if not _compare(item1, item2, current_path):
                        result = False
                return result

            # 集合类型比较
            elif isinstance(val1, set):
                if val1 != val2:
                    changes.append(path)
                    return False
                return True

            # 基本类型直接比较
            else:
                if val1 != val2:
                    changes.append(path)
                    return False
                return True

        result = _compare(value1, value2)
        return result, changes

    def __stop_worker_processes(self, immediate: bool, force_timeout: float):
        """私有方法：停止所有工作进程"""
        # TODO: 将 stop_all 中停止进程的逻辑移到这里
        logger.debug("  stop_all: __stop_worker_processes - 停止工作进程...")
        # 检查 self.worker_processes 是否存在且是列表
        if not self.is_single_process_mode:
            if not hasattr(self, 'worker_processes') or not isinstance(self.worker_processes, list) or not self.worker_processes:
                logger.info("  stop_all: 没有活动的工作进程需要停止。")
                return  # 明确返回

            logger.info(f"  stop_all: 开始停止 {len(self.worker_processes)} 个工作进程...")
            # 1. 设置停止标志 (用于优雅停止)
            if not immediate and hasattr(self, 'stop_event') and self.stop_event:
                if not self.stop_event.is_set():
                    try:
                        self.stop_event.set()
                        logger.info("  stop_all: 停止信号 (Event) 已设置。")
                    except Exception as e:
                        logger.error(f"  stop_all: 设置停止事件时出错: {e}", exc_info=True)
                else:
                    logger.info("  stop_all: 停止信号 (Event) 已被设置或不存在。")
            elif immediate:
                logger.info("  stop_all: 立即停止模式，不依赖停止事件。")
            else:  # 优雅停止但 stop_event 无效
                logger.warning("  stop_all: 停止事件 (stop_event) 不存在或未初始化，无法发送优雅停止信号。将尝试 join。")

            # 2. 终止或等待进程
            processes_to_stop = list(self.worker_processes)  # 操作副本
            terminated_count = 0
            joined_count = 0

            if immediate:
                logger.warning("  stop_all: 启用立即终止模式 (发送 SIGTERM)。")
                for p in processes_to_stop:
                    if isinstance(p, Process) and p.is_alive():
                        try:
                            logger.warning(f"    stop_all: 立即终止进程 {p.pid} (Name: {p.name})...")
                            p.terminate()  # 尝试终止
                            terminated_count += 1
                            # 短暂 join 尝试让操作系统回收资源
                            p.join(timeout=0.1)
                            if p.is_alive():
                                logger.warning(f"    stop_all: 进程 {p.pid} 在 terminate 后仍存活。")
                        except Exception as term_err:
                            logger.error(f"    stop_all: 终止进程 {p.pid} 时出错: {term_err}", exc_info=True)
                    elif isinstance(p, Process):
                        logger.debug(f"    stop_all: 进程 {p.pid} (Name: {p.name}) 已停止。")
                    else:
                        logger.warning(f"    stop_all: 发现无效的进程对象: {p}")
                logger.info(f"  stop_all: 立即终止操作完成，尝试终止 {terminated_count} 个进程。")
                self.worker_processes = []  # 清空列表，因为状态可能不可靠

            else:  # 优雅停止模式 (immediate=False)
                logger.info(f"  stop_all: 启用优雅停止模式，等待进程自行退出 (总超时 {force_timeout:.2f} 秒)...")
                start_time = time.time()
                # 需要操作原始列表以移除已退出的进程
                # 使用索引迭代以安全移除
                i = 0
                while i < len(self.worker_processes):
                    p = self.worker_processes[i]
                    if not isinstance(p, Process) or not p.is_alive():
                        logger.debug(f"  stop_all: 移除已停止或无效的进程 {p} 从列表。")
                        self.worker_processes.pop(i)
                        continue  # 不增加 i

                    current_time = time.time()
                    elapsed_time = current_time - start_time
                    if elapsed_time >= force_timeout:
                        logger.warning(f"  stop_all: 优雅停止超时 ({force_timeout:.2f}s)，将强制终止剩余 {len(self.worker_processes) - i} 个进程。")
                        # 对剩余进程执行立即终止
                        remaining_processes = self.worker_processes[i:]
                        for rem_p in remaining_processes:
                            if isinstance(rem_p, Process) and rem_p.is_alive():
                                try:
                                    logger.warning(f"    stop_all: 超时后强制终止进程 {rem_p.pid} (Name: {rem_p.name})...")
                                    rem_p.terminate()
                                    rem_p.join(timeout=0.1)  # 短暂 join
                                except Exception as term_err:
                                    logger.error(f"    stop_all: 强制终止进程 {rem_p.pid} 时出错: {term_err}", exc_info=True)
                        self.worker_processes = self.worker_processes[:i]  # 只保留已成功 join 的
                        break  # 退出 while 循环

                    # 计算当前 join 超时
                    remaining_time = force_timeout - elapsed_time
                    join_timeout = max(0.01, min(0.2, remaining_time))  # 使用较小的轮询超时

                    try:
                        # logger.debug(f"    stop_all: 尝试 join 进程 {p.pid} (超时: {join_timeout:.2f}s)...") # Debug 日志
                        p.join(timeout=join_timeout)
                        if not p.is_alive():
                            logger.info(f"    stop_all: 进程 {p.pid} (Name: {p.name}) 已优雅退出 (Exit code: {p.exitcode})。")
                            joined_count += 1
                            self.worker_processes.pop(i)  # 从列表中移除
                            continue  # 处理下一个（原来的 i 位置）
                    except Exception as join_err:
                        logger.error(f"    stop_all: 等待进程 {p.pid} 退出时出错: {join_err}", exc_info=True)
                        self.worker_processes.pop(i)  # 出错也移除
                        continue

                    i += 1  # 只有当进程仍在活动且未移除时才增加 i

                # 循环结束后检查
                if not any(p.is_alive() for p in self.worker_processes if isinstance(p, Process)):
                    logger.info(f"  stop_all: 所有工作进程已成功停止 (优雅退出 {joined_count} 个)。")
                    self.worker_processes = []  # 确认清空

            # 最后再次检查，以防万一
            final_alive = [p for p in self.worker_processes if isinstance(p, Process) and p.is_alive()]
            if final_alive:
                logger.warning(f"  stop_all: 停止操作后仍有 {len(final_alive)} 个进程存活: {[p.pid for p in final_alive]}。可能需要手动干预。")
            else:
                logger.info("  stop_all: 确认所有工作进程已停止。")
                self.worker_processes = []  # 确保清空

        else:  # 单进程模式
            logger.info("  stop_all: 单进程模式，没有外部工作进程需要停止。")

    def __shutdown_managers(self):
        """私有方法：关闭所有 Manager"""
        logger.debug("  stop_all: __shutdown_managers - 关闭 Managers...")
        # 关闭 DataManagerManager (管理 SharedDataManager 实例)
        if hasattr(self, '_ManagedMultiProcess__data_manager_manager') and self.__data_manager_manager:
            logger.info("  stop_all: 开始关闭 DataManagerManager...")
            try:
                # 确保在关闭前关闭所有代理对象的引用 (以防万一)
                self.__shared_data_manager_proxy = None
                # 现在可以安全地关闭管理器
                self.__data_manager_manager.shutdown()
                logger.info("  stop_all: DataManagerManager 已关闭。")
            except Exception as e:
                logger.error(f"  stop_all: 关闭 DataManagerManager 时出错: {e}", exc_info=True)
        else:
            logger.info("  stop_all: DataManagerManager 不存在或已关闭。")
        self.__data_manager_manager = None  # 显式置 None
        self.__shared_data_manager_proxy = None  # 代理也失效了

        # 关闭 status_manager
        if hasattr(self, '_ManagedMultiProcess__status_manager') and self.__status_manager:
            logger.info("  stop_all: 开始关闭状态 Manager...")
            try:
                # 确保先解除对状态字典的引用 (以防万一)
                self.__process_status_dict = None
                # 关闭管理器
                self.__status_manager.shutdown()
                logger.info("  stop_all: 状态 Manager 已关闭。")
            except Exception as e:
                logger.error(f"  stop_all: 关闭状态 Manager 时出错: {e}", exc_info=True)
        else:
            logger.info("  stop_all: 状态 Manager 不存在或已关闭。")
        self.__status_manager = None  # 显式置 None
        self.__process_status_dict = None  # 代理也失效了

    def __cleanup_resources(self):
        """私有方法：清理队列和事件"""
        logger.debug("  stop_all: __cleanup_resources - 清理队列和事件...")
        # 清理任务队列
        if hasattr(self, 'task_queue') and self.task_queue:
            # 不再需要检查 is_alive，因为 Manager 可能已关闭
            # 只需尝试清空本地队列对象即可
            logger.debug("  stop_all: 尝试清空任务队列...")
            cleared_count = 0
            try:
                while True:  # 不再使用 not empty() 因为它可能访问 Manager
                    try:
                        # 使用 get_nowait 避免阻塞
                        self.task_queue.get_nowait()
                        # 需要调用 task_done 来平衡 JoinableQueue 的内部计数
                        self.task_queue.task_done()
                        cleared_count += 1
                    except queue.Empty:  # 使用导入的 queue.Empty
                        logger.debug(f"  stop_all: 任务队列已清空 (移除了 {cleared_count} 项)。")
                        break
                    except (EOFError, OSError):  # EOFError 或 OSError 可能在连接断开时发生
                        logger.warning("  stop_all: 清空任务队列时连接断开，停止清空。")
                        break
                    except Exception as q_err:  # 捕获其他可能的异常
                        logger.error(f"  stop_all: 清空任务队列时发生内部错误: {q_err}", exc_info=True)
                        break  # 发生错误，停止清空
            except AttributeError:
                # 如果 task_queue 已经是 None (例如 stop_all 重复调用)
                logger.debug("  stop_all: 任务队列已被清理。")
            except Exception as e:
                logger.error(f"  stop_all: 清理任务队列时发生意外错误: {e}", exc_info=True)
            finally:
                self.task_queue = None  # 确保置 None

        # 清理停止事件
        if hasattr(self, 'stop_event') and self.stop_event:
            try:
                # 确保事件被设置，以防有进程仍在检查它
                if not self.stop_event.is_set():
                    self.stop_event.set()
                logger.debug("  stop_all: 停止事件已设置并清理。")
            except Exception as e:
                logger.error(f"  stop_all: 清理停止事件时出错: {e}", exc_info=True)
            finally:
                self.stop_event = None  # 置 None

        # 停止回调线程
        if hasattr(self, '__callback_running'):
            self.__callback_running.clear()

            if hasattr(self, '__callback_thread') and self.__callback_thread and self.__callback_thread.is_alive():
                # 等待线程结束（不超过1秒）
                self.__callback_thread.join(timeout=1.0)

        # 停止实时通知线程
        if hasattr(self, '__realtime_notify_running'):
            self.__realtime_notify_running.clear()

            if hasattr(self, '__realtime_notify_thread') and self.__realtime_notify_thread and self.__realtime_notify_thread.is_alive():
                # 等待线程结束（不超过1秒）
                self.__realtime_notify_thread.join(timeout=1.0)

        # 清空回调相关资源
        if hasattr(self, '__main_process_callbacks'):
            self.__main_process_callbacks.clear()

        if hasattr(self, '__monitored_keys'):
            self.__monitored_keys.clear()

        if hasattr(self, '__last_values'):
            self.__last_values.clear()

        if hasattr(self, '__key_paths'):
            self.__key_paths.clear()

    def watch_shared_data(self, keys: Union[str, List[str]], callback: Callable[..., None], *args, **kwargs):
        """
        注册一个回调函数，监听指定键的共享数据变化。当数据发生变化时，回调函数会被自动调用。

        这是一种高效的数据变化监听方式，不依赖轮询，而是基于事件通知。支持监听简单值、列表、
        字典等多种数据类型，以及深度嵌套数据结构的变化。

        回调函数会在独立线程中执行，并按照数据变化的顺序依次调用。回调函数可以使用传入的锁
        对象来安全地访问和修改共享数据。

        Args:
            keys: 要监听的键名，可以是单个字符串或字符串列表/元组（监听多个键）
            callback: 回调函数，接收多个参数：
                - key: 发生变化的键名
                - old_value: 变化前的旧值（首次调用时为None）
                - new_value: 变化后的新值
                - lock: 共享锁对象，用于在回调中安全访问/修改数据
                - shared_data_manager: 共享数据管理器代理对象
                - *args, **kwargs: 调用watch_shared_data时传入的额外参数

        Returns:
            bool: 是否成功注册监听。

        Raises:
            TypeError: 传入的参数类型不正确时抛出。

        示例：
            ```python
            # 监听单个键的变化
            def on_count_change(key, old_value, new_value, lock, shared_data_manager):
                print(f"计数器 {key} 从 {old_value} 变为 {new_value}")
                # 使用锁安全访问或修改其他共享数据
                with lock:
                    last_update = shared_data_manager.get_value('last_update_time')
                    print(f"上次更新时间: {last_update}")

            # 可以传递额外参数
            mp.watch_shared_data("num", on_num_change, "extra1", "extra2", tag="num_monitor", priority="high")
            ```
        """
        logger.debug(f"ManagedMultiProcess.watch_shared_data - 开始为键 '{keys}' 注册变化监听...")

        # 参数验证
        if not callable(callback):
            raise TypeError("callback 必须是可调用的函数")

        if isinstance(keys, str):
            keys = [keys]  # 转换单个键为列表
        elif not isinstance(keys, (list, tuple)) or not all(isinstance(k, str) for k in keys):
            raise TypeError("keys 必须是字符串或字符串列表/元组")

        # 启用实时通知功能（这是第一次调用watch_shared_data时启用）
        try:
            if self.__shared_data_manager_proxy:
                # 启用共享数据管理器的实时通知功能
                self.__shared_data_manager_proxy.set_enable_realtime_notify(True)
                logger.debug("已启用共享数据管理器的实时通知功能")
        except Exception as e:
            logger.error(f"启用实时通知功能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 确保实时通知处理线程已启动
        self.__ensure_realtime_notify_thread()

        # 将回调函数注册到主进程回调字典
        success = False
        for key in keys:
            # 将键添加到监控集合
            self.__monitored_keys.add(key)

            # 初始化当前值字典（如果尚未初始化）
            if key not in self.__last_values:
                # 修改: 不再尝试获取当前值，而是直接设置为None，确保首次调用回调时old_value为None
                self.__last_values[key] = None
                logger.debug(f"已初始化键 '{key}' 的当前值为 None (首次监听设置)")

            # 注册回调，包括额外参数
            if key not in self.__main_process_callbacks:
                self.__main_process_callbacks[key] = []

            # 存储回调函数和参数的元组
            callback_data = (callback, args, kwargs)
            if callback_data not in self.__main_process_callbacks[key]:
                self.__main_process_callbacks[key].append(callback_data)
                logger.debug(f"已将回调函数注册到主进程回调字典，键: {key}")
                success = True

        logger.info(f"已为 {len(keys)} 个键注册变化监听: {keys}")
        return success

    def unwatch_shared_data(self, keys: Union[str, List[str]], callback: Optional[Callable] = None) -> bool:
        """
        取消对指定键的数据变化监听。

        Args:
            keys: 要取消监听的键名，可以是单个字符串或字符串列表
            callback: 要取消的特定回调函数。如果为None，则取消所有与指定键相关的回调。

        Returns:
            bool: 是否成功取消监听。

        Raises:
            TypeError: 传入的参数类型不正确时抛出。

        示例:
            ```python
            # 取消单个键的所有监听
            process_manager.unwatch_shared_data('counter')

            # 取消多个键的所有监听
            process_manager.unwatch_shared_data(['counter', 'status'])

            # 只取消特定回调函数
            process_manager.unwatch_shared_data('counter', on_count_change)
            ```
        """
        logger.debug(f"ManagedMultiProcess.unwatch_shared_data - 开始为键 '{keys}' 取消变化监听...")

        # 参数验证
        if isinstance(keys, str):
            keys = [keys]  # 转换单个键为列表
        elif not isinstance(keys, (list, tuple)) or not all(isinstance(k, str) for k in keys):
            raise TypeError("keys 必须是字符串或字符串列表/元组")

        # 执行取消监听
        success = False
        for key in keys:
            # 从监控集合中移除
            if key in self.__monitored_keys:
                self.__monitored_keys.remove(key)

            # 从回调字典中移除指定回调或所有回调
            if key in self.__main_process_callbacks:
                if callback is None:
                    # 移除所有回调
                    self.__main_process_callbacks.pop(key)
                    logger.debug(f"已移除键 '{key}' 的所有回调")
                    success = True
                else:
                    # 移除特定回调
                    callbacks_to_keep = []
                    for cb_data in self.__main_process_callbacks[key]:
                        cb_func, _, _ = cb_data
                        if cb_func != callback:
                            callbacks_to_keep.append(cb_data)

                    # 更新回调列表
                    if len(callbacks_to_keep) < len(self.__main_process_callbacks[key]):
                        if callbacks_to_keep:
                            self.__main_process_callbacks[key] = callbacks_to_keep
                        else:
                            self.__main_process_callbacks.pop(key)
                        logger.debug(f"已移除键 '{key}' 的指定回调")
                        success = True

        # 如果没有任何监听键，禁用实时通知功能
        if not self.__main_process_callbacks and not self.__key_paths and self.__shared_data_manager_proxy:
            try:
                # 禁用共享数据管理器的实时通知功能
                self.__shared_data_manager_proxy.set_enable_realtime_notify(False)
                logger.debug("已禁用共享数据管理器的实时通知功能")

                # 停止实时通知线程
                if hasattr(self, '_ManagedMultiProcess__realtime_notify_running'):
                    self.__realtime_notify_running.clear()
                    logger.debug("已发送停止实时通知线程的信号")
            except Exception as e:
                logger.error(f"禁用实时通知功能时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

        return success

    def __process_realtime_notifications(self):
        """
        处理实时通知队列中的数据变化通知。

        此方法替代了原来的轮询机制，通过直接从共享队列中获取数据变化通知，
        实时检测数据变化并触发对应的回调函数。
        """
        logger.debug("启动数据实时变化监听线程")

        while self.__realtime_notify_running.is_set():
            try:
                # 获取共享数据管理器代理
                if not hasattr(self, '_ManagedMultiProcess__shared_data_manager_proxy') or not self.__shared_data_manager_proxy:
                    logger.warning("共享数据管理器代理未初始化，等待...")
                    time.sleep(0.1)
                    continue

                # 获取实时通知队列
                try:
                    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                except Exception as e:
                    logger.error(f"获取实时通知队列失败: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    time.sleep(0.1)
                    continue

                # 获取共享锁
                try:
                    lock_proxy = self.__shared_data_manager_proxy.get_lock()
                except Exception as e:
                    logger.error(f"获取共享锁失败: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                    time.sleep(0.1)
                    continue

                # 从队列中获取数据变化通知
                try:
                    key, value, timestamp = notify_queue.get(timeout=0.5)
                except (queue.Empty, EOFError):
                    # 队列为空或连接断开，继续循环
                    continue

                # 检查是否监控此键
                if key in self.__monitored_keys:
                    # 获取上次的值
                    old_value = self.__last_values.get(key)

                    # 更新保存的值
                    self.__last_values[key] = value

                    # 查找对应的回调并加入队列
                    if key in self.__main_process_callbacks:
                        callbacks = list(self.__main_process_callbacks[key])  # 复制避免迭代时修改
                        for callback_data in callbacks:
                            try:
                                # 解包回调函数和额外参数
                                callback, args, kwargs = callback_data

                                # 将回调任务加入队列，不直接执行
                                self.__callback_queue.put((callback, key, old_value, value, lock_proxy, self.__shared_data_manager_proxy, args, kwargs))
                                logger.debug(f"已将键 '{key}' 的回调任务添加到队列")
                            except Exception as e:
                                logger.error(f"将键 '{key}' 的回调任务添加到队列时出错: {e}")
                                import traceback
                                logger.error(traceback.format_exc())

            except Exception as e:
                # 捕获处理过程中的任何异常
                logger.error(f"数据实时变化监听线程发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(0.1)  # 短暂休眠避免CPU空转

        logger.debug("数据实时变化监听线程已停止")

    def __process_callbacks(self):
        """
        处理回调队列中的任务，异步执行回调函数。

        这个线程函数负责从回调队列中获取任务并执行，确保每个回调函数都能在不阻塞轮询线程的情况下执行。
        即使某个回调函数执行时间较长，其他数据变化也能及时被检测到并放入队列中等待处理。
        """
        logger.debug("启动回调函数处理工作线程")

        while self.__callback_worker_running.is_set():
            try:
                # 从队列获取任务，设置较短的超时时间以便定期检查运行状态
                try:
                    callback_task = self.__callback_queue.get(timeout=0.1)
                except queue.Empty:
                    # 队列为空，继续下一次循环
                    continue

                # 解包任务：回调函数、参数等
                callback, key, old_value, new_value, lock_proxy, shared_data_manager_proxy, args, kwargs = callback_task

                # 执行回调函数，捕获任何异常以避免影响队列处理
                try:
                    # 在主进程中执行回调，可以安全访问主进程的任何变量
                    callback(key, old_value, new_value, lock_proxy, shared_data_manager_proxy, *args, **kwargs)
                except Exception as e:
                    logger.error(f"执行键 '{key}' 的回调函数时出错: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                finally:
                    # 标记任务完成，无论是否成功执行
                    self.__callback_queue.task_done()

            except Exception as e:
                # 捕获工作线程中的任何异常
                logger.error(f"回调函数处理工作线程发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(0.1)  # 短暂休眠避免CPU空转

        logger.debug("回调函数处理工作线程已停止")

    def watch_data_path(self, paths: Union[str, List[str]], callback: Callable[..., None], *args, **kwargs):
        """
        注册一个回调函数，监听指定路径的共享数据变化。

        此方法扩展了watch_shared_data的功能，允许监听深度嵌套的数据结构中的特定路径，
        而不是整个顶层键。当路径上的数据发生变化时，回调函数会被自动调用。

        路径格式支持以下语法：
        - "key" - 监听顶层键
        - "key.field" - 监听字典中的特定字段
        - "key.field.subfield" - 监听嵌套字典
        - "key[0]" - 监听列表中的特定索引
        - "key[*]" - 监听列表中的任意元素变化
        - "key[*].field" - 监听列表中所有元素的特定字段

        Args:
            paths: 要监听的数据路径，可以是单个字符串或字符串列表
            callback: 回调函数，接收以下参数：
                - path: 发生变化的完整路径
                - old_value: 变化前路径处的旧值（首次调用时为None）
                - new_value: 变化后路径处的新值
                - lock: 共享锁对象，用于在回调中安全访问/修改数据
                - shared_data_manager: 共享数据管理器代理对象
                - *args: 传递给回调的额外位置参数
                - **kwargs: 传递给回调的额外关键字参数
            *args: 传递给回调函数的额外位置参数
            **kwargs: 传递给回调函数的额外关键字参数

        Returns:
            bool: 是否成功注册路径监听。

        Raises:
            TypeError: 传入的参数类型不正确时抛出。
            ValueError: 路径格式不正确时抛出。

        示例:
            ```python
            # 监听嵌套字典的特定字段
            def on_status_change(path, old_value, new_value, lock, shared_data_manager):
                print(f"路径 {path} 从 {old_value} 变为 {new_value}")

            # 监听用户设置中的主题变化
            mp.watch_data_path("settings.theme", on_theme_change)

            # 监听结果列表中所有元素的状态变化
            mp.watch_data_path("results[*].status", on_status_change)
            ```
        """
        logger.debug(f"ManagedMultiProcess.watch_data_path - 开始为路径 '{paths}' 注册变化监听...")

        # 参数验证
        if not callable(callback):
            raise TypeError("callback 必须是可调用的函数")

        if isinstance(paths, str):
            paths = [paths]  # 转换单个路径为列表
        elif not isinstance(paths, (list, tuple)) or not all(isinstance(p, str) for p in paths):
            raise TypeError("paths 必须是字符串或字符串列表/元组")

        # 启用实时通知功能（如果尚未启用）
        try:
            if self.__shared_data_manager_proxy:
                # 启用共享数据管理器的实时通知功能
                self.__shared_data_manager_proxy.set_enable_realtime_notify(True)
                logger.debug("已启用共享数据管理器的实时通知功能")
        except Exception as e:
            logger.error(f"启用实时通知功能时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # 确保实时通知处理线程已启动
        self.__ensure_realtime_notify_thread()

        success = False
        for path in paths:
            # 解析路径，获取顶层键和子路径
            parts = path.split(".", 1)
            top_key = parts[0]
            sub_path = parts[1] if len(parts) > 1 else ""

            # 检查是否包含数组索引表示
            if "[" in top_key:
                key_parts = top_key.split("[", 1)
                top_key = key_parts[0]
                if sub_path:
                    sub_path = f"[{key_parts[1]}.{sub_path}"
                else:
                    sub_path = f"[{key_parts[1]}"

            # 初始化顶层键的路径映射
            if top_key not in self.__key_paths:
                self.__key_paths[top_key] = {}

            # 创建一个包装回调函数，处理路径变化
            def path_wrapper(key, old_value, new_value, lock, shared_data_manager, path=path, sub_path=sub_path, callback=callback, args=args, kwargs=kwargs):
                """
                路径监听的包装回调函数。

                Args:
                    key: 顶层键名
                    old_value: 变化前的值（首次调用时为None）
                    new_value: 变化后的新值
                    lock: 共享锁对象
                    shared_data_manager: 共享数据管理器
                    path: 完整路径
                    sub_path: 子路径
                    callback: 用户回调函数
                    args: 额外位置参数
                    kwargs: 额外关键字参数
                """
                # 直接检查整体值变化
                self.__handle_path_change(key, old_value, new_value, path, sub_path, callback, lock, shared_data_manager, args, kwargs)

            # 将路径包装器保存到路径映射中
            self.__key_paths[top_key][path] = path_wrapper

            # 注册变化监听
            if self.watch_shared_data(top_key, path_wrapper):
                success = True
                logger.debug(f"已为路径 '{path}' 注册监听，顶层键: {top_key}")

        logger.info(f"已为 {len(paths)} 个路径注册变化监听: {paths}")
        return success

    def __handle_path_change(self, key, old_value, new_value, full_path, sub_path, callback, lock, shared_data_manager, args, kwargs):
        """
        处理路径变化，提取路径上的值并调用回调函数。

        Args:
            key: 顶层键名
            old_value: 变化前的值（首次调用时可能为None）
            new_value: 变化后的值
            full_path: 完整路径字符串
            sub_path: 子路径部分
            callback: 用户回调函数
            lock: 共享锁对象
            shared_data_manager: 共享数据管理器
            args: 额外位置参数
            kwargs: 额外关键字参数
        """
        try:
            # 如果没有子路径，直接调用回调
            if not sub_path:
                callback(full_path, old_value, new_value, lock, shared_data_manager, *args, **kwargs)
                return

            # 获取路径上的旧值和新值
            old_path_value = None
            new_path_value = None

            if old_value is not None:
                try:
                    old_path_value = self.__get_value_at_path(old_value, sub_path)
                except (KeyError, IndexError, TypeError) as e:
                    logger.debug(f"无法从旧值获取路径 '{sub_path}' 的值: {e}")

            if new_value is not None:
                try:
                    new_path_value = self.__get_value_at_path(new_value, sub_path)
                except (KeyError, IndexError, TypeError) as e:
                    logger.debug(f"无法从新值获取路径 '{sub_path}' 的值: {e}")

            # 检查路径上的值是否有变化
            if old_path_value != new_path_value:
                # 调用回调函数
                callback(full_path, old_path_value, new_path_value, lock, shared_data_manager, *args, **kwargs)

        except Exception as e:
            logger.error(f"处理路径 '{full_path}' 的变化时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def __path_matches(self, changed_path, sub_path, key):
        """
        检查变化的路径是否匹配我们关注的路径。

        Args:
            changed_path: 变化的路径
            sub_path: 我们关注的子路径
            key: 顶层键名

        Returns:
            bool: 是否匹配
        """
        # 如果子路径为空，任何变化都匹配
        if not sub_path:
            return True

        # 标准化路径格式
        changed_norm = changed_path.replace('[', '.').replace(']', '')
        sub_norm = sub_path.replace('[', '.').replace(']', '')

        # 移除开头的点
        if changed_norm.startswith('.'):
            changed_norm = changed_norm[1:]
        if sub_norm.startswith('.'):
            sub_norm = sub_norm[1:]

        # 检查路径匹配
        return changed_norm == sub_norm or changed_norm.startswith(sub_norm + '.') or sub_norm.startswith(changed_norm + '.')

    def __get_value_at_path(self, data, path):
        """
        根据路径从数据结构中获取值。

        Args:
            data: 要查询的数据
            path: 路径字符串，如 '.key1[0].key2'

        Returns:
            找到的值，或者 None
        """
        if not path or data is None:
            return data

        # 移除开头的点
        if path.startswith('.'):
            path = path[1:]

        # 如果路径为空，直接返回数据
        if not path:
            return data

        # 解析路径
        parts = []
        current = ""
        i = 0

        # 逐字符解析路径，处理嵌套的方括号和点
        while i < len(path):
            if path[i] == '.':
                if current:
                    parts.append(current)
                    current = ""
            elif path[i] == '[':
                if current:
                    parts.append(current)
                    current = ""
                # 寻找匹配的右括号
                j = i + 1
                while j < len(path) and path[j] != ']':
                    j += 1
                if j < len(path):
                    # 提取索引
                    parts.append(int(path[i + 1:j]))
                    i = j  # 跳过索引和右括号
            else:
                current += path[i]
            i += 1

        # 添加最后一部分
        if current:
            parts.append(current)

        # 逐步访问数据
        result = data
        try:
            for part in parts:
                if isinstance(result, (dict, list)) or hasattr(result, '__getitem__'):
                    result = result[part]
                else:
                    return None
            return result
        except (IndexError, KeyError, TypeError):
            return None

    def unwatch_data_path(self, paths: Union[str, List[str]], callback: Optional[Callable] = None) -> bool:
        """
        取消对指定数据路径的监听。

        Args:
            paths: 要取消监听的数据路径，可以是单个字符串或字符串列表
            callback: 特定的回调函数。如果为None，则移除所有监听该路径的回调。

        Returns:
            bool: 是否成功取消监听

        示例:
            ```python
            # 移除特定路径的特定回调
            process_manager.unwatch_data_path('config.server.port', on_port_change)

            # 移除多个路径的所有回调
            process_manager.unwatch_data_path(['config.server.port', 'users[0].name'])
            ```
        """
        logger.debug(f"ManagedMultiProcess.unwatch_data_path - 开始为路径 '{paths}' 取消变化监听...")

        # 参数验证
        if isinstance(paths, str):
            paths = [paths]  # 转换单个路径为列表
        elif not isinstance(paths, (list, tuple)) or not all(isinstance(p, str) for p in paths):
            raise TypeError("paths 必须是字符串或字符串列表/元组")

        # 取消每个路径的监听
        success = False
        for path in paths:
            # 解析路径，获取顶层键
            if '.' in path or '[' in path:
                top_key = path.split('.')[0].split('[')[0]
            else:
                top_key = path

            # 从路径映射中移除
            if top_key in self.__key_paths and path in self.__key_paths[top_key]:
                # 获取路径包装器
                path_wrapper = self.__key_paths[top_key][path]

                # 取消监听
                if self.unwatch_shared_data(top_key, path_wrapper):
                    # 从路径映射中移除
                    del self.__key_paths[top_key][path]
                    if not self.__key_paths[top_key]:
                        del self.__key_paths[top_key]
                    success = True
                    logger.debug(f"已取消路径 '{path}' 的监听")

        logger.info(f"已为 {len(paths)} 个路径取消变化监听: {paths}")
        return success

    def __ensure_realtime_notify_thread(self):
        """
        确保实时通知处理线程已启动，用于处理来自共享数据管理器的实时数据变化通知。
        同时确保回调工作线程已启动，用于异步执行回调函数。
        """
        # 确保实时通知处理线程已启动
        if self.__realtime_notify_thread is None or not self.__realtime_notify_thread.is_alive():
            self.__realtime_notify_running.set()
            self.__realtime_notify_thread = threading.Thread(
                target=self.__process_realtime_notifications,
                daemon=True  # 守护线程，随主线程退出
            )
            self.__realtime_notify_thread.start()
            logger.debug("数据实时变化监听线程已启动")

        # 确保回调工作线程已启动
        if self.__callback_worker_thread is None or not self.__callback_worker_thread.is_alive():
            self.__callback_worker_running.set()
            self.__callback_worker_thread = threading.Thread(
                target=self.__process_callbacks,
                daemon=True  # 守护线程，随主线程退出
            )
            self.__callback_worker_thread.start()
            logger.debug("回调函数处理工作线程已启动")

    def add_completion_callback(self, callback: Callable, *args, **kwargs):
        """
        添加一个在所有进程完成时调用的回调函数

        当所有子进程执行完成时，此回调函数将被异步调用。如果当前使用了watch_shared_data
        监听共享数据变化，则还会等待SharedDataManager实时通知队列为空

        参数:
            callback: 回调函数，第一个参数将接收ManagedMultiProcess实例
            *args, **kwargs: 传递给回调函数的额外参数

        使用示例:
        ```python
        def on_complete(mp_instance):
            print(f"所有任务已完成，获取结果: {mp_instance.get_results()}")

        mp = ManagedMultiProcess(data, worker_func)
        mp.add_completion_callback(on_complete)
        mp.run()
        ```
        """
        if not callable(callback):
            raise TypeError("回调必须是可调用对象")

        # 将回调和参数打包存储
        self.__completion_callbacks.append((callback, args, kwargs))

        # 如果进程已经完成，并且通知队列为空，则启动回调线程
        if not self.is_running() and self.__is_notify_queue_empty():
            self.__start_completion_thread()

        return self

    def __is_notify_queue_empty(self) -> bool:
        """
        检查实时通知队列是否为空

        如果使用了watch_shared_data，则需要检查实时通知队列是否为空
        否则直接返回True

        返回:
            bool: 如果队列为空或未使用watch_shared_data，则返回True
        """
        try:
            # 检查是否启用了实时通知
            if self._shared_data_manager.is_realtime_notify_enabled():
                # 获取实时通知队列
                notify_queue = self._shared_data_manager.get_realtime_notify_queue()
                # 检查队列是否为空
                return notify_queue.empty()
            return True
        except Exception:
            # 发生异常默认返回True，避免卡死
            import traceback
            traceback.print_exc()
            return True

    def __start_completion_thread(self):
        """
        启动异步回调执行线程
        """
        if self.__completion_thread is not None and self.__completion_thread.is_alive():
            return  # 已经存在回调线程，不重复启动

        # 重置完成事件
        self.__completion_event.clear()

        def _callback_runner():
            try:
                # 等待完成事件
                self.__completion_event.wait()

                # 执行所有回调
                for callback, args, kwargs in self.__completion_callbacks:
                    try:
                        # 第一个参数传入self (ManagedMultiProcess实例)
                        callback(self, *args, **kwargs)
                    except Exception:
                        import traceback
                        traceback.print_exc()
            except Exception:
                import traceback
                traceback.print_exc()

        # 创建并启动线程
        self.__completion_thread = threading.Thread(
            target=_callback_runner,
            name="ManagedMP-CompletionCallback",
            daemon=True
        )
        self.__completion_thread.start()

    def __trigger_completion_callbacks(self):
        """
        触发完成回调

        只有在所有进程完成且通知队列为空时才会触发
        """
        if self.__completion_callbacks and not self.is_running() and self.__is_notify_queue_empty():
            # 启动回调线程（如果尚未启动）
            self.__start_completion_thread()
            # 设置完成事件，触发回调执行
            self.__completion_event.set()


# 将示例工作函数移到顶层，以便子进程可以找到它
def simple_worker_example(shared_manager_proxy: SharedDataManager, task_item: int, prefix: str = "Task"):
    '''
    一个简单的工作函数示例，使用 SharedDataManager 代理。
    它处理一个数字，并将结果存储在共享数据中。

        Args:
        shared_manager_proxy: SharedDataManager代理对象，用于访问共享数据
        task_item: 要处理的任务项（整数）
        prefix: 任务前缀，默认为"Task"

        Returns:
        None: 结果通过共享数据管理器存储
    '''
    while True:
        lock = shared_manager_proxy.get_lock()
        with lock:
            num = shared_manager_proxy.get_value("num", default=0)
            new_num = num + 1
            shared_manager_proxy.add_value("num", new_num)
        time.sleep(random.uniform(0.1, 0.3))


def main():
    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(levelname)s - [%(processName)s:%(process)d] - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 创建一个简单的处理任务列表
    tasks = list(range(10))

    # 1. 创建并配置 ManagedMultiProcess 实例
    mp = ManagedMultiProcess(
        input_data=tasks,
        callback_func=simple_worker_example,  # 使用示例工作函数
        num_processes=3,
        prefix="Demo"  # 传递给示例工作函数的参数
    )

    # 2. 注册回调函数，监听键的变化
    def on_num_change(key, old_value, new_value, lock, shared_data_manager: SharedDataManager, *args, **kwargs):
        # 回调函数可以安全访问主进程变量，例如 mp
        current_value = shared_data_manager.get_value("num")
        print(f"【主进程回调】数据 '{key}' 从 {old_value} 变为 {new_value} 当前值为 {current_value}")

        # 延迟一段时间模拟处理
        time.sleep(random.uniform(0.1, 0.3))

    # 注册共享数据变化监听，并传递额外参数
    mp.watch_shared_data("num", on_num_change, "extra1", "extra2", tag="num_monitor", priority="high")

    # 3. 运行并等待完成
    print("启动处理...")
    mp.run()
    mp.wait_all()

    # 4. 获取结果
    result = mp.get_shared_value("num", default=0)
    print(f"处理结束。最终结果: num = {result}")

    # 获取所有错误
    errors = mp.get_all_errors()
    if errors:
        print(f"处理过程中发生了 {len(errors)} 个错误:")
        for error in errors:
            print(f"  - {error}")

    # 5. 停止所有进程
    mp.stop_all()

    print("示例运行完成。")


if __name__ == "__main__":
    main()
