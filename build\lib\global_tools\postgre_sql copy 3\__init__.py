"""
PostgreSQL数据库操作模块

这个模块是对原始单文件实现 `auto_annotation/src/core/postgre_sql.py` 的重构版本，
将单一文件拆分为多个功能模块，提高代码的可维护性和可扩展性，同时保持与原始模块相同的功能和接口。

主要变化：
1. 将连接池管理逻辑独立为 connection_pool.py
2. 将SQL条件解析逻辑独立为 sql_condition_parser.py
3. 将数据类型转换逻辑独立为 db_type_converter.py
4. 将异常处理逻辑独立为 exceptions.py
5. 将日志记录逻辑独立为 logger.py
6. 将配置管理逻辑独立为 config.py
7. 将数据库操作逻辑独立为各种 data_operations*.py
8. 增加了命令行接口 __main__.py

这个模块提供了与PostgreSQL数据库交互的高级API，支持连接池管理、表操作和数据操作等功能。
主要使用PostgreSQLClient类作为主要接口，该类提供了完整的数据库操作功能。
"""

# 核心客户端类
from .core_client import PostgreSQLClient

# SQL条件解析函数
from .sql_condition_parser import parse_condition

# 基本异常类
from .exceptions import PostgreSQLError, ConnectionError, ExecutionError, ConfigurationError

# 日志工具
from .logger import get_logger, configure_logger

# 内部组件 - 不导出但被core_client依赖
from .db_type_converter import convert_to_pg_type, adapt_value_for_db
from .connection_pool import get_connection_pool, close_connection_pool, close_all_connection_pools
from .config import DBConfig

__version__ = "1.0.0"

__all__ = [
    # 核心客户端
    'PostgreSQLClient',
    
    # SQL条件解析
    'parse_condition',
    
    # 基本异常类
    'PostgreSQLError',
    'ConnectionError',
    'ExecutionError',
    'ConfigurationError',
    
    # 日志工具
    'get_logger',
    'configure_logger',
    
    # 版本信息
    '__version__',
]
