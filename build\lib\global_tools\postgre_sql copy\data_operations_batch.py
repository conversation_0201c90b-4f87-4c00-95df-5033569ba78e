import json
import traceback
import time
import logging

from .core_client import logger
from .data_operations_1 import DataOperations1
from .data_operations_2 import is_condition_parse_error

class DataOperationsBatch:
    """
    数据操作批处理类，包含批量操作相关的方法
    """
    
    @staticmethod
    def _batch_update_data(client, table_name, batch_updates):
        """
        批量更新表中数据，所有更新在单个事务中执行。

        本函数采用高效的批处理策略，通过单个事务执行多个更新操作，具有以下特点：
        1. 性能优化：预处理数据、SQL模板缓存、条件字符串缓存等多种优化策略
        2. 错误隔离：单个更新项失败不会影响整个批处理的执行
        3. 完整反馈：返回每个更新项的详细执行结果
        4. 事务保证：所有更新在单个事务中执行，确保数据一致性

        参数:
            client: PostgreSQLClient实例
            table_name (str): 表名
            batch_updates (list): 批量更新数据列表，格式为 [[condition_str, data_json], ...]
                - condition_str: 条件字符串，例如 "id == 1"
                - data_json: 要更新的数据，可以是JSON字符串或字典

        返回:
            dict: 包含批量更新操作结果的字典
        """
        # 初始化事务状态跟踪和计时
        transaction_status = "initialized"  # 事务状态：初始化
        start_time = client._get_timestamp()  # 记录开始时间
        cursor = None  # 数据库游标
        results = []  # 存储每个更新项的结果

        # 【性能优化1】: 预先创建基础错误响应字典模板，减少重复初始化
        base_error_response = {
            "success": False,
            "table_name": table_name,
            "total_updates": len(batch_updates),
            "successful_updates": 0,
            "failed_updates": len(batch_updates),
            "results": [],
            "timestamp": None,  # 将在返回前更新
            "execution_time_ms": None  # 将在返回前更新
        }

        # 【性能优化2】: 创建集合用于收集所有更新中使用的字段名，减少重复处理
        unique_data_fields = set()

        try:
            # 第一阶段：验证表是否存在
            if not client._table_exists(table_name):
                error_msg = f"表 '{table_name}' 不存在"
                client.logger.error(error_msg)

                # 更新时间戳和执行时间
                current_timestamp = client._get_timestamp()
                base_error_response.update({
                    "error": error_msg,
                    "error_type": "TableNotExist",
                    "transaction_status": "not_started",  # 事务未开始
                    "timestamp": current_timestamp,
                    "execution_time_ms": int((current_timestamp - start_time) * 1000)
                })
                return base_error_response

            # 第二阶段：验证批量更新参数格式
            # 【性能优化3】: 使用next和生成器表达式快速找到第一个格式错误的项，避免遍历整个列表
            invalid_format_idx = next(
                (idx for idx, update_item in enumerate(batch_updates)
                 if not isinstance(update_item, list) or len(update_item) != 2),
                None
            )

            # 如果存在格式错误的项，立即返回错误
            if invalid_format_idx is not None:
                error_msg = f"批量更新数据格式错误，项 #{invalid_format_idx} 必须是包含两个元素的列表: [condition_str, data_json]"
                client.logger.error(error_msg)

                # 更新时间戳和执行时间
                current_timestamp = client._get_timestamp()
                base_error_response.update({
                    "error": error_msg,
                    "error_type": "BatchDataFormatError",
                    "transaction_status": "not_started",  # 事务未开始
                    "timestamp": current_timestamp,
                    "execution_time_ms": int((current_timestamp - start_time) * 1000)
                })
                return base_error_response

            # 第三阶段：数据预处理
            # 【性能优化4】: 预处理所有数据 - 一次性解析JSON并收集字段，而不是在执行阶段重复处理
            processed_updates = []  # 存储处理后的更新项
            invalid_items = []  # 存储格式无效的更新项

            # 遍历所有更新项进行预处理
            for idx, (condition_str, data_json) in enumerate(batch_updates):
                update_item = {"index": idx,
                               "condition": condition_str}  # 初始化更新项信息

                # 解析JSON数据(如果是字符串格式)
                try:
                    # 将字符串JSON转为字典对象
                    data = json.loads(data_json) if isinstance(
                        data_json, str) else data_json

                    # 验证数据类型是否为字典
                    if not isinstance(data, dict):
                        update_item["error"] = f"数据必须是字典类型，得到: {type(data)}"
                        update_item["error_type"] = "DataTypeError"
                        invalid_items.append((idx, update_item))
                        continue

                    # 验证数据是否为空
                    if not data:
                        update_item["error"] = "更新数据不能为空"
                        update_item["error_type"] = "EmptyDataError"
                        invalid_items.append((idx, update_item))
                        continue

                    # 收集所有字段名，用于后续处理
                    unique_data_fields.update(data.keys())
                    update_item["data"] = data
                    processed_updates.append(update_item)

                # 处理JSON解析错误
                except json.JSONDecodeError as e:
                    update_item["error"] = f"JSON 解析错误: {e}"
                    update_item["error_type"] = "JSONDecodeError"
                    invalid_items.append((idx, update_item))
                    client.logger.error(
                        f"{update_item['error']}, JSON 数据: {data_json}")
                    traceback.print_exc()  # 打印详细异常堆栈

            # 第四阶段：验证预处理结果
            # 如果所有项都无效，提前返回错误结果
            if not processed_updates:
                error_msg = "所有批量更新项格式无效"
                client.logger.error(error_msg)

                # 处理所有无效项的结果
                invalid_results = []
                for idx, item in invalid_items:
                    item["success"] = False
                    item["timestamp"] = client._get_timestamp()
                    item["execution_time_ms"] = 0  # 未实际执行
                    invalid_results.append(item)

                # 更新时间戳和执行时间
                current_timestamp = client._get_timestamp()
                base_error_response.update({
                    "error": error_msg,
                    "error_type": "AllItemsInvalid",
                    "transaction_status": "not_started",  # 事务未开始
                    "results": invalid_results,
                    "timestamp": current_timestamp,
                    "execution_time_ms": int((current_timestamp - start_time) * 1000)
                })
                return base_error_response

            # 第五阶段：获取表结构
            # 【性能优化5】: 只查询一次表结构，而不是为每个更新项单独查询
            schema_sql = f"""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = '{table_name}'
            """
            schema_result = client.execute_query(schema_sql, fetch=True)

            # 验证表结构查询结果
            if not schema_result:
                error_msg = f"无法获取表 '{table_name}' 的结构信息"
                client.logger.error(error_msg)

                # 更新时间戳和执行时间
                current_timestamp = client._get_timestamp()
                base_error_response.update({
                    "error": error_msg,
                    "error_type": "SchemaError",
                    "transaction_status": "not_started",  # 事务未开始
                    "timestamp": current_timestamp,
                    "execution_time_ms": int((current_timestamp - start_time) * 1000)
                })
                return base_error_response

            # 构建简化格式的表结构字典
            table_schema = {col[0]: {"type": col[1]} for col in schema_result}

            # 【性能优化6】: 创建缓存字典，避免重复构建SQL和重复解析条件
            sql_cache = {}  # SQL模板缓存
            condition_cache = {}  # 条件字符串解析结果缓存

            # 第六阶段：准备数据库操作
            client._ensure_connection()  # 确保数据库连接有效
            cursor = client.conn.cursor()  # 创建游标
            transaction_status = "started"  # 标记事务开始
            successful_updates = 0  # 成功更新计数器
            failed_updates = 0  # 失败更新计数器

            # 第七阶段：处理无效项
            # 将预处理阶段发现的无效项添加到结果中
            for idx, item in invalid_items:
                item["success"] = False
                item["timestamp"] = client._get_timestamp()
                item["execution_time_ms"] = 0  # 未实际执行
                results.append(item)
                failed_updates += 1

            # 第八阶段：过滤有效字段
            # 【性能优化7】: 一次性对所有预处理项进行数据字段过滤，减少重复逻辑
            for update_item in processed_updates:
                update_start_time = client._get_timestamp()  # 记录单个更新开始时间
                update_item["timestamp"] = update_start_time

                try:
                    data = update_item["data"]  # 获取更新数据

                    # 使用filter_insert_data_by_schema过滤掉不存在的字段
                    filter_result = DataOperations1.filter_insert_data_by_schema(
                        client, data, table_schema)

                    # 检查过滤结果
                    if not filter_result["success"]:
                        # 字段过滤失败
                        update_item["success"] = False
                        update_item["error"] = filter_result["message"]
                        update_item["error_type"] = "FilterError"
                        update_item["execution_time_ms"] = int(
                            (client._get_timestamp() - update_start_time) * 1000)
                        results.append(update_item)
                        failed_updates += 1
                        continue  # 跳过此项后续处理

                    # 获取过滤后的数据和被移除的字段
                    filtered_data = filter_result["filtered_data"]
                    removed_fields = filter_result["removed_fields"]
                    update_item["removed_fields"] = removed_fields

                    # 检查过滤后的数据是否为空
                    if not filtered_data:
                        # 过滤后没有有效字段
                        update_item["success"] = False
                        update_item["error"] = "过滤后没有有效的更新字段"
                        update_item["error_type"] = "NoValidFieldsError"
                        update_item["execution_time_ms"] = int(
                            (client._get_timestamp() - update_start_time) * 1000)
                        results.append(update_item)
                        failed_updates += 1
                        continue  # 跳过此项后续处理

                    # 保存过滤后的数据
                    update_item["filtered_data"] = filtered_data

                    # 第九阶段：解析条件字符串
                    # 【性能优化8】: 使用缓存减少重复解析相同的条件字符串
                    condition_str = update_item["condition"]
                    if condition_str in condition_cache:
                        # 使用缓存的解析结果
                        sql_where = condition_cache[condition_str]
                    else:
                        # 解析条件字符串并缓存结果
                        try:
                            sql_where = client._parse_condition_json(
                                condition_str)
                            condition_cache[condition_str] = sql_where
                        except Exception as e:
                            # 新增：递归判断异常链，只要有条件解析相关异常就结构化返回
                            if is_condition_parse_error(e):
                                update_item["success"] = False
                                update_item["error"] = f"条件解析错误: {str(e)}"
                                update_item["error_type"] = "ConditionError"
                                update_item["execution_time_ms"] = int((client._get_timestamp() - update_start_time) * 1000)
                                results.append(update_item)
                                failed_updates += 1
                                traceback.print_exc()  # 打印详细异常堆栈
                                continue
                            # 其他异常按原有逻辑处理
                            update_item["success"] = False
                            update_item["error"] = f"预处理更新项失败: {str(e)}"
                            update_item["error_type"] = "PreprocessError"
                            update_item["execution_time_ms"] = int((client._get_timestamp() - update_start_time) * 1000)
                            results.append(update_item)
                            failed_updates += 1
                            traceback.print_exc()  # 打印详细异常堆栈
                            continue

                    # 保存解析后的WHERE子句
                    update_item["sql_where"] = sql_where

                except Exception as e:
                    # 新增：递归判断异常链，只要有条件解析相关异常就结构化返回
                    if is_condition_parse_error(e):
                        update_item["success"] = False
                        update_item["error"] = f"条件解析错误: {str(e)}"
                        update_item["error_type"] = "ConditionError"
                        update_item["execution_time_ms"] = int((client._get_timestamp() - update_start_time) * 1000)
                        results.append(update_item)
                        failed_updates += 1
                        traceback.print_exc()  # 打印详细异常堆栈
                        continue
                    # 其他异常按原有逻辑处理
                    error_msg = f"更新项 #{update_item['index']} 失败: {str(e)}"
                    if len(batch_updates) < 100:
                        client.logger.error(error_msg)
                    update_item["success"] = False
                    update_item["error"] = error_msg
                    update_item["error_type"] = "UpdateItemError"
                    update_item["execution_time_ms"] = int((client._get_timestamp() - update_start_time) * 1000)
                    results.append(update_item)
                    failed_updates += 1
                    traceback.print_exc()  # 打印详细异常堆栈

            # 第十阶段：提取需要执行的项
            # 【性能优化9】: 使用列表推导式快速筛选有效的更新项
            execute_items = [
                item for item in processed_updates if "sql_where" in item]

            # 第十一阶段：执行更新操作
            for update_item in execute_items:
                # 使用已保存的时间戳，避免重复获取
                update_start_time = update_item["timestamp"]

                try:
                    # 获取过滤后的数据和WHERE子句
                    filtered_data = update_item["filtered_data"]
                    sql_where = update_item["sql_where"]

                    # 【性能优化10】: 使用缓存的SQL模板，避免重复构建SQL
                    # 使用排序后的字段元组作为缓存键，确保相同字段集合的SQL模板只生成一次
                    fields_key = tuple(sorted(filtered_data.keys()))

                    if fields_key in sql_cache:
                        # 使用缓存的SQL模板和占位符列表
                        sql_template, placeholders = sql_cache[fields_key]
                    else:
                        # 为新的字段组合创建SQL模板和占位符列表
                        set_parts = []
                        placeholders = []

                        for key in fields_key:
                            set_parts.append(f"{key} = %s")
                            placeholders.append(key)

                        # 创建SQL模板，使用{}占位以便后续填充WHERE子句
                        sql_template = f"UPDATE {table_name} SET {', '.join(set_parts)} WHERE {{}}"
                        # 缓存SQL模板和占位符列表
                        sql_cache[fields_key] = (sql_template, placeholders)

                    # 构建完整SQL和参数值
                    sql = sql_template.format(sql_where)
                    # 将数据值转换为数据库适用的格式
                    set_values = [client._adapt_value_for_db(
                        filtered_data[key]) for key in placeholders]

                    # 【性能优化11】: 减少大批量操作时的日志记录
                    if len(batch_updates) < 100:  # 只在批量数较小时记录详细日志
                        client.logger.debug(f"执行SQL: {sql}, 参数: {set_values}")

                    # 执行更新操作
                    cursor.execute(sql, set_values)
                    updated_count = cursor.rowcount  # 获取受影响的行数

                    # 记录成功更新的结果
                    update_item["success"] = True
                    update_item["updated_count"] = updated_count
                    update_item["updated_data"] = filtered_data
                    update_item["execution_time_ms"] = int(
                        (client._get_timestamp() - update_start_time) * 1000)
                    results.append(update_item)
                    # 修复点：只有实际更新了行才计入 successful_updates
                    if updated_count > 0:
                        successful_updates += 1

                except Exception as e:
                    # 【错误隔离】: 单个更新项的失败不会影响整个批处理
                    error_msg = f"更新项 #{update_item['index']} 失败: {str(e)}"

                    # 【性能优化12】: 针对大批量操作减少日志量
                    if len(batch_updates) < 100:
                        client.logger.error(error_msg)
                    
                    traceback.print_exc()  # 打印详细异常堆栈

                    # 记录失败项的信息
                    update_item["success"] = False
                    update_item["error"] = error_msg
                    update_item["error_type"] = "UpdateItemError"
                    update_item["execution_time_ms"] = int(
                        (client._get_timestamp() - update_start_time) * 1000)
                    results.append(update_item)
                    failed_updates += 1

            # 第十二阶段：提交事务
            # 所有操作完成后，提交事务
            client.conn.commit()
            transaction_status = "committed"  # 标记事务已提交

            # 第十三阶段：返回批量操作结果
            return {
                # 修复点：只要有一项成功就为 True
                "success": any(r.get("success", False) for r in results),
                "total_updates": len(batch_updates),
                "successful_updates": successful_updates,
                "failed_updates": failed_updates,
                "results": results,
                "transaction_status": transaction_status,
                "table_name": table_name,
                "timestamp": client._get_timestamp(),
                "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
            }

        except Exception as e:
            # 新增：递归判断异常链，只要有条件解析相关异常就结构化返回
            if is_condition_parse_error(e):
                error_msg = f"条件解析错误: {str(e)}"
                client.logger.error(error_msg)
                traceback.print_exc()  # 打印详细异常堆栈
                current_timestamp = client._get_timestamp()
                return {
                    "success": False,
                    "error": error_msg,
                    "error_type": "ConditionError",
                    "transaction_status": transaction_status,
                    "table_name": table_name,
                    "total_updates": len(batch_updates),
                    "successful_updates": len([r for r in results if r.get("success", False)]),
                    "failed_updates": len(batch_updates) - len([r for r in results if r.get("success", False)]),
                    "results": results,
                    "timestamp": current_timestamp,
                    "execution_time_ms": int((current_timestamp - start_time) * 1000)
                }
            # 批量处理的通用异常处理
            # 如果事务已开始但未完成，执行回滚
            if transaction_status == "started":
                try:
                    client.conn.rollback()
                except BaseException:
                    traceback.print_exc()  # 打印详细异常堆栈
                    pass
                transaction_status = "rolled_back"  # 标记事务已回滚

            # 记录错误信息
            error_msg = f"批量更新表 '{table_name}' 失败: {str(e)}"
            client.logger.error(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈

            # 获取完整的堆栈跟踪信息
            import traceback
            stack_trace = traceback.format_exc()

            # 返回错误信息
            current_timestamp = client._get_timestamp()
            return {
                "success": False,
                "error": error_msg,
                "error_type": "BatchUpdateError",
                "error_details": str(e),
                "stack_trace": stack_trace,
                "transaction_status": transaction_status,
                "table_name": table_name,
                "total_updates": len(batch_updates),
                "successful_updates": len([r for r in results if r.get("success", False)]),
                "failed_updates": len(batch_updates) - len([r for r in results if r.get("success", False)]),
                "results": results,
                "timestamp": current_timestamp,
                "execution_time_ms": int((current_timestamp - start_time) * 1000)
            }

        finally:
            # 第十四阶段：清理资源
            # 确保关闭游标，释放资源
            if cursor:
                try:
                    cursor.close()
                except BaseException:
                    traceback.print_exc()  # 打印详细异常堆栈
                    pass 