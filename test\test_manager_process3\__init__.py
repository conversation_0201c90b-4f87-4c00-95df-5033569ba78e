"""
ManagedMultiProcess 测试模块

该模块包含对 global_tools.utils.manager_process3.ManagedMultiProcess 类的全面测试，
涵盖7个核心事件的测试、边界条件测试、异常处理测试和集成测试。

测试模块结构:
- test_events_basic.py: 基础事件测试
- test_events_advanced.py: 高级事件组合测试  
- test_edge_cases.py: 边界条件和异常测试
- test_integration.py: 集成测试
- base_test.py: 基础测试类和工具函数
- test_utils.py: 测试工具函数
- mock_data.py: 模拟数据生成器

核心测试事件:
1. PROCESS_CREATED - 所有子进程创建完成后
2. PROCESS_COMPLETED - 所有子进程执行完成后立即触发
3. PROCESS_COMPLETED_WITH_DATA - 所有子进程执行完成且数据更新队列为空后
4. PROCESS_STOPPED - 所有子进程停止后立即触发
5. PROCESS_STOPPED_WITH_DATA - 所有子进程停止且数据更新队列为空后
6. PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP - 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
7. PROCESS_ALL_COMPLETED_BEFORE_STOP - 调用stop_all后，所有子进程正常执行完成(不等待数据更新队列为空)，在__perform_stop_all前触发

使用示例:
    ```python
    import unittest
    from test.test_manager_process3.test_events_basic import TestBasicEvents
    
    # 运行基础事件测试
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBasicEvents)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    ```
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入测试模块
from .base_test import BaseTestCase, EventTestMixin
from .test_utils import TestUtils, MockDataGenerator
from .mock_data import MockDataFactory

__all__ = [
    'BaseTestCase',
    'EventTestMixin', 
    'TestUtils',
    'MockDataGenerator',
    'MockDataFactory'
]
