#!/usr/bin/env python
# 简单测试脚本，直接导入sql_condition_parser模块

import logging
import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
# 配置日志记录
logging.basicConfig(
	level=logging.DEBUG,
	format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
	handlers=[ logging.StreamHandler() ]
	)

# 直接从当前目录导入
from global_tools.postgre_sql.sql_condition_parser import parse_condition


def test_condition( condition, expected=None ):
	"""测试条件解析"""
	print( f"\n测试条件: {condition}" )
	try:
		result = parse_condition( condition )
		print( f"解析结果: {result}" )
		if expected and result != expected:
			print( f"警告: 结果与预期不符" )
			print( f"预期: {expected}" )
			return False
		return True
	except Exception as e:
		print( f"解析失败: {str( e )}" )
		return False


def main():
	print( "开始测试SQL条件解析器...\n" )

	# 测试用例列表
	test_cases = [
		# 基本比较操作
		"name == '张三'",
		"age > 25",
		"age >= 18",
		"age <= 65",
		"age != 30",

		# 逻辑操作符
		"name == '张三' and age > 25",
		"name == '张三' or name == '李四'",
		"not age < 18",

		# 复合条件
		"name == '张三' and (age > 25 or city == '北京')",
		"(name == '张三' or name == '李四') and age > 18",

		# 特殊操作符
		"name is null",
		"name is not null",
		"city like '%京%'",
		"city in ('北京', '上海', '广州')",
		"age between 18 and 65",

		# 数字和字符串
		"age == 30",
		"score > 90.5",
		"name == '张三'",
		"address == \"朝阳区\"",

		# 复杂表达式
		"name == '张三' and age > 25 and (city == '北京' or city == '上海') and not is_deleted",
		"split_type->'pre_train' is not null",
		"profile->'address'->'city' =='北京'",
		"profile->>'nickname' like '%三%'",
		"profile->'score'::int > 80",
		"(profile->a->a->>b == 'x') and (profile->'c' is not null)",
		"data->arr->>0 == 'a'",

		# 布尔值字段操作
		"is_active == true",
		"is_active == false",
		"is_active",  # 隐式布尔(相当于is_active == true)
		"not is_active",  # 隐式布尔取反(相当于is_active == false)
		"is_deleted is false",
		"is_deleted is true",
		"is_verified is not false",
		"is_verified is not true",
		"is_active == true and is_verified == true",
		"is_active or is_admin",  # 两个布尔字段的OR操作
		"status->'is_approved' == true",  # JSON字段中的布尔值
		"profile->>'is_verified'::boolean is true",  # 带类型转换的JSON字段布尔值
		"not (is_deleted or is_suspended)",  # 复合布尔表达式取反
		"(is_active and is_verified) or is_admin",  # 复杂布尔表达式

		"annotation_count >= 1 and has_stable_annotation is true and inference_count >= 2"
	]

	# 运行测试
	success_count = 0
	failed_cases = [ ]
	for case in test_cases:
		if test_condition( case ):
			success_count += 1
		else:
			failed_cases.append( case )

	# 输出测试结果
	print( f"\n测试完成: {success_count}/{len( test_cases )} 个测试用例通过" )

	if success_count == len( test_cases ):
		print( "所有测试用例通过，SQL条件解析器工作正常！" )
	else:
		print( f"有 {len( test_cases ) - success_count} 个测试用例失败，请检查错误信息" )
		print( "失败的测试用例:" )
		for i, case in enumerate( failed_cases ):
			print( f"{i + 1}. {case}" )

	# 确保测试结果被完整输出
	print( "\n=========== 测试结束 ===========" )


if __name__ == "__main__":
	main()
