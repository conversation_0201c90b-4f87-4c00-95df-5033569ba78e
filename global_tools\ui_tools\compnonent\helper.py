from global_tools.ui_tools import LogOutput


class LogHandlerHelper:
    """
    日志处理辅助类，用于统一处理工作线程与UI界面的日志通信。
    
    这个辅助类提供了将工作线程中的日志信息处理并发送到UI界面的功能，
    同时处理颜色映射和格式化，减少代码重复。
    
    主要功能：
    1. 处理简单颜色日志消息
    2. 处理多样式复合日志消息
    3. 提供颜色名称到不同格式的映射
    
    用法示例：
    ```python
    # 在主线程类中创建辅助类实例
    self._log_helper = LogHandlerHelper(self._log_output)
    
    # 将工作线程的信号连接到辅助类的处理方法
    worker.log_message_signal.connect(self._log_helper.handle_log_message)
    worker.log_multi_style_message_signal.connect(self._log_helper.handle_multi_style_message)
    ```
    """
    
    def __init__(self, log_output: LogOutput):
        """
        初始化日志处理辅助类
        
        Args:
            log_output: LogOutput类实例，用于UI界面显示日志
        """
        self._log_output = log_output
        
        # 颜色名称映射字典 - 大写名称到颜色值
        self._color_map = {
            "BLUE": "blue",
            "GREEN": "green",
            "RED": "red",
            "ORANGE": "orange",
            "PURPLE": "purple",
            "BLACK": "black"
        }
    
    def handle_log_message(self, message: str, color_name: str) -> None:
        """
        处理简单的带颜色的日志消息
        
        Args:
            message: 日志消息文本
            color_name: 颜色名称或十六进制值
        """
        if self._log_output:
            # 转换颜色名称到实际颜色值
            actual_color = self._color_map.get(color_name.upper(), "black")
            self._log_output.append(message, color=actual_color)
    
    def handle_multi_style_message(self, parts: list) -> None:
        """
        处理多样式组合的日志消息
        
        Args:
            parts: 包含(文本, 样式字典)元组的列表
        """
        if self._log_output:
            processed_parts = []
            
            for item_tuple in parts:
                # 验证元组格式
                if not (isinstance(item_tuple, tuple) and len(item_tuple) == 2):
                    continue
                    
                text, style_dict = item_tuple
                
                # 验证样式字典
                if not isinstance(style_dict, dict):
                    processed_parts.append((text, {}))
                    continue

                # 处理颜色映射
                if "color" in style_dict and isinstance(style_dict["color"], str):
                    style_dict["color"] = self._color_map.get(style_dict["color"].upper(), "black")
                
                processed_parts.append((text, style_dict))
                
            # 发送到UI界面显示
            self._log_output.append_multi_style(processed_parts)

    @staticmethod
    def get_color_map() -> dict:
        """
        获取颜色名称到颜色值的映射字典
        
        Returns:
            dict: 颜色映射字典
        """
        return {
            "BLUE": "blue",
            "GREEN": "green",
            "RED": "red",
            "ORANGE": "orange",
            "PURPLE": "purple",
            "BLACK": "black"
        }