"""
PostgreSQL数据库操作异常模块

提供了一系列自定义异常类，用于精确表示数据库操作中可能出现的各种错误情况。
通过使用这些自定义异常，可以实现更精细的错误处理和更清晰的错误信息。
"""

class PostgreSQLError(Exception):
    """PostgreSQL数据库操作基础异常类"""
    
    def __init__(self, message="数据库操作错误", error_code=None, details=None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 详细错误信息
        """
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
        
    def to_dict(self):
        """
        将异常信息转换为字典格式
        
        Returns:
            dict: 包含异常详情的字典
        """
        result = {
            "error": self.message,
            "error_type": self.__class__.__name__,
        }
        
        if self.error_code:
            result["error_code"] = self.error_code
            
        if self.details:
            result["details"] = self.details
            
        return result


# 连接相关异常
class ConnectionError(PostgreSQLError):
    """数据库连接错误"""
    pass


class PoolError(ConnectionError):
    """连接池错误"""
    pass


class ConnectionTimeoutError(ConnectionError):
    """连接超时错误"""
    pass


# 查询相关异常
class QueryError(PostgreSQLError):
    """查询执行错误"""
    pass


class ExecutionError(QueryError):
    """SQL执行错误，包含详细的SQL信息"""
    
    def __init__(self, message="SQL执行错误", error_code=None, details=None, sql=None, params=None):
        """
        初始化SQL执行错误
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 详细错误信息
            sql: 执行失败的SQL语句
            params: SQL参数
        """
        super().__init__(message, error_code, details)
        self.sql = sql
        self.params = params
        
    def to_dict(self):
        """
        将异常信息转换为字典格式，包含SQL信息
        
        Returns:
            dict: 包含异常详情的字典
        """
        result = super().to_dict()
        
        if self.sql:
            result["sql"] = self.sql
            
        if self.params:
            result["params"] = str(self.params)
            
        return result


class QueryTimeoutError(QueryError):
    """查询超时错误"""
    pass


class QuerySyntaxError(QueryError):
    """SQL语法错误"""
    pass


# 数据操作相关异常
class DataError(PostgreSQLError):
    """数据操作错误"""
    pass


class TableNotExistError(DataError):
    """表不存在错误"""
    pass


class ColumnNotExistError(DataError):
    """列不存在错误"""
    pass


class DataTypeError(DataError):
    """数据类型错误"""
    pass


class UniqueViolationError(DataError):
    """唯一性约束违反错误"""
    pass


class ForeignKeyViolationError(DataError):
    """外键约束违反错误"""
    pass


class CheckViolationError(DataError):
    """检查约束违反错误"""
    pass


class NotNullViolationError(DataError):
    """非空约束违反错误"""
    pass


# 事务相关异常
class TransactionError(PostgreSQLError):
    """事务错误"""
    pass


class DeadlockError(TransactionError):
    """死锁错误"""
    pass


class SerializationError(TransactionError):
    """序列化错误"""
    pass


# 配置相关异常
class ConfigurationError(PostgreSQLError):
    """配置错误"""
    pass


# 解析相关异常
class ParseError(PostgreSQLError):
    """解析错误"""
    pass


class ConditionParseError(ParseError):
    """
    自定义条件解析异常，便于上层结构化捕获
    """
    pass


class JSONParseError(ParseError):
    """JSON解析错误"""
    pass


# 异常映射函数
def map_psycopg2_error(error):
    """
    将psycopg2异常映射为自定义异常
    
    Args:
        error: psycopg2异常对象
        
    Returns:
        PostgreSQLError: 对应的自定义异常
    """
    import psycopg2
    
    # 获取错误代码
    error_code = getattr(error, "pgcode", None)
    error_message = str(error)
    
    # 映射常见错误代码
    if error_code == "42P01":  # undefined_table
        return TableNotExistError(f"表不存在: {error_message}", error_code)
    elif error_code == "42703":  # undefined_column
        return ColumnNotExistError(f"列不存在: {error_message}", error_code)
    elif error_code == "23505":  # unique_violation
        return UniqueViolationError(f"唯一性约束违反: {error_message}", error_code)
    elif error_code == "23503":  # foreign_key_violation
        return ForeignKeyViolationError(f"外键约束违反: {error_message}", error_code)
    elif error_code == "23514":  # check_violation
        return CheckViolationError(f"检查约束违反: {error_message}", error_code)
    elif error_code == "23502":  # not_null_violation
        return NotNullViolationError(f"非空约束违反: {error_message}", error_code)
    elif error_code == "40P01":  # deadlock_detected
        return DeadlockError(f"检测到死锁: {error_message}", error_code)
    elif error_code == "40001":  # serialization_failure
        return SerializationError(f"序列化失败: {error_message}", error_code)
    elif error_code == "57014":  # query_canceled
        return QueryTimeoutError(f"查询超时或被取消: {error_message}", error_code)
    elif error_code == "42601":  # syntax_error
        return QuerySyntaxError(f"SQL语法错误: {error_message}", error_code)
    elif isinstance(error, psycopg2.OperationalError):
        return ConnectionError(f"数据库连接错误: {error_message}", error_code)
    else:
        # 默认返回基础异常
        return PostgreSQLError(f"数据库错误: {error_message}", error_code) 