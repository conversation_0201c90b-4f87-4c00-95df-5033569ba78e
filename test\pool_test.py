from global_tools.utils.manager_process2 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SharedDataManager


def process_task(
		shared_data_manager: SharedDataManager, task_item, *args,
		**kwargs
):
	# 处理任务
	print( task_item )


def main():
	tasks = [ 1, 2, 3, 4 ]
	pool_manager = ProcessPoolManager(
		process_count=3,
		target_function=process_task,
		tasks_list=tasks
	)
	pool_manager.execute_tasks(wait=True)
	# pool_manager.wait_for_completion()


if __name__ == '__main__':
	main()
