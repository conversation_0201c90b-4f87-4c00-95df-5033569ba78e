"""
PostgreSQL数据库模块入口点

当直接运行此模块时，提供基本的功能演示和测试。
通过此模块，用户可以快速了解如何使用模块提供的功能。
"""

import argparse
import json
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional

from .core_client import PostgreSQLClient
from .exceptions import PostgreSQLError
from .logger import configure_logger, get_logger


def setup_arg_parser() -> argparse.ArgumentParser:
    """
    设置命令行参数解析器
    
    Returns:
        配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        description="PostgreSQL数据库操作工具",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # 数据库连接参数
    parser.add_argument("--host", default="localhost", help="数据库主机")
    parser.add_argument("-p", "--port", type=int, default=5432, help="数据库端口")
    parser.add_argument("-d", "--database", required=True, help="数据库名称")
    parser.add_argument("-u", "--user", required=True, help="数据库用户名")
    parser.add_argument("--password", default="", help="数据库密码")
    
    # 日志参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        default="INFO", help="日志级别")
    parser.add_argument("--log-file", help="日志文件路径")
    
    # 操作参数
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 执行SQL命令
    sql_parser = subparsers.add_parser("sql", help="执行SQL命令")
    sql_parser.add_argument("sql", help="SQL命令")
    sql_parser.add_argument("--params", help="SQL参数(JSON格式)")
    
    # 创建表
    create_table_parser = subparsers.add_parser("create-table", help="创建表")
    create_table_parser.add_argument("table_name", help="表名")
    create_table_parser.add_argument("schema_file", help="表结构定义文件(JSON格式)")
    
    # 插入数据
    insert_parser = subparsers.add_parser("insert", help="插入数据")
    insert_parser.add_argument("table_name", help="表名")
    insert_parser.add_argument("data_file", help="数据文件(JSON格式)")
    
    # 更新数据
    update_parser = subparsers.add_parser("update", help="更新数据")
    update_parser.add_argument("table_name", help="表名")
    update_parser.add_argument("condition", help="条件表达式")
    update_parser.add_argument("data_file", help="更新数据文件(JSON格式)")
    
    # 查询数据
    fetch_parser = subparsers.add_parser("fetch", help="查询数据")
    fetch_parser.add_argument("table_name", help="表名")
    fetch_parser.add_argument("--condition", help="条件表达式")
    fetch_parser.add_argument("--columns", default="*", help="列名(逗号分隔)")
    fetch_parser.add_argument("--order-by", help="排序列")
    fetch_parser.add_argument("--limit", type=int, help="最大行数")
    fetch_parser.add_argument("--offset", type=int, help="起始偏移")
    fetch_parser.add_argument("--output", help="输出文件路径")
    
    # 删除数据
    delete_parser = subparsers.add_parser("delete", help="删除数据")
    delete_parser.add_argument("table_name", help="表名")
    delete_parser.add_argument("condition", help="条件表达式")
    
    # 删除表
    drop_table_parser = subparsers.add_parser("drop-table", help="删除表")
    drop_table_parser.add_argument("table_name", help="表名")
    drop_table_parser.add_argument("--force", action="store_true", help="强制删除")
    
    return parser


def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的JSON数据
        
    Raises:
        FileNotFoundError: 如果文件不存在
        json.JSONDecodeError: 如果JSON格式不正确
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 文件不存在: {file_path}")
        traceback.print_exc()
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式不正确: {e}")
        traceback.print_exc()
        sys.exit(1)


def save_json_file(file_path: str, data: Any) -> None:
    """
    保存数据到JSON文件
    
    Args:
        file_path: 文件路径
        data: 要保存的数据
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"错误: 保存文件失败: {e}")
        traceback.print_exc()
        sys.exit(1)


def execute_command(client: PostgreSQLClient, args: argparse.Namespace) -> None:
    """
    执行命令
    
    Args:
        client: PostgreSQL客户端
        args: 命令行参数
    """
    logger = get_logger("Command")
    
    try:
        if args.command == "sql":
            # 解析参数
            params = json.loads(args.params) if args.params else None
            
            # 执行SQL
            result = client.execute_query(args.sql, params)
            
            # 输出结果
            if result:
                print(json.dumps(result, ensure_ascii=False, indent=2))
            else:
                print("命令执行成功，无返回结果")
                
        elif args.command == "create-table":
            # 加载表结构
            schema = load_json_file(args.schema_file)
            
            # 创建表
            result = client.create_table(args.table_name, schema)
            
            # 输出结果
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        elif args.command == "insert":
            # 加载数据
            data = load_json_file(args.data_file)
            
            # 插入数据
            result = client.insert_data(args.table_name, data)
            
            # 输出结果
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        elif args.command == "update":
            # 加载数据
            data = load_json_file(args.data_file)
            
            # 更新数据
            result = client.update_data(args.table_name, args.condition, data)
            
            # 输出结果
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        elif args.command == "fetch":
            # 查询数据
            result = client.fetch_data(
                args.table_name,
                args.condition,
                args.columns,
                args.order_by,
                args.limit,
                args.offset
            )
            
            # 输出结果
            if args.output:
                save_json_file(args.output, result)
                print(f"查询结果已保存到: {args.output}")
            else:
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
        elif args.command == "delete":
            # 删除数据
            result = client.delete_data(args.table_name, args.condition)
            
            # 输出结果
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        elif args.command == "drop-table":
            # 删除表
            result = client.drop_table(args.table_name, args.force)
            
            # 输出结果
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
        else:
            print(f"错误: 未知命令: {args.command}")
            sys.exit(1)
            
    except PostgreSQLError as e:
        logger.error(f"命令执行失败: {e}")
        print(f"错误: {e}")
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        sys.exit(1)


def main() -> None:
    """主函数"""
    # 解析命令行参数
    parser = setup_arg_parser()
    args = parser.parse_args()
    
    # 如果没有指定命令，显示帮助信息
    if not args.command:
        parser.print_help()
        sys.exit(0)
        
    # 配置日志
    log_level = getattr(logging, args.log_level)
    configure_logger(level=log_level, log_file=args.log_file)
    logger = get_logger("Main")
    
    # 创建数据库客户端
    client = None
    try:
        logger.info(f"连接到数据库: {args.database}@{args.host}:{args.port}")
        client = PostgreSQLClient(
            host=args.host,
            port=args.port,
            database=args.database,
            user=args.user,
            password=args.password,
            log_level=log_level
        )
        
        # 执行命令
        execute_command(client, args)
        
    except PostgreSQLError as e:
        logger.error(f"数据库操作失败: {e}")
        print(f"错误: {e}")
        traceback.print_exc()
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 关闭客户端
        if client:
            client.close()


if __name__ == "__main__":
    main() 