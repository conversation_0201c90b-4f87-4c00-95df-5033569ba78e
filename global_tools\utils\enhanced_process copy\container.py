import time
from typing import Any, Dict


class ResultContainer:
    """
    用于存储中间结果的容器类。
    支持普通数据和列表数据的存储和检索。
    """

    def __init__(self):
        self._data = {}
        self._list_data = {}

    def set(self, key: str, value: Any) -> None:
        """
        设置一个键值对

        Args:
            key (str): 键名
            value (Any): 值
        """
        self._data[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取指定键的值

        Args:
            key (str): 键名
            default (Any, optional): 默认值

        Returns:
            Any: 键对应的值，如果键不存在则返回默认值
        """
        return self._data.get(key, default)

    def get_all(self) -> Dict[str, Any]:
        """
        获取所有数据

        Returns:
            Dict[str, Any]: 所有键值对
        """
        return self._data.copy()

    def append_to_list(self, key: str, value: Any) -> None:
        """
        向指定键的列表追加一个值

        Args:
            key (str): 键名
            value (Any): 要追加的值
        """
        if key not in self._list_data:
            self._list_data[key] = []
        self._list_data[key].append(value)

    def get_list(self, key: str, default: list = None) -> list:
        """
        获取指定键的列表

        Args:
            key (str): 键名
            default (list, optional): 默认值

        Returns:
            list: 键对应的列表，如果键不存在则返回默认值
        """
        return self._list_data.get(key, default)

    def get_all_lists(self) -> Dict[str, list]:
        """
        获取所有列表数据

        Returns:
            Dict[str, list]: 所有列表数据
        """
        return {k: v.copy() for k, v in self._list_data.items()}

    def clear_lists(self) -> None:
        """
        清空所有列表数据
        """
        self._list_data.clear()

    def update_lists(self, data: Dict[str, list]) -> None:
        """
        更新列表数据

        Args:
            data (Dict[str, list]): 要更新的列表数据
        """
        self._list_data.clear()
        self._list_data.update(data)

    def update_progress(self, progress: int) -> None:
        """
        更新进度

        Args:
            progress (int): 进度百分比
        """
        self._data["progress"] = f"{progress}%"
        self._data["timestamp"] = time.strftime("%H:%M:%S")
        self._data["steps"] = f"step_{progress}_completed"
