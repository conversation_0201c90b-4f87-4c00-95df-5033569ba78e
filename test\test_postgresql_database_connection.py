#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库实际连接测试

验证修复后的PostgreSQL客户端在实际数据库环境中的功能表现。
测试重点：IS NULL 和 IS NOT NULL 条件的实际执行效果。
"""

import sys
import os
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from global_tools.postgre_sql import PostgreSQLClient
from global_tools.postgre_sql.exceptions import PostgreSQLError, ConnectionError, ExecutionError


class DatabaseConnectionTester:
    """数据库连接测试器"""
    
    def __init__(self):
        self.client: Optional[PostgreSQLClient] = None
        self.test_results: List[Dict[str, Any]] = []
        
    def setup_connection(self) -> bool:
        """建立数据库连接"""
        print("=" * 80)
        print("PostgreSQL数据库连接测试")
        print("=" * 80)
        
        try:
            print("正在建立数据库连接...")
            self.client = PostgreSQLClient(
                host="localhost",
                port=5432,
                database="wow_data",
                user="postgres", 
                password="123456",
                min_connections=1,
                max_connections=5
            )
            
            print("✅ 数据库连接建立成功")
            print(f"   主机: localhost:5432")
            print(f"   数据库: wow_data")
            print(f"   用户: postgres")
            print(f"   连接池: 1-5 连接")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            print("\n可能的原因:")
            print("1. PostgreSQL服务未启动")
            print("2. 数据库 'wow_data' 不存在")
            print("3. 用户名或密码错误")
            print("4. 网络连接问题")
            return False
    
    def test_table_existence(self) -> bool:
        """测试目标表是否存在"""
        print(f"\n" + "=" * 80)
        print("表存在性检查")
        print("=" * 80)
        
        try:
            # 检查 yolo_obb 表是否存在
            result = self.client.execute_query("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'yolo_obb'
                );
            """)
            
            table_exists = result[0][0] if result else False
            
            if table_exists:
                print("✅ 表 'yolo_obb' 存在")
                
                # 获取表结构信息
                columns_result = self.client.execute_query("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = 'yolo_obb'
                    ORDER BY ordinal_position;
                """)
                
                print("   表结构:")
                for col_name, data_type, is_nullable in columns_result:
                    nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                    print(f"     {col_name:<20} {data_type:<15} {nullable}")
                
                # 检查关键字段
                column_names = [col[0] for col in columns_result]
                if 'segmentation_data' in column_names:
                    print("   ✅ 关键字段 'segmentation_data' 存在")
                else:
                    print("   ⚠️  关键字段 'segmentation_data' 不存在")
                
                if 'obb_data' in column_names:
                    print("   ✅ 对比字段 'obb_data' 存在")
                else:
                    print("   ⚠️  对比字段 'obb_data' 不存在")
                
                return True
            else:
                print("❌ 表 'yolo_obb' 不存在")
                print("\n建议:")
                print("1. 确认表名是否正确")
                print("2. 检查数据库中是否有相关表")
                print("3. 可能需要先创建测试表")
                return False
                
        except Exception as e:
            print(f"❌ 表检查失败: {e}")
            return False
    
    def test_basic_query(self) -> bool:
        """测试基础查询功能"""
        print(f"\n" + "=" * 80)
        print("基础查询功能测试")
        print("=" * 80)
        
        try:
            # 测试简单查询
            print("测试1: 简单计数查询")
            start_time = time.time()
            
            result = self.client.execute_query("SELECT COUNT(*) FROM yolo_obb;")
            total_count = result[0][0] if result else 0
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            
            print(f"   结果: 表中共有 {total_count} 条记录")
            print(f"   执行时间: {execution_time:.2f} ms")
            
            if total_count > 0:
                print("   ✅ 表中有数据，可以进行进一步测试")
                return True
            else:
                print("   ⚠️  表中无数据，将进行空表测试")
                return True
                
        except Exception as e:
            print(f"   ❌ 基础查询失败: {e}")
            return False
    
    def test_is_not_null_conditions(self) -> bool:
        """测试IS NOT NULL条件 - 核心修复验证"""
        print(f"\n" + "=" * 80)
        print("IS NOT NULL条件测试 - 核心修复验证")
        print("=" * 80)
        
        test_conditions = [
            ("segmentation_data is not null", "原始问题场景"),
            ("obb_data is not null", "对比场景"),
            ("segmentation_data is null", "IS NULL场景"),
            ("obb_data is null", "IS NULL对比场景"),
        ]
        
        all_passed = True
        
        for condition, description in test_conditions:
            print(f"\n测试: {description}")
            print(f"条件: {condition}")
            
            try:
                start_time = time.time()
                
                # 使用修复后的fetch_data方法
                data = self.client.fetch_data(
                    table_name="yolo_obb",
                    condition_str=condition
                )
                
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000
                
                record_count = len(data) if data else 0
                
                print(f"   ✅ 查询成功执行")
                print(f"   返回记录数: {record_count}")
                print(f"   执行时间: {execution_time:.2f} ms")
                
                # 记录测试结果
                self.test_results.append({
                    'condition': condition,
                    'description': description,
                    'success': True,
                    'record_count': record_count,
                    'execution_time_ms': execution_time,
                    'error': None
                })
                
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
                all_passed = False
                
                # 记录失败结果
                self.test_results.append({
                    'condition': condition,
                    'description': description,
                    'success': False,
                    'record_count': 0,
                    'execution_time_ms': 0,
                    'error': str(e)
                })
        
        return all_passed
    
    def test_complex_conditions(self) -> bool:
        """测试复杂条件组合"""
        print(f"\n" + "=" * 80)
        print("复杂条件组合测试")
        print("=" * 80)
        
        complex_conditions = [
            ("segmentation_data is not null and obb_data is not null", "双字段非空"),
            ("segmentation_data is not null or obb_data is not null", "双字段或条件"),
            ("segmentation_data is null and obb_data is not null", "混合空值条件"),
        ]
        
        all_passed = True
        
        for condition, description in complex_conditions:
            print(f"\n测试: {description}")
            print(f"条件: {condition}")
            
            try:
                start_time = time.time()
                
                data = self.client.fetch_data(
                    table_name="yolo_obb",
                    condition_str=condition
                )
                
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000
                
                record_count = len(data) if data else 0
                
                print(f"   ✅ 复杂查询成功执行")
                print(f"   返回记录数: {record_count}")
                print(f"   执行时间: {execution_time:.2f} ms")
                
            except Exception as e:
                print(f"   ❌ 复杂查询失败: {e}")
                all_passed = False
        
        return all_passed
    
    def test_performance_benchmark(self) -> bool:
        """性能基准测试"""
        print(f"\n" + "=" * 80)
        print("性能基准测试")
        print("=" * 80)
        
        try:
            condition = "segmentation_data is not null"
            iterations = 10
            
            print(f"执行 {iterations} 次查询: {condition}")
            
            execution_times = []
            
            for i in range(iterations):
                start_time = time.time()
                
                data = self.client.fetch_data(
                    table_name="yolo_obb",
                    condition_str=condition
                )
                
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000
                execution_times.append(execution_time)
                
                print(f"   第 {i+1:2d} 次: {execution_time:6.2f} ms")
            
            # 计算统计数据
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            print(f"\n性能统计:")
            print(f"   平均执行时间: {avg_time:.2f} ms")
            print(f"   最快执行时间: {min_time:.2f} ms")
            print(f"   最慢执行时间: {max_time:.2f} ms")
            print(f"   性能稳定性: {'✅ 良好' if max_time - min_time < avg_time else '⚠️ 波动较大'}")
            
            return True
            
        except Exception as e:
            print(f"❌ 性能测试失败: {e}")
            return False
    
    def generate_test_report(self) -> None:
        """生成测试报告"""
        print(f"\n" + "=" * 80)
        print("测试报告汇总")
        print("=" * 80)
        
        if not self.test_results:
            print("无测试结果数据")
            return
        
        successful_tests = [r for r in self.test_results if r['success']]
        failed_tests = [r for r in self.test_results if not r['success']]
        
        print(f"测试总数: {len(self.test_results)}")
        print(f"成功测试: {len(successful_tests)}")
        print(f"失败测试: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(self.test_results)*100:.1f}%")
        
        if successful_tests:
            print(f"\n✅ 成功的测试:")
            for result in successful_tests:
                print(f"   {result['description']}: {result['record_count']} 条记录, {result['execution_time_ms']:.2f} ms")
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for result in failed_tests:
                print(f"   {result['description']}: {result['error']}")
        
        # 核心修复验证结论
        segmentation_test = next((r for r in self.test_results if 'segmentation_data is not null' in r['condition']), None)
        
        if segmentation_test and segmentation_test['success']:
            print(f"\n🎉 核心修复验证成功!")
            print(f"   原始问题 'segmentation_data is not null' 已完全解决")
            print(f"   查询正常执行，返回 {segmentation_test['record_count']} 条记录")
            print(f"   执行时间: {segmentation_test['execution_time_ms']:.2f} ms")
        else:
            print(f"\n❌ 核心修复验证失败")
            if segmentation_test:
                print(f"   错误: {segmentation_test['error']}")
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.client:
            try:
                self.client.close()
                print(f"\n✅ 数据库连接已关闭")
            except Exception as e:
                print(f"\n⚠️  关闭连接时出错: {e}")


def main():
    """主测试函数"""
    tester = DatabaseConnectionTester()
    
    try:
        # 1. 建立连接
        if not tester.setup_connection():
            print("\n❌ 无法建立数据库连接，测试终止")
            return False
        
        # 2. 检查表存在性
        if not tester.test_table_existence():
            print("\n⚠️  目标表不存在，跳过数据测试")
            return False
        
        # 3. 基础查询测试
        if not tester.test_basic_query():
            print("\n❌ 基础查询失败，测试终止")
            return False
        
        # 4. 核心修复验证
        print("\n🎯 开始核心修复验证...")
        is_not_null_success = tester.test_is_not_null_conditions()
        
        # 5. 复杂条件测试
        complex_success = tester.test_complex_conditions()
        
        # 6. 性能测试
        performance_success = tester.test_performance_benchmark()
        
        # 7. 生成报告
        tester.generate_test_report()
        
        # 8. 总结
        overall_success = is_not_null_success and complex_success and performance_success
        
        if overall_success:
            print(f"\n🎉 所有数据库测试通过！PostgreSQL客户端修复完全成功！")
        else:
            print(f"\n⚠️  部分测试未通过，请检查具体错误信息")
        
        return overall_success
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        tester.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
