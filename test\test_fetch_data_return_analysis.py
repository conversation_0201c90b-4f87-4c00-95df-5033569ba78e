#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
fetch_data API 返回数据深度分析

详细分析 fetch_data 方法返回的数据类型、结构和内容，
确保修复后的功能完全正确。
"""

import sys
import os
import json
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from global_tools.postgre_sql import PostgreSQLClient


class FetchDataReturnAnalyzer:
    """fetch_data 返回数据分析器"""
    
    def __init__(self):
        self.client: Optional[PostgreSQLClient] = None
        self.analysis_results: List[Dict[str, Any]] = []
        
    def setup_connection(self) -> bool:
        """建立数据库连接"""
        try:
            self.client = PostgreSQLClient(
                host="localhost",
                port=5432,
                database="wow_data",
                user="postgres", 
                password="123456",
                min_connections=1,
                max_connections=5
            )
            print("✅ 数据库连接建立成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_return_structure(self, condition: str, description: str) -> Dict[str, Any]:
        """分析特定条件的返回结构"""
        print(f"\n" + "=" * 80)
        print(f"分析条件: {condition}")
        print(f"描述: {description}")
        print("=" * 80)
        
        try:
            # 调用 fetch_data 方法
            result = self.client.fetch_data(
                table_name="yolo_obb",
                condition_str=condition
            )
            
            # 分析返回数据结构
            analysis = {
                'condition': condition,
                'description': description,
                'success': True,
                'return_type': type(result).__name__,
                'return_structure': {},
                'data_analysis': {},
                'error': None
            }
            
            print(f"返回类型: {type(result).__name__}")
            
            if isinstance(result, dict):
                # 分析字典结构
                print(f"字典键: {list(result.keys())}")
                
                analysis['return_structure'] = {
                    'keys': list(result.keys()),
                    'key_types': {k: type(v).__name__ for k, v in result.items()}
                }
                
                # 详细分析每个字段
                for key, value in result.items():
                    print(f"  {key}: {type(value).__name__}")
                    if key == 'data' and isinstance(value, list):
                        print(f"    数据记录数: {len(value)}")
                        if value:
                            print(f"    第一条记录类型: {type(value[0]).__name__}")
                            if isinstance(value[0], dict):
                                print(f"    记录字段: {list(value[0].keys())}")
                                analysis['data_analysis'] = {
                                    'record_count': len(value),
                                    'first_record_type': type(value[0]).__name__,
                                    'record_fields': list(value[0].keys()) if value else [],
                                    'field_types': {k: type(v).__name__ for k, v in value[0].items()} if value else {}
                                }
                    elif key in ['count', 'execution_time_ms', 'query_time_ms']:
                        print(f"    值: {value}")
                    elif key in ['success']:
                        print(f"    值: {value}")
                    elif key in ['table_name', 'condition', 'order_by']:
                        print(f"    值: {repr(value)}")
                    elif key == 'columns' and isinstance(value, list):
                        print(f"    列名: {value}")
                    elif key == 'timestamp':
                        print(f"    时间戳: {value}")
                
                # 特别检查成功状态
                if 'success' in result:
                    if result['success']:
                        print(f"\n✅ 查询成功")
                        if 'data' in result:
                            data_count = len(result['data']) if result['data'] else 0
                            print(f"   返回数据: {data_count} 条记录")
                            
                            # 显示前几条数据示例
                            if result['data'] and data_count > 0:
                                print(f"   数据示例 (前3条):")
                                for i, record in enumerate(result['data'][:3]):
                                    print(f"     记录 {i+1}: {self._format_record_summary(record)}")
                    else:
                        print(f"\n❌ 查询失败")
                        if 'error' in result:
                            print(f"   错误信息: {result['error']}")
                        if 'error_type' in result:
                            print(f"   错误类型: {result['error_type']}")
                        analysis['error'] = result.get('error', 'Unknown error')
                        analysis['success'] = False
            else:
                print(f"⚠️  意外的返回类型: {type(result)}")
                analysis['return_structure'] = {'unexpected_type': str(type(result))}
            
            return analysis
            
        except Exception as e:
            print(f"❌ 分析过程中发生异常: {e}")
            analysis = {
                'condition': condition,
                'description': description,
                'success': False,
                'return_type': 'Exception',
                'return_structure': {},
                'data_analysis': {},
                'error': str(e)
            }
            import traceback
            traceback.print_exc()
            return analysis
    
    def _format_record_summary(self, record: Dict[str, Any]) -> str:
        """格式化记录摘要"""
        if not isinstance(record, dict):
            return str(record)
        
        # 显示关键字段
        key_fields = ['detection_id', 'image_id', 'segmentation_data', 'obb_data']
        summary_parts = []
        
        for field in key_fields:
            if field in record:
                value = record[field]
                if value is None:
                    summary_parts.append(f"{field}=NULL")
                elif isinstance(value, (dict, list)):
                    summary_parts.append(f"{field}={type(value).__name__}({len(value) if hasattr(value, '__len__') else '?'})")
                else:
                    summary_parts.append(f"{field}={repr(value)[:20]}...")
        
        return "{" + ", ".join(summary_parts) + "}"
    
    def run_comprehensive_analysis(self) -> bool:
        """运行全面的返回数据分析"""
        print("=" * 80)
        print("fetch_data API 返回数据深度分析")
        print("=" * 80)
        
        # 测试场景列表
        test_scenarios = [
            # 核心修复验证
            ("segmentation_data is not null", "原始问题场景 - 修复验证"),
            ("obb_data is not null", "对比场景 - 正常工作"),
            
            # NULL 条件测试
            ("segmentation_data is null", "IS NULL 条件"),
            ("obb_data is null", "IS NULL 对比"),
            
            # 复杂条件测试
            ("segmentation_data is not null and obb_data is not null", "双字段非空条件"),
            ("segmentation_data is not null or obb_data is not null", "双字段或条件"),
            
            # 边界条件测试
            ("detection_id > 0", "数值比较条件"),
            ("image_path like '%jpg%'", "字符串模式匹配"),
            
            # 错误条件测试（应该失败的）
            ("nonexistent_field is not null", "不存在字段"),
            ("invalid syntax condition", "无效语法"),
        ]
        
        all_success = True
        
        for condition, description in test_scenarios:
            analysis = self.analyze_return_structure(condition, description)
            self.analysis_results.append(analysis)
            
            # 检查是否符合预期
            if "原始问题场景" in description:
                # 这是最关键的测试 - 必须成功
                if not analysis['success'] or analysis.get('error'):
                    print(f"❌ 关键测试失败: {description}")
                    all_success = False
                else:
                    print(f"✅ 关键测试通过: {description}")
        
        return all_success
    
    def generate_analysis_report(self) -> None:
        """生成分析报告"""
        print(f"\n" + "=" * 80)
        print("返回数据分析报告")
        print("=" * 80)
        
        if not self.analysis_results:
            print("无分析数据")
            return
        
        successful_tests = [r for r in self.analysis_results if r['success']]
        failed_tests = [r for r in self.analysis_results if not r['success']]
        
        print(f"测试总数: {len(self.analysis_results)}")
        print(f"成功测试: {len(successful_tests)}")
        print(f"失败测试: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(self.analysis_results)*100:.1f}%")
        
        # 分析返回数据结构的一致性
        print(f"\n📊 返回数据结构分析:")
        
        if successful_tests:
            # 检查成功测试的返回结构一致性
            first_success = successful_tests[0]
            structure_keys = set(first_success['return_structure'].get('keys', []))
            
            print(f"   成功返回的标准结构键: {sorted(structure_keys)}")
            
            # 检查所有成功测试是否有一致的结构
            consistent_structure = True
            for test in successful_tests[1:]:
                test_keys = set(test['return_structure'].get('keys', []))
                if test_keys != structure_keys:
                    consistent_structure = False
                    break
            
            print(f"   结构一致性: {'✅ 一致' if consistent_structure else '❌ 不一致'}")
            
            # 分析数据字段
            data_tests = [t for t in successful_tests if t['data_analysis'].get('record_count', 0) > 0]
            if data_tests:
                print(f"   有数据的测试: {len(data_tests)}")
                first_data_test = data_tests[0]
                record_fields = first_data_test['data_analysis'].get('record_fields', [])
                print(f"   数据记录字段: {sorted(record_fields)}")
                
                # 检查关键字段
                key_fields = ['detection_id', 'segmentation_data', 'obb_data']
                for field in key_fields:
                    if field in record_fields:
                        print(f"     ✅ {field} 字段存在")
                    else:
                        print(f"     ❌ {field} 字段缺失")
        
        # 核心修复验证结论
        original_problem_test = next((r for r in self.analysis_results if 'segmentation_data is not null' in r['condition']), None)
        
        print(f"\n🎯 核心修复验证结论:")
        if original_problem_test:
            if original_problem_test['success'] and not original_problem_test.get('error'):
                print(f"   ✅ 原始问题 'segmentation_data is not null' 完全修复")
                print(f"   ✅ 返回类型: {original_problem_test['return_type']}")
                print(f"   ✅ 返回结构: 标准字典格式")
                if original_problem_test['data_analysis'].get('record_count', 0) > 0:
                    print(f"   ✅ 数据内容: {original_problem_test['data_analysis']['record_count']} 条记录")
                    print(f"   ✅ 数据格式: 字典列表，包含完整字段")
                else:
                    print(f"   ✅ 查询执行成功，但无匹配数据（这是正常的）")
            else:
                print(f"   ❌ 原始问题仍未修复")
                print(f"   ❌ 错误: {original_problem_test.get('error', 'Unknown')}")
        else:
            print(f"   ❌ 未找到原始问题的测试结果")
        
        # 失败测试分析
        if failed_tests:
            print(f"\n❌ 失败测试详情:")
            for test in failed_tests:
                print(f"   条件: {test['condition']}")
                print(f"   错误: {test.get('error', 'Unknown')}")
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.client:
            try:
                self.client.close()
                print(f"\n✅ 数据库连接已关闭")
            except Exception as e:
                print(f"\n⚠️  关闭连接时出错: {e}")


def main():
    """主分析函数"""
    analyzer = FetchDataReturnAnalyzer()
    
    try:
        # 1. 建立连接
        if not analyzer.setup_connection():
            print("\n❌ 无法建立数据库连接，分析终止")
            return False
        
        # 2. 运行全面分析
        success = analyzer.run_comprehensive_analysis()
        
        # 3. 生成报告
        analyzer.generate_analysis_report()
        
        # 4. 总结
        if success:
            print(f"\n🎉 fetch_data API 返回数据分析完成！修复验证成功！")
        else:
            print(f"\n⚠️  发现问题，需要进一步检查")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 分析过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
