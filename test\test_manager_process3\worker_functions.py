"""
可序列化的工作函数

定义在模块级别的工作函数，可以被 pickle 序列化用于多进程。
"""

import sys
import os
import time
import random

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def simple_worker(shared_data_manager, item, delay=0.1, *args, **kwargs):
    """
    简单的工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        delay: 处理延迟时间（秒）
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    time.sleep(delay)

    # 更新处理计数（使用锁保证原子性）
    lock = shared_data_manager.get_lock()
    with lock:
        count = shared_data_manager.get_value("processed_count", 0)
        shared_data_manager.add_value("processed_count", count + 1)

    # 记录处理结果
    results = shared_data_manager.get_list("results", [])
    results.append(f"processed_{item}")
    shared_data_manager.append_to_list("results", f"processed_{item}")

    return f"result_{item}"


def quick_worker(shared_data_manager, item, delay=0.05, *args, **kwargs):
    """
    快速工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        delay: 处理延迟时间（秒）
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    time.sleep(delay)

    shared_data_manager.add_value(f"quick_result_{item}", f"quick_{item}")
    return f"quick_{item}"


def cpu_intensive_worker(shared_data_manager, item, duration=0.5, *args, **kwargs):
    """
    CPU密集型工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        duration: 工作持续时间（秒）
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    start_time = time.time()
    result = 0

    # CPU密集型计算
    while time.time() - start_time < duration:
        result += sum(range(1000))

    shared_data_manager.add_value(f"cpu_result_{item}", result)
    return f"cpu_processed_{item}"


def io_intensive_worker(shared_data_manager, item, delay=0.3, *args, **kwargs):
    """
    IO密集型工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        delay: IO延迟时间（秒）
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    # 模拟IO操作
    time.sleep(delay)

    shared_data_manager.add_value(f"io_result_{item}", f"io_data_{item}")
    return f"io_processed_{item}"


def error_prone_worker(shared_data_manager, item, error_rate=0.3, *args, **kwargs):
    """
    容易出错的工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        error_rate: 错误率（0.0-1.0）
        *args, **kwargs: 额外参数

    Returns:
        处理结果

    Raises:
        ValueError: 模拟错误
    """
    if random.random() < error_rate:
        raise ValueError(f"模拟错误: item {item}")

    time.sleep(0.1)
    shared_data_manager.add_value(f"success_{item}", True)
    return f"success_{item}"


def data_sharing_worker(shared_data_manager, item, *args, **kwargs):
    """
    数据共享工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    # 更新共享计数器（使用锁保证原子性）
    lock = shared_data_manager.get_lock()
    with lock:
        counter = shared_data_manager.get_value("counter", 0)
        shared_data_manager.add_value("counter", counter + 1)

    # 添加到共享列表
    shared_data_manager.append_to_list("processed_items", item)

    return f"shared_{item}"


def long_running_worker(shared_data_manager, item, duration=1.5, *args, **kwargs):
    """
    长时间运行的工作函数

    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        duration: 运行持续时间（秒）
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    time.sleep(duration)

    shared_data_manager.add_value(f"long_result_{item}", f"long_data_{item}")
    return f"long_processed_{item}"


# 工作函数映射表
WORKER_FUNCTIONS = {
    "simple": simple_worker,
    "quick": quick_worker,
    "cpu_intensive": cpu_intensive_worker,
    "io_intensive": io_intensive_worker,
    "error_prone": error_prone_worker,
    "data_sharing": data_sharing_worker,
    "long_running": long_running_worker
}


def get_worker_function(worker_type: str, **params):
    """
    获取工作函数

    Args:
        worker_type: 工作函数类型
        **params: 工作函数参数（目前忽略，使用函数默认参数）

    Returns:
        工作函数
    """
    if worker_type not in WORKER_FUNCTIONS:
        raise ValueError(f"未知的工作函数类型: {worker_type}")

    # 直接返回原始函数，参数通过函数默认值处理
    return WORKER_FUNCTIONS[worker_type]
