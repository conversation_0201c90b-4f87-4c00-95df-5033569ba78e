#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import logging
import traceback
import time
from typing import Callable, Dict, List, Any, Optional, Union, Set, Tuple

# 配置日志
logger = logging.getLogger(__name__)


class ProcessEventManager:
    """
    ProcessEventManager - 进程事件管理器

    该类负责管理与ManagedMultiProcess结合使用的事件监听和回调函数，
    提供了统一的接口来注册和触发各类事件。

    核心功能:
    ----------
    - **事件注册**: 允许注册回调函数到特定事件
    - **事件触发**: 在特定条件下触发事件并执行回调
    - **异步执行**: 在单独线程中执行回调，不阻塞主线程
    - **参数传递**: 支持向回调函数传递额外参数
    - **事件组**: 支持预定义的事件组，简化API

    预定义事件:
    ----------
    - PROCESS_CREATED: 所有子进程创建完成后触发
    - PROCESS_COMPLETED: 所有子进程执行完成后立即触发
    - PROCESS_COMPLETED_WITH_DATA: 所有子进程执行完成且数据更新队列为空后触发
    - PROCESS_STOPPED: 所有子进程停止后立即触发
    - PROCESS_STOPPED_WITH_DATA: 所有子进程停止且数据更新队列为空后触发

    使用示例:
    ----------
    ```python
    # 创建ManagedMultiProcess实例
    mp = ManagedMultiProcess(...)

    # 注册事件回调
    def on_all_complete(mp_instance, *args, **kwargs):
        print("所有任务完成！")
        results = mp_instance.get_results()
        print(f"最终结果: {results}")

    # 监听任务完成事件
    mp.listen_event("PROCESS_COMPLETED", on_all_complete, "额外参数1", tag="demo")

    # 启动任务处理
    mp.run()
    ```
    """

    # 预定义事件常量
    PROCESS_CREATED = "PROCESS_CREATED"  # 事件1：所有子进程创建完成后
    PROCESS_COMPLETED = "PROCESS_COMPLETED"  # 事件2：所有子进程执行完成后立即触发
    PROCESS_COMPLETED_WITH_DATA = "PROCESS_COMPLETED_WITH_DATA"  # 事件3：所有子进程执行完成且数据更新队列为空后
    PROCESS_STOPPED = "PROCESS_STOPPED"  # 事件4：所有子进程停止后立即触发
    PROCESS_STOPPED_WITH_DATA = "PROCESS_STOPPED_WITH_DATA"  # 事件5：所有子进程停止且数据更新队列为空后

    def __init__(self, mp_instance=None):
        """
        初始化ProcessEventManager实例。

        Args:
            mp_instance: 关联的ManagedMultiProcess实例，可选。
                        如果不在初始化时提供，可以稍后通过set_mp_instance方法设置。
        """
        # 事件回调注册表
        self.__event_handlers: Dict[str, List[Tuple[Callable, tuple, dict]]] = {}

        # 记录事件触发状态，避免重复触发
        self.__triggered_events: Set[str] = set()

        # 关联的ManagedMultiProcess实例
        self.__mp_instance = mp_instance

        # 锁，用于保护事件注册表和触发状态
        self.__lock = threading.RLock()

        # 初始化预定义事件
        for event in [self.PROCESS_CREATED, self.PROCESS_COMPLETED,
                      self.PROCESS_COMPLETED_WITH_DATA, self.PROCESS_STOPPED,
                      self.PROCESS_STOPPED_WITH_DATA]:
            self.__event_handlers[event] = []

        logger.debug(f"ProcessEventManager初始化完成，预定义了{len(self.__event_handlers)}个事件")

    def set_mp_instance(self, mp_instance):
        """
        设置关联的ManagedMultiProcess实例。

        Args:
            mp_instance: ManagedMultiProcess实例
        """
        with self.__lock:
            self.__mp_instance = mp_instance
            logger.debug(f"已设置关联的ManagedMultiProcess实例: {mp_instance}")

    def register_event_handler(self, event_key: str, handler: Callable, *args, **kwargs) -> bool:
        """
        注册事件处理函数。

        Args:
            event_key: 事件键名，可以是预定义事件或自定义事件名
            handler: 回调函数，第一个参数会接收ManagedMultiProcess实例
            *args: 传递给回调函数的额外位置参数
            **kwargs: 传递给回调函数的额外关键字参数

        Returns:
            bool: 是否成功注册事件处理函数
        """
        if not callable(handler):
            logger.error(f"事件 '{event_key}' 的处理函数必须是可调用的")
            return False

        try:
            with self.__lock:
                # 确保事件键存在
                if event_key not in self.__event_handlers:
                    self.__event_handlers[event_key] = []

                # 注册回调函数及其参数
                handler_data = (handler, args, kwargs)
                self.__event_handlers[event_key].append(handler_data)

                # 如果事件已触发，立即执行新注册的处理函数
                if event_key in self.__triggered_events and self.__mp_instance:
                    self.__execute_handler(handler, self.__mp_instance, *args, **kwargs)

                logger.debug(f"已为事件 '{event_key}' 注册处理函数: {handler.__name__}")
                return True
        except Exception as e:
            logger.error(f"注册事件 '{event_key}' 处理函数时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def unregister_event_handler(self, event_key: str, handler: Optional[Callable] = None) -> bool:
        """
        取消注册事件处理函数。

        Args:
            event_key: 事件键名
            handler: 要取消的特定回调函数。如果为None，则移除所有关联该事件的处理函数。

        Returns:
            bool: 是否成功取消注册
        """
        try:
            with self.__lock:
                if event_key not in self.__event_handlers:
                    logger.warning(f"事件 '{event_key}' 未注册任何处理函数")
                    return False

                if handler is None:
                    # 移除所有处理函数
                    self.__event_handlers[event_key].clear()
                    logger.debug(f"已移除事件 '{event_key}' 的所有处理函数")
                    return True
                else:
                    # 移除特定处理函数
                    original_count = len(self.__event_handlers[event_key])
                    self.__event_handlers[event_key] = [
                        h for h in self.__event_handlers[event_key]
                        if h[0] != handler
                    ]
                    new_count = len(self.__event_handlers[event_key])

                    if original_count > new_count:
                        logger.debug(f"已移除事件 '{event_key}' 的指定处理函数")
                        return True
                    else:
                        logger.warning(f"未找到事件 '{event_key}' 的指定处理函数")
                        return False
        except Exception as e:
            logger.error(f"取消注册事件 '{event_key}' 处理函数时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def trigger_event(self, event_key: str, mp_instance=None) -> bool:
        """
        触发指定事件，执行所有注册的回调函数。

        Args:
            event_key: 要触发的事件键名
            mp_instance: ManagedMultiProcess实例，如果为None则使用已关联的实例

        Returns:
            bool: 是否成功触发事件
        """
        try:
            # 使用传入的实例或已关联的实例
            instance = mp_instance if mp_instance is not None else self.__mp_instance
            if instance is None:
                logger.error(f"触发事件 '{event_key}' 失败: 未关联ManagedMultiProcess实例")
                return False

            with self.__lock:
                # 检查事件是否已注册处理函数
                if event_key not in self.__event_handlers:
                    logger.warning(f"事件 '{event_key}' 未注册任何处理函数")
                    # 仍将事件标记为已触发
                    self.__triggered_events.add(event_key)
                    return True

                # 标记事件为已触发
                self.__triggered_events.add(event_key)

                # 复制处理函数列表，避免回调过程中的修改影响遍历
                handlers = list(self.__event_handlers[event_key])

            # 在锁外执行回调，避免长时间持有锁
            for handler, args, kwargs in handlers:
                self.__execute_handler(handler, instance, *args, **kwargs)

            logger.info(f"已触发事件 '{event_key}'，执行了 {len(handlers)} 个处理函数")
            return True
        except Exception as e:
            logger.error(f"触发事件 '{event_key}' 时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def __execute_handler(self, handler: Callable, mp_instance, *args, **kwargs) -> None:
        """
        在单独的线程中执行事件处理函数。

        Args:
            handler: 要执行的回调函数
            mp_instance: 要传递给回调函数的ManagedMultiProcess实例
            *args: 传递给回调函数的额外位置参数
            **kwargs: 传递给回调函数的额外关键字参数
        """
        def _run_handler():
            try:
                logger.debug(f"开始执行事件处理函数: {handler.__name__}")
                handler(mp_instance, *args, **kwargs)
                logger.debug(f"事件处理函数执行完成: {handler.__name__}")
            except Exception as e:
                logger.error(f"执行事件处理函数 {handler.__name__} 时出错: {e}")
                logger.error(traceback.format_exc())

        # 创建并启动线程
        thread = threading.Thread(target=_run_handler)
        thread.daemon = True
        thread.start()
        logger.debug(f"已在单独线程中启动事件处理函数: {handler.__name__}")

    def reset_event(self, event_key: str) -> bool:
        """
        重置事件的触发状态，允许再次触发。

        Args:
            event_key: 要重置的事件键名

        Returns:
            bool: 是否成功重置事件
        """
        try:
            with self.__lock:
                if event_key in self.__triggered_events:
                    self.__triggered_events.remove(event_key)
                    logger.debug(f"已重置事件 '{event_key}' 的触发状态")
                    return True
                return False
        except Exception as e:
            logger.error(f"重置事件 '{event_key}' 触发状态时出错: {e}")
            logger.error(traceback.format_exc())
            return False

    def reset_all_events(self) -> None:
        """
        重置所有事件的触发状态。
        """
        try:
            with self.__lock:
                self.__triggered_events.clear()
                logger.debug("已重置所有事件的触发状态")
        except Exception as e:
            logger.error(f"重置所有事件触发状态时出错: {e}")
            logger.error(traceback.format_exc())

    def is_event_triggered(self, event_key: str) -> bool:
        """
        检查事件是否已被触发。

        Args:
            event_key: 要检查的事件键名

        Returns:
            bool: 如果事件已触发返回True，否则返回False
        """
        with self.__lock:
            return event_key in self.__triggered_events

    def get_all_events(self) -> List[str]:
        """
        获取所有已注册的事件键名列表。

        Returns:
            List[str]: 已注册的事件键名列表
        """
        with self.__lock:
            return list(self.__event_handlers.keys())

    def get_triggered_events(self) -> List[str]:
        """
        获取所有已触发的事件键名列表。

        Returns:
            List[str]: 已触发的事件键名列表
        """
        with self.__lock:
            return list(self.__triggered_events)

    def has_handlers(self, event_key: str) -> bool:
        """
        检查事件是否有注册的处理函数。

        Args:
            event_key: 要检查的事件键名

        Returns:
            bool: 如果事件有处理函数返回True，否则返回False
        """
        with self.__lock:
            return event_key in self.__event_handlers and len(self.__event_handlers[event_key]) > 0
