#!/usr/bin/env python
# -*- coding: utf-8 -*-

import traceback
import json
import os
import time
from typing import List, Optional, Union, Callable, Dict, Any, Tuple, Generic, TypeVar
import logging
from PyQt5.QtCore import (
    Qt,
    QEvent,
    pyqtSignal,
    QTimer,
    QObject,
    QRunnable,
    QThreadPool,
    QPoint,
    QRect,
)
from PyQt5.QtWidgets import (
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QVBoxLayout,
    QWidget,
    QApplication,
    QLabel,
    QAbstractItemView,
)
from PyQt5.QtGui import QColor, QPalette, QFont, QCursor, QFocusEvent
from global_tools.utils import ClassInstanceManager, Logger


# 配置基本的日志记录器
# 在实际应用中，您可能希望从外部配置文件或主应用程序设置日志记录
# logging.basicConfig(
#     level=logging.INFO,  # 设置日志级别为 INFO 或更高级别
#     format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # 日志格式
#     datefmt="%Y-%m-%d %H:%M:%S",  # 时间格式
# )
# logging.disable(logging.CRITICAL)


class InputCompleter(QObject):
    """
    为 QLineEdit 提供一个基于输入内容动态过滤的自动完成下拉列表。

    当用户在关联的 QLineEdit 中输入文本时，此类会显示一个包含匹配项的下拉列表。
    此外，当QLineEdit获取焦点时，如果有可用的补全项，也会立即显示下拉列表。
    当QLineEdit的文本从有内容变为空时，如果有可用的补全项，也会显示下拉列表，无需等待用户输入。
    列表的宽度与 QLineEdit 相同，并紧贴其下方显示。
    用户可以通过键盘上下箭头导航列表，按 Enter 键或鼠标点击选择一个项目，
    选中的项目文本将填充到 QLineEdit 中。按 Esc 键可以隐藏列表。
    列表本身不会获取焦点，确保焦点始终保留在 QLineEdit 中。

    主要特性:
    - 实时过滤: 根据输入动态更新下拉列表内容。
    - 焦点保持: 下拉列表出现时，焦点仍在原 QLineEdit。
    - 焦点获取时显示: 当QLineEdit获取焦点且有补全项可用时，立即显示下拉列表。
    - 文本清空时显示: 当QLineEdit文本从有内容变为空时，显示所有补全项。
    - 键盘导航: 支持上/下箭头选择，Enter 确认，Esc 取消。
    - 鼠标交互: 支持点击列表项选择。
    - 可靠点击处理: 使用直接的鼠标事件监听确保点击列表项一定能正确填充文本。
    - 自动定位: 列表自动定位在 QLineEdit 下方，宽度一致。
    - 可配置数据源: 可以通过 `set_completion_items` 方法更新补全列表。
    - 回调函数支持: 通过 `set_completion_callback` 方法设置回调函数，异步获取补全列表。
    - 原始顺序保持: 下拉列表中的项目按照原始列表的顺序排列，不进行重新排序。
    - 健壮性: 包含错误处理和日志记录。
    - 鼠标事件稳定: 通过额外的事件过滤器确保列表项点击可靠，解决焦点冲突问题。
    - 失焦内容处理: 支持在QLineEdit失去焦点时获取内容并异步处理，可通过回调函数处理这些内容。

    使用示例:
        ```python
        # 1. 基本使用方法
        # 假设 'line_edit' 是一个 QLineEdit 实例
        # completion_list 是一个包含可能完成项的字符串列表
        completion_list = ["apple", "banana", "apricot", "blueberry", "avocado"]
        completer = InputCompleter(line_edit, completion_list)
        # 当用户点击或通过Tab键切换到line_edit时，会自动显示所有补全项
        # 用户输入内容时，列表会根据输入过滤
        # 当用户清空输入框内容时，也会显示所有补全项

        # 2. 高级用法 - 动态更新补全列表
        # 先创建实例，后设置补全项
        completer = InputCompleter(line_edit)  # 初始无补全项
        # 稍后更新列表
        new_list = ["new item 1", "new item 2"]
        completer.set_completion_items(new_list)

        # 3. 使用回调函数异步获取补全项
        def my_callback(text):
            # 可以在这里执行耗时操作，如数据库查询、网络请求等
            # 返回与 text 相关的补全项列表
            # 注意：当text为空字符串时，此函数应返回所有可能的补全项
            return [item for item in all_items if text.lower() in item.lower()] if text else all_items

        completer = InputCompleter(line_edit)
        completer.set_completion_callback(my_callback)
        # 当line_edit获取焦点时，my_callback将被调用一次
        # 传入空字符串("")作为参数，以获取初始补全列表
        # 当用户清空输入框时，my_callback也会被调用，传入空字符串

        # 4. 在类中使用
        class MyWidget(QWidget):
            def __init__(self):
                super().__init__()
                self.line_edit = QLineEdit()
                self.layout = QVBoxLayout()
                self.layout.addWidget(self.line_edit)
                self.setLayout(self.layout)

                # 创建并存储 InputCompleter 引用，防止被垃圾回收
                self.completer = InputCompleter(
                    self.line_edit,
                    ["item1", "item2", "item3"],
                    parent=self  # parent=self 确保 completer 生命周期与窗口关联
                )

        # 5. 使用失焦内容处理功能
        def focus_lost_callback(text_list):
            # 处理QLineEdit失去焦点时保存的文本列表
            for text in text_list:
                print(f"处理输入内容: {text}")

        completer = InputCompleter(line_edit, completion_list)
        completer.set_focus_lost_callback(focus_lost_callback)
        # 当用户在line_edit中输入内容并切换焦点到其他控件时
        # focus_lost_callback会被异步调用，参数为用户输入的内容列表

        # 6. 自定义字体配置
        completer = InputCompleter(
            line_edit,
            completion_list,
            font_family="Consolas, SimHei, monospace",  # 自定义字体族
            font_size=14,                               # 自定义字体大小
            font_weight=400                             # 自定义字体粗细 (400=normal, 600=semibold, 700=bold)
        )
        # 下拉列表将使用指定的字体样式显示
        ```

    注意事项:
    - 确保保持对 InputCompleter 实例的引用，防止被垃圾回收。
    - 当 QLineEdit 被销毁时，InputCompleter 会自动清理资源。
    - 此类使用事件过滤器监听 QLineEdit 的事件，请确保不与其他事件过滤器冲突。
    - 通过使用 Qt.ToolTip 而非 Qt.Popup 窗口，解决了弹出列表时输入框可能无法输入的问题。
    - 设置回调函数后，将优先使用回调函数来获取补全列表，而非使用 `set_completion_items` 设置的静态列表。
    - 下拉列表中的项目顺序与原始列表顺序一致，即使在过滤后也保持相对顺序不变。
    - 列表项点击处理通过多重机制确保可靠性，包括直接鼠标事件处理和延迟列表隐藏。
    - 当文本从有内容变为空时，将显示所有补全项，这对于常见的"清空后重新选择"场景非常有用。
    - 如果使用回调函数，应确保回调函数能适当处理空字符串输入，返回所有可能的补全项。
    - 使用失焦处理功能时，请确保回调函数能正确处理文本列表参数，并适当处理异常情况。
    """

    # 类级别的日志记录器
    __logger = logging.getLogger("InputCompleter")
    __logger.setLevel(logging.INFO)
    # __logger:Logger = ClassInstanceManager.get_instance(key="ui_logger")

    # 异步任务执行器类，用于在线程池中执行回调函数
    class __Worker(QRunnable):
        """
        异步任务执行器类，用于在后台线程中执行耗时的回调函数。

        此类继承自 QRunnable，可以被提交到 QThreadPool 中执行。
        执行完成后，通过信号通知主线程处理结果。
        """

        # 定义一个自定义信号类，用于在任务完成时发出信号
        class WorkerSignals(QObject):
            """用于在线程间通信的信号类"""

            # 定义任务完成信号：参数为回调返回的结果列表和原始输入文本
            finished = pyqtSignal(list, str)
            # 定义错误信号：参数为异常信息、堆栈跟踪和原始输入文本
            error = pyqtSignal(str, str, str)

        def __init__(self, callback_fn, text):
            """
            初始化工作任务。

            Args:
                callback_fn (callable): 回调函数，接受文本参数并返回补全项列表
                text (str): 当前输入框的文本
            """
            super().__init__()
            self.callback_fn = callback_fn
            self.text = text
            self.signals = self.WorkerSignals()

        def run(self):
            """执行回调函数，并通过信号发送结果或错误"""
            try:
                # 执行回调函数，获取结果
                result = self.callback_fn(self.text)

                # 验证结果是否为列表类型
                if result is None:
                    result = []  # 转换为空列表
                elif not isinstance(result, list):
                    try:
                        # 尝试将结果转换为列表（如果是可迭代对象）
                        result = list(result)
                    except (TypeError, ValueError):
                        # 如果转换失败，记录错误并使用空列表
                        error_msg = (
                            f"回调函数返回的结果无法转换为列表: {type(result).__name__}")
                        stack = traceback.format_stack()
                        self.signals.error.emit(error_msg, "".join(stack),
                                                self.text)
                        return

                # 通过信号发送结果和原始文本
                self.signals.finished.emit(result, self.text)

            except Exception as e:
                # 捕获并通过信号发送任何异常
                stack_trace = traceback.format_exc()
                error_message = str(e)
                self.signals.error.emit(error_message, stack_trace, self.text)

    class __FocusLostWorker(QRunnable):
        """
        用于异步处理QLineEdit失焦时的文本内容的工作线程类。

        该工作线程会在QLineEdit失去焦点时被触发，异步处理队列中的文本内容。
        通过信号与主线程通信，报告执行状态和结果。
        """

        class WorkerSignals(QObject):
            """用于在线程间通信的信号类"""

            # 定义任务完成信号：无参数
            finished = pyqtSignal()
            # 定义错误信号：参数为异常信息和堆栈跟踪
            error = pyqtSignal(str, str)

        def __init__(self, callback_fn, text_list):
            """
            初始化工作线程。

            Args:
                callback_fn: 处理文本列表的回调函数
                text_list: 要处理的文本列表
            """
            super().__init__()
            self.callback_fn = callback_fn
            self.text_list = text_list
            self.signals = self.WorkerSignals()

        def run(self):
            """
            执行工作线程的主要逻辑。

            调用回调函数处理文本列表，并通过信号通知执行状态和结果。
            """
            try:
                # 调用回调函数处理文本列表
                import logging

                logger = logging.getLogger("InputCompleter.FocusLostWorker")
                logger.debug("开始执行回调函数，参数: %s", self.text_list)
                self.callback_fn(self.text_list)
                logger.debug("回调函数执行完成")
                # 发送完成信号
                self.signals.finished.emit()
            except Exception as e:
                # 发送错误信号，携带异常信息和堆栈跟踪
                import traceback

                logger.error("执行回调函数时出错: %s\n%s", e, traceback.format_exc())
                self.signals.error.emit(str(e), traceback.format_exc())

    # 添加手动触发处理队列的公共方法
    def process_focus_lost_queue(self) -> None:
        """
        手动触发处理失焦内容队列。

        此方法可被外部调用，强制处理当前队列中的内容。
        这在特定情况下，希望立即处理队列内容时很有用。

        示例:
            ```python
            # 在某些特殊情况下，可以手动触发处理
            def on_special_event():
                completer.process_focus_lost_queue()
            ```
        """
        self.__logger.debug("手动触发处理失焦内容队列")
        self.__process_focus_lost_queue()

    def __init__(
        self,
        line_edit: QLineEdit,
        completion_items: Optional[List[str]] = None,
        parent: Optional[QObject] = None,
        font_family: str = "JetBrains Mono SemiBold, Microsoft YaHei, monospace",
        font_size: int = 12,
        font_weight: int = 600,
    ):
        """
        初始化 InputCompleter 实例。

        Args:
            line_edit: 要关联的 QLineEdit 控件。
            completion_items: 补全列表项，可选，默认为 None。
            parent: 父对象，可选，默认为 None。
            font_family: 下拉列表字体族，默认为 "JetBrains Mono SemiBold, Microsoft YaHei, monospace"。
            font_size: 下拉列表字体大小，默认为 12。
            font_weight: 下拉列表字体粗细，默认为 600 (SemiBold)。
        """
        super().__init__(parent)

        if not isinstance(line_edit, QLineEdit):
            self.__logger.error(
                "初始化失败: 'line_edit' 参数必须是 QLineEdit 类型，但收到了 %s",
                type(line_edit).__name__,
            )
            raise TypeError("'line_edit' must be an instance of QLineEdit")

        self.__line_edit: QLineEdit = line_edit

        # 保存字体配置参数
        self.__font_family = font_family
        self.__font_size = font_size
        self.__font_weight = font_weight

        # 1. 先创建和设置好 __popup_list
        # 创建下拉列表框，父窗口设为 line_edit 的顶层窗口
        self.__popup_list: QListWidget = QListWidget(self.__line_edit.window())
        self.__setup_popup_list()

        # 2. 再初始化和设置补全项列表
        self.__completion_items: List[str] = []  # 初始化为空列表
        # 初始时就设置一次补全项，这时 __popup_list 已存在
        self.set_completion_items(
            completion_items if completion_items is not None else [])

        # 添加回调函数相关属性
        self.__completion_callback = None  # 初始无回调函数
        self.__thread_pool = QThreadPool()  # 创建线程池
        self.__last_request_text = ""  # 记录最后一次请求的文本
        self.__last_update_text = ""  # 记录最后一次更新的文本
        self.__previous_text = ""  # 记录上一次的文本内容，用于检测文本清空事件

        # 添加标志属性，用于跟踪是否正在处理点击事件
        self.__is_item_click_in_progress = False
        # 添加属性用于跟踪鼠标事件状态
        self.__mouse_pressed_on_list = False
        self.__clicked_item = None
        self.__popup_visible_before_click = False

        # 记录最后一次鼠标点击的时间
        self.__last_click_time = 0

        # 安装事件过滤器以监视 QLineEdit 的事件
        self.__line_edit.installEventFilter(self)
        # 也为下拉列表安装事件过滤器，直接监控其鼠标事件
        self.__popup_list.installEventFilter(self)

        # 连接信号
        self.__line_edit.textChanged.connect(
            lambda *args: self.__on_text_changed(*args))
        self.__popup_list.itemClicked.connect(
            lambda *args: self.__on_item_clicked(*args))
        # 当 line_edit 被销毁时，尝试清理资源
        self.__line_edit.destroyed.connect(self.__cleanup_on_widget_destroyed)

        self.__logger.info(
            "InputCompleter 已成功初始化并附加到 QLineEdit: %s",
            self.__line_edit.objectName() or "Unnamed",
        )

        # 初始化失焦处理相关的属性
        self.__focus_lost_queue = []  # 存储失焦时的文本内容
        self.__focus_lost_callback = None  # 处理失焦内容的回调函数
        self.__focus_lost_processing = False  # 是否正在处理失焦内容
        self.__focus_lost_threadpool = QThreadPool()  # 用于异步处理失焦内容的线程池
        self.__logger.debug("初始化失焦处理队列和线程池")

        # 确保事件过滤器被安装
        self.__line_edit.installEventFilter(self)
        self.__logger.debug(
            "事件过滤器已安装到QLineEdit: %s",
            self.__line_edit.objectName() or "Unnamed",
        )

        # 直接连接QLineEdit的focusOutEvent
        original_focus_out_event = self.__line_edit.focusOutEvent

        def custom_focus_out_event(event):
            self.__logger.debug("QLineEdit的focusOutEvent被直接调用")
            # 调用原始的focusOutEvent处理
            if original_focus_out_event:
                original_focus_out_event(event)
            # 确保我们的处理也被执行
            self.__on_focus_out()

        # 替换QLineEdit的focusOutEvent方法
        self.__line_edit.focusOutEvent = custom_focus_out_event
        self.__logger.debug("QLineEdit的focusOutEvent方法已被替换")

    def set_completion_callback(
            self, callback_fn: Optional[Callable[[str], List[str]]]) -> None:
        """
        设置用于异步获取补全项的回调函数。

        当输入框文本变化时，会异步调用此回调函数，并将当前文本传递给它。
        如果回调函数返回非空列表，将用该列表更新下拉框；如果返回空列表，则不显示下拉框。

        此回调函数在以下情况会被调用：
        1. 当用户在输入框中输入或删除文本时
        2. 当输入框获取焦点时（传入空字符串）
        3. 当用户清空输入框内容时（传入空字符串）

        回调函数将在后台线程中执行，不会阻塞主线程。这适合执行耗时操作，
        如数据库查询、网络请求等。

        注意：设置回调函数后，将优先使用回调函数获取补全列表，而非使用
        set_completion_items 设置的静态列表。如果需要恢复使用静态列表，
        请将 callback_fn 设为 None。

        Args:
            callback_fn (Optional[Callable[[str], List[str]]]):
                接受文本参数并返回字符串列表的回调函数，或 None 以禁用回调。
                当传入空字符串时，回调函数应返回所有可能的补全项。

        示例:
            ```python
            def fetch_completions(text):
                # 示例：从数据库获取匹配的项目
                if not text:
                    # 文本为空时，返回所有可能的补全项
                    return database.query("SELECT name FROM items LIMIT 20")
                # 这里可以执行耗时的操作（在后台线程中）
                results = database.query(f"SELECT name FROM items WHERE name LIKE '%{text}%'")
                return [item.name for item in results]

            completer.set_completion_callback(fetch_completions)

            # 禁用回调，恢复使用静态列表
            completer.set_completion_callback(None)
            ```
        """
        if callback_fn is not None and not callable(callback_fn):
            self.__logger.error("设置回调函数失败: 参数不是可调用对象。")
            raise TypeError("callback_fn must be callable or None")

        self.__completion_callback = callback_fn
        self.__logger.info("补全回调函数已%s", "设置" if callback_fn else "清除")

    def __handle_callback_result(self, result_items: List[str],
                                 origin_text: str) -> None:
        """
        处理回调函数返回的结果。

        此方法在主线程中执行，接收从工作线程返回的补全列表，
        并更新UI。仅当结果对应于最新请求时才会更新下拉列表。
        为每个列表项设置 Tooltip 以显示完整文本。
        保持列表项的原始顺序，不进行重新排序。

        Args:
            result_items (List[str]): 回调函数返回的补全项列表
            origin_text (str): 发起回调请求时的输入文本
        """
        try:
            # 检查结果是否对应于最新请求
            if origin_text != self.__last_request_text:
                self.__logger.debug(
                    "忽略过时的回调结果。原始文本: '%s', 当前请求文本: '%s'",
                    origin_text,
                    self.__last_request_text,
                )
                return

            # 记录当前更新的文本
            self.__last_update_text = origin_text
            self.__logger.debug("处理回调结果 - 原始文本: '%s'", origin_text)

            # 首先确认结果是一个列表
            if not isinstance(result_items, list):
                self.__logger.warning(
                    "回调结果不是列表类型: %s，将使用空列表",
                    type(result_items).__name__,
                )
                result_items = []

            # 过滤非字符串元素
            original_count = len(result_items)
            # 确保所有项都是字符串，同时处理 None 的情况
            filtered_items = [
                str(item) for item in result_items if item is not None
            ]
            if original_count != len(filtered_items):
                self.__logger.warning(
                    "过滤掉 %d 个无效或非字符串项目",
                    original_count - len(filtered_items),
                )

            self.__logger.debug("回调返回有效项目数: %d", len(filtered_items))

            # 判断是否为获取焦点时的请求（即文本为空且是打开下拉列表的初始请求）
            # 或是文本清空事件的请求
            is_focus_or_clear_request = origin_text == ""
            self.__logger.debug(
                "判断请求类型 - 原始文本: '%s', 是否为获取焦点或清空请求: %s",
                origin_text,
                is_focus_or_clear_request,
            )

            # 检查当前输入框文本，用于记录日志
            current_text = self.__line_edit.text() if self.__line_edit else ""
            self.__logger.debug("当前输入框文本: '%s'", current_text)

            # 只有在过滤后没有匹配项时才隐藏列表
            # 不再根据文本是否为空来决定是否隐藏列表
            if not filtered_items:
                if self.__popup_list and self.__popup_list.isVisible():
                    self.__popup_list.hide()
                    self.__logger.debug("回调返回空列表或无匹配项，隐藏弹出列表。")
                return

            # 更新列表内容
            self.__popup_list.blockSignals(True)
            self.__popup_list.clear()

            # 限制最多显示项目数
            MAX_DISPLAY_ITEMS = 18
            if len(filtered_items) > MAX_DISPLAY_ITEMS:
                self.__logger.debug(
                    "回调返回超过 %d 个项目，将只显示前 %d 个。",
                    MAX_DISPLAY_ITEMS,
                    MAX_DISPLAY_ITEMS,
                )
                items_to_add = filtered_items[:MAX_DISPLAY_ITEMS]
            else:
                items_to_add = filtered_items

            # 按原始顺序添加项目并设置 Tooltip
            for text_item in items_to_add:
                # 创建 QListWidgetItem
                list_widget_item = QListWidgetItem(text_item)
                # 设置 Tooltip 为该项的完整文本
                list_widget_item.setToolTip(text_item)
                # 将带有 Tooltip 的项添加到列表中
                self.__popup_list.addItem(list_widget_item)

            # 修改：不再默认选中第一项，让用户通过键盘导航选择
            # if items_to_add:  # 只有在添加了项目后才设置当前行
            #     self.__popup_list.setCurrentRow(0)
            if items_to_add:  # 确保列表有项目，但不设置默认选中行
                self.__popup_list.setCurrentRow(-1)  # 设置为-1表示不选中任何项
            self.__popup_list.blockSignals(False)

            # 更新列表几何位置并显示
            self.__update_popup_geometry()

            # 显示下拉列表，确保它可见
            if not self.__popup_list.isVisible():
                self.__logger.debug("回调结果处理：显示下拉列表，项目数: %d", len(items_to_add))
                self.__popup_list.show()
                # 强制处理事件确保UI更新及时
                self.__force_process_events()
                # 验证显示状态
                self.__logger.debug("下拉列表显示状态: %s",
                                    self.__popup_list.isVisible())
            else:
                self.__logger.debug("下拉列表已经可见，不需要再次显示")

            self.__logger.debug("已使用回调返回的 %d 个项目更新下拉列表。", len(items_to_add))

        except Exception as e:
            self.__logger.error("处理回调结果时出错: %s\n%s", e, traceback.format_exc())
            # 发生错误时也尝试隐藏列表，避免UI状态不一致
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()

    def __handle_callback_error(self, error_msg: str, stack_trace: str,
                                origin_text: str) -> None:
        """
        处理回调函数执行过程中发生的错误。

        Args:
            error_msg (str): 错误消息
            stack_trace (str): 错误的堆栈跟踪
            origin_text (str): 发起回调请求时的输入文本
        """
        try:
            # 检查错误是否与最新请求相关
            if origin_text != self.__last_request_text:
                self.__logger.debug(
                    "忽略过时请求的错误。原始文本: '%s', 当前请求文本: '%s'",
                    origin_text,
                    self.__last_request_text,
                )
                return

            self.__logger.error(
                "回调函数执行出错 (文本: '%s'): %s\n%s",
                origin_text,
                error_msg,
                stack_trace,
            )

            # 出错时隐藏下拉列表
            if self.__popup_list.isVisible():
                self.__popup_list.hide()

        except Exception as e:
            self.__logger.error("处理回调错误时出现异常: %s\n%s", e,
                                traceback.format_exc())

    def __setup_popup_list(self) -> None:
        """配置下拉列表框(__popup_list)的外观和行为。"""
        try:
            # 1. 设置窗口标志 (Flags):
            #    - Qt.Popup: 使列表框成为一个弹出窗口，通常显示在所有其他窗口之上，并在失去焦点时自动隐藏（虽然我们主要手动控制隐藏）。
            #    - Qt.FramelessWindowHint: 移除窗口边框和标题栏。
            #    - Qt.NoDropShadowWindowHint: (可选) 移除某些平台可能添加的阴影效果，使外观更简洁。
            # self.__popup_list.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint
            #                                | Qt.NoDropShadowWindowHint)

            # --- 修复：使用 Qt.ToolTip 代替 Qt.Popup ---
            # 经过多次尝试，发现在弹出列表时，Qt.Popup 类型的窗口会导致输入框失去焦点或无法响应输入。
            # Qt.ToolTip 是一种轻量级窗口类型，它会浮动在其他窗口之上，但不会像 Qt.Popup 那样
            # 干扰键盘焦点。通常用于工具提示，但在这里用于创建自定义下拉菜单也很合适。
            # 使用 Qt.ToolTip 代替 Qt.Popup 可以解决输入框在下拉列表显示时无法输入的问题。
            self.__popup_list.setWindowFlags(Qt.ToolTip
                                             | Qt.FramelessWindowHint
                                             | Qt.NoDropShadowWindowHint)

            # 2. 设置焦点策略 (Focus Policy):
            #    - Qt.NoFocus: 这是关键！设置此策略意味着下拉列表本身永远不会接收键盘焦点。
            #      这确保了即使用户与列表交互（如用箭头导航），键盘焦点也始终保留在原始的 QLineEdit 上。
            self.__popup_list.setFocusPolicy(Qt.NoFocus)

            # 3. 禁用编辑触发器 (Edit Triggers):
            #    - QAbstractItemView.NoEditTriggers: 防止用户意外地尝试编辑列表中的项（例如通过双击）。
            self.__popup_list.setEditTriggers(QAbstractItemView.NoEditTriggers)

            # 4. 滚动条策略 (Scroll Bar Policy):
            #    - Qt.ScrollBarAlwaysOff: 由于列表宽度与 QLineEdit 相同，通常不需要水平滚动条。禁用它可以使界面更干净。
            #      垂直滚动条会根据需要自动出现。
            self.__popup_list.setHorizontalScrollBarPolicy(
                Qt.ScrollBarAlwaysOff)

            # 5. 选择行为和模式 (Selection Behavior/Mode):
            #    - QAbstractItemView.SelectRows: 设置为选择整行。
            #    - QAbstractItemView.SingleSelection: 只允许用户一次选择列表中的一个项。
            self.__popup_list.setSelectionBehavior(
                QAbstractItemView.SelectRows)
            self.__popup_list.setSelectionMode(
                QAbstractItemView.SingleSelection)

            # 6. 设置鼠标跟踪，以便能更好地处理鼠标事件
            self.__popup_list.setMouseTracking(True)

            # 7. 设置点击激活项的行为
            self.__popup_list.setTextElideMode(Qt.ElideRight)  # 显示长文本时使用省略号
            self.__popup_list.setAttribute(Qt.WA_TranslucentBackground,
                                           False)  # 确保背景不透明，更好地接收鼠标事件

            # 8. 启用列表项的工具提示和交互性
            self.__popup_list.setAlternatingRowColors(True)  # 交替行颜色，增强可读性
            self.__popup_list.setHorizontalScrollMode(
                QAbstractItemView.ScrollPerPixel)  # 平滑滚动
            self.__popup_list.setVerticalScrollMode(
                QAbstractItemView.ScrollPerPixel)  # 平滑滚动

            # 9. 自定义列表项高度，使点击区域更大，并设置字体
            self.__popup_list.setStyleSheet(f"""
                QListWidget {{
                    border: 1px solid #A0A0A0; /* 灰色边框 */
                    background-color: white; /* 白色背景 */
                    outline: 0; /* 移除可能的虚线选择框 */
                    selection-background-color: #E0E0E0; /* 选中项背景色 */
                    font-family: {self.__font_family}; /* 可配置的字体设置 */
                    font-size: {self.__font_size}px; /* 可配置的字体大小 */
                    font-weight: {self.__font_weight}; /* 可配置的字体粗细 */
                }}
                QListWidget::item {{
                    padding: 5px 8px; /* 增加项目内边距，提供更大的点击区域 */
                    min-height: 22px; /* 设置最小高度 */
                    border-bottom: 1px solid #F0F0F0; /* 项目间的分隔线 */
                    font-family: {self.__font_family}; /* 确保列表项也使用相同字体 */
                    font-size: {self.__font_size}px; /* 可配置的字体大小 */
                    font-weight: {self.__font_weight}; /* 可配置的字体粗细 */
                }}
                QListWidget::item:selected {{
                    background-color: #E0E0E0; /* 选中项背景色 */
                    color: black; /* 选中项文字颜色 */
                }}
                QListWidget::item:hover {{
                    background-color: #F0F0F0; /* 悬停项背景色 */
                }}
            """)

            # 10. 优化鼠标点击和选择逻辑
            # 设置鼠标左键点击立即选择，确保点击事件能被正确捕获
            self.__popup_list.setProperty("plainMode", True)  # 使用简单模式，减少内部处理

            self.__logger.debug("下拉列表窗口配置完成，设置了窗口标志、外观和行为特性")

        except Exception as e:
            self.__logger.error("配置弹出列表时出错: %s\n%s", e, traceback.format_exc())

    def set_completion_items(self, items: Optional[List[str]]) -> None:
        """
        设置或更新用于自动完成的字符串列表。

        可以随时调用此方法更新补全数据源。如果下拉列表当前可见，会立即使用新数据刷新显示。
        此方法会过滤掉 items 中的非字符串元素，并将其转换为字符串。

        Args:
            items (Optional[List[str]]): 新的完成项列表。如果为 None 或不是列表，将被视为空列表。
                                          列表中的非字符串元素将被忽略。

        示例:
            ```python
            # 初始化时提供空列表，稍后再添加项目
            completer = InputCompleter(my_line_edit, [])

            # 稍后更新补全项
            completer.set_completion_items(["新选项1", "新选项2", "新选项3"])

            # 根据某些条件动态过滤并更新列表
            filtered_items = [item for item in all_items if item.startswith("a")]
            completer.set_completion_items(filtered_items)

            # 清空补全列表
            completer.set_completion_items(None)  # 或 set_completion_items([])
            ```
        """
        if items is None:
            self.__completion_items = []
            self.__logger.info("补全列表已清空 (设置为 None)。")
        elif not isinstance(items, list):
            self.__completion_items = []
            self.__logger.warning(
                "设置补全列表失败: 'items' 参数期望是列表类型，但收到了 %s。列表已清空。",
                type(items).__name__,
            )
        else:
            # 过滤掉非字符串元素
            original_count = len(items)
            self.__completion_items = [
                str(item) for item in items if isinstance(item, str)
            ]
            filtered_count = len(self.__completion_items)
            if original_count != filtered_count:
                self.__logger.warning(
                    "在提供的补全列表中发现 %d 个非字符串元素，已被忽略。",
                    original_count - filtered_count,
                )
            self.__logger.info("补全列表已更新。当前包含 %d 个有效项目。", filtered_count)

        # 如果下拉列表当前是可见的，立即用新的数据源更新显示
        if self.__popup_list.isVisible():
            self.__update_completion_list()

    def __update_completion_list(self) -> None:
        """
        根据当前输入框文本过滤补全列表，并更新下拉列表框的显示内容。
        为每个列表项设置 Tooltip 以显示完整文本。
        保持原始列表的顺序，不对匹配项进行重新排序。

        当输入框文本为空时，不会自动隐藏列表，而是交由__handle_text_cleared方法处理。
        """
        try:
            self.__logger.debug("进入 __update_completion_list 方法")
            # 检查 line_edit 是否有效
            if not self.__line_edit:
                self.__logger.warning(
                    "__update_completion_list: line_edit 无效，无法更新。")
                return

            current_text: str = self.__line_edit.text()
            self.__logger.debug("输入框当前文本: '%s'", current_text)

            # 如果文本为空，调用显示所有项目的方法而不是隐藏下拉列表
            if not current_text:
                self.__logger.debug("输入框文本为空，显示所有补全项")
                if self.__completion_items:
                    self.__show_all_completion_items()
                else:
                    self.__logger.debug("无预定义补全项，不显示下拉列表")
                return

            lower_current_text = (current_text.lower())  # 转换为小写以进行不区分大小写的匹配
            self.__logger.debug("过滤匹配用的小写文本: '%s'", lower_current_text)
            # 添加日志，显示部分可用补全项以供调试
            if self.__completion_items:
                self.__logger.debug(
                    "可用补全项(前10项): %s",
                    self.
                    __completion_items[:min(10, len(self.__completion_items))],
                )
            else:
                self.__logger.debug("没有可用的补全项")

            # 执行过滤操作
            # 使用列表推导式进行过滤，匹配规则是: 补全项(小写) 包含 输入文本(小写)
            # 确保 self.__completion_items 中的项都是字符串
            # 保留原始列表顺序
            matched_items: List[str] = [
                item for item in self.__completion_items if
                isinstance(item, str) and lower_current_text in item.lower()
            ]
            self.__logger.debug("匹配到的项目数: %d", len(matched_items))

            if not matched_items:  # 如果没有找到匹配项
                if self.__popup_list and self.__popup_list.isVisible():
                    self.__popup_list.hide()
                    self.__logger.debug("没有找到匹配 '%s' 的补全项，隐藏弹出列表。",
                                        current_text)
                return

            # --- 修改：限制最多显示 18 个项目 ---
            MAX_DISPLAY_ITEMS = 18  # 定义最大显示数量常量
            if len(matched_items) > MAX_DISPLAY_ITEMS:
                self.__logger.debug(
                    "匹配项超过 %d 个，将只显示前 %d 个。",
                    MAX_DISPLAY_ITEMS,
                    MAX_DISPLAY_ITEMS,
                )
                items_to_add = matched_items[:MAX_DISPLAY_ITEMS]
            else:
                items_to_add = matched_items

            # 更新列表内容前先阻塞信号，防止触发不必要的信号
            self.__popup_list.blockSignals(True)
            self.__popup_list.clear()  # 清空旧列表项

            # --- 修改：循环添加项目并设置 Tooltip，确保按照原始顺序 ---
            for text_item in items_to_add:
                # 创建 QListWidgetItem
                list_widget_item = QListWidgetItem(text_item)
                # 设置 Tooltip 为该项的完整文本
                list_widget_item.setToolTip(text_item)
                # 将带有 Tooltip 的项添加到列表中
                self.__popup_list.addItem(list_widget_item)
            # ---------------------------------------

            # 修改：不再默认选中第一项，让用户通过键盘导航选择
            # if items_to_add:  # 只有在添加了项目后才设置当前行
            #     self.__popup_list.setCurrentRow(0)  # 默认高亮显示第一项 (视觉上，不改变焦点)
            if items_to_add:  # 确保列表有项目，但不设置默认选中行
                self.__popup_list.setCurrentRow(-1)  # 设置为-1表示不选中任何项
            self.__popup_list.blockSignals(False)  # 恢复信号

            # 调整列表的大小和位置
            self.__logger.debug("调整下拉列表几何形状")
            self.__update_popup_geometry()

            # 如果列表尚未显示，则显示它
            if not self.__popup_list.isVisible():
                self.__logger.debug("下拉列表未显示，调用show()显示")
                self.__popup_list.show()
                # 强制处理事件确保UI更新及时
                self.__force_process_events()
                # 验证显示状态
                self.__logger.debug("下拉列表显示状态: %s",
                                    self.__popup_list.isVisible())
            else:
                self.__logger.debug("下拉列表已经可见，不需要再次显示")

        except Exception as e:
            self.__logger.error("更新补全列表时出错: %s\n%s", e, traceback.format_exc())
            # 发生错误时也尝试隐藏列表
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()

    def __update_popup_geometry(self) -> None:
        """
        计算并设置下拉列表框的位置和大小，使其精确地位于输入框下方并与其同宽。

        此方法会根据QLineEdit的位置和大小，计算下拉列表应该显示的位置和大小，
        确保列表始终显示在输入框的正下方。
        """
        try:
            if not self.__line_edit:  # 检查 line_edit 是否还存在
                self.__logger.warning("更新几何形状时发现line_edit无效，跳过")
                return

            # 1. 获取 QLineEdit 的矩形区域（相对于 QLineEdit 自身）
            line_edit_rect = self.__line_edit.rect()
            self.__logger.debug(
                "QLineEdit矩形区域: x=%d, y=%d, w=%d, h=%d",
                line_edit_rect.x(),
                line_edit_rect.y(),
                line_edit_rect.width(),
                line_edit_rect.height(),
            )

            # 2. 获取 QLineEdit 左下角的点，并将其从 QLineEdit 的局部坐标映射到全局屏幕坐标。
            #    这确定了弹出列表的左上角应该在屏幕上的哪个位置。
            popup_top_left = self.__line_edit.mapToGlobal(
                line_edit_rect.bottomLeft())
            self.__logger.debug(
                "弹出列表左上角全局坐标: x=%d, y=%d",
                popup_top_left.x(),
                popup_top_left.y(),
            )

            # 3. 设置弹出列表的宽度与 QLineEdit 的当前宽度完全一致。
            popup_width = self.__line_edit.width()

            # 4. 计算弹出列表的理想高度：
            #    - 获取第一行的高度提示 (sizeHintForRow(0)) 作为单行的大致高度。如果列表为空，则使用一个默认值（例如 20）。
            #    - 理想高度是 行数 * 单行高度 + 一点额外的边距 (例如 5px)。
            #    - 为了防止列表过长，设置一个最大高度（例如 200px），大约可以显示 8-10 行。
            item_count = self.__popup_list.count()
            self.__logger.debug("下拉列表项目数: %d", item_count)

            item_height_estimate = (self.__popup_list.sizeHintForRow(0)
                                    if item_count > 0 else 20)
            calculated_height = item_count * item_height_estimate + 5

            # --- 修改：调整最大高度以适应最多 15 个项目 ---
            # 设置最大显示项目数
            MAX_DISPLAY_ITEMS = 15

            # 计算适合显示 MAX_DISPLAY_ITEMS 个项目的最大高度
            # 如果每项高度为 item_height_estimate，则最大高度为：
            # MAX_DISPLAY_ITEMS * item_height_estimate + 边距(5px)
            max_height = MAX_DISPLAY_ITEMS * item_height_estimate + 5

            # 使用计算出的最大高度，而不是硬编码的 200
            popup_height = min(calculated_height, max_height)

            # 添加日志，显示中间计算的高度值
            self.__logger.debug(
                "高度计算 - 项目数: %d, 每项估计高度: %d, "
                "计算高度: %d, 最大高度(适合%d项): %d, 最终高度: %d",
                item_count,
                item_height_estimate,
                calculated_height,
                MAX_DISPLAY_ITEMS,
                max_height,
                popup_height,
            )
            # 确保高度至少为0，防止负数或无效高度
            popup_height = max(0, popup_height)

            # 5. 设置弹出列表的几何形状（位置和大小）。
            #    - X 坐标：与 QLineEdit 的左边缘对齐 (popup_top_left.x())。
            #    - Y 坐标：紧贴 QLineEdit 的下边缘 (popup_top_left.y() + 1，+1是为了避免与输入框边框重叠)。
            #    - 宽度：popup_width。
            #    - 高度：popup_height。
            target_x = popup_top_left.x()
            target_y = popup_top_left.y() + 1

            # 保存设置前的几何信息用于调试
            old_geometry = self.__popup_list.geometry()
            self.__logger.debug(
                "原下拉列表几何形状: x=%d, y=%d, w=%d, h=%d",
                old_geometry.x(),
                old_geometry.y(),
                old_geometry.width(),
                old_geometry.height(),
            )

            # 设置新的几何形状
            self.__popup_list.setGeometry(target_x, target_y, popup_width,
                                          popup_height)

            # 验证设置后的几何信息
            new_geometry = self.__popup_list.geometry()
            self.__logger.debug(
                "新下拉列表几何形状: x=%d, y=%d, w=%d, h=%d",
                new_geometry.x(),
                new_geometry.y(),
                new_geometry.width(),
                new_geometry.height(),
            )

            # 更新日志，使其更清晰地显示计算出的坐标和尺寸
            self.__logger.debug(
                "最终下拉列表几何形状 - 位置: (%d, %d), 大小: %dx%d",
                target_x,
                target_y,
                popup_width,
                popup_height,
            )

        except Exception as e:
            # 特别处理 target C++ object already deleted 错误，这可能在窗口关闭时发生
            if "already deleted" in str(e).lower() and not self.__line_edit:
                self.__logger.warning("尝试更新几何形状时 QLineEdit 已被删除，忽略。")
            else:
                self.__logger.error("更新弹出列表几何形状时出错: %s\n%s", e,
                                    traceback.format_exc())

    # --- 信号处理槽函数 (Signal Handlers / Slots) ---

    def __on_text_changed(self, text: str) -> None:
        """
        处理 QLineEdit 的 textChanged 信号。
        当输入框文本发生任何变化时（用户输入、删除、程序设置），此方法被调用。

        Args:
            text (str): 输入框当前的文本内容。注意：此参数在此实现中未使用，
                        因为 __update_completion_list 会直接从 QLineEdit 读取最新文本。
                        保留它是为了符合 textChanged 信号的签名。
        """

        try:
            # 添加日志记录进入此方法的时间和接收到的文本
            self.__logger.debug(
                "Entering __on_text_changed with text: '%s', previous text: '%s'",
                text,
                self.__previous_text,
            )

            # 检测文本清空事件（从有内容变为空）
            is_text_cleared = bool(self.__previous_text) and not bool(text)
            self.__logger.debug(
                "检测文本清空事件: previous='%s', current='%s', is_cleared=%s",
                self.__previous_text,
                text,
                is_text_cleared,
            )

            # 先保存当前文本作为上一次的文本，再处理逻辑
            # 这样确保在处理函数中可以正确引用之前的文本
            previous_text = self.__previous_text
            # 更新上一次的文本记录
            self.__previous_text = text

            # 如果检测到文本清空事件，调用专门的处理方法
            if is_text_cleared:
                self.__logger.debug("触发文本清空事件处理，从 '%s' 变为空", previous_text)
                self.__handle_text_cleared()
                return

            # 检查是否设置了回调函数
            if self.__completion_callback is not None:
                # 记录最后一次请求的文本
                self.__last_request_text = text

                # 创建并提交工作任务
                worker = self.__Worker(self.__completion_callback, text)

                # 连接信号到处理方法
                worker.signals.finished.connect(self.__handle_callback_result)
                worker.signals.error.connect(self.__handle_callback_error)

                # 提交任务到线程池
                self.__thread_pool.start(worker)

                self.__logger.debug("已提交异步任务获取 '%s' 的补全项", text)
            else:
                # 如果没有设置回调函数，使用原有的过滤方法
                # 主要任务是调用 __update_completion_list 来根据新文本刷新下拉列表
                self.__update_completion_list()
        except Exception as e:
            # 捕获并记录在处理文本变化时可能发生的任何意外错误
            self.__logger.error("处理文本变化 '%s' 时出错: %s\n%s", text, e,
                                traceback.format_exc())

    def __handle_text_cleared(self) -> None:
        """
        处理文本清空事件（从有内容变为空）。

        当QLineEdit的文本从有内容变为空时，此方法被调用。
        如果设置了回调函数或有预定义的补全列表，将显示下拉框中的所有项目。
        """
        try:
            self.__logger.debug("开始处理文本清空事件")

            # 再次确认当前文本确实为空，加强健壮性
            current_text = self.__line_edit.text() if self.__line_edit else ""
            if current_text:
                self.__logger.debug("文本清空事件处理时发现当前文本不为空: '%s'，跳过处理",
                                    current_text)
                return

            # 检查是否设置了回调函数
            if self.__completion_callback is not None:
                self.__logger.debug("文本清空事件：使用回调函数获取所有补全项")
                # 记录最后一次请求的文本（空字符串）
                self.__last_request_text = ""

                # 创建并提交工作任务，传入空字符串
                worker = self.__Worker(self.__completion_callback, "")

                # 连接信号到处理方法
                worker.signals.finished.connect(self.__handle_callback_result)
                worker.signals.error.connect(self.__handle_callback_error)

                # 提交任务到线程池
                self.__thread_pool.start(worker)

                self.__logger.debug("文本清空事件：已提交异步任务获取所有补全项")
            else:
                # 如果有预定义的补全列表，则直接调用方法显示所有项目
                if self.__completion_items:
                    self.__logger.debug(
                        "文本清空事件：有预定义补全项 %d 个，立即显示",
                        len(self.__completion_items),
                    )
                    self.__show_all_completion_items()
                else:
                    self.__logger.debug("文本清空事件：无预定义补全项，不显示下拉列表")
        except Exception as e:
            self.__logger.error("处理文本清空事件时出错: %s\n%s", e,
                                traceback.format_exc())

    def __on_item_clicked(self, item: QListWidgetItem) -> None:
        """
        处理下拉列表框 (__popup_list) 的 itemClicked 信号。
        当用户用鼠标点击列表中的某个项目时，此方法被调用。

        Args:
            item (QListWidgetItem): 被点击的列表项对象。
        """
        try:
            # 记录当前时间，用于与mouseReleaseEvent时间比较
            current_time = time.time()
            time_since_last_click = current_time - self.__last_click_time
            self.__last_click_time = current_time

            # 如果与上次点击时间太接近(小于50ms)，可能是重复事件，直接忽略
            if time_since_last_click < 0.05:
                self.__logger.debug(
                    f"与上次点击间隔太短({time_since_last_click:.3f}秒)，可能是重复事件，忽略")
                return

            # 设置标志，指示正在处理点击事件，防止FocusOut事件过早隐藏列表
            self.__is_item_click_in_progress = True
            self.__logger.debug(f"开始处理列表项点击事件: '{item.text()}', 标记点击处理进行中")

            # 确保列表项有效
            if not item:
                self.__logger.warning("点击处理收到无效的列表项")
                self.__is_item_click_in_progress = False
                return

            # 保存点击项，以便在事件过滤器中使用
            self.__clicked_item = item

            selected_text: str = item.text()  # 获取被点击项的文本
            self.__logger.info("用户点击选择了项目: '%s'", selected_text)

            # **关键步骤：防止信号循环**
            # 当我们用程序设置 QLineEdit 的文本时 (setText)，它会再次触发 textChanged 信号。
            # 如果不加处理，这会导致 __on_text_changed -> __update_completion_list -> ... 的潜在循环或不必要的操作。
            # 因此，在设置文本之前，我们暂时阻止 QLineEdit 发出信号。
            self.__line_edit.blockSignals(True)
            self.__line_edit.setText(selected_text)  # 将选中的文本设置回 QLineEdit
            self.__line_edit.blockSignals(False)  # **重要：** 操作完成后，立即恢复信号

            # (可选但推荐) 将光标移动到文本末尾，方便用户继续输入或确认
            self.__line_edit.end(False)  # False 表示不选中移动过的文本
            self.__logger.debug(f"已将文本 '{selected_text}' 设置到QLineEdit并移动光标到末尾")

            # 确保焦点回到输入框
            self.__line_edit.setFocus()
            self.__logger.debug("已将焦点重新设置回QLineEdit")

            # 使用QTimer延迟隐藏列表，确保其他事件处理完成
            QTimer.singleShot(50, self.__hide_popup_after_selection)
            self.__logger.debug("安排延迟50ms隐藏下拉列表")

        except Exception as e:
            self.__logger.error(
                "处理列表项点击 '%s' 时出错: %s\n%s",
                item.text() if item else "None",
                e,
                traceback.format_exc(),
            )
            self.__is_item_click_in_progress = False
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()
        finally:
            # 使用 finally 确保即使在 try块 中发生错误，信号阻塞也会被解除
            if self.__line_edit and self.__line_edit.signalsBlocked():
                self.__line_edit.blockSignals(False)
                self.__logger.debug("QLineEdit 信号已恢复。")

    def __hide_popup_after_selection(self) -> None:
        """
        在选择项目后延迟隐藏下拉列表。

        此方法通过QTimer延迟调用，确保在所有点击事件处理完成后
        再隐藏列表，避免事件处理顺序导致的问题。
        """
        try:
            self.__logger.debug("执行延迟隐藏下拉列表")

            # 隐藏下拉列表
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()
                self.__logger.debug("选择项目后隐藏下拉列表")

            # 重置点击相关标志
            self.__is_item_click_in_progress = False
            self.__mouse_pressed_on_list = False
            self.__clicked_item = None
            self.__logger.debug("列表项点击处理完成，重置所有点击相关标志")
        except Exception as e:
            self.__logger.error("执行延迟隐藏下拉列表时出错: %s\n%s", e,
                                traceback.format_exc())

    def __on_focus_in(self) -> None:
        """
        处理 QLineEdit 获取焦点时的操作。

        当 QLineEdit 获取焦点时，如果存在回调函数或预定义的补全列表，
        则立即显示下拉框，无需等待用户输入。
        """
        try:
            self.__logger.debug("QLineEdit 获取焦点，准备显示下拉列表")

            # 检查是否设置了回调函数
            if self.__completion_callback is not None:
                # 获取当前文本（可能为空）
                current_text = self.__line_edit.text()
                # 记录最后一次请求的文本
                self.__last_request_text = current_text

                # 创建并提交工作任务
                worker = self.__Worker(self.__completion_callback,
                                       current_text)

                # 连接信号到处理方法
                worker.signals.finished.connect(self.__handle_callback_result)
                worker.signals.error.connect(self.__handle_callback_error)

                # 提交任务到线程池
                self.__thread_pool.start(worker)

                self.__logger.debug("已提交异步任务获取焦点时的补全项，文本为: '%s'", current_text)
            else:
                # 如果没有设置回调函数，则检查是否有预定义的补全项列表
                if self.__completion_items:
                    # 显示所有预定义的补全项
                    self.__show_all_completion_items()
                else:
                    self.__logger.debug("没有设置回调函数且没有预定义的补全项，不显示下拉列表")
        except Exception as e:
            self.__logger.error("处理QLineEdit获取焦点时出错: %s\n%s", e,
                                traceback.format_exc())

    def __show_all_completion_items(self) -> None:
        """
        显示所有预定义的补全项，不进行过滤。

        此方法在QLineEdit获取焦点或文本被清空且没有设置回调函数时调用，
        用于显示所有预定义的补全项列表。会强制显示下拉列表。
        """
        try:
            # 检查是否有预定义的补全项
            if not self.__completion_items:
                self.__logger.debug("没有预定义的补全项，不显示下拉列表")
                return

            self.__logger.debug("准备显示所有预定义补全项，总数：%d",
                                len(self.__completion_items))

            # 更新列表内容前先阻塞信号，防止触发不必要的信号
            self.__popup_list.blockSignals(True)
            self.__popup_list.clear()  # 清空旧列表项

            # 限制最多显示项目数
            MAX_DISPLAY_ITEMS = 18
            if len(self.__completion_items) > MAX_DISPLAY_ITEMS:
                self.__logger.debug(
                    "补全项超过 %d 个，将只显示前 %d 个。",
                    MAX_DISPLAY_ITEMS,
                    MAX_DISPLAY_ITEMS,
                )
                items_to_add = self.__completion_items[:MAX_DISPLAY_ITEMS]
            else:
                items_to_add = self.__completion_items

            # 按原始顺序添加项目
            for text_item in items_to_add:
                # 创建 QListWidgetItem
                list_widget_item = QListWidgetItem(text_item)
                # 设置 Tooltip 为该项的完整文本
                list_widget_item.setToolTip(text_item)
                # 将带有 Tooltip 的项添加到列表中
                self.__popup_list.addItem(list_widget_item)

            # 修改：不再默认选中第一项，让用户通过键盘导航选择
            # if items_to_add:  # 只有在添加了项目后才设置当前行
            #     self.__popup_list.setCurrentRow(0)  # 默认高亮显示第一项
            if items_to_add:  # 确保列表有项目，但不设置默认选中行
                self.__popup_list.setCurrentRow(-1)  # 设置为-1表示不选中任何项
            self.__popup_list.blockSignals(False)  # 恢复信号

            # 更新列表几何位置并显示
            self.__update_popup_geometry()

            # 确保下拉列表显示，强制处理事件确保更新立即可见
            if not self.__popup_list.isVisible():
                self.__logger.debug("强制显示下拉列表 - 项目数: %d", len(items_to_add))
                self.__popup_list.show()
                # 强制处理事件确保UI更新及时
                self.__force_process_events()
                # 再次确认列表已显示
                self.__logger.debug("下拉列表显示状态: %s",
                                    self.__popup_list.isVisible())
            else:
                self.__logger.debug("下拉列表已经可见，不需要再次显示")

            self.__logger.debug(
                "在QLineEdit获取焦点或文本清空时显示了 %d 个预定义补全项",
                len(items_to_add),
            )
        except Exception as e:
            self.__logger.error("显示所有补全项时出错: %s\n%s", e,
                                traceback.format_exc())
            # 发生错误时尝试隐藏列表
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()

    # --- 事件过滤器 (Event Filter) ---

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        """
        事件过滤器，用于拦截和处理安装此过滤器的对象（QLineEdit 和 popup_list）的特定事件。

        此方法实现了以下功能:
        1. 处理QLineEdit的焦点获取和丢失事件
        2. 处理列表导航键盘事件（上、下、回车、ESC）
        3. 直接处理下拉列表的鼠标事件（按下、释放、点击）
        4. 处理QLineEdit大小位置变化事件

        技术说明:
        - 返回 True 表示事件已被处理，阻止 Qt 进一步传递事件。
        - 返回 False 表示事件应继续传递给其他事件处理器。

        Args:
            watched (QObject): 触发事件的对象 (这里可能是 self.__line_edit 或 self.__popup_list)。
            event (QEvent): 发生的具体事件对象。

        Returns:
            bool: 如果事件已被处理则返回 True，否则返回 False。
        """
        try:
            # 处理下拉列表的事件
            if watched is self.__popup_list:
                # 处理下拉列表的鼠标事件
                if event.type() == QEvent.MouseButtonPress:
                    self.__logger.debug("捕获到下拉列表的鼠标按下事件")
                    self.__mouse_pressed_on_list = True
                    self.__popup_visible_before_click = self.__popup_list.isVisible(
                    )
                    # 允许事件继续传递
                    return False

                elif event.type() == QEvent.MouseButtonRelease:
                    self.__logger.debug("捕获到下拉列表的鼠标释放事件")
                    # 如果鼠标在列表上按下过，且列表之前是可见的
                    if (self.__mouse_pressed_on_list
                            and self.__popup_visible_before_click):
                        # 获取鼠标位置下的列表项
                        pos = event.pos()
                        item = self.__popup_list.itemAt(pos)
                        if item:
                            self.__logger.debug(
                                f"鼠标释放在列表项 '{item.text()}' 上，直接处理点击")
                            self.__clicked_item = item

                            # 直接执行点击处理，不等待itemClicked信号
                            # 在鼠标释放事件处理中调用点击方法，提高可靠性
                            self.__handle_direct_item_click(item)

                            # 已处理事件，阻止继续传播
                            return True
                    # 重置鼠标按下状态
                    self.__mouse_pressed_on_list = False
                    return False

            # 处理QLineEdit的事件
            if watched is not self.__line_edit:
                # 如果不是来自我们关心的 QLineEdit，调用父类的 eventFilter 处理
                return super().eventFilter(watched, event)

            # --- 处理焦点获取事件 (FocusIn) ---
            if event.type() == QEvent.FocusIn:
                self.__logger.debug("QLineEdit 获取焦点")
                # 调用焦点获取处理方法
                self.__on_focus_in()
                # 返回 False，让事件继续传递
                return False

            # --- 处理键盘按下事件 (KeyPress) ---
            # 仅当弹出列表当前可见时，才处理与列表导航相关的按键
            if event.type() == QEvent.KeyPress and self.__popup_list.isVisible(
            ):
                key = event.key()  # 获取按下的键值
                current_row = self.__popup_list.currentRow()  # 获取当前高亮显示的行索引
                count = self.__popup_list.count()  # 获取列表中的总项数

                if (count == 0):  # 如果列表为空（理论上不应在此发生，因为显示前会检查），则不处理按键
                    return False

                if key == Qt.Key_Down:
                    # 向下箭头：移动到下一项
                    if current_row == -1:  # 如果当前没有选中项，选中第一项
                        next_row = 0
                    else:
                        # 如果到末尾则循环回第一项
                        next_row = (current_row + 1) % count
                    self.__popup_list.setCurrentRow(next_row)
                    self.__logger.debug("键盘导航: 向下 -> 行 %d (从行 %d)", next_row, current_row)
                    return True  # 返回 True，消耗事件

                elif key == Qt.Key_Up:
                    # 向上箭头：移动到上一项
                    if current_row == -1:  # 如果当前没有选中项，选中最后一项
                        next_row = count - 1
                    else:
                        # 如果到开头则循环回最后一项
                        next_row = (current_row - 1 + count) % count
                    self.__popup_list.setCurrentRow(next_row)
                    self.__logger.debug("键盘导航: 向上 -> 行 %d (从行 %d)", next_row, current_row)
                    return True  # 返回 True，消耗事件

                elif key == Qt.Key_Return or key == Qt.Key_Enter:
                    # 回车键：确认选择当前高亮显示的项
                    if current_row >= 0:  # 确保有一个有效的选中项
                        item = self.__popup_list.item(current_row)
                        self.__logger.info("用户通过回车键选择了项目: '%s'", item.text())
                        self.__on_item_clicked(item)  # 调用处理点击的逻辑
                    else:
                        # 如果没有选中项（现在是正常情况，因为默认不选中任何项），则仅隐藏列表
                        self.__popup_list.hide()
                        self.__logger.debug("回车键按下但没有选中项，隐藏下拉列表")
                    return True  # 返回 True，消耗事件

                elif key == Qt.Key_Escape:
                    # Esc 键：隐藏下拉列表
                    self.__popup_list.hide()
                    self.__logger.debug("用户按 Esc 键，隐藏弹出列表。")
                    return True  # 返回 True，消耗事件
                else:
                    # --- 新增部分 ---
                    # 对于列表可见时，按下的不是 上/下/回车/Esc 等特殊导航键的其他所有按键
                    # (例如字母、数字、退格、删除等)，我们明确返回 False。
                    # 这确保了这些按键事件会被传递回 QLineEdit，允许用户继续正常的输入和编辑操作。
                    self.__logger.debug(f"未处理的按键 {key}，传递给 QLineEdit。")
                    return False

            # --- 处理焦点丢失事件 (FocusOut) ---
            elif event.type() == QEvent.FocusOut:
                # 当 QLineEdit 失去焦点时，隐藏下拉列表。
                # 注意：由于 QListWidget 设置了 Qt.Popup 标志，它在技术上是一个独立的顶层窗口。
                # 点击 QListWidget 外部通常会导致 QLineEdit 失去焦点，从而触发此事件。
                # 点击 QListWidget 内部的项会触发 itemClicked 信号，其中也会隐藏列表。
                # 因此，直接在此处隐藏通常是安全的。
                # 如果需要区分焦点是移到了列表还是完全移开，逻辑会更复杂，但对于 NoFocus 列表，这通常足够。
                # 添加日志记录 FocusOut 事件
                self.__logger.debug(
                    "Entering FocusOut event handler in eventFilter for %s",
                    watched.objectName() or "Unnamed QLineEdit",
                )

                # 检查是否正在处理点击事件，如果是则不隐藏列表
                if self.__is_item_click_in_progress:
                    self.__logger.debug("检测到列表项点击正在处理中，忽略焦点丢失事件")
                    return False

                # 检查是否是鼠标在下拉列表上按下导致的焦点丢失
                if self.__mouse_pressed_on_list:
                    self.__logger.debug("检测到鼠标在下拉列表上按下，忽略焦点丢失事件")
                    return False

                # --- 修复方案 2：检查 FocusOut 的原因 ---
                # 尝试将 QEvent 转换为 QFocusEvent 以获取焦点丢失的原因
                if isinstance(event, QFocusEvent):
                    focus_event: QFocusEvent = event
                    focus_reason = focus_event.reason()
                    self.__logger.debug(
                        f"Focus out reason: {focus_reason}")  # 记录原因

                    # 如果焦点丢失是因为 Popup 窗口出现 (Qt.PopupFocusReason)，则不隐藏列表。
                    # 这可以防止因为显示列表自身而立即触发隐藏。
                    if focus_reason == Qt.PopupFocusReason:
                        self.__logger.debug("焦点因 Popup 显示而丢失，暂时不隐藏列表。")
                        # 不执行隐藏操作，直接返回 False 让事件继续
                        return False
                    elif (focus_reason == Qt.MouseFocusReason
                          and self.__popup_list.isVisible()):
                        # 如果是鼠标点击导致的焦点丢失，且列表正在显示，则需要检查鼠标位置
                        # 这种情况下，可能是点击了下拉列表项
                        mouse_pos = QCursor.pos()
                        popup_geometry = self.__popup_list.geometry()
                        popup_global_pos = self.__popup_list.mapToGlobal(
                            QPoint(0, 0))
                        popup_global_rect = QRect(popup_global_pos,
                                                  popup_geometry.size())

                        self.__logger.debug(
                            f"鼠标位置: ({mouse_pos.x()}, {mouse_pos.y()}), " +
                            f"列表区域: ({popup_global_rect.left()}, {popup_global_rect.top()}, "
                            +
                            f"{popup_global_rect.right()}, {popup_global_rect.bottom()})"
                        )

                        if popup_global_rect.contains(mouse_pos):
                            self.__logger.debug("检测到鼠标点击在下拉列表区域内，不隐藏列表")
                            return False
                        else:
                            self.__logger.debug("鼠标点击在下拉列表区域外，将隐藏列表")

                    # 对于其他所有焦点丢失原因（鼠标点击别处、Tab切换、窗口失活等）
                    # 使用 QTimer 延迟隐藏，延长时间到200ms，确保足够时间处理点击事件
                    if self.__popup_list and self.__popup_list.isVisible():
                        # 使用 lambda 确保在执行时 self.__popup_list 仍然存在
                        QTimer.singleShot(
                            200, lambda: self.__check_and_hide_popup())
                        self.__logger.debug(
                            f"QLineEdit 因 '{focus_reason}' 失去焦点，已安排延迟200ms隐藏弹出列表。"
                        )
                    else:
                        self.__logger.debug(
                            f"QLineEdit 因 '{focus_reason}' 失去焦点，弹出列表已隐藏或不存在。")
                    # 返回 False 让事件继续
                    return False
                else:
                    # 如果事件不是 QFocusEvent (理论上 FocusOut 应该是)，记录警告并使用备用逻辑
                    self.__logger.warning(
                        "收到了 FocusOut 事件，但无法转换为 QFocusEvent。使用备用隐藏逻辑。")
                    # 作为备用方案，仍然尝试延迟隐藏，延长时间到200ms
                    if self.__popup_list and self.__popup_list.isVisible():
                        QTimer.singleShot(
                            200, lambda: self.__check_and_hide_popup())
                        self.__logger.debug(
                            "无法确定 FocusOut 原因，已安排延迟200ms隐藏弹出列表 (备用逻辑)。")
                    # 返回 False 让事件继续
                    return False

            # --- 处理输入框移动或大小调整事件 (Resize / Move) ---
            elif event.type() in [QEvent.Resize, QEvent.Move]:
                # 如果 QLineEdit 的大小或位置发生变化，并且下拉列表当前可见，
                # 则需要重新计算并更新下拉列表的位置和大小，以保持对齐。
                if self.__popup_list.isVisible():
                    self.__logger.debug(
                        "QLineEdit 发生 %s 事件，更新弹出列表几何形状。",
                        event.type().name,
                    )
                    self.__update_popup_geometry()
                # 这些事件通常也不应被消耗
                return False

            # 处理焦点离开事件
            if watched == self.__line_edit and event.type() == QEvent.FocusOut:
                self.__logger.debug("捕获到QLineEdit的FocusOut事件")
                # 处理自动完成列表的隐藏
                self.__check_and_hide_popup()
                # 处理失焦内容保存和处理
                self.__on_focus_out()
                return False  # 继续传递事件

        except Exception as e:
            self.__logger.error(
                "在 eventFilter 中处理事件 %s 时出错: %s\n%s",
                event.type().name,
                e,
                traceback.format_exc(),
            )
            # 发生错误时，最好也隐藏列表，避免处于不一致状态
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()
            # 重置点击处理标志
            self.__is_item_click_in_progress = False
            self.__mouse_pressed_on_list = False
            self.__clicked_item = None
            return False  # 发生错误时，让默认处理继续

        # 对于所有未在此处显式处理的事件，返回 False，让 Qt 或 QLineEdit 进行默认处理
        return False

    def __handle_direct_item_click(self, item: QListWidgetItem) -> None:
        """
        直接处理列表项点击的方法，绕过标准的信号-槽机制。

        这个方法在鼠标释放事件中直接调用，提高了点击处理的可靠性，
        特别是在事件处理顺序不确定的情况下。

        Args:
            item (QListWidgetItem): 被点击的列表项对象。
        """
        try:
            # 设置标志
            current_time = time.time()
            self.__last_click_time = current_time
            self.__is_item_click_in_progress = True
            self.__logger.debug(f"开始直接处理列表项点击: '{item.text()}'")

            # 获取文本并设置到输入框
            selected_text = item.text()

            # 阻塞信号以避免循环
            self.__line_edit.blockSignals(True)
            self.__line_edit.setText(selected_text)  # 将选中的文本设置到输入框
            self.__line_edit.blockSignals(False)

            # 光标移到末尾
            self.__line_edit.end(False)

            # 确保焦点回到输入框
            self.__line_edit.setFocus()

            # 使用延迟隐藏
            QTimer.singleShot(100, self.__hide_popup_after_selection)
            self.__logger.debug(f"直接处理完成，文本已设置为 '{selected_text}'，安排延迟隐藏列表")

        except Exception as e:
            self.__logger.error(f"直接处理列表项点击时出错: {e}\n{traceback.format_exc()}")
            self.__is_item_click_in_progress = False
            if self.__popup_list and self.__popup_list.isVisible():
                self.__popup_list.hide()

    def __check_and_hide_popup(self) -> None:
        """
        检查条件并决定是否隐藏弹出列表。

        此方法用于延迟处理焦点丢失事件，确保不会在点击列表项时
        因为焦点丢失而过早隐藏列表。
        """
        try:
            # 如果正在处理点击事件，不隐藏列表
            if self.__is_item_click_in_progress:
                self.__logger.debug("延迟隐藏检查：列表项点击处理中，不隐藏列表")
                return

            # 检查列表是否仍然可见
            if self.__popup_list and self.__popup_list.isVisible():
                self.__logger.debug("延迟隐藏检查：隐藏列表")
                self.__popup_list.hide()
        except Exception as e:
            self.__logger.error("检查延迟隐藏条件时出错: %s\n%s", e,
                                traceback.format_exc())

    # --- 清理方法 (Cleanup) ---
    def __cleanup(self) -> None:
        """执行清理操作，移除事件过滤器并断开连接。"""
        if self.__line_edit:
            try:
                # 移除事件过滤器
                self.__line_edit.removeEventFilter(self)
                self.__logger.info(
                    "已从 QLineEdit (%s) 移除事件过滤器。",
                    self.__line_edit.objectName() or "Unnamed",
                )
                # 尝试断开所有与此对象相关的连接（更安全的方式）
                # self.__line_edit.textChanged.disconnect(self.__on_text_changed) # Requires specific slot, safer to let Qt handle on destroy
                # self.__popup_list.itemClicked.disconnect(self.__on_item_clicked)
            except RuntimeError as e:
                # "wrapped C/C++ object of type ... has been deleted"
                self.__logger.warning("清理期间访问 QLineEdit 时出错（可能已被删除）: %s", e)
            except Exception as e:
                self.__logger.error("移除事件过滤器时出错: %s\n%s", e,
                                    traceback.format_exc())
            finally:
                # 将引用置为 None，帮助垃圾回收并防止后续访问已删除对象
                self.__line_edit = None

        if self.__popup_list:
            # 弹出列表是一个独立的窗口，最好也显式关闭和删除
            self.__popup_list.close()  # 关闭窗口
            self.__popup_list.deleteLater()  # 安排稍后安全删除
            self.__popup_list = None
            self.__logger.info("弹出列表已关闭并安排删除。")

        # 等待所有工作线程完成
        if hasattr(self, "__thread_pool"):
            self.__thread_pool.waitForDone(1000)  # 等待最多1秒
            self.__logger.info("线程池已等待工作线程完成。")

        # 清理失焦处理相关资源
        try:
            if hasattr(self, "__focus_lost_threadpool"):
                self.__focus_lost_threadpool.waitForDone()
                self.__logger.debug("等待所有失焦处理线程完成")

            if hasattr(self, "__focus_lost_queue"):
                self.__focus_lost_queue.clear()
                self.__logger.debug("清空失焦处理队列")

            self.__focus_lost_callback = None
            self.__focus_lost_processing = False
            self.__logger.debug("清理失焦处理相关资源完成")
        except Exception as e:
            self.__logger.error("清理失焦处理资源时出错: %s\n%s", e,
                                traceback.format_exc())

    def __cleanup_on_widget_destroyed(self) -> None:
        """当关联的 QLineEdit 被销毁时调用的槽函数。"""
        self.__logger.info("关联的 QLineEdit 即将被销毁，执行清理操作。")
        self.__cleanup()

    def __del__(self):
        """
        对象析构函数。在对象被垃圾回收时调用。
        注意：不保证 __del__ 一定会被调用，尤其是在程序退出时。
              依赖 QLineEdit 的 destroyed 信号进行清理通常更可靠。
        """
        self.__logger.debug("InputCompleter 对象 (%s) 正在被销毁。", id(self))
        # 在 __del__ 中调用清理可能不是最安全的，因为依赖的对象可能已不存在
        # self.__cleanup() # 最好依赖 destroyed 信号

    def __force_process_events(self) -> None:
        """
        强制处理事件队列并刷新UI，确保下拉列表正确显示。

        此方法用于在显示或更新下拉列表后，立即处理事件队列，
        确保UI刷新，列表可见性更新等操作能立即执行。
        """
        try:
            # 处理所有待处理的事件
            QApplication.processEvents()
            # 短暂等待，给Qt渲染系统一点时间
            QTimer.singleShot(1, lambda: None)
            # 再次处理事件
            QApplication.processEvents()
            self.__logger.debug("强制处理事件队列完成")
        except Exception as e:
            self.__logger.error("强制处理事件时出错: %s\n%s", e, traceback.format_exc())

    def set_focus_lost_callback(
            self, callback_fn: Optional[Callable[[List[str]], None]]) -> None:
        """
        设置在QLineEdit失去焦点时处理输入内容的回调函数。

        当QLineEdit失去焦点时，会将其当前内容保存到队列中，并异步调用此回调函数。
        如果回调函数已在执行，新的内容会被添加到队列中，等待当前执行完成后再处理。

        Args:
            callback_fn: 接收文本列表作为参数的回调函数，
                         如果为 None，则清除之前设置的回调函数。

        示例:
            ```python
            def process_texts(text_list):
                # 处理文本列表
                for text in text_list:
                    print(f"处理: {text}")

            completer.set_focus_lost_callback(process_texts)
            ```
        """
        self.__focus_lost_callback = callback_fn
        self.__logger.debug("设置失焦处理回调函数: %s", "已设置" if callback_fn else "已清除")
        # 如果已有队列中的内容，尝试处理它们
        if self.__focus_lost_queue and callback_fn:
            self.__logger.debug("检测到队列中有 %d 条待处理内容，开始处理",
                                len(self.__focus_lost_queue))
            self.__process_focus_lost_queue()

    def __on_focus_out(self) -> None:
        """
        当QLineEdit失去焦点时的处理函数。

        获取当前QLineEdit的文本内容，将其添加到队列中，
        并尝试启动异步处理队列中的内容。
        """
        try:
            self.__logger.debug("QLineEdit失去焦点事件触发")
            # 获取当前文本内容
            current_text = self.__line_edit.text().strip()
            self.__logger.debug("当前文本内容: '%s'", current_text)

            # 即使文本为空也添加到队列，由调用者决定如何处理
            self.__focus_lost_queue.append(current_text)
            self.__logger.debug(
                "文本'%s'已添加到队列，当前队列长度: %d",
                current_text,
                len(self.__focus_lost_queue),
            )

            # 尝试处理队列
            if self.__focus_lost_callback:
                self.__logger.debug("检测到回调函数已设置，准备处理队列")
                self.__process_focus_lost_queue()
            else:
                self.__logger.debug("未设置回调函数，无法处理队列")
        except Exception as e:
            self.__logger.error("处理失焦事件时出错: %s\n%s", e, traceback.format_exc())

    def __process_focus_lost_queue(self) -> None:
        """
        处理失焦内容队列。

        如果队列非空且回调函数已设置且当前没有正在进行的处理，
        则创建工作线程异步处理队列中的内容。
        """
        try:
            # 检查是否满足处理条件
            if not self.__focus_lost_queue:
                self.__logger.debug("队列为空，不处理")
                return

            if not self.__focus_lost_callback:
                self.__logger.debug("未设置回调函数，不处理")
                return

            if self.__focus_lost_processing:
                self.__logger.debug("正在处理中，稍后再处理新队列")
                return

            # 标记为正在处理
            self.__focus_lost_processing = True

            # 获取队列中的所有内容并清空队列
            text_list = self.__focus_lost_queue.copy()
            self.__focus_lost_queue.clear()

            self.__logger.debug(
                "开始异步处理失焦内容队列，内容数量: %d，内容: %s",
                len(text_list),
                text_list,
            )

            # 创建工作线程
            worker = self.__FocusLostWorker(self.__focus_lost_callback,
                                            text_list)

            # 连接信号
            worker.signals.finished.connect(
                self.__on_focus_lost_processing_finished)
            worker.signals.error.connect(self.__on_focus_lost_processing_error)

            # 启动工作线程
            self.__focus_lost_threadpool.start(worker)
            self.__logger.debug("工作线程已启动")
        except Exception as e:
            # 出错时重置处理状态
            self.__focus_lost_processing = False
            self.__logger.error("启动失焦内容处理线程时出错: %s\n%s", e,
                                traceback.format_exc())

    def __on_focus_lost_processing_finished(self) -> None:
        """
        当失焦内容处理完成时的回调函数。

        重置处理状态，并检查队列中是否有新的内容需要处理。
        """
        try:
            self.__logger.debug("失焦内容处理完成")
            # 重置处理状态
            self.__focus_lost_processing = False
            # 检查是否有新的内容需要处理
            self.__process_focus_lost_queue()
        except Exception as e:
            self.__logger.error("处理失焦内容完成回调时出错: %s\n%s", e,
                                traceback.format_exc())

    def __on_focus_lost_processing_error(self, error_msg: str,
                                         stack_trace: str) -> None:
        """
        当失焦内容处理出错时的回调函数。

        记录错误信息，重置处理状态，并检查队列中是否有新的内容需要处理。

        Args:
            error_msg: 错误信息
            stack_trace: 堆栈跟踪信息
        """
        try:
            self.__logger.error("处理失焦内容时出错: %s\n%s", error_msg, stack_trace)
            # 重置处理状态
            self.__focus_lost_processing = False
            # 检查是否有新的内容需要处理
            self.__process_focus_lost_queue()
        except Exception as e:
            self.__logger.error("处理失焦内容错误回调时出错: %s\n%s", e,
                                traceback.format_exc())


class InputCompleterCache:
    """
    与InputCompleter结合使用的缓存管理类，提供输入历史的持久化和自动补全功能。

    此类负责管理QLineEdit的输入历史记录，提供：
    1. 历史输入的持久化存储和加载
    2. 基于历史记录的输入补全建议
    3. 自动限制历史记录数量(最多20条)

    与InputCompleter配合使用，可以为QLineEdit提供智能且持久化的输入补全功能，
    历史记录会保存到配置文件，应用重启后依然可用。

    使用示例:
    --------
    ```python
    from GUI.utils.utils import InputCompleterCache

    # 单个QLineEdit使用方式（使用默认字体）
    line_edit = QLineEdit()
    cache_manager = InputCompleterCache(line_edit)

    # 多个QLineEdit使用方式（使用默认字体）
    line_edit1 = QLineEdit()
    line_edit2 = QLineEdit()
    cache_manager = InputCompleterCache([line_edit1, line_edit2])

    # 自定义字体配置
    cache_manager = InputCompleterCache(
        line_edit,
        font_family="Consolas, SimHei, monospace",  # 自定义字体族
        font_size=14,                               # 自定义字体大小
        font_weight=400                             # 自定义字体粗细
    )

    # 完成！line_edit已具备历史记录补全功能和自定义字体样式
    ```

    注意事项:
    - 确保保持对InputCompleterCache实例的引用，防止被垃圾回收。
    - 配置文件默认保存在'GUI/config/input_completer.json'，可以通过设置自定义路径。
    - 历史记录最多保存20条，超过时自动移除最早的记录。
    """

    # 类级别日志记录器
    __logger = logging.getLogger("InputCompleterCache")

    def __init__(
        self,
        line_edit_or_list,
        config_path: str = None,
        font_family: str = "JetBrains Mono SemiBold, Microsoft YaHei, monospace",
        font_size: int = 14,
        font_weight: int = 600,
    ):
        """
        初始化InputCompleterCache实例并自动设置所有功能。

        Args:
            line_edit_or_list: 要关联的QLineEdit控件，或QLineEdit控件列表
            config_path: 配置文件路径，默认为'GUI/config/input_completer.json'
            font_family: 下拉列表字体族，默认为 "JetBrains Mono SemiBold, Microsoft YaHei, monospace"
            font_size: 下拉列表字体大小，默认为 12
            font_weight: 下拉列表字体粗细，默认为 600 (SemiBold)
        """
        self.__config_path = config_path or os.path.join("GUI", "config", "input_completer.json")
        self.__completion_cache = {}  # 缓存{line_edit_name: [历史记录列表]}
        self.__line_edits = []  # 存储所有关联的QLineEdit实例
        self.__completers = {}  # 存储所有关联的InputCompleter实例 {line_edit_id: completer}
        self.__current_line_edit_name = None  # 当前活动的QLineEdit名称

        # 保存字体配置参数
        self.__font_family = font_family
        self.__font_size = font_size
        self.__font_weight = font_weight

        # 确保配置目录存在
        config_dir = os.path.dirname(self.__config_path)
        if not os.path.exists(config_dir):
            try:
                os.makedirs(config_dir)
                self.__logger.debug(f"创建配置目录: {config_dir}")
            except OSError as e:
                self.__logger.error(f"创建配置目录失败: {e}\n{traceback.format_exc()}")

        # 加载现有配置文件
        self.__load_completion_cache()

        # 处理输入参数，支持单个QLineEdit或QLineEdit列表
        if isinstance(line_edit_or_list, list):
            # 如果是列表，验证列表中的每个项是否为QLineEdit
            for idx, line_edit in enumerate(line_edit_or_list):
                if not isinstance(line_edit, QLineEdit):
                    self.__logger.error(
                        f"初始化失败: 列表中第{idx+1}项必须是QLineEdit类型，但收到了{type(line_edit).__name__}"
                    )
                    raise TypeError(f"列表中第{idx+1}项必须是QLineEdit类型")
                self.__add_line_edit(line_edit)
        elif isinstance(line_edit_or_list, QLineEdit):
            # 如果是单个QLineEdit
            self.__add_line_edit(line_edit_or_list)
        else:
            self.__logger.error(
                f"初始化失败: 'line_edit_or_list'参数必须是QLineEdit类型或QLineEdit列表，但收到了{type(line_edit_or_list).__name__}"
            )
            raise TypeError("'line_edit_or_list'必须是QLineEdit类型或QLineEdit列表")

        if not self.__line_edits:
            self.__logger.warning("没有有效的QLineEdit被添加")

        self.__logger.debug("InputCompleterCache初始化完成，已自动设置所有功能")

    def __add_line_edit(self, line_edit: QLineEdit):
        """
        添加单个QLineEdit到管理器中并设置其补全功能。

        Args:
            line_edit: 要添加的QLineEdit实例
        """
        try:
            # 将QLineEdit添加到列表
            self.__line_edits.append(line_edit)

            # 获取QLineEdit的唯一标识符
            line_edit_key = self.__get_line_edit_key(line_edit)

            # 如果这是第一个添加的QLineEdit，设置为当前活动的
            if self.__current_line_edit_name is None:
                self.__current_line_edit_name = line_edit_key

            # 为QLineEdit设置自动补全功能
            self.__setup_auto_completion(line_edit)

            # 当QLineEdit被销毁时，断开连接
            line_edit.destroyed.connect(lambda obj=line_edit: self.__on_line_edit_destroyed(obj))

            self.__logger.debug(f"已添加并设置QLineEdit: '{line_edit_key}'")
        except Exception as e:
            self.__logger.error(f"添加QLineEdit时出错: {e}\n{traceback.format_exc()}")

    def __setup_auto_completion(self, line_edit: QLineEdit):
        """
        为指定的QLineEdit设置补全功能。

        Args:
            line_edit: 要设置补全功能的QLineEdit实例
        """
        try:
            # 导入InputCompleter
            from global_tools.ui_tools import InputCompleter

            # 获取QLineEdit的唯一标识符
            line_edit_key = self.__get_line_edit_key(line_edit)

            # 创建InputCompleter实例，传递字体配置参数
            completer = InputCompleter(
                line_edit,
                font_family=self.__font_family,
                font_size=self.__font_size,
                font_weight=self.__font_weight
            )
            self.__completers[line_edit_key] = completer
            self.__logger.debug(f"已为QLineEdit '{line_edit_key}' 创建InputCompleter实例，字体配置: {self.__font_family}, {self.__font_size}px, {self.__font_weight}")

            # 设置回调函数
            completer.set_completion_callback(
                lambda text, le=line_edit: self.__get_completion_items_for_line_edit(text, le)
            )
            self.__logger.debug(f"已为QLineEdit '{line_edit_key}' 设置补全项回调函数")

            # 设置失焦回调函数
            completer.set_focus_lost_callback(
                lambda text_list, le=line_edit: self.__handle_focus_lost_text_for_line_edit(text_list, le)
            )
            self.__logger.debug(f"已为QLineEdit '{line_edit_key}' 设置失焦处理回调函数")

            self.__logger.info(f"QLineEdit '{line_edit_key}' 的补全功能已自动设置完成")

        except ImportError as e:
            self.__logger.error(f"导入InputCompleter失败，无法设置自动补全: {e}\n{traceback.format_exc()}")
        except Exception as e:
            self.__logger.error(f"设置自动补全功能时出错: {e}\n{traceback.format_exc()}")

    def __on_line_edit_destroyed(self, line_edit):
        """
        当QLineEdit被销毁时的处理函数。

        清理资源，防止内存泄漏。

        Args:
            line_edit: 被销毁的QLineEdit实例
        """
        try:
            # 获取QLineEdit的唯一标识符
            line_edit_key = self.__get_line_edit_key(line_edit)
            self.__logger.debug(f"QLineEdit '{line_edit_key}' 被销毁，执行清理")

            # 从列表和字典中移除
            if line_edit in self.__line_edits:
                self.__line_edits.remove(line_edit)

            # 清理对应的completer
            if line_edit_key in self.__completers:
                self.__completers.pop(line_edit_key)

            # 如果当前活动的QLineEdit被销毁，重置为None或另一个可用的
            if self.__current_line_edit_name == line_edit_key:
                self.__current_line_edit_name = next((self.__get_line_edit_key(le) for le in self.__line_edits), None)

        except Exception as e:
            self.__logger.error(f"处理QLineEdit销毁事件时出错: {e}\n{traceback.format_exc()}")

    def __get_line_edit_key(self, line_edit: QLineEdit) -> str:
        """
        获取QLineEdit的唯一键名，优先使用objectName，如果没有则使用内存地址。

        Args:
            line_edit: QLineEdit实例

        Returns:
            用于标识QLineEdit的唯一字符串
        """
        try:
            # 优先使用objectName作为键
            name = line_edit.objectName()
            if name:
                return name

            # 如果没有objectName，使用内存地址
            return f"line_edit_{id(line_edit)}"
        except Exception as e:
            self.__logger.error(f"获取QLineEdit键名时出错: {e}\n{traceback.format_exc()}")
            # 防止返回None，使用备用ID
            return f"line_edit_fallback_{id(line_edit)}"

    def __load_completion_cache(self) -> None:
        """
        从配置文件加载输入补全缓存。

        如果配置文件不存在或格式不正确，将初始化为空字典。
        """
        try:
            self.__logger.debug(f"尝试从 {self.__config_path} 加载补全缓存")

            if not os.path.exists(self.__config_path):
                self.__logger.debug("配置文件不存在，初始化为空缓存")
                self.__completion_cache = {}
                return

            with open(self.__config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 验证数据格式
                if not isinstance(data, dict):
                    self.__logger.warning(f"配置文件格式不正确，预期字典，实际为{type(data).__name__}，初始化为空缓存")
                    self.__completion_cache = {}
                    return

                # 对每个键值对验证值是否为列表
                valid_data = {}
                for key, value in data.items():
                    if isinstance(value, list):
                        # 确保列表项都是字符串
                        valid_list = [str(item) for item in value if item]
                        valid_data[key] = valid_list
                    else:
                        self.__logger.warning(f"键'{key}'的值不是列表类型，已跳过")

                self.__completion_cache = valid_data
                self.__logger.debug(f"成功加载补全缓存，包含 {len(self.__completion_cache)} 个QLineEdit的历史记录")

        except json.JSONDecodeError as e:
            self.__logger.error(f"解析配置文件时出错: {e}\n{traceback.format_exc()}")
            self.__completion_cache = {}
        except Exception as e:
            self.__logger.error(f"加载补全缓存时出错: {e}\n{traceback.format_exc()}")
            self.__completion_cache = {}

    def __save_completion_cache(self) -> None:
        """
        将补全缓存保存到配置文件。

        会创建必要的目录结构，并使用json格式保存数据。
        """
        try:
            self.__logger.debug(f"尝试将补全缓存保存到 {self.__config_path}")

            # 确保目录存在
            config_dir = os.path.dirname(self.__config_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
                self.__logger.debug(f"创建配置目录: {config_dir}")

            with open(self.__config_path, 'w', encoding='utf-8') as f:
                json.dump(self.__completion_cache, f, ensure_ascii=False, indent=2)

            self.__logger.debug(f"成功保存补全缓存，包含 {len(self.__completion_cache)} 个QLineEdit的历史记录")

        except OSError as e:
            self.__logger.error(f"保存配置文件时出错 (OS错误): {e}\n{traceback.format_exc()}")
        except Exception as e:
            self.__logger.error(f"保存补全缓存时出错: {e}\n{traceback.format_exc()}")

    def __get_completion_items_for_line_edit(self, text: str, line_edit: QLineEdit) -> List[str]:
        """
        为特定QLineEdit获取匹配的补全项列表。

        Args:
            text: 当前输入的文本，用于匹配历史记录
            line_edit: 需要获取补全项的QLineEdit实例

        Returns:
            匹配的补全项列表，如果没有匹配项则返回空列表
        """
        try:
            line_edit_key = self.__get_line_edit_key(line_edit)
            self.__logger.debug(f"为QLineEdit '{line_edit_key}' 获取匹配'{text}'的补全项")

            # 更新当前活动的QLineEdit
            self.__current_line_edit_name = line_edit_key

            # 获取QLineEdit的历史记录列表
            history_list = self.__completion_cache.get(line_edit_key, [])

            if not history_list:
                self.__logger.debug(f"QLineEdit '{line_edit_key}' 没有历史记录")
                return []

            # 如果text为空，返回全部历史记录
            if not text:
                self.__logger.debug(f"返回全部 {len(history_list)} 条历史记录")
                return history_list

            # 过滤匹配的项
            text_lower = text.lower()
            matched_items = [item for item in history_list if text_lower in item.lower()]

            self.__logger.debug(f"找到 {len(matched_items)} 条匹配'{text}'的历史记录")
            return matched_items

        except Exception as e:
            self.__logger.error(f"获取QLineEdit '{line_edit_key if 'line_edit_key' in locals() else 'unknown'}' 的补全项时出错: {e}\n{traceback.format_exc()}")
            return []

    def __handle_focus_lost_text_for_line_edit(self, text_list: List[str], line_edit: QLineEdit) -> None:
        """
        处理特定QLineEdit失去焦点时的文本，将其添加到历史记录并保存。

        Args:
            text_list: 要处理的文本列表
            line_edit: 失去焦点的QLineEdit实例
        """
        try:
            if not text_list:
                self.__logger.debug("收到空文本列表，不处理")
                return

            line_edit_key = self.__get_line_edit_key(line_edit)
            self.__logger.debug(f"处理QLineEdit '{line_edit_key}' 的失焦文本列表: {text_list}")

            # 处理每个文本
            for text in text_list:
                # 忽略空文本
                if not text or not text.strip():
                    continue

                # 获取现有历史记录，如果不存在则创建新列表
                if line_edit_key not in self.__completion_cache:
                    self.__completion_cache[line_edit_key] = []

                history_list = self.__completion_cache[line_edit_key]

                # 如果文本已存在于列表中，先移除它(避免重复)
                if text in history_list:
                    history_list.remove(text)

                # 添加新文本到列表开头(最新的在前面)
                history_list.insert(0, text)

                # 限制列表最大长度为20条
                if len(history_list) > 20:
                    history_list.pop()  # 移除最后一项(最旧的记录)

                self.__logger.debug(f"已将文本 '{text}' 添加到 '{line_edit_key}' 的历史记录中，当前记录数: {len(history_list)}")

            # 保存更新后的缓存
            self.__save_completion_cache()

        except Exception as e:
            self.__logger.error(f"处理QLineEdit '{line_edit_key if 'line_edit_key' in locals() else 'unknown'}' 的失焦文本时出错: {e}\n{traceback.format_exc()}")

    # 兼容原有接口的方法

    def get_completion_items(self, text: str) -> List[str]:
        """
        根据输入文本返回当前活动QLineEdit匹配的补全项列表。

        此方法作为InputCompleter的回调函数，无需手动调用。
        兼容原有接口，实际会调用__get_completion_items_for_line_edit方法。

        Args:
            text: 当前输入的文本，用于匹配历史记录

        Returns:
            匹配的补全项列表，如果没有匹配项则返回空列表
        """
        try:
            # 获取当前活动的QLineEdit
            current_line_edit = next((le for le in self.__line_edits if self.__get_line_edit_key(le) == self.__current_line_edit_name), None)

            if current_line_edit:
                return self.__get_completion_items_for_line_edit(text, current_line_edit)
            else:
                self.__logger.warning("没有找到当前活动的QLineEdit，无法获取补全项")
                return []

        except Exception as e:
            self.__logger.error(f"获取补全项时出错: {e}\n{traceback.format_exc()}")
            return []

    def handle_focus_lost_text(self, text_list: List[str]) -> None:
        """
        处理当前活动QLineEdit失去焦点时的文本，将其添加到历史记录并保存。

        此方法作为InputCompleter的回调函数，无需手动调用。
        兼容原有接口，实际会调用__handle_focus_lost_text_for_line_edit方法。

        Args:
            text_list: 要处理的文本列表，通常只有一个元素，即最后一次输入的文本
        """
        try:
            # 获取当前活动的QLineEdit
            current_line_edit = next((le for le in self.__line_edits if self.__get_line_edit_key(le) == self.__current_line_edit_name), None)

            if current_line_edit:
                self.__handle_focus_lost_text_for_line_edit(text_list, current_line_edit)
            else:
                self.__logger.warning("没有找到当前活动的QLineEdit，无法处理失焦文本")

        except Exception as e:
            self.__logger.error(f"处理失焦文本时出错: {e}\n{traceback.format_exc()}")

    def clear_history(self, line_edit_key: str = None) -> None:
        """
        清除指定QLineEdit或当前QLineEdit的历史记录。

        Args:
            line_edit_key: 要清除历史记录的QLineEdit标识符，如果为None则清除当前QLineEdit的历史
        """
        try:
            if line_edit_key is None:
                line_edit_key = self.__current_line_edit_name

            if line_edit_key in self.__completion_cache:
                del self.__completion_cache[line_edit_key]
                self.__logger.debug(f"已清除 '{line_edit_key}' 的历史记录")
                # 保存更新后的缓存
                self.__save_completion_cache()
            else:
                self.__logger.debug(f"'{line_edit_key}' 没有历史记录，无需清除")

        except Exception as e:
            self.__logger.error(f"清除历史记录时出错: {e}\n{traceback.format_exc()}")

    def get_history(self, line_edit_key: str = None) -> List[str]:
        """
        获取指定QLineEdit或当前QLineEdit的历史记录。

        Args:
            line_edit_key: 要获取历史记录的QLineEdit标识符，如果为None则获取当前QLineEdit的历史

        Returns:
            历史记录列表，如果没有记录则返回空列表
        """
        try:
            if line_edit_key is None:
                line_edit_key = self.__current_line_edit_name

            history = self.__completion_cache.get(line_edit_key, [])
            self.__logger.debug(f"获取 '{line_edit_key}' 的历史记录，条数: {len(history)}")
            return history

        except Exception as e:
            self.__logger.error(f"获取历史记录时出错: {e}\n{traceback.format_exc()}")
            return []
