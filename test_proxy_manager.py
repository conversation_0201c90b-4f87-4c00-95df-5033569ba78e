# -*- coding: utf-8 -*-
"""
ProcessProxy 跨进程调用测试用例
==============================

该测试文件专门测试子进程调用主进程方法、函数、实例的功能，
验证真正的跨进程通信和对象共享机制。

测试重点：
1. 子进程调用主进程的方法
2. 子进程修改主进程的对象状态
3. 多个子进程并发调用主进程
4. 跨进程异常处理和传播
5. 复杂对象的跨进程操作

作者：Augment Agent
版本：2.0.0 - 专注跨进程测试
"""

import unittest
import time
import multiprocessing
import os
import sys
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入被测试的模块
from global_tools.utils.enhanced_process.proxy_manager import (
    ProxyConfiguration,
    ProcessProxyManager
)

from global_tools.utils import ClassInstanceManager, LogLevel


def child_process_worker(manager_address, authkey, result_queue):
    """子进程工作函数 - 测试子进程调用主进程的方法"""
    try:
        result_queue.put(('debug', f'子进程 {os.getpid()} 开始工作'))

        # 在子进程中创建客户端代理管理器
        result_queue.put(('debug', f'创建客户端管理器，地址: {manager_address}'))
        client_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey
        )

        # 连接到主进程
        result_queue.put(('debug', '尝试连接到主进程'))
        client_manager.connect()
        result_queue.put(('debug', '成功连接到主进程'))

        # 创建服务代理
        result_queue.put(('debug', '创建服务代理'))
        service_proxy = client_manager.create_proxy('shared_service')
        result_queue.put(('debug', '服务代理创建成功'))

        # 在子进程中调用主进程的方法
        results = []

        # 测试基本方法调用
        result_queue.put(('debug', '开始测试方法调用'))
        result = service_proxy.increment()
        results.append(('increment_1', result))
        result_queue.put(('debug', f'第一次increment结果: {result}'))

        result = service_proxy.increment()
        results.append(('increment_2', result))
        result_queue.put(('debug', f'第二次increment结果: {result}'))

        # 测试获取状态
        count = service_proxy.get_count()
        results.append(('get_count', count))
        result_queue.put(('debug', f'get_count结果: {count}'))

        # 测试设置状态
        service_proxy.set_status('child_process_active')
        status = service_proxy.get_status()
        results.append(('get_status', status))
        result_queue.put(('debug', f'get_status结果: {status}'))

        # 测试获取数据
        data = service_proxy.get_data()
        results.append(('get_data', data))
        result_queue.put(('debug', f'get_data结果: {data}'))

        # 测试添加消息（验证跨进程数据传递）
        msg_count = service_proxy.add_message("来自子进程的消息", os.getpid())
        results.append(('add_message', msg_count))
        result_queue.put(('debug', f'add_message结果: {msg_count}'))

        # 将结果放入队列
        result_queue.put(('success', results))
        result_queue.put(('debug', '子进程工作完成'))

    except Exception as e:
        import traceback
        error_msg = f'子进程异常: {str(e)}'
        traceback_str = traceback.format_exc()
        result_queue.put(('error', error_msg, traceback_str))
        result_queue.put(('debug', f'子进程异常退出: {error_msg}'))


def multi_child_worker(worker_id, manager_address, authkey, results_queue):
    """多子进程工作函数 - 测试多个子进程并发调用"""
    try:
        # 连接到主进程
        client_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey
        )
        client_manager.connect()

        # 获取服务代理
        service_proxy = client_manager.create_proxy('counter_service')

        # 每个子进程执行5次increment操作
        for i in range(5):
            result = service_proxy.increment()
            results_queue.put(f"worker_{worker_id}_increment_{i+1}: {result}")
            time.sleep(0.01)  # 短暂延迟

        # 获取最终计数
        final_count = service_proxy.get_count()
        results_queue.put(f"worker_{worker_id}_final_count: {final_count}")

        # 添加工作者消息
        service_proxy.add_message(f"Worker {worker_id} 完成工作", os.getpid())

    except Exception as e:
        results_queue.put(f"worker_{worker_id}_error: {str(e)}")


def complex_service_worker(worker_id, manager_address, authkey, results_queue):
    """复杂服务工作函数 - 测试复杂对象的跨进程操作"""
    try:
        # 连接到主进程
        client_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey
        )
        client_manager.connect()

        # 获取复杂服务代理
        complex_proxy = client_manager.create_proxy('complex_service')

        # 创建专属计数器
        counter_name = f"worker_{worker_id}_counter"
        complex_proxy.create_counter(counter_name, 0)

        # 执行一些操作
        for i in range(3):
            new_value = complex_proxy.increment_counter(counter_name, 1)
            results_queue.put(f"worker_{worker_id}_counter_increment_{i+1}: {new_value}")

        # 存储一些数据
        data_key = f"worker_{worker_id}_data"
        complex_proxy.store_data(data_key, f"来自Worker {worker_id}的数据")

        # 获取存储的数据
        stored_data = complex_proxy.get_data(data_key)
        results_queue.put(f"worker_{worker_id}_stored_data: {stored_data}")

        # 获取服务统计
        stats = complex_proxy.get_service_stats()
        results_queue.put(f"worker_{worker_id}_stats: {stats['total_operations']}")

    except Exception as e:
        results_queue.put(f"worker_{worker_id}_error: {str(e)}")


class TestService:
    """测试用的服务类 - 用于跨进程调用测试"""

    def __init__(self, name="TestService"):
        """初始化测试服务"""
        self.name = name
        self.data = {"count": 0, "status": "active", "messages": []}
        self.call_history = []
        self.process_info = {"created_in": os.getpid()}

    def increment(self):
        """递增计数器"""
        self.data["count"] += 1
        self.call_history.append(f"increment_by_pid_{os.getpid()}")
        return self.data["count"]

    def get_count(self):
        """获取当前计数"""
        self.call_history.append(f"get_count_by_pid_{os.getpid()}")
        return self.data["count"]

    def get_data(self):
        """获取所有数据"""
        self.call_history.append(f"get_data_by_pid_{os.getpid()}")
        return self.data.copy()

    def set_status(self, status):
        """设置状态"""
        self.data["status"] = status
        self.call_history.append(f"set_status({status})_by_pid_{os.getpid()}")
        return True

    def get_status(self):
        """获取状态"""
        self.call_history.append(f"get_status_by_pid_{os.getpid()}")
        return self.data["status"]

    def add_message(self, message, sender_pid=None):
        """添加消息（用于测试跨进程数据传递）"""
        if sender_pid is None:
            sender_pid = os.getpid()

        msg_data = {
            "message": message,
            "sender_pid": sender_pid,
            "received_at": time.time(),
            "received_by_pid": os.getpid()
        }
        self.data["messages"].append(msg_data)
        self.call_history.append(f"add_message({message})_by_pid_{sender_pid}")
        return len(self.data["messages"])

    def get_messages(self):
        """获取所有消息"""
        self.call_history.append(f"get_messages_by_pid_{os.getpid()}")
        return self.data["messages"].copy()

    def clear_messages(self):
        """清空消息"""
        count = len(self.data["messages"])
        self.data["messages"].clear()
        self.call_history.append(f"clear_messages_by_pid_{os.getpid()}")
        return count

    def raise_error(self):
        """故意抛出异常用于测试跨进程错误处理"""
        self.call_history.append(f"raise_error_by_pid_{os.getpid()}")
        raise ValueError(f"测试异常来自进程 {os.getpid()}")

    def slow_operation(self, delay=0.1):
        """模拟慢操作"""
        time.sleep(delay)
        self.call_history.append(f"slow_operation({delay})_by_pid_{os.getpid()}")
        return f"操作完成，延迟{delay}秒，执行进程：{os.getpid()}"

    def get_process_info(self):
        """获取进程信息"""
        self.call_history.append(f"get_process_info_by_pid_{os.getpid()}")
        return {
            "created_in": self.process_info["created_in"],
            "current_pid": os.getpid(),
            "call_count": len(self.call_history)
        }

    def __len__(self):
        """支持len()函数"""
        return len(self.data)

    def __str__(self):
        """支持str()函数"""
        return f"TestService(name={self.name}, count={self.data['count']}, pid={os.getpid()})"

    def __bool__(self):
        """支持bool()函数"""
        return self.data["status"] == "active"


class TestComplexService:
    """复杂服务类 - 用于测试复杂对象的跨进程操作"""

    def __init__(self, service_id):
        """初始化复杂服务"""
        self.service_id = service_id
        self.state = {
            "initialized_at": time.time(),
            "initialized_by": os.getpid(),
            "counters": {},
            "data_store": {},
            "operation_log": []
        }

    def create_counter(self, counter_name, initial_value=0):
        """创建计数器"""
        self.state["counters"][counter_name] = initial_value
        self.state["operation_log"].append({
            "operation": "create_counter",
            "counter_name": counter_name,
            "initial_value": initial_value,
            "pid": os.getpid(),
            "timestamp": time.time()
        })
        return True

    def increment_counter(self, counter_name, amount=1):
        """递增计数器"""
        if counter_name not in self.state["counters"]:
            raise KeyError(f"计数器 '{counter_name}' 不存在")

        old_value = self.state["counters"][counter_name]
        self.state["counters"][counter_name] += amount
        new_value = self.state["counters"][counter_name]

        self.state["operation_log"].append({
            "operation": "increment_counter",
            "counter_name": counter_name,
            "amount": amount,
            "old_value": old_value,
            "new_value": new_value,
            "pid": os.getpid(),
            "timestamp": time.time()
        })

        return new_value

    def get_counter(self, counter_name):
        """获取计数器值"""
        if counter_name not in self.state["counters"]:
            raise KeyError(f"计数器 '{counter_name}' 不存在")

        value = self.state["counters"][counter_name]
        self.state["operation_log"].append({
            "operation": "get_counter",
            "counter_name": counter_name,
            "value": value,
            "pid": os.getpid(),
            "timestamp": time.time()
        })

        return value

    def store_data(self, key, value):
        """存储数据"""
        self.state["data_store"][key] = {
            "value": value,
            "stored_by": os.getpid(),
            "stored_at": time.time()
        }

        self.state["operation_log"].append({
            "operation": "store_data",
            "key": key,
            "value_type": type(value).__name__,
            "pid": os.getpid(),
            "timestamp": time.time()
        })

        return True

    def get_data(self, key):
        """获取数据"""
        if key not in self.state["data_store"]:
            raise KeyError(f"数据键 '{key}' 不存在")

        data = self.state["data_store"][key]
        self.state["operation_log"].append({
            "operation": "get_data",
            "key": key,
            "pid": os.getpid(),
            "timestamp": time.time()
        })

        return data["value"]

    def get_all_counters(self):
        """获取所有计数器"""
        self.state["operation_log"].append({
            "operation": "get_all_counters",
            "pid": os.getpid(),
            "timestamp": time.time()
        })
        return self.state["counters"].copy()

    def get_operation_log(self):
        """获取操作日志"""
        return self.state["operation_log"].copy()

    def get_service_stats(self):
        """获取服务统计信息"""
        stats = {
            "service_id": self.service_id,
            "initialized_at": self.state["initialized_at"],
            "initialized_by": self.state["initialized_by"],
            "current_pid": os.getpid(),
            "total_operations": len(self.state["operation_log"]),
            "counter_count": len(self.state["counters"]),
            "data_store_count": len(self.state["data_store"]),
            "last_operation_time": self.state["operation_log"][-1]["timestamp"] if self.state["operation_log"] else None
        }

        self.state["operation_log"].append({
            "operation": "get_service_stats",
            "pid": os.getpid(),
            "timestamp": time.time()
        })

        return stats


class TestCrossProcessCommunication(unittest.TestCase):
    """真正的跨进程通信测试 - 专门测试子进程调用主进程的方法、函数、实例"""

    def setUp(self):
        """测试前的设置"""
        # 设置日志级别
        logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
        if logger:
            logger.set_instance_level(LogLevel.INFO)

        # 设置多进程启动方法
        if hasattr(multiprocessing, 'set_start_method'):
            try:
                multiprocessing.set_start_method('spawn', force=True)
            except RuntimeError:
                pass  # 已经设置过了

    def test_child_process_calls_main_process(self):
        """测试子进程调用主进程的方法"""

        # 创建共享服务
        shared_service = TestService("SharedService")

        # 创建代理管理器
        config = ProxyConfiguration()
        config.debug_mode = True

        # 使用网络地址以支持跨进程通信
        manager_address = ('127.0.0.1', 0)  # 0表示自动分配端口
        authkey = b'test_multiprocess_key'

        proxy_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey,
            config=config
        )

        # 注册共享对象
        proxy_manager.register_object('shared_service', shared_service)

        # 启动管理器
        proxy_manager.start()

        try:
            # 获取实际的服务器地址
            actual_address = proxy_manager.address

            # 创建结果队列
            result_queue = multiprocessing.Queue()

            # 创建子进程
            child_process = multiprocessing.Process(
                target=child_process_worker,
                args=(actual_address, authkey, result_queue),
                name="TestChildProcess"
            )

            # 启动子进程
            child_process.start()

            # 等待子进程完成
            child_process.join(timeout=15)  # 15秒超时

            # 检查子进程是否正常结束
            self.assertEqual(child_process.exitcode, 0, "子进程异常退出")

            # 获取子进程结果
            self.assertFalse(result_queue.empty(), "子进程应该返回结果")
            result_data = result_queue.get()

            # 检查是否有错误
            if result_data[0] == 'error':
                self.fail(f"子进程执行失败: {result_data[1]}\n{result_data[2] if len(result_data) > 2 else ''}")

            # 验证子进程的调用结果
            self.assertEqual(result_data[0], 'success')
            child_results = result_data[1]

            # 验证主进程中的对象状态被子进程修改了
            self.assertEqual(shared_service.get_count(), 2, "子进程应该调用了2次increment")
            self.assertEqual(shared_service.get_status(), 'child_process_active', "子进程应该设置了状态")

            # 验证调用历史
            expected_calls = ['increment', 'increment', 'get_count', 'set_status(child_process_active)', 'get_status', 'get_data']
            for call in expected_calls:
                self.assertIn(call, shared_service.call_history, f"应该包含调用: {call}")

            # 验证子进程返回的结果
            expected_results = [
                ('increment_1', 1),
                ('increment_2', 2),
                ('get_count', 2),
                ('get_status', 'child_process_active')
            ]

            for expected in expected_results:
                found = False
                for actual in child_results:
                    if actual[0] == expected[0] and actual[1] == expected[1]:
                        found = True
                        break
                self.assertTrue(found, f"子进程结果中应该包含: {expected}")

        finally:
            # 清理资源
            if child_process.is_alive():
                child_process.terminate()
                child_process.join(timeout=2)

            proxy_manager.shutdown()

    def test_multiple_child_processes(self):
        """测试多个子进程同时调用主进程的方法"""

        def child_worker(worker_id, manager_address, authkey, results_queue):
            """子进程工作函数"""
            try:
                from multiprocessing.managers import BaseManager

                class ClientManager(BaseManager):
                    pass

                ClientManager.register('get_proxy_manager')

                client_manager = ClientManager(address=manager_address, authkey=authkey)
                client_manager.connect()

                proxy_manager = client_manager.get_proxy_manager()
                service_proxy = proxy_manager.create_proxy('counter_service')

                # 每个子进程执行5次increment操作
                for i in range(5):
                    result = service_proxy.increment()
                    results_queue.put(f"worker_{worker_id}_increment_{i+1}: {result}")
                    time.sleep(0.01)  # 短暂延迟

                # 获取最终计数
                final_count = service_proxy.get_count()
                results_queue.put(f"worker_{worker_id}_final_count: {final_count}")

            except Exception as e:
                results_queue.put(f"worker_{worker_id}_error: {str(e)}")

        # 创建计数器服务
        counter_service = TestService("CounterService")

        # 创建代理管理器
        config = ProxyConfiguration()
        config.debug_mode = True

        manager_address = ('127.0.0.1', 0)
        authkey = b'test_multi_child_key'

        proxy_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey,
            config=config
        )

        proxy_manager.register_object('counter_service', counter_service)
        proxy_manager.start()

        try:
            actual_address = proxy_manager.address

            # 创建结果队列
            results_queue = multiprocessing.Queue()

            # 创建3个子进程
            num_workers = 3
            processes = []

            for worker_id in range(num_workers):
                process = multiprocessing.Process(
                    target=child_worker,
                    args=(worker_id, actual_address, authkey, results_queue),
                    name=f"Worker_{worker_id}"
                )
                processes.append(process)
                process.start()

            # 等待所有子进程完成
            for process in processes:
                process.join(timeout=15)
                self.assertEqual(process.exitcode, 0, f"子进程 {process.name} 异常退出")

            # 收集结果
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())

            # 验证结果
            self.assertEqual(len(results), num_workers * 6)  # 每个worker 5次increment + 1次final_count

            # 验证最终计数（3个worker * 5次increment = 15）
            final_count = counter_service.get_count()
            self.assertEqual(final_count, 15, "最终计数应该是15")

            # 验证调用历史包含所有increment调用
            increment_calls = [call for call in counter_service.call_history if call == 'increment']
            self.assertEqual(len(increment_calls), 15, "应该有15次increment调用")

        finally:
            # 清理资源
            for process in processes:
                if process.is_alive():
                    process.terminate()
                    process.join(timeout=2)

            proxy_manager.shutdown()


if __name__ == '__main__':
    # 设置日志级别
    logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
    if logger:
        logger.set_instance_level(LogLevel.INFO)

    # 运行测试
    unittest.main(verbosity=2)
