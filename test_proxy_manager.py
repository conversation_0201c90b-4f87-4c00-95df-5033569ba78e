# -*- coding: utf-8 -*-
"""
ProcessProxy 系统完整测试用例
============================

该测试文件对 proxy_manager.py 中的所有核心类和功能进行全面测试，
确保进程间通信代理系统的正确性和稳定性。

测试覆盖：
1. ProxyConfiguration - 代理配置类
2. ErrorHandler - 错误处理器
3. PerformanceMonitor - 性能监控器
4. StateManager - 状态管理器
5. CallDispatcher - 调用分发器
6. CommandProcessor - 命令处理器
7. ProcessProxyManager - 代理管理器
8. ProcessProxy - 代理对象
9. ProxyFactory - 代理工厂
10. 完整的跨进程通信场景

作者：Augment Agent
版本：1.0.0
"""

import unittest
import time
import threading
import multiprocessing
import os
import sys
import traceback
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入被测试的模块
from global_tools.utils.enhanced_process.proxy_manager import (
    ProxyConfiguration,
    ErrorHandler,
    PerformanceMonitor,
    StateManager,
    CallDispatcher,
    CommandProcessor,
    ProcessProxyManager,
    ProcessProxy,
    ProxyFactory,
    create_proxy_manager,
    create_proxy
)

from global_tools.utils import Logger, Colors, ClassInstanceManager, LogLevel


class TestService:
    """测试用的服务类"""
    
    def __init__(self, name="TestService"):
        """初始化测试服务"""
        self.name = name
        self.data = {"count": 0, "status": "active"}
        self.call_history = []
    
    def increment(self):
        """递增计数器"""
        self.data["count"] += 1
        self.call_history.append("increment")
        return self.data["count"]
    
    def get_count(self):
        """获取当前计数"""
        self.call_history.append("get_count")
        return self.data["count"]
    
    def get_data(self):
        """获取所有数据"""
        self.call_history.append("get_data")
        return self.data.copy()
    
    def set_status(self, status):
        """设置状态"""
        self.data["status"] = status
        self.call_history.append(f"set_status({status})")
        return True
    
    def get_status(self):
        """获取状态"""
        self.call_history.append("get_status")
        return self.data["status"]
    
    def raise_error(self):
        """故意抛出异常用于测试错误处理"""
        self.call_history.append("raise_error")
        raise ValueError("这是一个测试异常")
    
    def slow_operation(self, delay=0.1):
        """模拟慢操作"""
        time.sleep(delay)
        self.call_history.append(f"slow_operation({delay})")
        return f"操作完成，延迟{delay}秒"
    
    def __len__(self):
        """支持len()函数"""
        return len(self.data)
    
    def __str__(self):
        """支持str()函数"""
        return f"TestService(name={self.name}, count={self.data['count']})"
    
    def __bool__(self):
        """支持bool()函数"""
        return self.data["status"] == "active"


class TestProxyConfiguration(unittest.TestCase):
    """测试 ProxyConfiguration 类"""
    
    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
    
    def test_default_configuration(self):
        """测试默认配置"""
        self.assertEqual(self.config.cache_ttl, 5.0)
        self.assertEqual(self.config.batch_sync_threshold, 10)
        self.assertEqual(self.config.queue_timeout, 1.0)
        self.assertFalse(self.config.debug_mode)
        self.assertTrue(self.config.performance_monitoring)
        self.assertEqual(self.config.max_retries, 3)
        self.assertEqual(self.config.retry_delay, 0.1)
    
    def test_optimize_for_scenario(self):
        """测试场景优化"""
        # 测试高频访问场景
        self.config.optimize_for_scenario('high_frequency')
        self.assertEqual(self.config.cache_ttl, 1.0)
        self.assertEqual(self.config.batch_sync_threshold, 5)
        self.assertEqual(self.config.queue_timeout, 0.5)
        self.assertEqual(self.config.max_retries, 2)
        
        # 测试大数据传输场景
        self.config.optimize_for_scenario('large_data')
        self.assertEqual(self.config.cache_ttl, 10.0)
        self.assertEqual(self.config.batch_sync_threshold, 20)
        self.assertEqual(self.config.queue_timeout, 5.0)
        self.assertEqual(self.config.max_retries, 5)
        
        # 测试低延迟场景
        self.config.optimize_for_scenario('low_latency')
        self.assertEqual(self.config.cache_ttl, 0.5)
        self.assertEqual(self.config.batch_sync_threshold, 1)
        self.assertEqual(self.config.queue_timeout, 0.1)
        self.assertEqual(self.config.max_retries, 1)
        
        # 测试调试场景
        self.config.optimize_for_scenario('debug')
        self.assertTrue(self.config.debug_mode)
        self.assertTrue(self.config.performance_monitoring)
        self.assertEqual(self.config.cache_ttl, 2.0)
        
        # 测试未知场景
        original_cache_ttl = self.config.cache_ttl
        self.config.optimize_for_scenario('unknown_scenario')
        self.assertEqual(self.config.cache_ttl, original_cache_ttl)  # 应该保持不变
    
    def test_validate_config(self):
        """测试配置验证"""
        # 测试有效配置
        self.assertTrue(self.config.validate_config())
        
        # 测试无效配置
        self.config.cache_ttl = -1
        self.assertFalse(self.config.validate_config())
        
        self.config.cache_ttl = 5.0
        self.config.batch_sync_threshold = 0
        self.assertFalse(self.config.validate_config())
        
        self.config.batch_sync_threshold = 10
        self.config.queue_timeout = -0.5
        self.assertFalse(self.config.validate_config())
        
        self.config.queue_timeout = 1.0
        self.config.max_retries = -1
        self.assertFalse(self.config.validate_config())
        
        self.config.max_retries = 3
        self.config.retry_delay = -0.1
        self.assertFalse(self.config.validate_config())
    
    def test_to_dict(self):
        """测试配置导出为字典"""
        config_dict = self.config.to_dict()
        
        expected_keys = {
            'cache_ttl', 'batch_sync_threshold', 'queue_timeout',
            'debug_mode', 'performance_monitoring', 'max_retries', 'retry_delay'
        }
        
        self.assertEqual(set(config_dict.keys()), expected_keys)
        self.assertEqual(config_dict['cache_ttl'], 5.0)
        self.assertEqual(config_dict['debug_mode'], False)
        self.assertEqual(config_dict['performance_monitoring'], True)


class TestErrorHandler(unittest.TestCase):
    """测试 ErrorHandler 类"""
    
    def setUp(self):
        """测试前的设置"""
        self.error_handler = ErrorHandler(debug_mode=True)
    
    def test_handle_cross_process_exception(self):
        """测试跨进程异常处理"""
        # 创建一个测试异常
        try:
            raise ValueError("测试异常消息")
        except Exception as e:
            context = {
                'obj_id': 'test_service',
                'method_name': 'test_method',
                'args': (1, 2, 3),
                'kwargs': {'key': 'value'}
            }
            
            # 处理异常
            error_info = self.error_handler.handle_cross_process_exception(e, context)
            
            # 验证错误信息
            self.assertEqual(error_info['exception_type'], 'ValueError')
            self.assertEqual(error_info['exception_message'], '测试异常消息')
            self.assertIn('traceback', error_info)
            self.assertEqual(error_info['process_id'], os.getpid())
            self.assertIn('timestamp', error_info)
            self.assertEqual(error_info['context'], context)
            self.assertIn('error_id', error_info)
    
    def test_create_serializable_exception(self):
        """测试创建可序列化异常"""
        error_info = {
            'exception_type': 'ValueError',
            'exception_message': '测试异常',
            'traceback': 'test traceback',
            'process_id': 12345,
            'timestamp': time.time(),
            'context': {'test': 'context'}
        }
        
        proxy_exception = self.error_handler.create_serializable_exception(error_info)
        
        self.assertIsInstance(proxy_exception, Exception)
        self.assertEqual(str(proxy_exception), "[ProcessProxy] ValueError: 测试异常")
        self.assertEqual(proxy_exception.error_info, error_info)
    
    def test_get_error_statistics(self):
        """测试错误统计"""
        # 初始状态应该没有错误
        stats = self.error_handler.get_error_statistics()
        self.assertEqual(stats['total_errors'], 0)
        self.assertEqual(stats['error_types'], {})
        
        # 添加一些错误
        try:
            raise ValueError("错误1")
        except Exception as e:
            self.error_handler.handle_cross_process_exception(e, {'test': 1})
        
        try:
            raise TypeError("错误2")
        except Exception as e:
            self.error_handler.handle_cross_process_exception(e, {'test': 2})
        
        try:
            raise ValueError("错误3")
        except Exception as e:
            self.error_handler.handle_cross_process_exception(e, {'test': 3})
        
        # 检查统计信息
        stats = self.error_handler.get_error_statistics()
        self.assertEqual(stats['total_errors'], 3)
        self.assertEqual(stats['error_types']['ValueError'], 2)
        self.assertEqual(stats['error_types']['TypeError'], 1)
        self.assertEqual(len(stats['recent_errors']), 3)


class TestPerformanceMonitor(unittest.TestCase):
    """测试 PerformanceMonitor 类"""
    
    def setUp(self):
        """测试前的设置"""
        self.monitor = PerformanceMonitor()
    
    def test_record_call(self):
        """测试记录方法调用"""
        # 记录一些调用
        self.monitor.record_call('service.increment', 0.05, True)
        self.monitor.record_call('service.increment', 0.03, True)
        self.monitor.record_call('service.increment', 0.08, False)
        self.monitor.record_call('db.query', 1.2, True)
        
        # 获取性能报告
        report = self.monitor.get_performance_report()
        
        # 验证报告内容
        self.assertEqual(report['summary']['total_methods'], 2)
        self.assertEqual(report['summary']['total_calls'], 4)
        self.assertEqual(report['summary']['total_success_calls'], 3)
        
        # 验证方法详情
        increment_detail = report['method_details']['service.increment']
        self.assertEqual(increment_detail['total_calls'], 3)
        self.assertEqual(increment_detail['success_rate'], 2/3)
        self.assertAlmostEqual(increment_detail['avg_latency'], (0.05 + 0.03 + 0.08) / 3, places=3)
        self.assertEqual(increment_detail['min_latency'], 0.03)
        self.assertEqual(increment_detail['max_latency'], 0.08)
        
        query_detail = report['method_details']['db.query']
        self.assertEqual(query_detail['total_calls'], 1)
        self.assertEqual(query_detail['success_rate'], 1.0)
        self.assertEqual(query_detail['avg_latency'], 1.2)

    def test_get_optimization_suggestions(self):
        """测试优化建议"""
        # 记录一些性能数据
        # 高延迟方法
        for _ in range(10):
            self.monitor.record_call('slow_method', 3.0, True)

        # 低成功率方法
        for _ in range(10):
            self.monitor.record_call('unreliable_method', 0.1, False)
        for _ in range(2):
            self.monitor.record_call('unreliable_method', 0.1, True)

        # 高频高延迟方法
        for _ in range(1500):
            self.monitor.record_call('frequent_slow_method', 0.2, True)

        # 获取优化建议
        suggestions = self.monitor.get_optimization_suggestions()

        # 验证建议
        self.assertIn('slow_method', suggestions)
        self.assertIn('平均延迟过高', suggestions['slow_method'])

        self.assertIn('unreliable_method', suggestions)
        self.assertIn('成功率较低', suggestions['unreliable_method'])

        self.assertIn('frequent_slow_method', suggestions)
        self.assertIn('高频调用且有延迟', suggestions['frequent_slow_method'])


class TestStateManager(unittest.TestCase):
    """测试 StateManager 类"""

    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
        self.config.cache_ttl = 0.1  # 短TTL用于测试
        self.state_manager = StateManager(self.config)

    def test_cache_operations(self):
        """测试缓存操作"""
        # 测试缓存更新和获取
        self.state_manager.update_cache('service', 'data', {'key': 'value'})

        # 验证缓存有效
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data'))

        # 获取缓存值
        cached_value = self.state_manager.get_from_cache('service', 'data')
        self.assertEqual(cached_value, {'key': 'value'})

        # 等待缓存过期
        time.sleep(0.15)

        # 验证缓存已过期
        self.assertFalse(self.state_manager.is_cache_valid('service', 'data'))

        # 获取过期缓存应该返回None
        cached_value = self.state_manager.get_from_cache('service', 'data')
        self.assertIsNone(cached_value)

    def test_cache_invalidation(self):
        """测试缓存失效"""
        # 添加一些缓存
        self.state_manager.update_cache('service', 'data1', 'value1')
        self.state_manager.update_cache('service', 'data2', 'value2')
        self.state_manager.update_cache('other', 'data3', 'value3')

        # 验证缓存存在
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data1'))
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data2'))
        self.assertTrue(self.state_manager.is_cache_valid('other', 'data3'))

        # 失效特定缓存
        self.state_manager.invalidate_cache('service', 'data1')
        self.assertFalse(self.state_manager.is_cache_valid('service', 'data1'))
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data2'))
        self.assertTrue(self.state_manager.is_cache_valid('other', 'data3'))

        # 失效对象的所有缓存
        self.state_manager.invalidate_cache('service')
        self.assertFalse(self.state_manager.is_cache_valid('service', 'data2'))
        self.assertTrue(self.state_manager.is_cache_valid('other', 'data3'))

    def test_dirty_flags(self):
        """测试脏数据标记"""
        # 添加缓存
        self.state_manager.update_cache('service', 'data', 'value')
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data'))

        # 标记为脏数据
        self.state_manager.mark_dirty('service', 'data')
        self.assertFalse(self.state_manager.is_cache_valid('service', 'data'))

        # 重新更新缓存应该清除脏标记
        self.state_manager.update_cache('service', 'data', 'new_value')
        self.assertTrue(self.state_manager.is_cache_valid('service', 'data'))

    def test_cache_statistics(self):
        """测试缓存统计"""
        # 初始统计
        stats = self.state_manager.get_cache_statistics()
        self.assertEqual(stats['total_cache_items'], 0)
        self.assertEqual(stats['valid_cache_items'], 0)
        self.assertEqual(stats['dirty_cache_items'], 0)

        # 添加一些缓存
        self.state_manager.update_cache('service', 'data1', 'value1')
        self.state_manager.update_cache('service', 'data2', 'value2')

        # 标记一个为脏数据
        self.state_manager.mark_dirty('service', 'data1')

        # 检查统计
        stats = self.state_manager.get_cache_statistics()
        self.assertEqual(stats['total_cache_items'], 2)
        self.assertEqual(stats['valid_cache_items'], 1)  # 只有data2有效
        self.assertEqual(stats['dirty_cache_items'], 1)  # data1被标记为脏


class TestCallDispatcher(unittest.TestCase):
    """测试 CallDispatcher 类"""

    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
        self.state_manager = StateManager(self.config)
        self.performance_monitor = PerformanceMonitor()
        self.dispatcher = CallDispatcher(self.config, self.state_manager, self.performance_monitor)

    def test_analyze_method_type(self):
        """测试方法类型分析"""
        # 测试简单getter方法
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'get_data'), 'simple_getter')
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'is_active'), 'simple_getter')
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'has_permission'), 'simple_getter')
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'count'), 'simple_getter')

        # 测试简单setter方法
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'set_data'), 'simple_setter')

        # 测试魔术方法
        self.assertEqual(self.dispatcher.analyze_method_type('service', '__len__'), 'magic_method')
        self.assertEqual(self.dispatcher.analyze_method_type('service', '__str__'), 'magic_method')
        self.assertEqual(self.dispatcher.analyze_method_type('service', '__bool__'), 'magic_method')

        # 测试复杂操作方法
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'process_data'), 'complex_operation')
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'calculate'), 'complex_operation')

        # 测试缓存
        # 第二次调用应该从缓存返回
        self.assertEqual(self.dispatcher.analyze_method_type('service', 'get_data'), 'simple_getter')

    def test_should_use_manager_proxy(self):
        """测试是否应该使用Manager代理"""
        self.assertTrue(self.dispatcher.should_use_manager_proxy('simple_getter'))
        self.assertTrue(self.dispatcher.should_use_manager_proxy('magic_method'))
        self.assertFalse(self.dispatcher.should_use_manager_proxy('simple_setter'))
        self.assertFalse(self.dispatcher.should_use_manager_proxy('complex_operation'))


class TestCommandProcessor(unittest.TestCase):
    """测试 CommandProcessor 类"""

    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
        self.config.queue_timeout = 0.5  # 短超时用于测试
        self.error_handler = ErrorHandler(debug_mode=True)
        self.test_service = TestService("TestCommandProcessor")
        self.target_objects = {'test_service': self.test_service}
        self.processor = CommandProcessor(self.target_objects, self.config, self.error_handler)

    def test_start_stop(self):
        """测试启动和停止"""
        # 初始状态应该未运行
        stats = self.processor.get_statistics()
        self.assertFalse(stats['running'])

        # 启动处理器
        self.processor.start()
        stats = self.processor.get_statistics()
        self.assertTrue(stats['running'])

        # 停止处理器
        self.processor.stop()
        stats = self.processor.get_statistics()
        self.assertFalse(stats['running'])

    def test_execute_command_success(self):
        """测试成功执行命令"""
        self.processor.start()

        try:
            # 执行increment命令
            result = self.processor.execute_command('test_service', 'increment', (), {})
            self.assertEqual(result, 1)

            # 执行get_count命令
            result = self.processor.execute_command('test_service', 'get_count', (), {})
            self.assertEqual(result, 1)

            # 执行带参数的命令
            result = self.processor.execute_command('test_service', 'set_status', ('inactive',), {})
            self.assertTrue(result)

            result = self.processor.execute_command('test_service', 'get_status', (), {})
            self.assertEqual(result, 'inactive')

        finally:
            self.processor.stop()

    def test_execute_command_error(self):
        """测试命令执行错误"""
        self.processor.start()

        try:
            # 测试对象不存在
            with self.assertRaises(Exception):
                self.processor.execute_command('nonexistent', 'method', (), {})

            # 测试方法不存在
            with self.assertRaises(Exception):
                self.processor.execute_command('test_service', 'nonexistent_method', (), {})

            # 测试方法抛出异常
            with self.assertRaises(Exception):
                self.processor.execute_command('test_service', 'raise_error', (), {})

        finally:
            self.processor.stop()

    def test_execute_command_timeout(self):
        """测试命令执行超时"""
        # 不启动处理器，这样命令会超时
        with self.assertRaises(RuntimeError):
            self.processor.execute_command('test_service', 'increment', (), {})

    def test_statistics(self):
        """测试统计信息"""
        stats = self.processor.get_statistics()

        self.assertIn('running', stats)
        self.assertIn('command_queue_size', stats)
        self.assertIn('active_response_queues', stats)
        self.assertIn('registered_objects', stats)

        self.assertEqual(stats['registered_objects'], ['test_service'])


class TestProcessProxyManager(unittest.TestCase):
    """测试 ProcessProxyManager 类"""

    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
        self.config.debug_mode = True
        self.test_service = TestService("TestProxyManager")

    def test_initialization(self):
        """测试初始化"""
        manager = ProcessProxyManager(config=self.config)

        # 验证配置
        self.assertEqual(manager.config, self.config)
        self.assertTrue(manager.config.debug_mode)

        # 验证初始状态
        self.assertEqual(len(manager.get_registered_objects()), 0)

    def test_object_registration(self):
        """测试对象注册"""
        manager = ProcessProxyManager(config=self.config)

        # 注册对象
        manager.register_object('test_service', self.test_service)

        # 验证注册
        registered_objects = manager.get_registered_objects()
        self.assertIn('test_service', registered_objects)
        self.assertEqual(len(registered_objects), 1)

        # 测试重复注册应该失败
        with self.assertRaises(ValueError):
            manager.register_object('test_service', self.test_service)

        # 测试无效参数
        with self.assertRaises(ValueError):
            manager.register_object('', self.test_service)

        with self.assertRaises(ValueError):
            manager.register_object('test', None)

    def test_object_unregistration(self):
        """测试对象注销"""
        manager = ProcessProxyManager(config=self.config)

        # 注册对象
        manager.register_object('test_service', self.test_service)
        self.assertIn('test_service', manager.get_registered_objects())

        # 注销对象
        manager.unregister_object('test_service')
        self.assertNotIn('test_service', manager.get_registered_objects())

        # 测试注销不存在的对象
        with self.assertRaises(KeyError):
            manager.unregister_object('nonexistent')

    def test_create_proxy(self):
        """测试创建代理对象"""
        manager = ProcessProxyManager(config=self.config)
        manager.register_object('test_service', self.test_service)

        # 创建代理
        proxy = manager.create_proxy('test_service')
        self.assertIsInstance(proxy, ProcessProxy)

        # 测试创建不存在对象的代理
        with self.assertRaises(KeyError):
            manager.create_proxy('nonexistent')

    def test_start_shutdown(self):
        """测试启动和关闭"""
        manager = ProcessProxyManager(config=self.config)
        manager.register_object('test_service', self.test_service)

        # 启动管理器
        manager.start()

        # 验证启动状态
        stats = manager.get_statistics()
        self.assertTrue(stats['is_server'])

        # 关闭管理器
        manager.shutdown()

    def test_statistics(self):
        """测试统计信息"""
        manager = ProcessProxyManager(config=self.config)
        manager.register_object('test_service', self.test_service)

        stats = manager.get_statistics()

        self.assertIn('is_server', stats)
        self.assertIn('registered_objects', stats)
        self.assertIn('object_list', stats)
        self.assertIn('config', stats)

        self.assertEqual(stats['registered_objects'], 1)
        self.assertEqual(stats['object_list'], ['test_service'])
        self.assertEqual(stats['config'], self.config.to_dict())


class TestProcessProxy(unittest.TestCase):
    """测试 ProcessProxy 类"""

    def setUp(self):
        """测试前的设置"""
        self.config = ProxyConfiguration()
        self.config.debug_mode = True
        self.test_service = TestService("TestProcessProxy")
        self.manager = ProcessProxyManager(config=self.config)
        self.manager.register_object('test_service', self.test_service)
        self.manager.start()

    def tearDown(self):
        """测试后的清理"""
        try:
            self.manager.shutdown()
        except:
            pass

    def test_proxy_creation(self):
        """测试代理对象创建"""
        proxy = self.manager.create_proxy('test_service')
        self.assertIsInstance(proxy, ProcessProxy)

        # 验证代理信息
        info = proxy._get_proxy_info()
        self.assertEqual(info['obj_id'], 'test_service')
        self.assertTrue(info['is_server_mode'])
        self.assertTrue(info['initialized'])

    def test_method_calls(self):
        """测试方法调用"""
        proxy = self.manager.create_proxy('test_service')

        # 测试无参数方法调用
        result = proxy.increment()
        self.assertEqual(result, 1)

        result = proxy.get_count()
        self.assertEqual(result, 1)

        # 测试带参数方法调用
        result = proxy.set_status('testing')
        self.assertTrue(result)

        result = proxy.get_status()
        self.assertEqual(result, 'testing')

        # 验证调用历史
        self.assertIn('increment', self.test_service.call_history)
        self.assertIn('get_count', self.test_service.call_history)
        self.assertIn('set_status(testing)', self.test_service.call_history)
        self.assertIn('get_status', self.test_service.call_history)

    def test_magic_methods(self):
        """测试魔术方法"""
        proxy = self.manager.create_proxy('test_service')

        # 测试__len__
        try:
            length = len(proxy)
            self.assertEqual(length, len(self.test_service.data))
        except:
            # 如果魔术方法不可用，跳过测试
            pass

        # 测试__str__
        try:
            str_repr = str(proxy)
            self.assertIsInstance(str_repr, str)
        except:
            pass

        # 测试__bool__
        try:
            bool_value = bool(proxy)
            self.assertIsInstance(bool_value, bool)
        except:
            pass

    def test_error_handling(self):
        """测试错误处理"""
        proxy = self.manager.create_proxy('test_service')

        # 测试方法抛出异常
        with self.assertRaises(Exception):
            proxy.raise_error()

        # 测试访问不存在的属性
        with self.assertRaises(AttributeError):
            _ = proxy.nonexistent_attribute

    def test_cache_refresh(self):
        """测试缓存刷新"""
        proxy = self.manager.create_proxy('test_service')

        # 刷新缓存应该不抛出异常
        proxy._refresh_cache()

        # 同步主进程应该不抛出异常
        proxy._sync_with_main_process()


class TestProxyFactory(unittest.TestCase):
    """测试 ProxyFactory 类"""

    def setUp(self):
        """测试前的设置"""
        self.factory = ProxyFactory()
        self.config = ProxyConfiguration()
        self.manager = ProcessProxyManager(config=self.config)
        self.test_service = TestService("TestProxyFactory")
        self.manager.register_object('test_service', self.test_service)
        self.manager.start()

    def tearDown(self):
        """测试后的清理"""
        try:
            self.manager.shutdown()
        except:
            pass

    def test_create_proxy(self):
        """测试创建代理"""
        proxy = self.factory.create_proxy('test_service', TestService, self.manager)
        self.assertIsInstance(proxy, ProcessProxy)

        # 测试代理池功能
        proxy2 = self.factory.create_proxy('test_service', TestService, self.manager)
        self.assertIs(proxy, proxy2)  # 应该返回相同的代理对象

    def test_proxy_strategies(self):
        """测试代理策略"""
        # 测试数据类策略
        class DataModel:
            pass

        data_model = DataModel()
        self.manager.register_object('data_model', data_model)
        proxy = self.factory.create_proxy('data_model', DataModel, self.manager)
        self.assertIsInstance(proxy, ProcessProxy)

        # 测试服务类策略
        class ServiceManager:
            pass

        service_manager = ServiceManager()
        self.manager.register_object('service_manager', service_manager)
        proxy = self.factory.create_proxy('service_manager', ServiceManager, self.manager)
        self.assertIsInstance(proxy, ProcessProxy)

    def test_custom_strategy(self):
        """测试自定义策略"""
        def custom_strategy(obj_id, obj_type, proxy_manager):
            return ProcessProxy(proxy_manager, obj_id)

        # 注册自定义策略
        self.factory.register_proxy_strategy(TestService, custom_strategy)

        # 注册对象
        custom_service = TestService("CustomService")
        self.manager.register_object('custom_service', custom_service)

        # 使用自定义策略创建代理
        proxy = self.factory.create_proxy('custom_service', TestService, self.manager)
        self.assertIsInstance(proxy, ProcessProxy)

    def test_proxy_pool_stats(self):
        """测试代理池统计"""
        # 初始统计
        stats = self.factory.get_proxy_pool_stats()
        initial_count = stats['total_proxies']

        # 注册对象
        service1 = TestService("Service1")
        service2 = TestService("Service2")
        self.manager.register_object('service1', service1)
        self.manager.register_object('service2', service2)

        # 创建一些代理
        self.factory.create_proxy('service1', TestService, self.manager)
        self.factory.create_proxy('service2', TestService, self.manager)

        # 检查统计
        stats = self.factory.get_proxy_pool_stats()
        self.assertEqual(stats['total_proxies'], initial_count + 2)
        self.assertIn('service1', stats['proxy_objects'])
        self.assertIn('service2', stats['proxy_objects'])

    def test_cleanup_unused_proxies(self):
        """测试清理未使用的代理"""
        # 注册对象
        temp1 = TestService("Temp1")
        temp2 = TestService("Temp2")
        self.manager.register_object('temp1', temp1)
        self.manager.register_object('temp2', temp2)

        # 创建一些代理
        self.factory.create_proxy('temp1', TestService, self.manager)
        self.factory.create_proxy('temp2', TestService, self.manager)

        # 清理代理池
        self.factory.cleanup_unused_proxies()

        # 验证清理结果
        stats = self.factory.get_proxy_pool_stats()
        self.assertEqual(stats['total_proxies'], 0)


class TestConvenienceFunctions(unittest.TestCase):
    """测试便捷函数"""

    def test_create_proxy_manager(self):
        """测试创建代理管理器便捷函数"""
        # 测试默认参数
        manager = create_proxy_manager()
        self.assertIsInstance(manager, ProcessProxyManager)

        # 测试自定义参数
        config = ProxyConfiguration()
        config.debug_mode = True

        manager = create_proxy_manager(
            address=('127.0.0.1', 0),
            authkey=b'test_key',
            config=config
        )
        self.assertIsInstance(manager, ProcessProxyManager)
        self.assertTrue(manager.config.debug_mode)

    def test_create_proxy(self):
        """测试创建代理对象便捷函数"""
        manager = create_proxy_manager()
        test_service = TestService("ConvenienceTest")
        manager.register_object('test_service', test_service)
        manager.start()

        try:
            proxy = create_proxy(manager, 'test_service')
            self.assertIsInstance(proxy, ProcessProxy)

            # 测试代理功能
            result = proxy.increment()
            self.assertEqual(result, 1)

        finally:
            manager.shutdown()


class TestIntegration(unittest.TestCase):
    """集成测试 - 测试完整的跨进程通信场景"""

    def setUp(self):
        """测试前的设置"""
        # 设置日志级别
        logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
        if logger:
            logger.set_instance_level(LogLevel.DEBUG)

    def test_single_process_integration(self):
        """测试单进程集成场景"""
        # 创建配置
        config = ProxyConfiguration()
        config.debug_mode = True
        config.optimize_for_scenario('debug')

        # 创建服务
        service = TestService("IntegrationTest")

        # 创建管理器
        manager = ProcessProxyManager(config=config)
        manager.register_object('service', service)
        manager.start()

        try:
            # 创建代理
            proxy = manager.create_proxy('service')

            # 测试各种操作
            # 1. 基本方法调用
            result = proxy.increment()
            self.assertEqual(result, 1)

            result = proxy.increment()
            self.assertEqual(result, 2)

            # 2. 获取数据
            count = proxy.get_count()
            self.assertEqual(count, 2)

            data = proxy.get_data()
            self.assertEqual(data['count'], 2)

            # 3. 设置状态
            proxy.set_status('running')
            status = proxy.get_status()
            self.assertEqual(status, 'running')

            # 4. 测试慢操作
            result = proxy.slow_operation(0.01)
            self.assertIn('操作完成', result)

            # 5. 验证调用历史
            expected_calls = ['increment', 'increment', 'get_count', 'get_data', 'set_status(running)', 'get_status', 'slow_operation(0.01)']
            for call in expected_calls:
                self.assertIn(call, service.call_history)

            # 6. 测试缓存功能
            proxy._refresh_cache()
            proxy._sync_with_main_process()

            # 7. 获取统计信息
            stats = manager.get_statistics()
            self.assertIn('performance', stats)
            self.assertIn('state_manager', stats)
            self.assertIn('command_processor', stats)

        finally:
            manager.shutdown()

    def test_error_handling_integration(self):
        """测试错误处理集成"""
        config = ProxyConfiguration()
        config.debug_mode = True

        service = TestService("ErrorTest")
        manager = ProcessProxyManager(config=config)
        manager.register_object('service', service)
        manager.start()

        try:
            proxy = manager.create_proxy('service')

            # 测试异常处理
            with self.assertRaises(Exception):
                proxy.raise_error()

            # 验证异常被记录
            self.assertIn('raise_error', service.call_history)

            # 测试错误后的恢复
            result = proxy.increment()
            self.assertEqual(result, 1)

        finally:
            manager.shutdown()

    def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        config = ProxyConfiguration()
        config.performance_monitoring = True
        config.debug_mode = True

        service = TestService("PerformanceTest")
        manager = ProcessProxyManager(config=config)
        manager.register_object('service', service)
        manager.start()

        try:
            proxy = manager.create_proxy('service')

            # 执行一些操作
            for _ in range(10):
                proxy.increment()

            for _ in range(5):
                proxy.get_count()

            # 执行慢操作
            proxy.slow_operation(0.01)

            # 获取性能统计
            stats = manager.get_statistics()
            self.assertIn('performance', stats)

            performance_stats = stats['performance']
            self.assertIn('summary', performance_stats)
            self.assertIn('method_details', performance_stats)

            # 验证方法调用被记录
            method_details = performance_stats['method_details']
            self.assertIn('service.increment', method_details)
            self.assertIn('service.get_count', method_details)

            # 验证调用次数
            increment_stats = method_details['service.increment']
            self.assertEqual(increment_stats['total_calls'], 10)

        finally:
            manager.shutdown()


class TestRealMultiprocessing(unittest.TestCase):
    """真正的多进程测试 - 测试子进程调用主进程的方法"""

    def setUp(self):
        """测试前的设置"""
        # 设置日志级别
        logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
        if logger:
            logger.set_instance_level(LogLevel.INFO)

    def test_child_process_calls_main_process(self):
        """测试子进程调用主进程的方法"""

        def child_process_worker(manager_address, authkey, result_queue):
            """子进程工作函数"""
            try:
                # 在子进程中创建客户端代理管理器
                client_manager = ProcessProxyManager(
                    address=manager_address,
                    authkey=authkey
                )

                # 连接到主进程
                client_manager.connect()

                # 创建服务代理
                service_proxy = client_manager.create_proxy('shared_service')

                # 在子进程中调用主进程的方法
                results = []

                # 测试基本方法调用
                result = service_proxy.increment()
                results.append(('increment_1', result))

                result = service_proxy.increment()
                results.append(('increment_2', result))

                # 测试获取状态
                count = service_proxy.get_count()
                results.append(('get_count', count))

                # 测试设置状态
                service_proxy.set_status('child_process_active')
                status = service_proxy.get_status()
                results.append(('get_status', status))

                # 测试获取数据
                data = service_proxy.get_data()
                results.append(('get_data', data))

                # 将结果放入队列
                result_queue.put(('success', results))

            except Exception as e:
                import traceback
                result_queue.put(('error', str(e), traceback.format_exc()))

        # 创建共享服务
        shared_service = TestService("SharedService")

        # 创建代理管理器
        config = ProxyConfiguration()
        config.debug_mode = True

        # 使用网络地址以支持跨进程通信
        manager_address = ('127.0.0.1', 0)  # 0表示自动分配端口
        authkey = b'test_multiprocess_key'

        proxy_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey,
            config=config
        )

        # 注册共享对象
        proxy_manager.register_object('shared_service', shared_service)

        # 启动管理器
        proxy_manager.start()

        try:
            # 获取实际的服务器地址
            actual_address = proxy_manager.address

            # 创建结果队列
            result_queue = multiprocessing.Queue()

            # 创建子进程
            child_process = multiprocessing.Process(
                target=child_process_worker,
                args=(actual_address, authkey, result_queue),
                name="TestChildProcess"
            )

            # 启动子进程
            child_process.start()

            # 等待子进程完成
            child_process.join(timeout=15)  # 15秒超时

            # 检查子进程是否正常结束
            self.assertEqual(child_process.exitcode, 0, "子进程异常退出")

            # 获取子进程结果
            self.assertFalse(result_queue.empty(), "子进程应该返回结果")
            result_data = result_queue.get()

            # 检查是否有错误
            if result_data[0] == 'error':
                self.fail(f"子进程执行失败: {result_data[1]}\n{result_data[2] if len(result_data) > 2 else ''}")

            # 验证子进程的调用结果
            self.assertEqual(result_data[0], 'success')
            child_results = result_data[1]

            # 验证主进程中的对象状态被子进程修改了
            self.assertEqual(shared_service.get_count(), 2, "子进程应该调用了2次increment")
            self.assertEqual(shared_service.get_status(), 'child_process_active', "子进程应该设置了状态")

            # 验证调用历史
            expected_calls = ['increment', 'increment', 'get_count', 'set_status(child_process_active)', 'get_status', 'get_data']
            for call in expected_calls:
                self.assertIn(call, shared_service.call_history, f"应该包含调用: {call}")

            # 验证子进程返回的结果
            expected_results = [
                ('increment_1', 1),
                ('increment_2', 2),
                ('get_count', 2),
                ('get_status', 'child_process_active')
            ]

            for expected in expected_results:
                found = False
                for actual in child_results:
                    if actual[0] == expected[0] and actual[1] == expected[1]:
                        found = True
                        break
                self.assertTrue(found, f"子进程结果中应该包含: {expected}")

        finally:
            # 清理资源
            if child_process.is_alive():
                child_process.terminate()
                child_process.join(timeout=2)

            proxy_manager.shutdown()

    def test_multiple_child_processes(self):
        """测试多个子进程同时调用主进程的方法"""

        def child_worker(worker_id, manager_address, authkey, results_queue):
            """子进程工作函数"""
            try:
                from multiprocessing.managers import BaseManager

                class ClientManager(BaseManager):
                    pass

                ClientManager.register('get_proxy_manager')

                client_manager = ClientManager(address=manager_address, authkey=authkey)
                client_manager.connect()

                proxy_manager = client_manager.get_proxy_manager()
                service_proxy = proxy_manager.create_proxy('counter_service')

                # 每个子进程执行5次increment操作
                for i in range(5):
                    result = service_proxy.increment()
                    results_queue.put(f"worker_{worker_id}_increment_{i+1}: {result}")
                    time.sleep(0.01)  # 短暂延迟

                # 获取最终计数
                final_count = service_proxy.get_count()
                results_queue.put(f"worker_{worker_id}_final_count: {final_count}")

            except Exception as e:
                results_queue.put(f"worker_{worker_id}_error: {str(e)}")

        # 创建计数器服务
        counter_service = TestService("CounterService")

        # 创建代理管理器
        config = ProxyConfiguration()
        config.debug_mode = True

        manager_address = ('127.0.0.1', 0)
        authkey = b'test_multi_child_key'

        proxy_manager = ProcessProxyManager(
            address=manager_address,
            authkey=authkey,
            config=config
        )

        proxy_manager.register_object('counter_service', counter_service)
        proxy_manager.start()

        try:
            actual_address = proxy_manager.address

            # 创建结果队列
            results_queue = multiprocessing.Queue()

            # 创建3个子进程
            num_workers = 3
            processes = []

            for worker_id in range(num_workers):
                process = multiprocessing.Process(
                    target=child_worker,
                    args=(worker_id, actual_address, authkey, results_queue),
                    name=f"Worker_{worker_id}"
                )
                processes.append(process)
                process.start()

            # 等待所有子进程完成
            for process in processes:
                process.join(timeout=15)
                self.assertEqual(process.exitcode, 0, f"子进程 {process.name} 异常退出")

            # 收集结果
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())

            # 验证结果
            self.assertEqual(len(results), num_workers * 6)  # 每个worker 5次increment + 1次final_count

            # 验证最终计数（3个worker * 5次increment = 15）
            final_count = counter_service.get_count()
            self.assertEqual(final_count, 15, "最终计数应该是15")

            # 验证调用历史包含所有increment调用
            increment_calls = [call for call in counter_service.call_history if call == 'increment']
            self.assertEqual(len(increment_calls), 15, "应该有15次increment调用")

        finally:
            # 清理资源
            for process in processes:
                if process.is_alive():
                    process.terminate()
                    process.join(timeout=2)

            proxy_manager.shutdown()


if __name__ == '__main__':
    # 设置日志级别
    logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
    if logger:
        logger.set_instance_level(LogLevel.INFO)

    # 运行测试
    unittest.main(verbosity=2)
