#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件驱动模块：用于实现基于事件的解耦通信机制

这个模块提供了一个强大的事件驱动系统，允许组件之间通过事件机制进行通信，
而不需要直接依赖。该系统支持事件注册、触发、取消订阅，以及优先级、
命名空间和异步事件处理等高级功能。

主要特性:
- 支持同步和异步事件处理
- 基于优先级的事件处理顺序
- 通配符事件订阅
- 命名空间/层次结构事件支持
- 完善的错误处理和日志记录
- 线程安全操作
- 内存优化（弱引用）
- 性能优化（正则缓存、协程池）
- 批量事件处理

=============================================================================
详细参数说明
=============================================================================

1. 事件优先级 (EventPriority)
----------------------------
事件处理器的执行优先级，数值越低优先级越高：

- HIGHEST = 0  : 最高优先级，最先执行
- HIGH = 25    : 高优先级
- NORMAL = 50  : 普通优先级（默认）
- LOW = 75     : 低优先级
- LOWEST = 100 : 最低优先级

使用场景：
- 关键事件处理需要最先执行时使用 HIGHEST
- 初始化相关处理使用 HIGH
- 普通业务逻辑使用 NORMAL
- 日志记录等使用 LOW
- 清理工作使用 LOWEST

2. 节流配置 (ThrottleConfig)
---------------------------
控制事件处理频率的配置参数：

enabled: bool = False
    是否启用节流功能
    - True: 启用节流控制
    - False: 不启用节流（默认）

interval: float = 1.0
    节流时间间隔（秒）
    - 建议值: 0.1 ~ 5.0
    - 默认值: 1.0
    注意: 间隔太小可能影响性能，太大可能影响用户体验

leading: bool = True
    是否在开始时立即执行
    - True: 第一次调用立即执行（默认）
    - False: 第一次调用也需等待
    使用场景: 需要立即响应用户操作时设为 True

trailing: bool = True
    是否在结束时执行最后一次调用
    - True: 确保最后一次调用被执行（默认）
    - False: 可能丢弃最后一次调用
    使用场景: 需要确保最终状态更新时设为 True

3. 防抖配置 (DebounceConfig)
---------------------------
控制事件触发时机的配置参数：

enabled: bool = False
    是否启用防抖功能
    - True: 启用防抖控制
    - False: 不启用防抖（默认）

wait: float = 1.0
    防抖等待时间（秒）
    - 建议值: 0.1 ~ 2.0
    - 默认值: 1.0
    注意: 等待时间过长可能影响用户体验

leading: bool = False
    是否在开始时立即执行
    - True: 第一次调用立即执行
    - False: 第一次调用需等待（默认）
    使用场景: 需要立即反馈时设为 True

trailing: bool = True
    是否在结束时执行
    - True: 等待结束后执行（默认）
    - False: 不执行最后一次调用
    使用场景: 大多数情况下应设为 True

max_wait: Optional[float] = None
    最大等待时间（秒）
    - None: 无限等待（默认）
    - float: 最大等待秒数
    使用场景: 防止等待时间过长时设置

4. 事件发射器配置 (EventEmitterConfig)
-----------------------------------
全局配置参数：

max_listeners: int = 10
    每个事件的最大监听器数量
    - 建议值: 5 ~ 20
    - 默认值: 10
    注意: 数量过多可能导致性能问题

async_pool_size: int = 10
    异步事件处理的协程池大小
    - 建议值: 5 ~ 20
    - 默认值: 10
    使用场景: 有大量异步事件处理时调整

pattern_cache_size: int = 100
    正则表达式缓存大小
    - 建议值: 50 ~ 200
    - 默认值: 100
    注意: 缓存过大会占用更多内存

enable_performance_monitoring: bool = False
    是否启用性能监控
    - True: 收集性能统计数据
    - False: 不收集（默认）
    使用场景: 需要监控性能时启用

batch_event_max_size: int = 1000
    批量事件最大数量
    - 建议值: 100 ~ 5000
    - 默认值: 1000
    注意: 数量过大可能导致内存问题

5. 方法参数说明
--------------

on(event: str, callback: Callable, ...) -> Callable:
    event: str
        事件名称，支持以下格式：
        - 普通事件名: "click", "user.login"
        - 通配符: "user.*", "*.create"
        - 正则模式: "user.+", "data[0-9]+"
    
    callback: Callable
        事件处理函数，支持：
        - 同步函数
        - 异步函数 (async def)
        - 实例方法
        - 类方法
        - lambda 表达式
    
    priority: EventPriority = EventPriority.NORMAL
        处理器优先级，默认为 NORMAL
    
    once: bool = False
        是否为一次性处理器
        - True: 执行一次后自动移除
        - False: 持续有效（默认）
    
    context: Any = None
        传递给处理器的上下文数据
        - None: 不传递上下文（默认）
        - Any: 任意类型的上下文数据
    
    throttle: Optional[ThrottleConfig] = None
        节流配置
        - None: 使用默认配置（默认）
        - ThrottleConfig: 自定义配置
    
    debounce: Optional[DebounceConfig] = None
        防抖配置
        - None: 使用默认配置（默认）
        - DebounceConfig: 自定义配置

emit(event: str, *args, **kwargs) -> bool:
    event: str
        要触发的事件名称
    
    *args: Any
        传递给处理器的位置参数
    
    **kwargs: Any
        传递给处理器的关键字参数
    
    返回值: bool
        - True: 有处理器被调用
        - False: 没有处理器被调用

emit_async(event: str, *args, **kwargs) -> asyncio.Future:
    参数同 emit()
    返回值: Future
        包含处理结果的 Future 对象

wait_for(event: str, timeout: float = None) -> asyncio.Future:
    event: str
        要等待的事件名称
    
    timeout: Optional[float]
        超时时间（秒）
        - None: 永久等待（默认）
        - float: 最大等待秒数
    
    返回值: Future
        包含事件参数的 Future 对象
        - 成功: 事件参数
        - 超时: 抛出 TimeoutError

6. 使用注意事项
--------------

1. 节流和防抖不能同时启用：
   ```python
   # 错误示例
   emitter.on("event", handler,
              throttle=ThrottleConfig(enabled=True),
              debounce=DebounceConfig(enabled=True))  # 将抛出异常
   ```

2. 异步处理器必须使用 async def 定义：
   ```python
   # 正确示例
   async def async_handler(data):
       await some_async_operation()
   
   # 错误示例
   def sync_handler(data):
       await some_async_operation()  # 这将导致错误
   ```

3. 弱引用不适用于 lambda 表达式：
   ```python
   # 错误示例
   emitter.on("event", lambda x: print(x), weak_ref=True)  # 不要这样做
   
   # 正确示例
   def handler(x):
       print(x)
   emitter.on("event", handler, weak_ref=True)
   ```

4. 性能监控会带来少量开销：
   ```python
   # 仅在需要时启用
   emitter.configure(enable_performance_monitoring=True)
   ```

5. 批量事件处理要注意数量限制：
   ```python
   # 正确示例
   events = [("event", [data], {}) for data in data_list[:1000]]
   emitter.emit_batch(events)
   
   # 错误示例
   events = [("event", [data], {}) for data in large_data_list]  # 可能超出限制
   ```

用法示例见类文档和模块末尾的示例代码。
"""

import inspect
import logging
import re
import sys
import threading
import time
import traceback
import asyncio
import functools
import weakref
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union, Pattern
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache

# 添加全局计数器
_performance_stats = {
    'total_time': 0,
    'call_count': 0,
    'emit_count': 0,
    'handler_count': 0,
    'pattern_cache_hits': 0,
    'pattern_cache_misses': 0
}

def performance_monitor(func):
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if not args[0].config.enable_performance_monitoring:
            return func(*args, **kwargs)
        
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # 更新全局统计数据
        with args[0]._stats_lock:
            _performance_stats['total_time'] += (end_time - start_time)
            _performance_stats['call_count'] += 1
            # 增加发射计数
            if func.__name__ == 'emit' or func.__name__ == 'emit_async' or func.__name__ == 'emit_batch':
                args[0]._stats['emit_count'] += 1
        
        return result
    
    return wrapper

class EventPriority(Enum):
    """事件处理优先级枚举，数值越低优先级越高"""
    HIGHEST = 0
    HIGH = 25
    NORMAL = 50
    LOW = 75
    LOWEST = 100

# 添加节流和防抖配置类
@dataclass
class ThrottleConfig:
    """节流配置"""
    enabled: bool = False  # 是否启用节流
    interval: float = 1.0  # 节流时间间隔（秒）
    leading: bool = True   # 是否在开始时执行
    trailing: bool = True  # 是否在结束时执行
    
@dataclass
class DebounceConfig:
    """防抖配置"""
    enabled: bool = False  # 是否启用防抖
    wait: float = 1.0     # 防抖等待时间（秒）
    leading: bool = False  # 是否在开始时执行
    trailing: bool = True  # 是否在结束时执行
    max_wait: Optional[float] = None  # 最大等待时间，None表示无限制

@dataclass
class RateControlState:
    """速率控制状态"""
    last_exec_time: float = 0.0  # 上次执行时间
    timer: Optional[threading.Timer] = None  # 定时器引用
    pending_args: Tuple = ()  # 待执行的位置参数
    pending_kwargs: Dict = field(default_factory=dict)  # 待执行的关键字参数

@dataclass
class EventHandler:
    """事件处理器数据类，存储事件订阅相关信息"""
    callback: Callable  # 回调函数或方法
    priority: EventPriority  # 优先级
    once: bool = False  # 是否只执行一次
    is_async: bool = False  # 是否是异步回调
    filter_pattern: Optional[Pattern] = None  # 事件名过滤正则模式
    context: Any = None  # 任意用户上下文数据
    weak_ref: bool = False  # 是否使用弱引用
    throttle: ThrottleConfig = field(default_factory=ThrottleConfig)  # 节流配置
    debounce: DebounceConfig = field(default_factory=DebounceConfig)  # 防抖配置
    _rate_state: RateControlState = field(default_factory=RateControlState)  # 速率控制状态

    def __post_init__(self):
        """初始化后处理"""
        if self.weak_ref and not isinstance(self.callback, (staticmethod, classmethod)):
            self.original_callback = self.callback
            self.callback = weakref.proxy(self.callback)
        
        # 确保节流和防抖不能同时启用
        if self.throttle.enabled and self.debounce.enabled:
            raise ValueError("节流和防抖不能同时启用")

class EventEmitterConfig:
    """事件发射器配置类"""
    def __init__(self):
        self.max_listeners = 10  # 每个事件的最大监听器数量
        self.async_pool_size = 10  # 异步事件处理的协程池大小
        self.pattern_cache_size = 100  # 正则表达式缓存大小
        self.enable_performance_monitoring = False  # 是否启用性能监控
        self.batch_event_max_size = 1000  # 批量事件最大数量
        # 默认节流配置
        self.default_throttle = ThrottleConfig(
            enabled=False,
            interval=1.0,
            leading=True,
            trailing=True
        )
        # 默认防抖配置
        self.default_debounce = DebounceConfig(
            enabled=False,
            wait=1.0,
            leading=False,
            trailing=True,
            max_wait=None
        )

class EventEmitter:
    """
    强大的事件发射器类，提供完整的事件驱动功能
    
    主要功能:
    - 注册/订阅事件
    - 触发/发射事件
    - 移除事件订阅
    - 支持优先级
    - 支持一次性事件处理器
    - 支持通配符事件订阅
    - 支持异步事件处理
    - 提供错误处理和日志记录
    - 支持单例模式，可通过静态方法获取全局唯一实例
    
    使用示例:
        # 创建事件发射器（所有方式都会获得相同的单例实例）
        emitter1 = EventEmitter()
        emitter2 = EventEmitter.get_instance()
        assert emitter1 is emitter2  # 总是为True
        
        # 定义事件处理函数
        def on_data_received(data):
            print(f"接收到数据: {data}")
        
        # 注册事件处理器
        emitter1.on("data", on_data_received)
        
        # 通过任意实例都能触发事件
        emitter2.emit("data", "Hello, World!")
        
        # 更多高级示例见模块末尾
    """
    
    # 单例实例存储
    _instance = None
    # 用于保护单例创建的锁
    _instance_lock = threading.RLock()
    
    def __new__(cls, *args, **kwargs):
        """
        重写 __new__ 方法，确保所有实例化操作都返回单例
        
        无论是直接实例化还是通过 get_instance() 方法，都会得到相同的实例。
        """
        with cls._instance_lock:
            if cls._instance is None:
                cls._instance = super(EventEmitter, cls).__new__(cls)
                # 标记为尚未初始化
                cls._instance._initialized = False
        return cls._instance
    
    @classmethod
    def get_instance(cls):
        """
        获取 EventEmitter 的全局单例实例
        
        返回与直接实例化相同的单例对象。
        
        Returns:
            EventEmitter: 全局单例实例
            
        示例:
            # 获取全局事件发射器实例
            emitter = EventEmitter.get_instance()
            emitter.on("global.event", handle_global_event)
            
            # 验证与直接实例化是同一个对象
            emitter2 = EventEmitter()
            assert emitter is emitter2  # 总是为True
        """
        # 由于重写了 __new__，这里直接实例化就会返回单例
        return cls()
    
    @classmethod
    def create_instance(cls, reset=False):
        """
        创建或重置 EventEmitter 的全局单例实例
        
        Args:
            reset: 是否强制重置现有实例，默认为False
            
        Returns:
            EventEmitter: 全局单例实例
            
        示例:
            # 重置全局事件发射器实例
            emitter = EventEmitter.create_instance(reset=True) 
        """
        with cls._instance_lock:
            if reset and cls._instance is not None:
                print("重置实例")
                try:
                    cls._instance.clear()
                    # 重新初始化实例
                    cls._instance._initialized = False
                    logging.getLogger(cls.__name__).debug("已重置EventEmitter单例实例")
                except Exception as e:
                    logging.getLogger(cls.__name__).warning(f"重置实例时出错: {e}")
                    logging.getLogger(cls.__name__).warning(traceback.format_exc())
            
            # 通过 __new__ 获取单例，然后确保初始化
            instance = cls()
            if not instance._initialized:
                # __init__ 会被自动调用，但如果已经初始化过就不会再调用
                # 此标记由 __init__ 方法设置
                pass
            
            return instance
    
    def __init__(self):
        """初始化事件发射器，仅在第一次创建实例时执行"""
        # 检查是否已经初始化过，避免重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
        
        # 使用线程锁确保线程安全
        self._lock = threading.RLock()
        # 添加统计数据锁
        self._stats_lock = threading.RLock()
        # 事件处理器映射，格式: {事件名: [EventHandler实例, ...]}
        self._handlers = defaultdict(list)
        # 通配符事件处理器列表
        self._wildcard_handlers = []
        # 设置日志记录器
        self._logger = logging.getLogger(self.__class__.__name__)
        # 用于异步事件处理的事件循环
        self._loop = None
        # 配置对象
        self.config = EventEmitterConfig()
        # 性能统计
        self._stats = {
            'emit_count': 0,
            'handler_count': 0,
            'pattern_cache_hits': 0,
            'pattern_cache_misses': 0
        }
        # 正则表达式缓存
        self._pattern_cache = {}
        # 协程池
        self._async_pool = None
        # 批量事件队列
        self._batch_queue = []
        # 标记为已初始化
        self._initialized = True
        
        self._logger.debug("已初始化EventEmitter单例实例")

    def _get_loop(self):
        """获取或创建一个事件循环用于异步事件处理"""
        if self._loop is None:
            try:
                self._loop = asyncio.get_event_loop()
            except RuntimeError:
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
        return self._loop

    def on(self, event: str, callback: Callable, priority: EventPriority = EventPriority.NORMAL, 
           once: bool = False, context: Any = None, throttle: Optional[ThrottleConfig] = None,
           debounce: Optional[DebounceConfig] = None) -> Callable:
        """
        注册事件处理器
        
        Args:
            event: 事件名称，支持以下格式：
                  - 普通事件名: "click", "user.login"
                  - 通配符: "user.*", "*.create"
                  - 正则模式: 不再直接支持，请使用通配符
            callback: 回调函数或方法
            priority: 优先级，数值越小优先级越高
            once: 是否仅执行一次
            context: 可选的上下文对象，将作为第一个参数传递给回调函数
            throttle: 节流配置
            debounce: 防抖配置
            
        Returns:
            注册的回调函数（便于后续取消注册）
        
        示例:
            # 注册普通事件
            def on_click(data):
                print(f"点击事件: {data}")
            emitter.on("click", on_click)
            
            # 注册带上下文的事件
            class Handler:
                def process(self, ctx, data):
                    print(f"上下文 {ctx} 处理数据 {data}")
            handler = Handler()
            emitter.on("data", handler.process, context="my_context")
            
            # 注册通配符事件（匹配所有以user.开头的事件）
            emitter.on("user.*", lambda data: print(f"用户事件: {data}"))
            
            # 注册一次性事件
            emitter.on("one-time", lambda: print("只执行一次"), once=True)
            
            # 使用节流控制事件处理频率
            throttle_config = ThrottleConfig(enabled=True, interval=0.5)
            emitter.on("scroll", handle_scroll, throttle=throttle_config)
            
            # 使用防抖延迟处理事件
            debounce_config = DebounceConfig(enabled=True, wait=0.3)
            emitter.on("resize", handle_resize, debounce=debounce_config)
        """
        if not callable(callback):
            raise TypeError("回调必须是可调用对象")
        
        # 配置节流防抖
        throttle_config = throttle or ThrottleConfig()
        debounce_config = debounce or DebounceConfig()
        
        # 确保节流和防抖不会同时启用
        if throttle_config.enabled and debounce_config.enabled:
            raise ValueError("节流(throttle)和防抖(debounce)不能同时启用")
        
        # 检查是否是异步回调
        is_async = asyncio.iscoroutinefunction(callback)
        
        if "*" in event or "+" in event:
            # 这是一个通配符或正则模式
            handler = EventHandler(
                callback=callback,
                priority=priority,
                once=once,
                is_async=is_async,
                filter_pattern=self._compile_pattern(event),
                context=context,
                throttle=throttle_config,
                debounce=debounce_config
            )
            
            with self._lock:
                self._wildcard_handlers.append(handler)
                self._wildcard_handlers.sort(key=lambda h: h.priority.value)
            
            self._logger.debug(f"已注册通配符事件处理器: {event} (优先级: {priority.name}, 一次性: {once})")
        else:
            # 这是一个常规事件
            handler = EventHandler(
                callback=callback,
                priority=priority,
                once=once,
                is_async=is_async,
                filter_pattern=None,
                context=context,
                throttle=throttle_config,
                debounce=debounce_config
            )
            
            with self._lock:
                if event not in self._handlers:
                    self._handlers[event] = []
                
                self._handlers[event].append(handler)
                
                # 按优先级排序
                self._handlers[event].sort(key=lambda h: h.priority.value)
                
                # 检查是否超出最大监听器限制
                if len(self._handlers[event]) > self.config.max_listeners:
                    self._logger.warning(
                        f"事件 '{event}' 超出最大监听器数量限制 (当前 {len(self._handlers[event])}, "
                        f"限制 {self.config.max_listeners})"
                    )
                
            self._logger.debug(f"已注册事件处理器: {event} (优先级: {priority.name}, 一次性: {once})")
        
        return callback
            
    def once(self, event: str, callback: Callable, priority: EventPriority = EventPriority.NORMAL, 
             context: Any = None) -> Callable:
        """
        注册一个只执行一次的事件处理器
        
        Args:
            event: 事件名称，支持正则表达式
            callback: 回调函数或方法
            priority: 事件处理优先级
            context: 传递给回调的上下文数据
            
        Returns:
            返回原回调函数，方便链式调用
            
        示例:
            # 只执行一次的初始化事件
            emitter.once("init", initialize_system)
        """
        return self.on(event, callback, priority, once=True, context=context)
        
    def off(self, event: Optional[str] = None, callback: Optional[Callable] = None) -> bool:
        """
        取消事件处理器注册
        
        Args:
            event: 事件名称，如果为None则移除所有事件的指定回调
            callback: 要移除的回调函数，如果为None则移除事件的所有回调
            
        Returns:
            是否成功移除任何处理器
            
        示例:
            # 移除特定事件的特定处理器
            emitter.off("data", handle_data)
            
            # 移除特定事件的所有处理器
            emitter.off("data")
            
            # 移除所有事件的特定处理器
            emitter.off(callback=handle_data)
            
            # 移除所有事件处理器
            emitter.off()
        """
        with self._lock:
            removed = False
            
            # 如果没有指定事件名，则从所有事件中移除指定回调
            if event is None:
                # 处理常规事件
                for evt in list(self._handlers.keys()):
                    if callback is None:
                        # 移除所有事件的所有处理器
                        self._handlers[evt].clear()
                        removed = True
                    else:
                        # 移除所有事件的特定处理器
                        original_length = len(self._handlers[evt])
                        self._handlers[evt] = [h for h in self._handlers[evt] if h.callback != callback]
                        removed = removed or (original_length > len(self._handlers[evt]))
                
                # 处理通配符事件
                if callback is None:
                    # 移除所有通配符处理器
                    removed = removed or bool(self._wildcard_handlers)
                    self._wildcard_handlers.clear()
                else:
                    # 移除特定通配符处理器
                    original_length = len(self._wildcard_handlers)
                    self._wildcard_handlers = [h for h in self._wildcard_handlers if h.callback != callback]
                    removed = removed or (original_length > len(self._wildcard_handlers))
            
            # 如果指定了事件名
            else:
                if '*' in event or '?' in event or '+' in event:
                    # 通配符模式，需要在通配符处理器中查找匹配的模式
                    pattern_str = event.replace('.', r'\.').replace('*', '.*').replace('?', '.').replace('+', r'\+')
                    pattern = re.compile(f"^{pattern_str}$")
                    
                    if callback is None:
                        # 移除所有匹配的通配符处理器
                        original_length = len(self._wildcard_handlers)
                        self._wildcard_handlers = [h for h in self._wildcard_handlers if not pattern.match(str(h.filter_pattern.pattern))]
                        removed = original_length > len(self._wildcard_handlers)
                    else:
                        # 移除特定回调的匹配通配符处理器
                        original_length = len(self._wildcard_handlers)
                        self._wildcard_handlers = [h for h in self._wildcard_handlers 
                                                if not (h.callback == callback and pattern.match(str(h.filter_pattern.pattern)))]
                        removed = original_length > len(self._wildcard_handlers)
                else:
                    # 精确事件名
                    if event in self._handlers:
                        if callback is None:
                            # 移除特定事件的所有处理器
                            removed = bool(self._handlers[event])
                            self._handlers[event].clear()
                        else:
                            # 移除特定事件的特定处理器
                            original_length = len(self._handlers[event])
                            self._handlers[event] = [h for h in self._handlers[event] if h.callback != callback]
                            removed = original_length > len(self._handlers[event])
            
            if removed:
                self._logger.debug(f"已移除事件处理器: {event if event else '所有事件'}")
            
            return removed
    
    @performance_monitor
    def emit(self, event: str, *args, **kwargs) -> list:
        """
        触发事件，调用所有注册的事件处理器
        
        本方法会依次执行以下流程：
        1. 查找精确匹配事件名的处理器
        2. 查找通过通配符模式匹配事件名的处理器
        3. 合并并按优先级排序所有匹配的处理器
        4. 应用节流和防抖控制
        5. 分别执行同步和异步处理器
        6. 移除一次性处理器
        
        通配符匹配说明:
        - 精确匹配: 如 "user.login" 只匹配完全相同的事件名
        - 通配符匹配: 如 "user.*" 会匹配 "user.login", "user.logout" 等
        - 通配符位置: "*" 可以在任何位置，如 "*.created", "user.*", "*.*"
        - 多通配符: 可以使用多个 "*"，如 "*.user.*" 匹配 "app.user.login"
        - 全匹配模式: 单独的 "*" 匹配任何事件名
        
        Args:
            event: 事件名称，如 "user.login", "document.change"
            *args, **kwargs: 传递给事件处理器的参数
            
        Returns:
            处理器的返回值列表。如果没有处理器被调用则返回空列表。
            对于异步处理器，返回值可能是协程对象或Task对象。
            
        示例:
            # 基本用法
            results = emitter.emit("data", {"value": 42})
            # 会触发 "data" 和 "data.*" 和 "*" 等模式的处理器并返回其结果
            
            # 带多个参数
            results = emitter.emit("user.login", user_id, timestamp, success=True)
            # 会触发 "user.login" 和 "user.*" 和 "*.login" 和 "*" 等模式的处理器并返回其结果
        """
        handlers_called = False
        # 用于收集处理器的返回值
        results = []
        
        with self._lock:
            handlers_to_remove = []
            
            # 第一步: 查找精确匹配的事件处理器
            # 这些是通过 emitter.on("exact.event", handler) 方式注册的处理器
            # 它们不包含通配符，精确匹配事件名
            event_handlers = self._handlers.get(event, [])
            
            # 第二步: 查找通配符事件处理器
            # 通配符处理器是通过包含 "*" 的事件名注册的，如 "user.*", "*.create", "*"
            # 这些处理器存储在 _wildcard_handlers 列表中，需要通过正则匹配检查
            wildcard_matches = []
            for handler in self._wildcard_handlers:
                # 通配符处理器有一个已编译的正则表达式模式 filter_pattern
                # 这个模式在 on() 方法中通过 _compile_pattern() 创建
                # 例如 "user.*" 被编译为 "^user\..*$" 的正则表达式
                if handler.filter_pattern and handler.filter_pattern.match(event):
                    # 如果事件名匹配通配符模式，将该处理器添加到匹配列表
                    # 例如: "user.login" 会匹配 "user.*" 的模式
                    wildcard_matches.append(handler)
            
            # 第三步: 合并所有匹配的处理器并按优先级排序
            # 优先级从高到低排序 (HIGHEST -> HIGH -> NORMAL -> LOW -> LOWEST)
            all_handlers = event_handlers + wildcard_matches
            all_handlers.sort(key=lambda h: h.priority.value)
            
            # 如果没有找到任何匹配的处理器，则返回空列表
            if not all_handlers:
                self._logger.debug(f"事件 '{event}' 没有处理器")
                return results
                
            self._logger.debug(f"触发事件 '{event}' 有 {len(all_handlers)} 个处理器")
            
            # 第四步: 处理节流和防抖，并分类同步与异步处理器
            sync_handlers = []
            async_handlers = []
            
            for handler in all_handlers:
                # 处理节流和防抖逻辑
                # _handle_rate_control 会根据处理器的节流/防抖配置决定是否执行
                # 如果返回 False，则跳过此处理器
                if not self._handle_rate_control(handler, args, kwargs):
                    continue
                    
                # 区分同步和异步处理器，分别处理
                if handler.is_async:
                    async_handlers.append(handler)
                else:
                    sync_handlers.append(handler)
                    
                # 标记一次性处理器，供稍后移除
                if handler.once:
                    handlers_to_remove.append(handler)
            
            # 第五步: 执行同步处理器
            for handler in sync_handlers:
                result = self._execute_handler(handler, args, kwargs)
                # 收集返回值
                results.append(result)
                # 仅当结果不是异常时才增加调用计数
                if not isinstance(result, Exception):
                    handlers_called = True
                    # 记录统计信息
                    with self._stats_lock:
                        self._stats['handler_count'] += 1
            
            # 第六步: 执行异步处理器
            if async_handlers:
                loop = self._get_loop()
                
                async def run_async_handlers():
                    async_results = []
                    for handler in async_handlers:
                        coro_or_task = self._execute_handler(handler, args, kwargs)
                        
                        # 如果已经是异常对象(处理器执行失败)，直接添加到结果
                        if isinstance(coro_or_task, Exception):
                            async_results.append(coro_or_task)
                            continue
                            
                        if coro_or_task:  # 确保返回了有效的协程或任务
                            try:
                                if isinstance(coro_or_task, asyncio.Task):
                                    await coro_or_task  # 如果是任务，等待它完成
                                    async_results.append(coro_or_task.result() if coro_or_task.done() else coro_or_task)
                                else:
                                    # 是协程但不是任务，收集它
                                    async_results.append(coro_or_task)
                                nonlocal handlers_called
                                handlers_called = True
                                # 记录统计信息
                                with self._stats_lock:
                                    self._stats['handler_count'] += 1
                            except Exception as e:
                                self._logger.error(f"异步事件处理器 '{event}' 异常: {str(e)}")
                                self._logger.error(traceback.format_exc())
                                # 将异常也添加到结果中
                                async_results.append(e)
                        else:
                            self._logger.debug("返回的不是任务，可能已经被处理")
                            async_results.append(None)
                    return async_results
                
                # 在事件循环中运行异步处理器
                if loop.is_running():
                    task = asyncio.create_task(run_async_handlers())
                    # 将异步任务添加到结果列表
                    results.append(task)
                else:
                    # 如果循环没有运行，则等待并获取结果
                    async_results = loop.run_until_complete(run_async_handlers())
                    results.extend(async_results)
            
            # 第七步: 移除一次性处理器
            # 这些处理器被标记为 once=True，执行一次后应该被移除
            for handler in handlers_to_remove:
                if handler in event_handlers:
                    event_handlers.remove(handler)
                if handler in self._wildcard_handlers:
                    self._wildcard_handlers.remove(handler)
            
            return results
    
    def emit_async(self, event: str, *args, **kwargs) -> asyncio.Future:
        """
        异步触发事件，返回一个Future
        
        Args:
            event: 事件名称
            *args, **kwargs: 传递给事件处理器的参数
            
        Returns:
            包含处理结果的Future对象
            
        示例:
            # 异步触发事件
            await emitter.emit_async("data.process", large_dataset)
        """
        loop = self._get_loop()
        
        async def _emit_async():
            return self.emit(event, *args, **kwargs)
        
        return asyncio.ensure_future(_emit_async(), loop=loop)
    
    def wait_for(self, event: str, timeout: float = None) -> asyncio.Future:
        """
        等待事件被触发，返回一个Future
        
        Args:
            event: 要等待的事件名称
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            包含事件参数的Future对象
            
        示例:
            # 等待事件触发
            try:
                result = await emitter.wait_for("connection.ready", timeout=5.0)
                print("连接就绪:", result)
            except asyncio.TimeoutError:
                print("等待连接超时")
        """
        loop = self._get_loop()
        future = loop.create_future()
        
        def callback(*args, **kwargs):
            if not future.done():
                if args and kwargs:
                    future.set_result((args, kwargs))
                elif args:
                    future.set_result(args[0] if len(args) == 1 else args)
                elif kwargs:
                    future.set_result(kwargs)
                else:
                    future.set_result(None)
        
        # 注册一次性事件处理器
        self.once(event, callback, priority=EventPriority.HIGHEST)
        
        # 设置超时
        if timeout is not None:
            def on_timeout():
                if not future.done():
                    future.set_exception(asyncio.TimeoutError(f"等待事件 '{event}' 超时"))
                    self.off(event, callback)
            
            loop.call_later(timeout, on_timeout)
        
        return future
    
    def emit_after(self, delay: float, event: str, *args, **kwargs) -> None:
        """
        延迟触发事件
        
        Args:
            delay: 延迟时间（秒）
            event: 事件名称
            *args, **kwargs: 传递给事件处理器的参数
            
        示例:
            # 5秒后触发超时事件
            emitter.emit_after(5.0, "connection.timeout", connection_id)
        """
        def delayed_emit():
            self.emit(event, *args, **kwargs)
        
        threading.Timer(delay, delayed_emit).start()
    
    def event_names(self) -> List[str]:
        """
        获取所有已注册事件的名称列表
        
        Returns:
            事件名称列表
            
        示例:
            # 获取所有事件名称
            events = emitter.event_names()
            print(f"已注册的事件: {events}")
        """
        with self._lock:
            return list(self._handlers.keys())
    
    def listeners_count(self, event: Optional[str] = None, use_pattern: bool = False) -> int:
        """
        获取指定事件的监听器数量，如果不指定事件则返回所有监听器数量
        
        Args:
            event: 事件名称或模式，None表示所有事件
            use_pattern: 是否将事件名作为模式匹配使用
            
        Returns:
            监听器数量
            
        示例:
            # 获取某个事件的监听器数量
            count = emitter.listeners_count("data")
            
            # 获取匹配 "user.*" 模式的所有监听器数量
            user_events_count = emitter.listeners_count("user.*", use_pattern=True)
            
            # 获取所有监听器数量
            total = emitter.listeners_count()
        """
        # 如果没有指定事件，返回所有监听器数量
        if event is None:
            with self._lock:
                # 统计常规事件处理器
                regular_count = sum(len(handlers) for handlers in self._handlers.values())
                # 加上通配符处理器
                return regular_count + len(self._wildcard_handlers)
        
        # 不使用模式匹配，直接查询特定事件
        if not use_pattern:
            with self._lock:
                # 常规事件处理器数量
                direct_count = len(self._handlers.get(event, []))
                
                # 统计匹配该事件的通配符处理器
                wildcard_count = 0
                for handler in self._wildcard_handlers:
                    if handler.filter_pattern and handler.filter_pattern.match(event):
                        wildcard_count += 1
                
                return direct_count + wildcard_count
    
        # 使用模式匹配
        else:
            pattern = self._compile_pattern(event)
            total_count = 0
            
            with self._lock:
                # 统计匹配模式的常规事件处理器
                for registered_event, handlers in self._handlers.items():
                    if pattern.match(registered_event):
                        total_count += len(handlers)
                
                # 统计可能重叠的通配符处理器
                for handler in self._wildcard_handlers:
                    if handler.filter_pattern:
                        try:
                            # 简单的启发式方法检查模式重叠
                            test_str = event.replace("*", "test_value").replace(".", "_")
                            if pattern.match(test_str) and handler.filter_pattern.match(test_str):
                                total_count += 1
                        except Exception:
                            # 忽略模式匹配错误
                            pass
                            
            return total_count
    
    def has_listeners(self, event: str, use_pattern: bool = False) -> bool:
        """
        检查事件是否有监听器
        
        Args:
            event: 事件名称或事件模式(当 use_pattern=True 时)
            use_pattern: 是否将事件名作为模式匹配使用
            
        Returns:
            是否有监听器
            
        示例:
            # 检查特定事件是否有监听器
            if emitter.has_listeners("data"):
                print("数据事件有监听器")
                
            # 检查是否有匹配 "user.*" 模式的事件监听器
            if emitter.has_listeners("user.*", use_pattern=True):
                print("存在用户相关事件的监听器")
        """
        if not use_pattern:
            return self.listeners_count(event) > 0
        else:
            # 使用模式匹配
            pattern = self._compile_pattern(event)
            
            # 检查是否有直接匹配的事件
            with self._lock:
                # 检查常规事件
                for registered_event in self._handlers.keys():
                    if pattern.match(registered_event) and self._handlers[registered_event]:
                        return True
                
                # 检查通配符事件处理器中是否有匹配的
                for handler in self._wildcard_handlers:
                    if handler.filter_pattern:
                        # 如果通配符处理器的模式是当前查询模式的子集
                        # 例如: 查询"user.*"，而有处理器注册了"user.login"
                        # 或者查询"*"，而有处理器注册了"user.*"
                        try:
                            # 创建一个测试字符串，看是否能同时匹配两个模式
                            # 这里使用简单的启发式方法，不保证100%准确
                            test_str = event.replace("*", "test_value").replace(".", "_")
                            if pattern.match(test_str) and handler.filter_pattern.match(test_str):
                                return True
                        except Exception:
                            # 忽略模式匹配错误
                            pass
            
            return False
    
    def clear(self) -> None:
        """
        清除所有事件和处理器
        
        示例:
            # 清除所有事件订阅
            emitter.clear()
        """
        with self._lock:
            # 取消所有定时器
            for handlers in self._handlers.values():
                for handler in handlers:
                    if handler._rate_state.timer is not None:
                        handler._rate_state.timer.cancel()
            
            for handler in self._wildcard_handlers:
                if handler._rate_state.timer is not None:
                    handler._rate_state.timer.cancel()
            
            self._handlers.clear()
            self._wildcard_handlers.clear()
            self._logger.debug("已清除所有事件处理器")

    @lru_cache(maxsize=100)
    def _compile_pattern(self, pattern_str: str) -> Pattern:
        """
        将通配符模式编译为正则表达式
        
        Args:
            pattern_str: 通配符模式，支持 * 作为通配符
            
        Returns:
            编译后的正则表达式模式
        """
        # 处理特殊情况
        if pattern_str == "*":
            pattern_str = ".*"  # 匹配任意字符
        else:
            # 将通配符 * 转换为正则表达式 .*
            pattern_str = pattern_str.replace(".", r"\.").replace("*", ".*")
            
        return re.compile(f"^{pattern_str}$")

    def _get_async_pool(self):
        """获取或创建异步协程池"""
        if self._async_pool is None:
            self._async_pool = ThreadPoolExecutor(
                max_workers=self.config.async_pool_size,
                thread_name_prefix="EventEmitter"
            )
        return self._async_pool

    @performance_monitor
    def emit_batch(self, events: List[Tuple[str, list, dict]]) -> List[bool]:
        """
        批量触发多个事件
        
        Args:
            events: 事件列表，每个元素是(事件名, 位置参数列表, 关键字参数字典)的元组
            
        Returns:
            每个事件是否有处理器被调用的布尔值列表
            
        示例:
            events = [
                ("event1", [arg1, arg2], {"kwarg1": value1}),
                ("event2", [], {"kwarg2": value2})
            ]
            results = emitter.emit_batch(events)
        """
        if len(events) > self.config.batch_event_max_size:
            raise ValueError(f"批量事件数量超过限制 ({self.config.batch_event_max_size})")
        
        results = []
        for event, args, kwargs in events:
            results.append(self.emit(event, *args, **kwargs))
        return results

    def get_stats(self) -> dict:
        """
        获取事件发射器的性能统计信息
        
        Returns:
            包含性能统计数据的字典
            
        示例:
            stats = emitter.get_stats()
            print(f"事件触发次数: {stats['emit_count']}")
        """
        with self._stats_lock:
            # 创建一个统计数据的副本
            stats = self._stats.copy()
            
            # 添加全局性能数据
            if self.config.enable_performance_monitoring:
                call_count = _performance_stats['call_count']
                if call_count > 0:
                    stats['emit_avg_time'] = _performance_stats['total_time'] / call_count
                else:
                    stats['emit_avg_time'] = 0
                stats['emit_call_count'] = call_count
            
            # 补充缺失的统计数据，防止KeyError
            if 'pattern_cache_hits' not in stats:
                stats['pattern_cache_hits'] = 0
            if 'pattern_cache_misses' not in stats:
                stats['pattern_cache_misses'] = 0
                
            return stats

    def optimize_memory(self):
        """
        优化内存使用，清理无效的弱引用和缓存
        
        示例:
            # 定期调用以优化内存使用
            emitter.optimize_memory()
        """
        with self._lock:
            # 清理无效的弱引用处理器
            for event in list(self._handlers.keys()):
                self._handlers[event] = [h for h in self._handlers[event] 
                                       if not h.weak_ref or h.callback is not None]
            
            # 清理空的事件
            self._handlers = {k: v for k, v in self._handlers.items() if v}
            
            # 清理正则表达式缓存
            if len(self._pattern_cache) > self.config.pattern_cache_size:
                self._pattern_cache.clear()
            
            # 清理协程池
            if self._async_pool is not None:
                self._async_pool.shutdown(wait=False)
                self._async_pool = None

    def configure(self, **kwargs):
        """
        配置事件发射器
        
        Args:
            **kwargs: 配置参数
                - max_listeners: 每个事件的最大监听器数量
                - async_pool_size: 异步事件处理的协程池大小
                - pattern_cache_size: 正则表达式缓存大小
                - enable_performance_monitoring: 是否启用性能监控
                - batch_event_max_size: 批量事件最大数量
                
        示例:
            emitter.configure(
                max_listeners=20,
                async_pool_size=5,
                enable_performance_monitoring=True
            )
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                raise ValueError(f"未知的配置参数: {key}")
        
        # 更新相关组件
        if 'async_pool_size' in kwargs and self._async_pool is not None:
            self._async_pool.shutdown(wait=True)
            self._async_pool = None
        
        if 'pattern_cache_size' in kwargs:
            self._compile_pattern.cache_clear()

    def _handle_rate_control(self, handler: EventHandler, args: tuple, kwargs: dict) -> bool:
        """
        处理节流和防抖逻辑
        
        Returns:
            bool: 是否应该执行处理器
        """
        current_time = time.time()
        state = handler._rate_state
        
        # 处理节流
        if handler.throttle.enabled:
            elapsed = current_time - state.last_exec_time
            should_exec = False
            
            # 检查是否应该执行
            if elapsed >= handler.throttle.interval:
                should_exec = True
            elif handler.throttle.trailing:
                # 存储参数，用于后续执行
                state.pending_args = args
                state.pending_kwargs = kwargs
                
                # 设置定时器执行尾随调用
                if state.timer is None:
                    def trailing_call():
                        if state.pending_args:
                            try:
                                if handler.is_async:
                                    loop = self._get_loop()
                                    # 使用线程安全的方式调用异步函数
                                    if handler.context is not None:
                                        future = asyncio.run_coroutine_threadsafe(
                                            handler.callback(handler.context, *state.pending_args, **state.pending_kwargs),
                                            loop
                                        )
                                    else:
                                        future = asyncio.run_coroutine_threadsafe(
                                            handler.callback(*state.pending_args, **state.pending_kwargs),
                                            loop
                                        )
                                else:
                                    self._execute_handler(handler, state.pending_args, state.pending_kwargs)
                                # 更新状态
                                state.last_exec_time = time.time()
                            except Exception as e:
                                self._logger.error(f"节流尾随调用执行异常: {str(e)}")
                                self._logger.error(traceback.format_exc())
                            finally:
                                # 清理状态
                                state.pending_args = ()
                                state.pending_kwargs = {}
                        state.timer = None
                    
                    remaining = handler.throttle.interval - elapsed
                    state.timer = threading.Timer(remaining, trailing_call)
                    state.timer.start()
            
            if should_exec:
                state.last_exec_time = current_time
                return True
            return False
        
        # 处理防抖
        elif handler.debounce.enabled:
            # 取消现有定时器
            if state.timer is not None:
                state.timer.cancel()
                state.timer = None
            
            # 检查是否超过最大等待时间
            if handler.debounce.max_wait is not None:
                elapsed = current_time - state.last_exec_time
                if elapsed >= handler.debounce.max_wait and state.pending_args:
                    state.last_exec_time = current_time
                    return True
            
            # 存储最新的参数
            state.pending_args = args
            state.pending_kwargs = kwargs
            
            # 设置新的定时器
            def debounced_call():
                if state.pending_args:
                    try:
                        if handler.is_async:
                            loop = self._get_loop()
                            # 使用线程安全的方式调用异步函数
                            if handler.context is not None:
                                future = asyncio.run_coroutine_threadsafe(
                                    handler.callback(handler.context, *state.pending_args, **state.pending_kwargs),
                                    loop
                                )
                            else:
                                future = asyncio.run_coroutine_threadsafe(
                                    handler.callback(*state.pending_args, **state.pending_kwargs),
                                    loop
                                )
                        else:
                            self._execute_handler(handler, state.pending_args, state.pending_kwargs)
                        # 更新状态
                        state.last_exec_time = time.time()
                    except Exception as e:
                        self._logger.error(f"防抖调用执行异常: {str(e)}")
                        self._logger.error(traceback.format_exc())
                    finally:
                        # 清理状态
                        state.pending_args = ()
                        state.pending_kwargs = {}
                state.timer = None
            
            state.timer = threading.Timer(handler.debounce.wait, debounced_call)
            state.timer.start()
            
            # 如果配置了前置执行且这是第一次调用
            if handler.debounce.leading and state.last_exec_time == 0:
                state.last_exec_time = current_time
                return True
            
            return False
        
        return True

    def _execute_handler(self, handler: EventHandler, args: tuple, kwargs: dict):
        """执行事件处理器"""
        try:
            if handler.is_async:
                loop = self._get_loop()
                if handler.context is not None:
                    # 捕获并记录参数用于调试
                    self._logger.debug(f"执行异步处理器，上下文: {handler.context}, 参数: {args}, 关键字参数: {kwargs}")
                    return loop.create_task(handler.callback(handler.context, *args, **kwargs))
                else:
                    # 捕获并记录参数用于调试
                    self._logger.debug(f"执行异步处理器，参数: {args}, 关键字参数: {kwargs}")
                    return loop.create_task(handler.callback(*args, **kwargs))
            else:
                if handler.context is not None:
                    return handler.callback(handler.context, *args, **kwargs)
                else:
                    return handler.callback(*args, **kwargs)
        except Exception as e:
            self._logger.error(f"事件处理器执行异常: {str(e)}")
            self._logger.error(traceback.format_exc())
            # 返回异常对象而不是抛出它，让emit函数能够收集此异常
            return e


# ----------------------------------------------------------------------------
# 示例用法
# ----------------------------------------------------------------------------

def example_usage():
    """使用示例演示"""
    # 设置日志
    logging.basicConfig(level=logging.DEBUG)
    
    # 1. 基本用法
    def on_message(message):
        print(f"收到消息: {message}")
    
    # 创建事件发射器
    events = EventEmitter()
    events.on("message", on_message)
    events.emit("message", "Hello World")  # 输出: 收到消息: Hello World
    
    # 2. 带参数的事件
    def on_user_login(user_id, time, success=False):
        print(f"用户 {user_id} 登录{'成功' if success else '失败'} 于 {time}")
    
    events.on("user.login", on_user_login)
    events.emit("user.login", "user123", "2023-04-01 12:34:56", success=True)
    
    # 3. 优先级
    def high_priority_handler(data):
        print("高优先级处理器:", data)
    
    def low_priority_handler(data):
        print("低优先级处理器:", data)
    
    events.on("priority.test", high_priority_handler, EventPriority.HIGH)
    events.on("priority.test", low_priority_handler, EventPriority.LOW)
    events.emit("priority.test", "测试数据")  # 高优先级先执行
    
    # 4. 一次性事件
    def one_time_handler(data):
        print("一次性处理器:", data)
    
    events.once("one.time", one_time_handler)
    events.emit("one.time", "第一次")  # 输出: 一次性处理器: 第一次
    events.emit("one.time", "第二次")  # 没有输出，处理器已被移除
    
    # 5. 通配符事件
    def wildcard_handler(data):
        print("通配符处理器:", data)
    
    events.on("user.*", wildcard_handler)
    events.emit("user.created", "新用户")  # 输出: 通配符处理器: 新用户
    events.emit("user.deleted", "删除用户")  # 输出: 通配符处理器: 删除用户
    
    # 6. 取消事件订阅
    events.off("message", on_message)
    events.emit("message", "不会被处理")  # 没有输出
    
    # 7. 异步事件处理
    async def async_handler(data):
        await asyncio.sleep(0.1)  # 模拟异步操作
        print("异步处理器:", data)
    
    events.on("async.event", async_handler)
    
    # 运行异步事件
    loop = asyncio.get_event_loop()
    loop.run_until_complete(events.emit_async("async.event", "异步数据"))
    
    # 8. 等待事件
    def trigger_later():
        time.sleep(0.5)
        events.emit("waited.event", "等待的数据")
    
    threading.Thread(target=trigger_later).start()
    
    async def wait_example():
        try:
            data = await events.wait_for("waited.event", timeout=1.0)
            print("等待事件结果:", data)
        except asyncio.TimeoutError:
            print("等待超时")
    
    loop.run_until_complete(wait_example())
    
    # 9. 延迟事件触发
    events.on("delayed.event", lambda data: print("延迟事件:", data))
    events.emit_after(0.5, "delayed.event", "延迟数据")
    time.sleep(0.6)  # 等待延迟事件触发
    
    # 10. 事件统计
    print(f"事件名称: {events.event_names()}")
    print(f"监听器总数: {events.listeners_count()}")
    print(f"user.* 事件的监听器数: {events.listeners_count('user.created')}")
    
    # 11. 单例模式
    print("\n=== 单例模式示例 ===")
    
    # 创建一个新的实例
    direct_instance = EventEmitter()
    
    # 通过get_instance获取实例
    singleton_instance = EventEmitter.get_instance()
    
    # 验证所有实例是同一个对象
    print(f"直接创建的实例与get_instance()获取的是同一个实例: {direct_instance is singleton_instance}")
    print(f"所有实例与最初创建的事件发射器是同一个: {events is direct_instance and events is singleton_instance}")
    
    # 在不同实例上注册和触发事件，验证它们共享状态
    direct_instance.on("singleton.test", lambda data: print(f"单例事件测试: {data}"))
    # 通过另一个引用触发事件
    singleton_instance.emit("singleton.test", "通过singleton_instance触发")
    
    # 重置单例实例
    print("\n重置全局实例:")
    reset_instance = EventEmitter.create_instance(reset=True)
    
    # 验证重置后是否仍然是相同的实例
    print(f"重置后仍然是同一个实例对象: {reset_instance is direct_instance}")
    
    # 验证重置后事件处理器已清除
    has_handlers = reset_instance.has_listeners("singleton.test")
    print(f"重置后单例事件还有处理器: {has_handlers}")
    
    # 在重置的实例上注册新事件
    reset_instance.on("new.singleton.test", lambda data: print(f"新单例事件: {data}"))
    
    # 通过原始实例触发新事件，验证是同一个实例
    events.emit("new.singleton.test", "通过原始实例触发新事件")
    
    print("\n单例模式确保了无论通过何种方式创建的实例，都是同一个对象，")
    print("这在多模块场景中特别有用，可以实现真正的全局事件通信")
    
    # 展示通配符查询功能
    print("\n=== 测试通配符查询功能 ===")
    
    # 注册几个不同的事件处理器
    events.on("user.login", lambda user: logging.info(f"用户登录: {user}"))
    events.on("user.logout", lambda user: logging.info(f"用户登出: {user}"))
    events.on("system.startup", lambda: logging.info("系统启动"))
    
    # 使用通配符查询
    print(f"是否有用户相关事件监听器: {events.has_listeners('user.*', use_pattern=True)}")
    print(f"是否有系统相关事件监听器: {events.has_listeners('system.*', use_pattern=True)}")
    print(f"是否有消息相关事件监听器: {events.has_listeners('message.*', use_pattern=True)}")
    print(f"是否有任意事件监听器: {events.has_listeners('*', use_pattern=True)}")
    
    # 使用通配符获取监听器数量
    print(f"用户相关事件监听器数量: {events.listeners_count('user.*', use_pattern=True)}")
    print(f"系统相关事件监听器数量: {events.listeners_count('system.*', use_pattern=True)}")
    print(f"所有事件监听器总数: {events.listeners_count()}")
    
    # 清理所有事件
    events.clear()


# ----------------------------------------------------------------------------
# 详细使用示例
# ----------------------------------------------------------------------------

def throttle_debounce_examples():
    """
    节流和防抖功能的详细使用示例
    
    本示例展示了：
    1. 基本的节流使用
    2. 基本的防抖使用
    3. 不同配置组合的效果
    4. 异步处理场景
    5. 性能监控
    6. 实际应用场景
    7. 表单提交防抖
    8. 按钮点击节流防止重复提交
    9. 自动保存功能防抖
    10. API请求节流
    11. 不同配置组合对比
    """
    import asyncio
    import logging
    import time
    import random
    from datetime import datetime
    
    # 设置日志
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger(__name__)
    
    # 创建事件发射器实例
    emitter = EventEmitter()
    
    # 配置事件发射器
    emitter.configure(
        enable_performance_monitoring=True,
        async_pool_size=5
    )
    
    print("\n*** 性能监控已启用，开始测试... ***")
    
    # -------------------------------------------------------------------------
    # 1. 节流示例 - 控制滚动事件处理频率
    # -------------------------------------------------------------------------
    def handle_scroll(position):
        """处理滚动事件"""
        logger.info(f"处理滚动位置: {position} at {datetime.now()}")
    
    # 配置节流：每0.5秒最多执行一次，开始和结束时都执行
    scroll_throttle = ThrottleConfig(
        enabled=True,      # 启用节流
        interval=0.5,      # 时间间隔（秒）
        leading=True,      # 第一次调用立即执行
        trailing=True      # 最后一次调用也要执行
    )
    
    # 注册带节流的滚动处理器
    emitter.on("scroll", handle_scroll, throttle=scroll_throttle)
    
    # 模拟快速滚动
    print("\n=== 测试节流效果（滚动事件）===")
    print("说明: 用户快速滚动页面，但我们希望限制滚动处理频率以提高性能")
    print("配置: 每0.5秒最多处理一次，第一次立即处理，最后一次也要处理")
    print("预期: 10次滚动事件，但只有约3次实际处理")
    for i in range(10):
        emitter.emit("scroll", i * 100)  # 快速发送10个滚动事件
        time.sleep(0.1)  # 模拟每0.1秒滚动一次
    time.sleep(1)  # 等待最后的trailing调用
    
    # -------------------------------------------------------------------------
    # 2. 防抖示例 - 处理搜索输入
    # -------------------------------------------------------------------------
    def handle_search(keyword):
        """处理搜索关键词"""
        logger.info(f"执行搜索: {keyword} at {datetime.now()}")
    
    # 配置防抖：等待0.5秒无新输入才执行，有最大等待时间限制
    search_debounce = DebounceConfig(
        enabled=True,      # 启用防抖
        wait=0.5,         # 等待时间（秒）
        leading=False,     # 第一次调用不立即执行
        trailing=True,     # 最后一次调用要执行
        max_wait=2.0       # 最大等待2秒
    )
    
    # 注册带防抖的搜索处理器
    emitter.on("search", handle_search, debounce=search_debounce)
    
    # 模拟快速输入
    print("\n=== 测试防抖效果（搜索输入）===")
    print("说明: 用户在搜索框快速输入，但我们希望等待输入完成后再执行搜索")
    print("配置: 等待0.5秒无新输入才执行搜索，最多等待2秒")
    print("预期: 多次输入事件，但只会在输入停止后执行一次搜索")
    keywords = ["p", "py", "pyt", "pyth", "pytho", "python"]
    for keyword in keywords:
        emitter.emit("search", keyword)  # 快速发送搜索事件
        time.sleep(0.2)  # 模拟每0.2秒输入一个字符
    time.sleep(1)  # 等待防抖执行
    
    # -------------------------------------------------------------------------
    # 3. 异步处理示例 - 实时数据更新
    # -------------------------------------------------------------------------
    async def handle_data_update(data):
        """异步处理数据更新"""
        await asyncio.sleep(0.1)  # 模拟异步操作
        logger.info(f"更新数据: {data} at {datetime.now()}")
    
    # 配置节流：每1秒最多执行一次更新
    update_throttle = ThrottleConfig(
        enabled=True,
        interval=1.0,
        leading=True,
        trailing=True
    )
    
    # 注册带节流的异步处理器
    emitter.on("data.update", handle_data_update, throttle=update_throttle)
    
    # 模拟高频数据更新
    print("\n=== 测试异步节流效果（数据更新）===")
    print("说明: 后台频繁接收数据更新，但我们希望控制UI更新频率")
    print("配置: 异步处理函数，每1秒最多执行一次")
    print("预期: 5次更新请求，但实际更新次数更少")
    async def test_async_updates():
        for i in range(5):
            await emitter.emit_async("data.update", f"数据 #{i}")
            await asyncio.sleep(0.3)
    
    loop = asyncio.get_event_loop()
    loop.run_until_complete(test_async_updates())
    
    # -------------------------------------------------------------------------
    # 4. 实际应用场景示例 - 窗口调整大小
    # -------------------------------------------------------------------------
    class WindowResizer:
        def __init__(self, emitter):
            self.emitter = emitter
            self.setup_handlers()
        
        def setup_handlers(self):
            # 使用防抖处理窗口大小调整完成事件
            resize_end_debounce = DebounceConfig(
                enabled=True,
                wait=0.5,      # 等待0.5秒无调整才触发
                leading=False,  # 开始调整时不触发
                trailing=True   # 调整结束后触发
            )
            
            # 使用节流处理调整过程中的事件
            resize_throttle = ThrottleConfig(
                enabled=True,
                interval=0.2,  # 每0.2秒最多触发一次
                leading=True,  # 开始调整时触发
                trailing=True  # 结束调整时触发
            )
            
            # 注册调整过程处理器（节流）
            self.emitter.on(
                "window.resize",
                self.handle_resize_progress,
                throttle=resize_throttle
            )
            
            # 注册调整完成处理器（防抖）
            self.emitter.on(
                "window.resize",
                self.handle_resize_end,
                debounce=resize_end_debounce
            )
        
        def handle_resize_progress(self, width, height):
            """处理调整过程中的事件（频繁触发）"""
            logger.info(f"调整中 - 宽度: {width}, 高度: {height}")
        
        def handle_resize_end(self, width, height):
            """处理调整完成事件（调整结束后触发一次）"""
            logger.info(f"调整完成 - 最终宽度: {width}, 高度: {height}")
    
    # 创建窗口调整处理器
    print("\n=== 测试实际应用（窗口调整）===")
    print("说明: 用户拖动窗口调整大小，同时使用节流和防抖处理不同需求")
    print("配置: 节流用于过程反馈(0.2秒一次)，防抖用于完成后重新布局(0.5秒无调整后)")
    print("预期: 调整过程多次触发进度更新，但完成布局只在最后触发一次")
    resizer = WindowResizer(emitter)
    
    # 模拟窗口调整过程
    sizes = [
        (800, 600),
        (850, 650),
        (900, 700),
        (950, 750),
        (1000, 800)
    ]
    
    for width, height in sizes:
        emitter.emit("window.resize", width, height)
        time.sleep(0.1)  # 模拟快速调整
    time.sleep(1)  # 等待最终的防抖调用
    
    # -------------------------------------------------------------------------
    # 5. 表单验证防抖 - 输入字段验证
    # -------------------------------------------------------------------------
    def validate_email(email):
        """验证邮箱格式"""
        import re
        valid = bool(re.match(r"[^@]+@[^@]+\.[^@]+", email))
        logger.info(f"验证邮箱 '{email}': {'通过' if valid else '不通过'} at {datetime.now()}")
        return valid
    
    # 配置防抖：等待0.3秒无输入再验证，但首次输入不验证
    validation_debounce = DebounceConfig(
        enabled=True,
        wait=0.3,          # 等待时间短，提供较快反馈
        leading=False,     # 首次输入不验证，避免对空输入验证
        trailing=True,     # 输入停止后验证
        max_wait=1.0       # 最多等待1秒，提供及时反馈
    )
    
    # 注册带防抖的验证处理器
    emitter.on("validate.email", validate_email, debounce=validation_debounce)
    
    # 模拟用户输入邮箱
    print("\n=== 测试表单验证防抖 ===")
    print("说明: 用户输入邮箱时，我们希望在输入暂停后才验证，避免频繁验证")
    print("配置: 等待0.3秒无输入才验证，最多等待1秒")
    print("预期: 多次输入字符，但只在输入暂停时验证")
    
    emails = ["u", "us", "use", "user", "user@", "user@e", "user@exa", "user@exam", "user@examp", "user@exampl", "user@example", "<EMAIL>"]
    for email in emails:
        emitter.emit("validate.email", email)
        time.sleep(0.15)  # 模拟快速输入
    time.sleep(0.5)  # 等待最终验证
    
    # -------------------------------------------------------------------------
    # 6. 按钮点击节流 - 防止重复提交
    # -------------------------------------------------------------------------
    def submit_form(form_data):
        """提交表单数据"""
        logger.info(f"提交表单: {form_data} at {datetime.now()}")
        # 模拟API请求
        time.sleep(0.3)
        return {"success": True, "id": random.randint(1000, 9999)}
    
    # 配置节流：严格限制2秒内只能提交一次，且只在首次点击时提交
    submit_throttle = ThrottleConfig(
        enabled=True,
        interval=2.0,      # 较长间隔，防止重复提交
        leading=True,      # 第一次点击立即提交
        trailing=False     # 忽略间隔内的后续点击
    )
    
    # 注册带节流的提交处理器
    emitter.on("form.submit", submit_form, throttle=submit_throttle)
    
    # 模拟用户快速连续点击提交按钮
    print("\n=== 测试按钮点击节流（防止重复提交）===")
    print("说明: 防止用户快速多次点击提交按钮导致重复提交")
    print("配置: 2秒内只能提交一次，且只处理第一次点击")
    print("预期: 5次连续点击，但只有第1次会实际提交")
    
    form_data = {"name": "张三", "age": 30, "email": "<EMAIL>"}
    for i in range(5):
        emitter.emit("form.submit", form_data)
        time.sleep(0.2)  # 模拟快速连续点击
    time.sleep(2.5)  # 等待节流间隔过去
    
    # 间隔后再次点击，应该能够再次提交
    emitter.emit("form.submit", form_data)
    time.sleep(0.5)
    
    # -------------------------------------------------------------------------
    # 7. 自动保存功能防抖 - 编辑器自动保存
    # -------------------------------------------------------------------------
    def auto_save(document):
        """自动保存文档"""
        logger.info(f"自动保存文档: {document[:20]}... at {datetime.now()}")
        # 模拟保存操作
        time.sleep(0.2)
        return {"saved": True, "timestamp": time.time()}
    
    # 配置防抖：等待1秒无编辑后自动保存，最长等待5秒强制保存
    autosave_debounce = DebounceConfig(
        enabled=True,
        wait=1.0,          # 等待1秒无编辑再保存
        leading=False,     # 开始编辑时不保存
        trailing=True,     # 编辑停止后保存
        max_wait=5.0       # 最多等待5秒，防止长时间不保存
    )
    
    # 注册带防抖的自动保存处理器
    emitter.on("document.change", auto_save, debounce=autosave_debounce)
    
    # 模拟用户编辑文档
    print("\n=== 测试自动保存功能防抖 ===")
    print("说明: 编辑器中用户持续编辑，需要在编辑暂停后自动保存")
    print("配置: 等待1秒无编辑再保存，最多等待5秒强制保存")
    print("预期: 多次编辑事件，但只在编辑暂停或达到最大等待时间时保存")
    
    # 模拟连续10次小编辑
    document = "这是一个测试文档"
    for i in range(10):
        document += f" 编辑{i+1}"
        emitter.emit("document.change", document)
        time.sleep(0.3)  # 编辑间隔小于防抖等待时间
    
    time.sleep(1.5)  # 等待编辑停止后的保存
    
    # 测试max_wait - 持续编辑超过max_wait
    print("\n--- 测试最大等待时间 ---")
    for i in range(20):  # 持续编辑超过5秒
        document += f" 长时间编辑{i+1}"
        emitter.emit("document.change", document)
        time.sleep(0.3)  # 持续编辑，总时间超过max_wait
    
    time.sleep(1.5)  # 等待最后的保存
    
    # -------------------------------------------------------------------------
    # 8. API请求节流 - 限制请求频率
    # -------------------------------------------------------------------------
    async def fetch_data(query):
        """从API获取数据"""
        logger.info(f"API请求: '{query}' at {datetime.now()}")
        # 模拟API延迟
        await asyncio.sleep(0.3)
        return {"results": [f"结果-{query}-{i}" for i in range(3)], "timestamp": time.time()}
    
    # 配置节流：严格限制API请求频率，每1.5秒最多一次请求
    api_throttle = ThrottleConfig(
        enabled=True,
        interval=1.5,      # 限制API请求频率
        leading=True,      # 第一次请求立即发送
        trailing=True      # 节流期间有新请求，待间隔后发送最后一次
    )
    
    # 注册带节流的API请求处理器
    emitter.on("api.fetch", fetch_data, throttle=api_throttle)
    
    # 模拟高频API请求
    print("\n=== 测试API请求节流 ===")
    print("说明: 限制向后端API发送请求的频率，避免过载和被限流")
    print("配置: 每1.5秒最多一次请求，首尾都处理")
    print("预期: 多次请求事件，但实际请求频率受到限制")
    
    async def test_api_requests():
        # 模拟8次快速请求
        queries = ["商品", "服务", "价格", "评价", "位置", "配送", "促销", "客服"]
        for query in queries:
            await emitter.emit_async("api.fetch", query)
            await asyncio.sleep(0.4)  # 请求间隔小于节流间隔
        
        # 等待最后可能的trailing请求
        await asyncio.sleep(2)
    
    loop = asyncio.get_event_loop()
    loop.run_until_complete(test_api_requests())
    
    # -------------------------------------------------------------------------
    # 9. 不同配置组合对比 - 展示不同配置效果
    # -------------------------------------------------------------------------
    print("\n=== 不同配置组合对比 ===")
    print("说明: 展示节流和防抖不同配置参数组合的效果")
    
    # 记录不同配置触发的次数
    config_results = {}
    
    # 9.1 标准节流 (leading=true, trailing=true)
    def standard_throttle_handler(data):
        logger.info(f"标准节流处理: {data} at {datetime.now()}")
        config_results.setdefault("standard_throttle", 0)
        config_results["standard_throttle"] += 1
    
    # 9.2 仅首次节流 (leading=true, trailing=false)
    def leading_only_throttle_handler(data):
        logger.info(f"仅首次节流处理: {data} at {datetime.now()}")
        config_results.setdefault("leading_only_throttle", 0)
        config_results["leading_only_throttle"] += 1
    
    # 9.3 仅末次节流 (leading=false, trailing=true)
    def trailing_only_throttle_handler(data):
        logger.info(f"仅末次节流处理: {data} at {datetime.now()}")
        config_results.setdefault("trailing_only_throttle", 0)
        config_results["trailing_only_throttle"] += 1
    
    # 9.4 标准防抖 (leading=false, trailing=true)
    def standard_debounce_handler(data):
        logger.info(f"标准防抖处理: {data} at {datetime.now()}")
        config_results.setdefault("standard_debounce", 0)
        config_results["standard_debounce"] += 1
    
    # 9.5 首次触发防抖 (leading=true, trailing=false)
    def leading_only_debounce_handler(data):
        logger.info(f"首次触发防抖处理: {data} at {datetime.now()}")
        config_results.setdefault("leading_only_debounce", 0)
        config_results["leading_only_debounce"] += 1
    
    # 9.6 首尾都触发防抖 (leading=true, trailing=true)
    def both_ends_debounce_handler(data):
        logger.info(f"首尾都触发防抖处理: {data} at {datetime.now()}")
        config_results.setdefault("both_ends_debounce", 0)
        config_results["both_ends_debounce"] += 1
    
    # 注册不同配置的处理器
    # 节流配置
    emitter.on("config.test", standard_throttle_handler, 
               throttle=ThrottleConfig(enabled=True, interval=0.5, leading=True, trailing=True))
    
    emitter.on("config.test", leading_only_throttle_handler, 
               throttle=ThrottleConfig(enabled=True, interval=0.5, leading=True, trailing=False))
    
    emitter.on("config.test", trailing_only_throttle_handler, 
               throttle=ThrottleConfig(enabled=True, interval=0.5, leading=False, trailing=True))
    
    # 防抖配置
    emitter.on("config.test", standard_debounce_handler, 
               debounce=DebounceConfig(enabled=True, wait=0.5, leading=False, trailing=True))
    
    emitter.on("config.test", leading_only_debounce_handler, 
               debounce=DebounceConfig(enabled=True, wait=0.5, leading=True, trailing=False))
    
    emitter.on("config.test", both_ends_debounce_handler, 
               debounce=DebounceConfig(enabled=True, wait=0.5, leading=True, trailing=True))
    
    # 发送测试事件
    print("各种配置对比测试开始，将发送10个快速事件...")
    for i in range(10):
        emitter.emit("config.test", f"测试-{i}")
        time.sleep(0.1)
    
    # 等待所有可能的trailing调用完成
    time.sleep(1.0)
    
    # 打印结果对比
    print("\n=== 配置效果对比结果 ===")
    print(f"共发送10个事件，不同配置的处理次数：")
    print(f"标准节流 (leading=true, trailing=true): {config_results.get('standard_throttle', 0)}次")
    print(f"仅首次节流 (leading=true, trailing=false): {config_results.get('leading_only_throttle', 0)}次")
    print(f"仅末次节流 (leading=false, trailing=true): {config_results.get('trailing_only_throttle', 0)}次")
    print(f"标准防抖 (leading=false, trailing=true): {config_results.get('standard_debounce', 0)}次")
    print(f"首次触发防抖 (leading=true, trailing=false): {config_results.get('leading_only_debounce', 0)}次")
    print(f"首尾都触发防抖 (leading=true, trailing=true): {config_results.get('both_ends_debounce', 0)}次")
    
    # 配置说明
    print("\n节流与防抖选择指南:")
    print("1. 使用节流(throttle)场景:")
    print("   - 滚动事件处理")
    print("   - 页面调整大小")
    print("   - 鼠标移动")
    print("   - 限制API请求频率")
    print("   - 防止按钮重复点击")
    
    print("\n2. 使用防抖(debounce)场景:")
    print("   - 搜索输入")
    print("   - 表单验证")
    print("   - 自动保存")
    print("   - 窗口调整完成后重新布局")
    print("   - 用户停止交互后执行复杂计算")
    
    print("\n3. 配置参数选择:")
    print("   leading=true: 需要立即响应用户第一次操作时")
    print("   trailing=true: 需要确保最终状态被处理时")
    print("   max_wait: 防止长时间不执行的场景，如编辑器自动保存")
    
    # -------------------------------------------------------------------------
    # 5. 性能统计示例
    # -------------------------------------------------------------------------
    print("\n=== 性能统计 ===")
    stats = emitter.get_stats()
    print(f"事件触发次数: {stats['emit_count']}")
    print(f"平均执行时间: {stats.get('emit_avg_time', 0):.4f} 秒")
    print(f"处理器调用次数: {stats['handler_count']}")
    print(f"正则缓存命中率: {stats['pattern_cache_hits']}/{stats['pattern_cache_hits'] + stats['pattern_cache_misses']}")

    # 展示通配符查询功能
    print("\n=== 测试通配符查询功能 ===")
    
    # 注册几个不同的事件处理器
    emitter.on("user.login", lambda user: logger.info(f"用户登录: {user}"))
    emitter.on("user.logout", lambda user: logger.info(f"用户登出: {user}"))
    emitter.on("system.startup", lambda: logger.info("系统启动"))
    
    # 使用通配符查询
    print(f"是否有用户相关事件监听器: {emitter.has_listeners('user.*', use_pattern=True)}")
    print(f"是否有系统相关事件监听器: {emitter.has_listeners('system.*', use_pattern=True)}")
    print(f"是否有消息相关事件监听器: {emitter.has_listeners('message.*', use_pattern=True)}")
    print(f"是否有任意事件监听器: {emitter.has_listeners('*', use_pattern=True)}")
    
    # 使用通配符获取监听器数量
    print(f"用户相关事件监听器数量: {emitter.listeners_count('user.*', use_pattern=True)}")
    print(f"系统相关事件监听器数量: {emitter.listeners_count('system.*', use_pattern=True)}")
    print(f"所有事件监听器总数: {emitter.listeners_count()}")
    
    # 清理所有事件
    emitter.clear()
    
    print("演示完成")

def test_emit_return_values():
    """
    测试emit函数返回处理器结果值的功能
    
    本测试演示如何获取和处理事件处理器的返回值
    """
    print("\n" + "=" * 50)
    print("测试emit函数返回值功能")
    print("=" * 50)
    
    # 获取事件发射器实例
    emitter = EventEmitter.get_instance()
    emitter.clear()  # 清除之前的事件处理器
    
    # 定义几个会返回不同类型值的同步处理器
    def handler_return_int(data):
        print(f"整数返回处理器收到: {data}")
        return 42
    
    def handler_return_dict(data):
        print(f"字典返回处理器收到: {data}")
        return {"status": "success", "data": data}
    
    def handler_return_none(data):
        print(f"无返回值处理器收到: {data}")
        # 不返回任何值
    
    def handler_with_exception(data):
        print(f"异常处理器收到: {data}")
        raise ValueError("测试异常情况")
    
    # 定义异步处理器
    async def async_handler(data):
        print(f"异步处理器收到: {data}")
        await asyncio.sleep(0.1)  # 模拟异步操作
        return f"异步处理完成: {data}"
    
    # 注册处理器
    emitter.on("test.return", handler_return_int, priority=EventPriority.HIGH)
    emitter.on("test.return", handler_return_dict)
    emitter.on("test.return", handler_return_none)
    emitter.on("test.return", handler_with_exception, priority=EventPriority.LOW)
    emitter.on("test.return", async_handler)
    
    # 触发事件并获取返回值
    print("\n触发事件并获取返回值:")
    results = emitter.emit("test.return", "测试数据")
    
    # 处理返回值
    print("\n处理返回值:")
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"结果 {i+1}: 异常 - {type(result).__name__}: {result}")
        elif asyncio.iscoroutine(result):
            print(f"结果 {i+1}: 协程对象 - 需要异步等待获取实际结果")
        elif isinstance(result, asyncio.Task):
            print(f"结果 {i+1}: 异步任务 - 需要await task.result()获取结果")
            # 注意：这里不能直接await，因为我们在同步上下文中
        else:
            print(f"结果 {i+1}: {type(result).__name__} - {result}")
    
    # 演示如何在异步函数中处理这些结果
    async def process_async_results():
        print("\n异步处理结果:")
        results = emitter.emit("test.return", "异步处理数据")
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"异步处理结果 {i+1}: 异常 - {result}")
            elif asyncio.iscoroutine(result):
                # 等待协程完成并获取结果
                real_result = await result
                print(f"异步处理结果 {i+1}: 协程实际结果 - {real_result}")
            elif isinstance(result, asyncio.Task):
                # 等待任务完成并获取结果
                try:
                    await result  # 等待任务完成
                    task_result = await asyncio.gather(*result.result())  # 收集所有异步结果
                    print(f"异步处理结果 {i+1}: 任务实际结果 - {task_result}")
                except Exception as e:
                    print(f"异步处理结果 {i+1}: 任务执行异常 - {e}")
            else:
                print(f"异步处理结果 {i+1}: {result}")
    
    # 运行异步处理函数
    loop = asyncio.get_event_loop()
    loop.run_until_complete(process_async_results())
    
    # 测试单一处理器的返回值
    emitter.clear()
    
    def single_handler(data):
        print(f"单一处理器收到: {data}")
        return {"processed": True, "value": data}
    
    emitter.on("single.event", single_handler)
    
    print("\n测试单一处理器返回值:")
    result = emitter.emit("single.event", "单一数据")
    print(f"返回结果: {result}")  # 返回的是包含一个元素的列表
    
    # 如果想直接获取第一个结果，可以这样做：
    if result:
        first_result = result[0]
        print(f"第一个结果: {first_result}")
    
    print("\nemit返回值功能测试完成")

if __name__ == "__main__":
    # 运行示例
    example_usage()
    # events = EventEmitter()
    # events1 = EventEmitter.create_instance(True)
    # print(events == events1) # True
    # # 运行详细示例
    # throttle_debounce_examples()
    # 测试emit返回值功能
    # test_emit_return_values()
