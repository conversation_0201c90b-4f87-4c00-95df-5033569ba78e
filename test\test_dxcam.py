import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from global_tools.window_operation.dxcam_core import DXCamCore
import cv2
import time

if __name__ == "__main__":
	dx = DXCamCore( monitor_index=0 )
	info = dx.get_current_monitor_info()
	for i in range( 10 ):
		time.sleep(0.1)
		s = time.time()
		images = dx.capture()
		print( images.shape, time.time() - s )
		# img = dx.capture()
		# cv2.imshow( "test", images )
		# cv2.waitKey( 0 )
