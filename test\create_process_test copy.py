import time
import random
import logging
import threading
# from global_tools.utils.manager_process.create_process import ManagedMultiProcess, SharedDataManager, \
# 	ProcessEventManager
import sys
import os
from global_tools.utils.manager_process.create_process import ManagedMultiProcess, SharedDataManager, \
	ProcessEventManager

sys.path.insert( 0, os.path.abspath( os.path.join( os.path.dirname( __file__ ), '..' ) ) )


# 将示例工作函数移到顶层，以便子进程可以找到它
def simple_worker_example( shared_manager_proxy: SharedDataManager, task_item: int, prefix: str = "Task" ):
	'''
	一个简单的工作函数示例，使用 SharedDataManager 代理。
	它处理一个数字，并将结果存储在共享数据中。

					Args:
					shared_manager_proxy: SharedDataManager代理对象，用于访问共享数据
					task_item: 要处理的任务项（整数）
					prefix: 任务前缀，默认为"Task"

					Returns:
					None: 结果通过共享数据管理器存储
	'''

	for i in range( 30 ):
		lock = shared_manager_proxy.get_lock()
		with lock:
			num = shared_manager_proxy.get_value( "num", default=0 )
			new_num = num + 1
			shared_manager_proxy.add_value( "num", new_num )
			shared_manager_proxy.append_to_list( "list", new_num )
			print( f"id {os.getpid()} - {i}" )
		time.sleep( random.uniform( 0.01, 0.15 ) )


# 定义回调函数
def stop_callback( mp_instance: ManagedMultiProcess, *args, **kwargs ):
	if mp_instance.wait_event( key="event_stop" ):
		return True


def on_num_changed( keys, old_value, new_value, *args, **kwargs ):
	pass
	print( f"共享数据 {keys} 已更改: {old_value} -> {new_value}" )
	# time.sleep( random.uniform( 0.05, 0.25 ) )
	pass


def on_processes_completed_callback( mp, *args, **kwargs ):
	print( "完成" )


def main():
	# 配置日志
	logging.basicConfig(
		level=logging.DEBUG,
		format="%(asctime)s - %(levelname)s - [%(processName)s:%(process)d] - %(message)s",
		datefmt="%Y-%m-%d %H:%M:%S",
	)

	# 创建一个简单的处理任务列表
	tasks = list( range( 3 ) )

	# 1. 创建并配置 ManagedMultiProcess 实例
	mp = ManagedMultiProcess(
		input_data=tasks,
		callback_func=simple_worker_example,  # 使用示例工作函数
		num_processes=3,
		prefix="Demo"  # 传递给示例工作函数的参数
	)
	mp.create_event( "event_stop" )

	def func():
		for i in range( 5 ):
			print( f"等待事件 {i + 1} 秒停止..." )
			time.sleep( 1 )
		mp.set_event( "event_stop" )

	# threading.Thread(target=func).start()
	mp.listen_event( ProcessEventManager.PROCESS_NORMALLY_COMPLETED, on_processes_completed_callback )
	mp.watch_shared_data( "num", on_num_changed )
	# 3. 运行并等待完成
	print( "启动处理..." )
	mp.run()
	# 阻塞方式调用，等待停止过程完成
	mp.stop_all( callback=stop_callback, block=True )
	# mp.wait_all()


# mp.wait_all()
# 4. 获取结果
# result = mp.get_shared_value( "num", default=0 )
# result_list = mp.get_shared_list("list")
# print( f"处理结束。最终结果: num = {result} list = {result_list}" )

# 获取所有错误
# errors = mp.get_all_errors()
# if errors:
# 	print( f"处理过程中发生了 {len( errors )} 个错误:" )
# 	for error in errors:
# 		print( f"  - {error}" )


if __name__ == "__main__":
	main()
