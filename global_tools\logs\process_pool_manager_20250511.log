2025-05-11 18:37:53,157 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:37 - 初始化进程池管理类
2025-05-11 18:37:53,158 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:41 - 进程池进程数量: 3
2025-05-11 18:37:53,158 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:48 - 任务数量: 0
2025-05-11 18:37:53,681 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:100 - 创建进程池，进程数量: 3
2025-05-11 18:37:53,691 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:102 - 进程池创建成功
2025-05-11 18:37:53,699 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:113 - 进程状态初始化完成
2025-05-11 18:37:53,699 - 30648 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:72 - 进程池管理类初始化完成
2025-05-11 18:37:53,699 - 30648 - MainThread - process_pool_manager - WARNING - process_pool_manager.py:232 - 任务列表为空，无任务可执行
2025-05-11 18:37:53,699 - 30648 - MainThread - process_pool_manager - WARNING - process_pool_manager.py:347 - 没有任务在执行，无需等待
2025-05-11 18:38:37,329 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:37 - 初始化进程池管理类
2025-05-11 18:38:37,330 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:41 - 进程池进程数量: 3
2025-05-11 18:38:37,330 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:48 - 任务数量: 10
2025-05-11 18:38:37,879 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:100 - 创建进程池，进程数量: 3
2025-05-11 18:38:37,888 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:102 - 进程池创建成功
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:113 - 进程状态初始化完成
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:72 - 进程池管理类初始化完成
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:267 - 执行所有任务，任务数量: 10
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:274 - 已提交所有任务
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:278 - 等待所有任务完成
2025-05-11 18:38:37,889 - 29140 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:287 - 执行所有任务异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:38:37,892 - 29140 - MainThread - process_pool_manager - WARNING - process_pool_manager.py:347 - 没有任务在执行，无需等待
2025-05-11 18:38:56,965 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:37 - 初始化进程池管理类
2025-05-11 18:38:56,965 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:41 - 进程池进程数量: 3
2025-05-11 18:38:56,966 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:48 - 任务数量: 10
2025-05-11 18:38:57,481 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:100 - 创建进程池，进程数量: 3
2025-05-11 18:38:57,491 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:102 - 进程池创建成功
2025-05-11 18:38:57,492 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:113 - 进程状态初始化完成
2025-05-11 18:38:57,492 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:72 - 进程池管理类初始化完成
2025-05-11 18:38:57,492 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:267 - 执行所有任务，任务数量: 10
2025-05-11 18:38:57,492 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:274 - 已提交所有任务
2025-05-11 18:38:57,493 - 21312 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:278 - 等待所有任务完成
2025-05-11 18:38:57,493 - 21312 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:287 - 执行所有任务异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:39:22,077 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:37 - 初始化进程池管理类
2025-05-11 18:39:22,077 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:41 - 进程池进程数量: 3
2025-05-11 18:39:22,077 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:48 - 任务数量: 4
2025-05-11 18:39:22,607 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:100 - 创建进程池，进程数量: 3
2025-05-11 18:39:22,617 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:102 - 进程池创建成功
2025-05-11 18:39:22,618 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:113 - 进程状态初始化完成
2025-05-11 18:39:22,618 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:72 - 进程池管理类初始化完成
2025-05-11 18:39:22,618 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:267 - 执行所有任务，任务数量: 4
2025-05-11 18:39:22,618 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:274 - 已提交所有任务
2025-05-11 18:39:22,619 - 29256 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:278 - 等待所有任务完成
2025-05-11 18:39:22,619 - 29256 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:287 - 执行所有任务异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:56:44,624 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:38 - 初始化进程池管理类
2025-05-11 18:56:44,625 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:42 - 进程池进程数量: 3
2025-05-11 18:56:44,625 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:49 - 任务数量: 4
2025-05-11 18:56:45,709 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:169 - 创建进程池，进程数量: 3
2025-05-11 18:56:45,720 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:172 - 进程池创建成功
2025-05-11 18:56:45,721 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:183 - 进程状态初始化完成
2025-05-11 18:56:45,721 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:79 - 进程池管理类初始化完成
2025-05-11 18:56:45,721 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:410 - 执行所有任务，任务数量: 4
2025-05-11 18:56:45,721 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:442 - 已提交所有可序列化任务: 4/4
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:446 - 等待所有任务完成
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:56:45,722 - 27972 - MainThread - process_pool_manager - DEBUG - process_pool_manager.py:461 - 所有任务已完成
