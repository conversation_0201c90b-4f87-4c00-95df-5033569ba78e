<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="73" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="GlobalNameCanbeLocal" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonAsciiCharacters" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="opencv-python" />
            <item index="1" class="java.lang.String" itemvalue="onnxruntime-gpu" />
            <item index="2" class="java.lang.String" itemvalue="scipy" />
            <item index="3" class="java.lang.String" itemvalue="timm" />
            <item index="4" class="java.lang.String" itemvalue="gradio" />
            <item index="5" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="6" class="java.lang.String" itemvalue="PyYAML" />
            <item index="7" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="psutil" />
            <item index="10" class="java.lang.String" itemvalue="albumentations" />
            <item index="11" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="12" class="java.lang.String" itemvalue="onnxslim" />
            <item index="13" class="java.lang.String" itemvalue="safetensors" />
            <item index="14" class="java.lang.String" itemvalue="onnx" />
            <item index="15" class="java.lang.String" itemvalue="pycocotools" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E201" />
          <option value="E202" />
          <option value="E251" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N803" />
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingNamesInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="SpellCheckingInspection" enabled="false" level="ERROR" enabled_by_default="false" editorAttributes="ERRORS_ATTRIBUTES">
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
    <inspection_tool class="UnresolvedSymbolInEmmyDoc" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES">
      <scope name="Project Files" level="WEAK WARNING" enabled="false" />
    </inspection_tool>
  </profile>
</component>