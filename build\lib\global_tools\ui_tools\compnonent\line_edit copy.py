import os
import json
import logging
import traceback
from typing import List, Optional, Dict, Union, Callable, Any, Set, Tuple
from functools import partial
import time
from threading import Lock, RLock
from pathlib import Path
from collections import deque
import queue
import threading
from dataclasses import dataclass
from enum import Enum

from PyQt5.QtWidgets import (
    QWidget, QLayout, QGridLayout, QCheckBox, QVBoxLayout, QHBoxLayout, QGroupBox, QFrame, QTextEdit, QLineEdit
)
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, Qt, QTimer, QThread, QMutex, QMutexLocker, QEvent
from PyQt5 import sip    # 导入sip模块，用于检查Qt对象是否已被销毁
from global_tools.utils import ClassInstanceManager, Logger, LogLevel, Colors
from global_tools.ui_tools import LogOutput
from global_tools.ui_tools.compnonent.helper import LogHandlerHelper


class SavePriority(Enum):
    """保存优先级枚举"""
    LOW = 1      # 低优先级：常规保存
    NORMAL = 2   # 普通优先级：用户操作触发
    HIGH = 3     # 高优先级：紧急保存
    CRITICAL = 4 # 关键优先级：程序退出时保存


@dataclass
class SaveTask:
    """保存任务数据结构"""
    widget_key: str
    data: Dict[str, Any]
    priority: SavePriority
    timestamp: float
    retry_count: int = 0
    max_retries: int = 3

    def __lt__(self, other):
        """支持优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value  # 高优先级在前
        return self.timestamp < other.timestamp  # 时间早的在前


class EnhancedSaveQueue:
    """增强的保存队列管理器"""

    def __init__(self, max_size: int = 1000):
        self._queue = queue.PriorityQueue(maxsize=max_size)
        self._lock = threading.RLock()
        self._pending_tasks = {}  # widget_key -> SaveTask
        self._total_tasks = 0

        # 性能统计
        self._stats = {
            'total_added': 0,
            'total_processed': 0,
            'total_updated': 0,
            'total_failed': 0,
            'peak_size': 0,
            'last_reset_time': time.time()
        }

    def put_task(self, task: SaveTask) -> bool:
        """添加保存任务到队列"""
        try:
            with self._lock:
                # 如果已有相同控件的任务，更新为最新的
                if task.widget_key in self._pending_tasks:
                    old_task = self._pending_tasks[task.widget_key]
                    # 保持更高的优先级
                    if task.priority.value > old_task.priority.value:
                        old_task.priority = task.priority
                    # 更新数据和时间戳
                    old_task.data = task.data
                    old_task.timestamp = task.timestamp
                    return True

                # 添加新任务
                self._pending_tasks[task.widget_key] = task
                self._queue.put(task)
                self._total_tasks += 1
                self._stats['total_added'] += 1

                # 更新峰值大小
                current_size = self._queue.qsize()
                if current_size > self._stats['peak_size']:
                    self._stats['peak_size'] = current_size

                return True

        except queue.Full:
            return False
        except Exception:
            return False

    def get_task(self, timeout: float = None) -> Optional[SaveTask]:
        """从队列获取保存任务"""
        try:
            task = self._queue.get(timeout=timeout)
            with self._lock:
                # 从待处理任务中移除
                if task.widget_key in self._pending_tasks:
                    del self._pending_tasks[task.widget_key]
                self._stats['total_processed'] += 1
            return task
        except queue.Empty:
            return None
        except Exception:
            self._stats['total_failed'] += 1
            return None

    def get_batch_tasks(self, max_count: int = 10, timeout: float = 0.1) -> List[SaveTask]:
        """批量获取保存任务"""
        tasks = []
        try:
            # 获取第一个任务（阻塞）
            first_task = self.get_task(timeout=timeout)
            if first_task:
                tasks.append(first_task)

                # 获取更多任务（非阻塞）
                for _ in range(max_count - 1):
                    task = self.get_task(timeout=0)
                    if task:
                        tasks.append(task)
                    else:
                        break
        except Exception:
            pass

        return tasks

    def size(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()

    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()

    def clear(self):
        """清空队列"""
        with self._lock:
            while not self._queue.empty():
                try:
                    self._queue.get_nowait()
                except queue.Empty:
                    break
            self._pending_tasks.clear()

    def get_statistics(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        with self._lock:
            current_time = time.time()
            uptime = current_time - self._stats['last_reset_time']

            return {
                'current_size': self._queue.qsize(),
                'pending_tasks': len(self._pending_tasks),
                'total_added': self._stats['total_added'],
                'total_processed': self._stats['total_processed'],
                'total_updated': self._stats['total_updated'],
                'total_failed': self._stats['total_failed'],
                'peak_size': self._stats['peak_size'],
                'uptime_seconds': uptime,
                'processing_rate': self._stats['total_processed'] / uptime if uptime > 0 else 0,
                'success_rate': (self._stats['total_processed'] / max(1, self._stats['total_added'])) * 100
            }

    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'total_added': 0,
                'total_processed': 0,
                'total_updated': 0,
                'total_failed': 0,
                'peak_size': 0,
                'last_reset_time': time.time()
            }


class _LineEditSaveWorker( QObject ):
    """
    增强的文本输入框内容保存工作器

    支持以下功能：
    - 批量保存处理
    - 优先级队列
    - 重试机制
    - 性能优化
    """
    # 信号定义
    log_message_signal = pyqtSignal( str, str )    # (message, color_name_or_hex)
    log_multi_style_message_signal = pyqtSignal( list )    # (message_parts_with_style_dicts)
    save_finished_signal = pyqtSignal( bool, str )    # (success, message)
    batch_save_finished_signal = pyqtSignal( int, int, str )    # (success_count, total_count, message)

    def __init__( self, parent: Optional[ QObject ] = None, logger: Optional[ logging.Logger ] = None ):
        """
        初始化SaveWorker

        Args:
            parent: 父QObject对象，通常应为None，因为会被moveToThread
            logger: 日志记录器实例，如果为None则创建新的logger
        """
        super().__init__( parent )
        self.logger = logger or logging.getLogger( __name__ )
        self.logger.debug( "LineEditSaveWorker已初始化。" )

    @pyqtSlot( list, str, str )
    def perform_save(
        self, data_to_save: List[ Tuple[ str, Optional[ Union[ str, Dict ] ] ] ], config_file_path: str, config_dir_path: str
    ) -> None:
        """
        在工作线程中执行文本输入框内容保存到配置文件的操作。
        
        Args:
            data_to_save: 要保存的数据列表，每项是(widget_key, text)或(widget_key, dict)元组。
                          如果text为None，表示删除该键。
                          如果是dict，包含value和timestamp字段。
                          例如：[("MainWindow_inputField1", {"value": "用户输入的文本", "timestamp": 1683456789.123}), 
                                ("SettingGroup_inputField2", {"value": None, "timestamp": 1683456790.456})]
            config_file_path: 配置文件的完整路径。
            config_dir_path: 配置目录的路径。
        """
        self.logger.info( f"LineEditSaveWorker: 开始保存输入框内容到 {config_file_path}" )
        try:
            # 确保配置目录存在
            os.makedirs( config_dir_path, exist_ok = True )

            # 读取现有配置（如果存在）
            existing_config = {}
            if os.path.exists( config_file_path ):
                try:
                    with open( config_file_path, 'r', encoding = 'utf-8' ) as f:
                        content = f.read()
                        if not content.strip():
                            self.logger.warning( f"配置文件 {config_file_path} 为空，将视为空字典。" )
                            existing_config = {}
                        else:
                            existing_config = json.loads( content )
                    if not isinstance( existing_config, dict ):
                        self.logger.warning( f"配置文件 {config_file_path} 格式错误，重置为空字典。" )
                        self.log_message_signal.emit( f"配置文件格式错误，重置为空", "ORANGE" )
                        existing_config = {}
                except json.JSONDecodeError as jde:
                    self.logger.error( f"读取并解析配置文件失败 (JSONDecodeError): {jde}。文件内容可能已损坏。重置为空字典。" )
                    self.log_message_signal.emit( f"配置文件解析失败: {jde}，重置为空", "RED" )
                    traceback.print_exc()    # 添加错误堆栈打印
                    existing_config = {}
                except Exception as e:
                    self.logger.error( f"读取配置文件时发生未知错误: {e}" )
                    self.log_message_signal.emit( f"读取配置文件失败: {e}", "RED" )
                    traceback.print_exc()
                    existing_config = {}

            # 更新配置
            updated = False
            for widget_key, data in data_to_save:
                # 处理不同数据格式
                if data is None:    # 完全删除该项
                    if widget_key in existing_config:
                        del existing_config[ widget_key ]
                        updated = True
                elif isinstance( data, dict ):    # 新格式：包含value和timestamp
                    if data.get( "value" ) is None:    # 值为None表示删除该项
                        if widget_key in existing_config:
                            del existing_config[ widget_key ]
                            updated = True
                    else:
                        existing_config[ widget_key ] = data
                        updated = True
                else:    # 旧格式：直接存储文本值
                    existing_config[ widget_key ] = data
                    updated = True

            # 如果没有更新，就不需要写入文件
            if not updated:
                self.logger.info( "没有内容变更，跳过保存。" )
                self.save_finished_signal.emit( True, "无内容变更" )
                return

            # 原子化写入配置文件 - 先写临时文件，成功后再替换原文件
            temp_file_path = f"{config_file_path}.tmp"
            try:
                # 写入临时文件
                with open( temp_file_path, 'w', encoding = 'utf-8' ) as f:
                    json.dump( existing_config, f, ensure_ascii = False, indent = 2 )

                # 成功写入临时文件后，替换原文件
                if os.path.exists( config_file_path ):
                    # 在某些文件系统上，可能需要先删除原文件
                    try:
                        os.remove( config_file_path )
                    except OSError:
                        pass    # 如果删除失败，重命名可能会覆盖它

                os.rename( temp_file_path, config_file_path )

                saved_count = len( data_to_save )
                total_count = len( existing_config )
                self.logger.info( f"LineEditSaveWorker: 已保存 {saved_count} 个文本输入框内容到 {config_file_path}，总配置项: {total_count}" )

                # 发送多样式日志消息
                log_parts = [ ( "输入记忆: ", {
                    "color": "GREEN",
                    "bold": True
                } ), ( f"已保存 {saved_count} 个输入框内容", {
                    "color": "BLACK"
                } ) ]
                self.log_multi_style_message_signal.emit( log_parts )
                self.save_finished_signal.emit( True, f"已保存 {saved_count} 个输入框内容" )

            except Exception as e:
                # 保存失败，尝试清理临时文件
                self.logger.error( f"写入配置文件失败: {e}" )
                self.log_message_signal.emit( f"写入配置文件失败: {e}", "RED" )
                traceback.print_exc()

                # 尝试删除临时文件
                try:
                    if os.path.exists( temp_file_path ):
                        os.remove( temp_file_path )
                except Exception as e2:
                    self.logger.error( f"删除临时文件失败: {e2}" )
                    traceback.print_exc()

                self.save_finished_signal.emit( False, f"保存失败: {e}" )
                return

        except Exception as e:
            self.logger.error( f"LineEditSaveWorker: 保存输入框内容失败: {e}" )
            self.log_message_signal.emit( f"保存输入框内容失败: {e}", "RED" )
            traceback.print_exc()
            self.save_finished_signal.emit( False, f"保存失败: {e}" )

    @pyqtSlot( list, str, str )
    def perform_batch_save(self, save_tasks: List[SaveTask], config_file_path: str, config_dir_path: str) -> None:
        """
        批量保存多个输入框内容到配置文件

        Args:
            save_tasks: SaveTask 对象列表
            config_file_path: 配置文件的完整路径
            config_dir_path: 配置目录的路径
        """
        if not save_tasks:
            self.batch_save_finished_signal.emit(0, 0, "没有需要保存的任务")
            return

        self.logger.info(f"开始批量保存 {len(save_tasks)} 个输入框内容")

        success_count = 0
        total_count = len(save_tasks)

        try:
            # 确保配置目录存在
            os.makedirs(config_dir_path, exist_ok=True)

            # 读取现有配置
            existing_config = {}
            if os.path.exists(config_file_path):
                try:
                    with open(config_file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if content.strip():
                            existing_config = json.loads(content)
                        if not isinstance(existing_config, dict):
                            existing_config = {}
                except (json.JSONDecodeError, Exception) as e:
                    self.logger.error(f"读取配置文件失败: {e}")
                    existing_config = {}

            # 按优先级排序任务
            sorted_tasks = sorted(save_tasks, key=lambda x: (x.priority.value, x.timestamp), reverse=True)

            # 批量更新配置
            updated = False
            for task in sorted_tasks:
                try:
                    if task.data.get("value") is None:
                        # 删除项
                        if task.widget_key in existing_config:
                            del existing_config[task.widget_key]
                            updated = True
                            success_count += 1
                    else:
                        # 更新项
                        existing_config[task.widget_key] = task.data
                        updated = True
                        success_count += 1

                except Exception as e:
                    self.logger.error(f"处理任务 {task.widget_key} 失败: {e}")
                    # 继续处理其他任务

            # 如果有更新，写入文件
            if updated:
                # 原子化写入
                temp_file_path = f"{config_file_path}.tmp"
                try:
                    with open(temp_file_path, 'w', encoding='utf-8') as f:
                        json.dump(existing_config, f, ensure_ascii=False, indent=2)

                    # 替换原文件
                    if os.path.exists(config_file_path):
                        try:
                            os.remove(config_file_path)
                        except OSError:
                            pass

                    os.rename(temp_file_path, config_file_path)

                    self.logger.info(f"批量保存完成: {success_count}/{total_count} 成功")

                    # 发送成功日志
                    log_parts = [
                        ("批量保存: ", {"color": "GREEN", "bold": True}),
                        (f"成功保存 {success_count}/{total_count} 个输入框", {"color": "BLACK"})
                    ]
                    self.log_multi_style_message_signal.emit(log_parts)

                except Exception as e:
                    self.logger.error(f"批量写入文件失败: {e}")
                    # 清理临时文件
                    try:
                        if os.path.exists(temp_file_path):
                            os.remove(temp_file_path)
                    except Exception:
                        pass

                    self.batch_save_finished_signal.emit(success_count, total_count, f"写入失败: {e}")
                    return

            self.batch_save_finished_signal.emit(success_count, total_count, f"批量保存完成")

        except Exception as e:
            self.logger.error(f"批量保存过程失败: {e}")
            traceback.print_exc()
            self.batch_save_finished_signal.emit(success_count, total_count, f"批量保存失败: {e}")



class LineEditMemory( QObject ):
    """
    实现 QLineEdit 控件输入记忆功能。

    该类监控一个或多个 QLineEdit 控件，当控件失去焦点且内容发生改变时，
    会自动将其文本内容通过异步方式保存到 JSON 配置文件中。
    下次程序启动时，会自动从配置文件加载并恢复这些文本。

    主要特性:
    - 支持单个或多个 QLineEdit 控件。
    - 支持互斥控件组，组内只有一个控件会被填充值。
    - 配置文件路径和名称可自定义。
    - 使用防抖机制避免频繁写入。
    - 异步保存，不阻塞UI主线程。
    - 通过 LogOutput 实例输出用户操作日志。
    - 详细的开发日志记录。
    - 保存输入时附加时间戳信息。
    - **增强的同步检测功能**：能够检测通过同步机制更新的内容变化。
    - **智能防重复机制**：避免重复记录相同的内容变化。
    - **多层次检测策略**：结合实时检测、定时检测和失焦检测。

    参数:
        line_edits (Union[QLineEdit, List[QLineEdit]]):
            一个或多个 QLineEdit 控件对象。
        log_output (LogOutput):
            LogOutput 类的实例，用于UI日志输出。
        mutual_exclusive_groups (List[Union[QLineEdit, List[QLineEdit]]], optional):
            互斥控件组列表。每个组可以是单个QLineEdit对象或QLineEdit对象的列表。
            组内只有一个控件会被填充值（最后修改的那个）。
            默认为 None，表示没有互斥组。
        config_filename (str, optional):
            配置文件名。默认为 "lineEdit_memory.json"。
        config_path (str, optional):
            配置文件存放目录。默认为 "GUI/config"。
        debounce_interval_ms (int, optional):
            防抖延迟时间（毫秒）。默认为 500。
        sync_detection_interval_ms (int, optional):
            同步检测间隔时间（毫秒）。默认为 1000。用于定期检测通过同步机制更新的内容变化。
        enable_sync_detection (bool, optional):
            是否启用同步检测功能。默认为 True。启用后可以检测通过 QLineEditSynchronizer 等同步工具更新的内容变化。
        parent (QObject, optional):
            Qt父对象。默认为 None。
        logger (Logger, optional):
            日志记录器实例。默认为 None，将使用全局 logger。

    使用示例:
    --------------------------------------------------------------------------
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QTextEdit
    from GUI.utils.ui_tools import LineEditMemory
    from global_tools.ui_tools import LogOutput
    import sys
    import logging

    class MyWindow(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("QLineEdit Memory Test")
            layout = QVBoxLayout(self)

            # 创建日志显示控件
            self.log_display = QTextEdit()
            self.log_display.setReadOnly(True)
            # 创建LogOutput实例
            self.log_output = LogOutput(self.log_display)

            # 创建输入框
            self.lineEdit1 = QLineEdit()
            self.lineEdit1.setObjectName("inputField1")
            self.lineEdit1.setPlaceholderText("Input for field 1 (parent: MyWindow)")

            self.lineEdit2 = QLineEdit()
            self.lineEdit2.setObjectName("inputField2")
            self.lineEdit2.setPlaceholderText("Input for field 2 (parent: MyWindow)")

            # 创建互斥组
            self.lineEdit3 = QLineEdit()
            self.lineEdit3.setObjectName("mutexField1")
            self.lineEdit3.setPlaceholderText("Mutex field 1")

            self.lineEdit4 = QLineEdit()
            self.lineEdit4.setObjectName("mutexField2")
            self.lineEdit4.setPlaceholderText("Mutex field 2")

            # 为父控件设置 objectName 以便生成更可读的key
            self.groupBox = QWidget()
            self.groupBox.setObjectName("SettingsGroup")
            groupLayout = QVBoxLayout(self.groupBox)
            groupLayout.addWidget(self.lineEdit3)
            groupLayout.addWidget(self.lineEdit4)

            layout.addWidget(self.lineEdit1)
            layout.addWidget(self.lineEdit2)
            layout.addWidget(self.groupBox)
            layout.addWidget(self.log_display)

            # 定义互斥组 - 可以是列表列表或者单个控件列表
            mutual_exclusive_groups = [
                [self.lineEdit3, self.lineEdit4],  # 第一个互斥组
                self.lineEdit2  # 第二个互斥组只有一个控件（通常无意义，但为了演示用法）
            ]

            # 实例化 LineEditMemory
            self.memory_handler = LineEditMemory(
                line_edits=[self.lineEdit1, self.lineEdit2, self.lineEdit3, self.lineEdit4],
                log_output=self.log_output,
                mutual_exclusive_groups=mutual_exclusive_groups
                # config_path="custom_config_dir" # 可选
            )
            # 确保父控件也设置了 objectName
            self.setObjectName("MyWindow")

    if __name__ == "__main__":
        # 配置logging基础设置，以便看到 LineEditMemory 的开发日志
        logging.basicConfig(level=logging.DEBUG,
                            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        app = QApplication(sys.argv)
        window = MyWindow()
        window.show()
        sys.exit(app.exec_())
    --------------------------------------------------------------------------
    """

    # 定义保存工作请求信号
    _request_save_on_worker_signal = pyqtSignal( list, str, str )
    _request_batch_save_on_worker_signal = pyqtSignal( list, str, str )  # 批量保存信号

    def __init__(
        self,
        line_edits: Union[ QLineEdit, List[ QLineEdit ] ],
        log_output: LogOutput,
        mutual_exclusive_groups: Optional[ List[ Union[ QLineEdit, List[ QLineEdit ] ] ] ] = None,
        config_filename: str = "lineEdit_memory.json",
        config_path: str = "GUI/config",
        debounce_interval_ms: int = 500,
        sync_detection_interval_ms: int = 1000,
        enable_sync_detection: bool = True,
        parent: Optional[ QObject ] = None,
        logger: Optional[ Logger ] = None
    ):
        """
        初始化LineEditMemory。

        Args:
            line_edits: 一个或多个QLineEdit控件
            log_output: LogOutput实例，用于UI日志输出
            mutual_exclusive_groups: 互斥控件组列表，组内只有一个控件会被填充值
            config_filename: 配置文件名称
            config_path: 配置文件目录
            debounce_interval_ms: 防抖延迟（毫秒）
            sync_detection_interval_ms: 同步检测间隔（毫秒），用于检测同步更新的内容变化
            enable_sync_detection: 是否启用同步检测功能
            parent: 父QObject对象
            logger: 日志记录器实例，如果为None则使用全局logger
        """
        super().__init__( parent )

        # 设置开发日志记录器
        self.logger = Logger()
        self.logger.set_instance_level( LogLevel.DEBUG )

        self.logger.debug( "初始化LineEditMemory" )

        # 将line_edits参数统一处理为列表
        self._line_edits = line_edits if isinstance( line_edits, list ) else [ line_edits ]

        # 存储互斥组信息
        self._mutual_exclusive_groups = []
        if mutual_exclusive_groups:
            for group in mutual_exclusive_groups:
                # 确保每个组都是列表
                if isinstance( group, list ):
                    self._mutual_exclusive_groups.append( group )
                else:
                    # 如果传入的是单个QLineEdit对象，将其包装成列表
                    self._mutual_exclusive_groups.append( [ group ] )
                    self.logger.warning( f"互斥组中检测到非列表项，已自动转换为列表" )

        # 创建互斥组查找表，记录每个控件所属的组
        self._widget_to_group_map = {}
        for group_idx, group in enumerate( self._mutual_exclusive_groups ):
            for widget in group:
                self._widget_to_group_map[ widget ] = group_idx

        # 存储LogOutput实例
        self._log_output = log_output

        # 创建日志处理辅助类
        self._log_helper = LogHandlerHelper( self._log_output )

        # 配置文件相关
        self._config_path = config_path
        self._config_file_path = os.path.join( config_path, config_filename )

        # 初始化数据保存队列（保持向后兼容）
        self._data_to_save_queue = deque()

        # 初始化增强的保存队列
        self._enhanced_save_queue = EnhancedSaveQueue(max_size=1000)

        # 初始化保存操作锁
        self._save_lock = Lock()

        # 保存操作状态标志
        self._is_saving_flag = False

        # 批量保存相关配置
        self._batch_save_enabled = True  # 启用批量保存
        self._batch_size = 10  # 批量保存大小
        self._batch_timeout = 0.5  # 批量等待超时（秒）

        # 控件级别的防抖定时器
        self._widget_timers = {}  # widget_key -> QTimer

        # 防抖定时器设置
        self._save_timer = QTimer( self )
        self._save_timer.setSingleShot( True )
        self._save_timer.timeout.connect( self._process_save_queue )
        self._debounce_interval_ms = debounce_interval_ms

        # 同步检测相关配置
        self._sync_detection_interval_ms = sync_detection_interval_ms
        self._enable_sync_detection = enable_sync_detection

        # 同步检测定时器
        self._sync_detection_timer = QTimer( self )
        self._sync_detection_timer.timeout.connect( self._check_sync_updates )

        # 内容快照，用于检测同步更新
        self._content_snapshots = {}  # widget_key -> {"text": str, "timestamp": float, "hash": str}

        # 同步检测状态标志
        self._sync_detection_running = False

        # 防重复记录的时间窗口（毫秒）
        self._duplicate_prevention_window_ms = 100

        # 防循环检测标志
        self._sync_detection_in_progress = {}  # widget_key -> bool

        # 内容变化历史记录（用于更精确的防重复）
        self._change_history = {}  # widget_key -> List[{"hash": str, "timestamp": float}]
        self._max_history_size = 10  # 每个控件保留的历史记录数量

        # 初始化用于存储QLineEdit获取焦点时文本的字典
        self._initial_texts = {}

        # 初始化一个字典，用于在防抖期间暂存最新的待保存数据
        self._pending_save_data = {}

        # 工作线程和保存工作对象
        self._save_qthread = QThread( self )
        self._save_worker = _LineEditSaveWorker( logger = self.logger )
        self._save_worker.moveToThread( self._save_qthread )

        # 连接保存请求信号到worker的执行方法
        self._request_save_on_worker_signal.connect( self._save_worker.perform_save )
        self._request_batch_save_on_worker_signal.connect( self._save_worker.perform_batch_save )

        # 连接worker的日志信号到主线程的处理方法 - 使用日志处理辅助类
        self._save_worker.log_message_signal.connect( self._log_helper.handle_log_message )
        self._save_worker.log_multi_style_message_signal.connect( self._log_helper.handle_multi_style_message )
        self._save_worker.save_finished_signal.connect( self._handle_save_finished )
        self._save_worker.batch_save_finished_signal.connect( self._handle_batch_save_finished )

        # 启动工作线程
        self._save_qthread.start()
        self.logger.debug( "保存工作线程已启动" )

        # 初始化
        self._ensure_config_dir_exists()
        self._connect_signals()
        self._load_config_and_populate()

        # 启动同步检测
        self._start_sync_detection()

    def _ensure_config_dir_exists( self ) -> None:
        """确保配置文件目录存在"""
        try:
            os.makedirs( self._config_path, exist_ok = True )
            self.logger.info( f"确保配置目录存在: {self._config_path}" )
        except Exception as e:
            self.logger.error( f"创建配置目录失败: {e}" )
            if self._log_output:
                self._log_output.append( f"创建配置目录失败: {e}", color = "red" )
            traceback.print_exc()    # 添加错误堆栈打印

    def _connect_signals( self ) -> None:
        """为所有输入框连接必要的信号和事件过滤器"""
        for line_edit in self._line_edits:
            if not isinstance( line_edit, QLineEdit ):
                self.logger.warning( f"忽略非QLineEdit对象: {line_edit}" )
                continue

            # 检查Qt对象是否有效
            if sip.isdeleted( line_edit ):
                self.logger.warning( f"忽略已被销毁的Qt对象: {line_edit}" )
                continue

            try:
                # 安装事件过滤器
                line_edit.installEventFilter( self )

                # 连接文本变化信号，用于捕获通过编程方式更改的文本
                line_edit.textChanged.connect( partial( self._handle_text_changed, line_edit ) )

                widget_key = self._get_widget_key( line_edit )
                if widget_key:
                    self.logger.debug( f"已为输入框 '{widget_key}' 安装事件过滤器和信号" )
                else:
                    self.logger.warning( f"输入框缺少有效的对象名: {line_edit}" )
            except Exception as e:
                self.logger.error( f"连接输入框信号失败: {e}" )
                if self._log_output:
                    self._log_output.append( f"连接输入框信号失败: {e}", color = "red" )
                traceback.print_exc()    # 添加错误堆栈打印

    def _handle_text_changed( self, line_edit_widget: QLineEdit, new_text: str ) -> None:
        """
        处理文本变化事件，用于捕获通过编程方式更改的文本
        
        Args:
            line_edit_widget: QLineEdit对象
            new_text: 新文本
        """
        # 检查对象是否有效
        if sip.isdeleted( line_edit_widget ):
            return

        widget_key = self._get_widget_key( line_edit_widget )
        if not widget_key:
            return

        # 更新初始文本记录
        old_text = self._initial_texts.get( widget_key, "" )

        if old_text != new_text:
            self.logger.debug( f"输入框 '{widget_key}' 文本通过编程方式变化: '{old_text}' -> '{new_text}'" )
            self._initial_texts[ widget_key ] = new_text

            # 检查是否有实际值并保存
            has_value = bool( new_text and new_text.strip() )
            current_timestamp = time.time()

            if not has_value:
                # 如果没有值，标记为删除
                self._pending_save_data[ widget_key ] = {
                    "value": None,
                    "timestamp": current_timestamp,
                    "has_value": False
                }
                self.logger.info( f"文本变化: 输入框 '{widget_key}' 内容为空或无有效值，将从配置中删除" )
            else:
                # 有值，保存
                self._pending_save_data[ widget_key ] = {
                    "value": new_text,
                    "timestamp": current_timestamp,
                    "has_value": True
                }
                self.logger.info( f"文本变化: 输入框 '{widget_key}' 有值，将保存: '{new_text}'" )

            # 启动防抖定时器
            self._save_timer.start( self._debounce_interval_ms )

    def eventFilter( self, watched_object, event ) -> bool:
        """
        事件过滤器，捕获QLineEdit的焦点获取和失去事件
        
        Args:
            watched_object: 被监视的Qt对象
            event: Qt事件对象
            
        Returns:
            bool: 是否已处理事件
        """
        # 检查对象是否有效且在管理列表中
        if ( watched_object in self._line_edits and not sip.isdeleted( watched_object ) ):
            event_type = event.type()
            # 使用QEvent.Type枚举
            if event_type == QEvent.Type.FocusIn:    # 8
                self._handle_focus_in( watched_object )
            elif event_type == QEvent.Type.FocusOut:    # 9
                self._handle_focus_out( watched_object )

        # 将事件传递给基类处理
        return super().eventFilter( watched_object, event )

    def _handle_focus_in( self, line_edit_widget: QLineEdit ) -> None:
        """
        处理输入框获取焦点事件
        
        Args:
            line_edit_widget: 获取焦点的QLineEdit对象
        """
        # 检查对象是否有效
        if sip.isdeleted( line_edit_widget ):
            self.logger.warning( "尝试处理已销毁的输入框对象" )
            return

        widget_key = self._get_widget_key( line_edit_widget )
        if widget_key:
            # 记录获取焦点时的文本，用于后续比较是否变化
            current_text = line_edit_widget.text()
            self._initial_texts[ widget_key ] = current_text
            self.logger.debug( f"输入框 '{widget_key}' 获取焦点，初始文本: '{current_text}'" )

    def _handle_focus_out( self, line_edit_widget: QLineEdit ) -> None:
        """
        处理输入框失去焦点事件
        
        Args:
            line_edit_widget: 失去焦点的QLineEdit对象
        """
        # 检查对象是否有效
        if sip.isdeleted( line_edit_widget ):
            self.logger.warning( "尝试处理已销毁的输入框对象" )
            return

        widget_key = self._get_widget_key( line_edit_widget )
        if not widget_key:
            self.logger.warning( f"无法获取输入框的唯一键: {line_edit_widget}" )
            return

        current_text = line_edit_widget.text()
        initial_text = self._initial_texts.get( widget_key, "" )

        # 检查QLineEdit是否有值
        has_value = bool( current_text and current_text.strip() )

        # 无论是否变化，都保存当前文本，确保数据始终最新
        self.logger.info( f"输入框 '{widget_key}' 失去焦点，当前文本: '{current_text}'" )

        # 添加时间戳
        current_timestamp = time.time()

        # 如果文本为空或没有实际值，标记为删除
        if not has_value:
            # 如果之前有配置记录，则删除；否则不进行任何操作
            self._pending_save_data[ widget_key ] = {
                "value": None,
                "timestamp": current_timestamp,
                "has_value": False
            }
            self.logger.info( f"输入框 '{widget_key}' 内容为空或无有效值，将从配置中删除" )
        else:
            # 有实际值，保存
            self._pending_save_data[ widget_key ] = {
                "value": current_text,
                "timestamp": current_timestamp,
                "has_value": True
            }
            self.logger.info( f"输入框 '{widget_key}' 有值，将保存: '{current_text}'" )

        # 使用增强的队列写入机制
        if self._batch_save_enabled:
            self._add_to_enhanced_queue(widget_key, current_text, has_value, current_timestamp)
        else:
            # 启动防抖定时器（保持向后兼容）
            self._save_timer.start( self._debounce_interval_ms )

    def _get_widget_key( self, line_edit_widget: QLineEdit ) -> Optional[ str ]:
        """
        为输入框生成唯一的键名
        
        使用格式: "父容器名称_输入框名称"
        如果父容器没有名称，则只使用输入框名称
        
        Args:
            line_edit_widget: QLineEdit控件
            
        Returns:
            Optional[str]: 生成的键名，如果无法生成则返回None
        """
        # 检查对象是否有效
        if not line_edit_widget or sip.isdeleted( line_edit_widget ) or not hasattr( line_edit_widget, 'objectName' ):
            return None

        widget_name = line_edit_widget.objectName()
        if not widget_name:
            self.logger.warning( f"输入框没有设置objectName: {line_edit_widget}" )
            return None

        try:
            parent_widget = line_edit_widget.parentWidget()
            parent_name = parent_widget.objectName() if parent_widget and hasattr( parent_widget, 'objectName' ) else None

            # 生成键名
            if parent_name and parent_name.strip():
                key = f"{parent_name}_{widget_name}"
            else:
                key = widget_name

            return key
        except Exception as e:
            self.logger.error( f"获取widget_key时发生错误: {e}" )
            traceback.print_exc()    # 添加错误堆栈打印
            return None

    def _load_config_and_populate( self ) -> None:
        """加载配置文件并填充输入框"""
        if not os.path.exists( self._config_file_path ):
            self.logger.info( f"配置文件不存在: {self._config_file_path}, 将在首次保存时创建" )
            if self._log_output:
                self._log_output.append( "输入记忆配置文件不存在，将在首次使用时创建", color = "orange" )
            return

        try:
            # 读取配置文件
            with open( self._config_file_path, 'r', encoding = 'utf-8' ) as f:
                content = f.read()

                # 处理空文件情况
                if not content.strip():
                    self.logger.warning( f"配置文件 {self._config_file_path} 为空，将视为空字典" )
                    if self._log_output:
                        self._log_output.append( "输入记忆配置文件为空", color = "orange" )
                    return

                # 解析JSON
                saved_data = json.loads( content )

            if not isinstance( saved_data, dict ):
                self.logger.error( f"配置文件格式错误: {self._config_file_path}" )
                if self._log_output:
                    self._log_output.append( "输入记忆配置文件格式错误", color = "red" )
                return

            # 提取互斥组的最新数据
            exclusive_group_latest = {}
            if self._mutual_exclusive_groups:
                # 为每个互斥组找出最新的输入
                for group_idx in range( len( self._mutual_exclusive_groups ) ):
                    latest_widget = None
                    latest_timestamp = 0
                    latest_value = None

                    # 遍历组内所有控件
                    for widget in self._mutual_exclusive_groups[ group_idx ]:
                        widget_key = self._get_widget_key( widget )
                        if not widget_key or widget_key not in saved_data:
                            continue

                        # 兼容旧格式（没有时间戳的数据）
                        if isinstance( saved_data[ widget_key ], dict ):
                            value_data = saved_data[ widget_key ]
                            timestamp = value_data.get( "timestamp", 0 )
                            value = value_data.get( "value" )
                            has_value = value_data.get( "has_value", bool( value and value.strip() ) )
                        else:
                            timestamp = 0
                            value = saved_data[ widget_key ]
                            has_value = bool( value and value.strip() )

                        if value is not None and has_value and ( latest_widget is None or timestamp > latest_timestamp ):
                            latest_widget = widget
                            latest_timestamp = timestamp
                            latest_value = value

                    if latest_widget is not None:
                        exclusive_group_latest[ group_idx ] = ( latest_widget, latest_value )

            # 填充输入框
            applied_count = 0
            for line_edit in self._line_edits:
                # 检查对象是否有效
                if sip.isdeleted( line_edit ):
                    self.logger.warning( f"跳过已销毁的输入框对象" )
                    continue

                widget_key = self._get_widget_key( line_edit )
                if not widget_key:
                    continue

                # 检查控件是否在互斥组中
                group_idx = self._widget_to_group_map.get( line_edit )

                # 如果在互斥组中，且不是组内最新值的控件，则跳过
                if group_idx is not None:
                    if group_idx in exclusive_group_latest:
                        latest_widget, latest_value = exclusive_group_latest[ group_idx ]
                        if line_edit != latest_widget:
                            self.logger.debug( f"互斥组 {group_idx} 中的控件 '{widget_key}' 不是最新值的控件，跳过填充" )
                            continue
                        else:
                            # 是最新值的控件，使用最新值填充
                            line_edit.setText( latest_value )
                            applied_count += 1
                            self.logger.debug( f"已为互斥组 {group_idx} 中的控件 '{widget_key}' 设置最新值: '{latest_value}'" )
                    continue

                # 非互斥组控件的常规处理
                if widget_key in saved_data:
                    # 兼容新旧格式
                    if isinstance( saved_data[ widget_key ], dict ):
                        value = saved_data[ widget_key ].get( "value" )
                        has_value = saved_data[ widget_key ].get( "has_value", bool( value and value.strip() ) )
                    else:
                        value = saved_data[ widget_key ]
                        has_value = bool( value and value.strip() )

                    if value is not None and has_value:
                        line_edit.setText( value )
                        applied_count += 1
                        self.logger.debug( f"已恢复输入框 '{widget_key}' 的文本: '{value}'" )

            if applied_count > 0:
                self.logger.info( f"已从配置加载并应用 {applied_count}/{len(self._line_edits)} 个输入框内容" )
                if self._log_output:
                    self._log_output.append_multi_style( [ ( "输入记忆: ", {
                        "color": "green",
                        "bold": True
                    } ), ( f"已恢复 {applied_count} 个输入框的内容", {
                        "color": "black"
                    } ) ] )
            else:
                self.logger.info( "未找到匹配的输入框内容可恢复" )

        except json.JSONDecodeError as e:
            self.logger.error( f"解析配置文件失败: {e}" )
            if self._log_output:
                self._log_output.append( f"输入记忆配置文件格式错误: {e}", color = "red" )
            traceback.print_exc()    # 添加错误堆栈打印
        except Exception as e:
            self.logger.error( f"加载配置文件时发生未知错误: {e}" )
            if self._log_output:
                self._log_output.append( f"加载输入记忆失败: {e}", color = "red" )
            traceback.print_exc()    # 添加错误堆栈打印

    def _process_save_queue( self ) -> None:
        """处理保存队列（定时器触发）"""
        if not self._pending_save_data:
            self.logger.debug( "没有待保存的数据" )
            return

        # 将pending数据转移到保存队列
        for key, value_data in self._pending_save_data.items():
            self._data_to_save_queue.append( ( key, value_data ) )

        # 清空待保存数据
        self._pending_save_data.clear()

        # 如果已有保存任务在运行，直接返回
        if self._is_saving_flag:
            self.logger.info( "已有保存任务在运行，新数据已添加到队列" )
            return

        # 如果队列为空，直接返回
        if not self._data_to_save_queue:
            self.logger.debug( "保存队列为空" )
            return

        # 设置保存标志
        self._is_saving_flag = True

        # 准备数据，发送到工作线程
        data_to_save = list( self._data_to_save_queue )
        self._data_to_save_queue.clear()

        # 发送信号给工作线程
        self._request_save_on_worker_signal.emit( data_to_save, self._config_file_path, self._config_path )
        self.logger.info( f"已向工作线程发出保存 {len(data_to_save)} 个输入框内容的请求" )

    @pyqtSlot( str, str )
    def _handle_worker_log( self, message: str, color_name: str ) -> None:
        """
        处理来自工作线程的日志消息。
        此方法已被_log_helper.handle_log_message替代，保留是为了兼容性。
        
        Args:
            message: 日志消息
            color_name: 颜色名称或十六进制值
        """
        self._log_helper.handle_log_message( message, color_name )

    @pyqtSlot( list )
    def _handle_worker_log_multi_style( self, parts: list ) -> None:
        """
        处理来自工作线程的多样式日志消息。
        此方法已被_log_helper.handle_multi_style_message替代，保留是为了兼容性。
        
        Args:
            parts: 包含文本和样式的元组列表
        """
        self._log_helper.handle_multi_style_message( parts )

    @pyqtSlot( bool, str )
    def _handle_save_finished( self, success: bool, message: str ) -> None:
        """
        处理保存完成的回调
        
        Args:
            success: 是否成功
            message: 消息内容
        """
        # 重置保存标志
        self._is_saving_flag = False

        # 记录结果
        if success:
            self.logger.info( f"保存成功: {message}" )
            # 如果需要在UI上显示成功消息，取消下面的注释
            # self._log_output.append(message, color="green")
        else:
            self.logger.error( f"保存失败: {message}" )
            if self._log_output:
                self._log_output.append( f"保存输入内容失败: {message}", color = "red" )

        # 检查是否有新的待保存项
        if self._pending_save_data:
            self.logger.info( "检测到新的待保存数据，立即处理" )
            self._process_save_queue()

    def _add_to_enhanced_queue(self, widget_key: str, current_text: str, has_value: bool, timestamp: float) -> None:
        """
        添加保存任务到增强队列

        Args:
            widget_key: 控件键名
            current_text: 当前文本
            has_value: 是否有值
            timestamp: 时间戳
        """
        try:
            # 创建保存任务
            data = {
                "value": current_text if has_value else None,
                "timestamp": timestamp,
                "has_value": has_value
            }

            # 确定优先级
            priority = SavePriority.NORMAL
            if not has_value:
                priority = SavePriority.LOW  # 删除操作优先级较低

            task = SaveTask(
                widget_key=widget_key,
                data=data,
                priority=priority,
                timestamp=timestamp
            )

            # 添加到增强队列
            if self._enhanced_save_queue.put_task(task):
                self.logger.debug(f"已添加保存任务到增强队列: {widget_key}")

                # 启动或重启控件级别的防抖定时器
                self._start_widget_timer(widget_key)
            else:
                self.logger.warning(f"增强队列已满，无法添加任务: {widget_key}")
                # 回退到原始方法
                self._pending_save_data[widget_key] = data
                self._save_timer.start(self._debounce_interval_ms)

        except Exception as e:
            self.logger.error(f"添加到增强队列失败: {e}")
            # 回退到原始方法
            self._pending_save_data[widget_key] = {
                "value": current_text if has_value else None,
                "timestamp": timestamp,
                "has_value": has_value
            }
            self._save_timer.start(self._debounce_interval_ms)

    def _start_widget_timer(self, widget_key: str) -> None:
        """
        启动控件级别的防抖定时器

        Args:
            widget_key: 控件键名
        """
        try:
            # 如果已有定时器，先停止
            if widget_key in self._widget_timers:
                self._widget_timers[widget_key].stop()
            else:
                # 创建新的定时器
                timer = QTimer(self)
                timer.setSingleShot(True)
                timer.timeout.connect(lambda: self._process_enhanced_queue())
                self._widget_timers[widget_key] = timer

            # 启动定时器
            self._widget_timers[widget_key].start(self._debounce_interval_ms)

        except Exception as e:
            self.logger.error(f"启动控件定时器失败: {e}")

    def _process_enhanced_queue(self) -> None:
        """
        处理增强保存队列
        """
        try:
            if self._enhanced_save_queue.is_empty():
                self.logger.debug("增强队列为空")
                return

            # 如果已有保存任务在运行，延迟处理
            if self._is_saving_flag:
                self.logger.info("已有保存任务在运行，延迟处理增强队列")
                # 延迟100ms后重试
                QTimer.singleShot(100, self._process_enhanced_queue)
                return

            # 批量获取任务
            tasks = self._enhanced_save_queue.get_batch_tasks(
                max_count=self._batch_size,
                timeout=self._batch_timeout
            )

            if not tasks:
                self.logger.debug("没有获取到保存任务")
                return

            # 设置保存标志
            self._is_saving_flag = True

            self.logger.info(f"开始处理 {len(tasks)} 个增强队列保存任务")

            # 发送批量保存信号到工作线程
            self._request_batch_save_on_worker_signal.emit(
                tasks,
                self._config_file_path,
                self._config_path
            )

        except Exception as e:
            self.logger.error(f"处理增强队列失败: {e}")
            self._is_saving_flag = False

    @pyqtSlot(int, int, str)
    def _handle_batch_save_finished(self, success_count: int, total_count: int, message: str) -> None:
        """
        处理批量保存完成的回调

        Args:
            success_count: 成功保存的数量
            total_count: 总任务数量
            message: 消息内容
        """
        # 重置保存标志
        self._is_saving_flag = False

        # 记录结果
        if success_count == total_count:
            self.logger.info(f"批量保存全部成功: {success_count}/{total_count}")
        elif success_count > 0:
            self.logger.warning(f"批量保存部分成功: {success_count}/{total_count}")
            if self._log_output:
                self._log_output.append(f"部分保存失败: {message}", color="orange")
        else:
            self.logger.error(f"批量保存全部失败: {message}")
            if self._log_output:
                self._log_output.append(f"批量保存失败: {message}", color="red")

        # 检查是否还有任务需要处理
        if not self._enhanced_save_queue.is_empty():
            self.logger.info("检测到更多增强队列任务，继续处理")
            # 延迟50ms后继续处理，避免过于频繁
            QTimer.singleShot(50, self._process_enhanced_queue)

    def add_line_edit( self, line_edit: QLineEdit, mutual_exclusive_group_index: Optional[ int ] = None ) -> bool:
        """
        动态添加一个新的QLineEdit控件到管理列表
        
        Args:
            line_edit: 要添加的QLineEdit控件
            mutual_exclusive_group_index: 互斥组索引，如果指定，将控件添加到指定的互斥组
            
        Returns:
            bool: 是否成功添加
            
        示例:
            ```python
            # 假设已经创建了LineEditMemory实例memory_handler
            new_line_edit = QLineEdit()
            new_line_edit.setObjectName("dynamicInput")
            success = memory_handler.add_line_edit(new_line_edit)
            if success:
                print("成功添加输入框到记忆管理")
            
            # 添加到互斥组
            new_mutex_line_edit = QLineEdit()
            new_mutex_line_edit.setObjectName("newMutexInput")
            success = memory_handler.add_line_edit(new_mutex_line_edit, 0)  # 添加到第一个互斥组
            if success:
                print(f"成功添加输入框到互斥组")
            
            # 批量添加多个输入框
            line_edit1 = QLineEdit()
            line_edit1.setObjectName("input1")
            line_edit2 = QLineEdit()
            line_edit2.setObjectName("input2")
            for edit in [line_edit1, line_edit2]:
                if memory_handler.add_line_edit(edit):
                    print(f"成功添加输入框 {edit.objectName()}")
            ```
        """
        # 检查是否是QLineEdit实例
        if not isinstance( line_edit, QLineEdit ):
            self.logger.warning( f"尝试添加非QLineEdit对象: {line_edit}" )
            return False

        # 检查对象是否有效
        if sip.isdeleted( line_edit ):
            self.logger.warning( f"尝试添加已销毁的Qt对象: {line_edit}" )
            return False

        # 检查是否已经在管理列表中
        if line_edit in self._line_edits:
            self.logger.warning( f"输入框已在管理列表中: {line_edit}" )
            return False

        # 获取控件键名
        widget_key = self._get_widget_key( line_edit )
        if not widget_key:
            self.logger.warning( f"无法为输入框生成有效的键名: {line_edit}" )
            return False

        # 添加到管理列表
        self._line_edits.append( line_edit )

        # 如果指定了互斥组索引，添加到对应的互斥组
        if mutual_exclusive_group_index is not None:
            # 检查索引是否有效
            if mutual_exclusive_group_index < 0 or mutual_exclusive_group_index >= len( self._mutual_exclusive_groups ):
                # 如果索引超出范围，创建新的互斥组
                if mutual_exclusive_group_index < 0:
                    self.logger.warning( f"互斥组索引 {mutual_exclusive_group_index} 小于0，将创建新的互斥组" )
                    mutual_exclusive_group_index = len( self._mutual_exclusive_groups )
                else:
                    self.logger.warning( f"互斥组索引 {mutual_exclusive_group_index} 超出范围，将创建新的互斥组" )

                # 创建新的互斥组
                while len( self._mutual_exclusive_groups ) <= mutual_exclusive_group_index:
                    self._mutual_exclusive_groups.append( [] )

            # 添加到互斥组
            self._mutual_exclusive_groups[ mutual_exclusive_group_index ].append( line_edit )
            # 更新互斥组查找表
            self._widget_to_group_map[ line_edit ] = mutual_exclusive_group_index
            self.logger.info( f"输入框 '{widget_key}' 已添加到互斥组 {mutual_exclusive_group_index}" )

        # 安装事件过滤器
        line_edit.installEventFilter( self )

        # 连接文本变化信号，用于捕获通过编程方式更改的文本
        line_edit.textChanged.connect( partial( self._handle_text_changed, line_edit ) )

        # 尝试从配置加载内容
        if os.path.exists( self._config_file_path ):
            try:
                with open( self._config_file_path, 'r', encoding = 'utf-8' ) as f:
                    content = f.read()

                    # 处理空文件情况
                    if not content.strip():
                        self.logger.debug( f"配置文件为空，跳过加载内容" )
                        return True

                    saved_data = json.loads( content )

                # 检查是否在互斥组中，如果是，判断是否应该恢复值
                if mutual_exclusive_group_index is not None:
                    group = self._mutual_exclusive_groups[ mutual_exclusive_group_index ]
                    latest_widget = None
                    latest_timestamp = 0
                    latest_value = None

                    # 找出互斥组中最新的值
                    for widget in group:
                        group_widget_key = self._get_widget_key( widget )
                        if not group_widget_key or group_widget_key not in saved_data:
                            continue

                        # 兼容旧格式（没有时间戳的数据）
                        if isinstance( saved_data[ group_widget_key ], dict ):
                            value_data = saved_data[ group_widget_key ]
                            timestamp = value_data.get( "timestamp", 0 )
                            value = value_data.get( "value" )
                            has_value = value_data.get( "has_value", bool( value and value.strip() ) )
                        else:
                            timestamp = 0
                            value = saved_data[ group_widget_key ]
                            has_value = bool( value and value.strip() )

                        if value is not None and has_value and ( latest_widget is None or timestamp > latest_timestamp ):
                            latest_widget = widget
                            latest_timestamp = timestamp
                            latest_value = value

                    # 如果当前添加的控件是组内最新的，则设置值
                    if latest_widget == line_edit and latest_value is not None:
                        line_edit.setText( latest_value )
                        self.logger.info( f"已为互斥组中的新输入框 '{widget_key}' 恢复内容: '{latest_value}'" )
                        return True

                    # 不是最新的，跳过设置值
                    return True

                # 非互斥组控件的处理
                if isinstance( saved_data, dict ):
                    if widget_key in saved_data:
                        # 兼容新旧格式
                        if isinstance( saved_data[ widget_key ], dict ):
                            value = saved_data[ widget_key ].get( "value" )
                            has_value = saved_data[ widget_key ].get( "has_value", bool( value and value.strip() ) )
                        else:
                            value = saved_data[ widget_key ]
                            has_value = bool( value and value.strip() )

                        if value is not None and has_value:
                            line_edit.setText( value )
                            self.logger.info( f"已为新添加的输入框 '{widget_key}' 恢复内容: '{value}'" )

            except Exception as e:
                self.logger.error( f"为新添加的输入框加载内容时出错: {e}" )
                traceback.print_exc()    # 添加错误堆栈打印

        self.logger.info( f"已添加输入框 '{widget_key}' 到管理列表" )
        return True

    def remove_line_edit( self, line_edit: QLineEdit ) -> bool:
        """
        从管理列表中移除QLineEdit控件
        
        Args:
            line_edit: 要移除的QLineEdit控件
            
        Returns:
            bool: 是否成功移除
            
        示例:
            ```python
            # 假设已经创建了LineEditMemory实例memory_handler
            # 并且之前添加了一个叫做line_edit1的控件
            success = memory_handler.remove_line_edit(line_edit1)
            if success:
                print("成功从记忆管理中移除输入框")
            
            # 尝试移除所有符合条件的输入框
            line_edits_to_remove = [edit for edit in all_edits if not edit.isEnabled()]
            for edit in line_edits_to_remove:
                if memory_handler.remove_line_edit(edit):
                    print(f"成功移除禁用的输入框 {edit.objectName()}")
            ```
        """
        # 检查对象是否有效
        if sip.isdeleted( line_edit ):
            self.logger.warning( f"尝试移除已销毁的Qt对象: {line_edit}" )
            return False

        if line_edit not in self._line_edits:
            self.logger.warning( f"输入框不在管理列表中: {line_edit}" )
            return False

        # 获取控件键名
        widget_key = self._get_widget_key( line_edit )

        # 从互斥组中移除
        group_idx = self._widget_to_group_map.get( line_edit )
        if group_idx is not None and 0 <= group_idx < len( self._mutual_exclusive_groups ):
            if line_edit in self._mutual_exclusive_groups[ group_idx ]:
                self._mutual_exclusive_groups[ group_idx ].remove( line_edit )
                self.logger.info( f"输入框 '{widget_key}' 已从互斥组 {group_idx} 中移除" )

            # 从互斥组查找表中移除
            del self._widget_to_group_map[ line_edit ]

        # 移除事件过滤器和信号连接
        try:
            line_edit.removeEventFilter( self )
            # 断开文本变化信号
            try:
                line_edit.textChanged.disconnect()
            except Exception:
                # 如果没有连接的信号，disconnect会抛出异常，忽略它
                pass
        except Exception as e:
            self.logger.warning( f"清理过程中移除事件过滤器或断开信号失败: {e}" )
            traceback.print_exc()    # 添加错误堆栈打印

        # 从管理列表移除
        self._line_edits.remove( line_edit )

        # 从初始文本字典中移除
        if widget_key and widget_key in self._initial_texts:
            del self._initial_texts[ widget_key ]

        # 从待保存数据中移除
        if widget_key and widget_key in self._pending_save_data:
            del self._pending_save_data[ widget_key ]

        self.logger.info( f"已移除输入框 '{widget_key}' 从管理列表" )
        return True

    def cleanup( self ) -> None:
        """
        清理资源，应用程序退出前调用
        
        此方法会执行以下清理操作：
        1. 停止定时器
        2. 将待处理数据保存到配置文件
        3. 等待工作线程结束
        4. 移除所有事件过滤器
        5. 清空所有数据结构
        
        在应用程序关闭前，应主动调用此方法以确保数据被正确保存并释放资源。
        
        示例:
            ```python
            # 在窗口关闭事件中调用
            def closeEvent(self, event):
                # 确保输入记忆被保存
                self.memory_handler.cleanup()
                # 调用基类方法继续关闭流程
                super().closeEvent(event)
                
            # 或者在应用程序退出前的任何适当位置调用
            app = QApplication(sys.argv)
            # ... 应用程序初始化代码 ...
            # 在退出前连接清理方法
            app.aboutToQuit.connect(memory_handler.cleanup)
            ```
        """
        self.logger.debug( "开始清理LineEditMemory资源" )

        # 停止定时器
        if self._save_timer.isActive():
            self._save_timer.stop()

        # 停止同步检测定时器
        if hasattr(self, '_sync_detection_timer') and self._sync_detection_timer.isActive():
            self._sync_detection_timer.stop()
            self.logger.debug("同步检测定时器已停止")

        # 立即保存待处理数据
        if self._pending_save_data:
            self.logger.info( "检测到待保存数据，在清理前进行最后保存" )
            self._process_save_queue()

        # 等待工作线程结束
        if self._save_qthread.isRunning():
            self.logger.debug( "等待工作线程结束..." )
            self._save_qthread.quit()
            if not self._save_qthread.wait( 5000 ):    # 等待最多5秒
                self.logger.warning( "工作线程未能在5秒内结束，尝试强制终止" )
                self._save_qthread.terminate()
                self._save_qthread.wait()

        # 移除所有事件过滤器
        for line_edit in list( self._line_edits ):
            if line_edit and not sip.isdeleted( line_edit ):
                try:
                    line_edit.removeEventFilter( self )
                except Exception as e:
                    self.logger.warning( f"清理过程中移除事件过滤器失败: {e}" )
                    traceback.print_exc()    # 添加错误堆栈打印

        # 清空数据结构
        self._line_edits.clear()
        self._initial_texts.clear()
        self._pending_save_data.clear()
        self._data_to_save_queue.clear()
        self._mutual_exclusive_groups.clear()    # 清空互斥组列表
        self._widget_to_group_map.clear()    # 清空互斥组查找表

        # 清空同步检测相关数据结构
        if hasattr(self, '_content_snapshots'):
            self._content_snapshots.clear()
        if hasattr(self, '_widget_timers'):
            # 停止并清理控件定时器
            for timer in self._widget_timers.values():
                if timer.isActive():
                    timer.stop()
            self._widget_timers.clear()
        if hasattr(self, '_sync_detection_in_progress'):
            self._sync_detection_in_progress.clear()
        if hasattr(self, '_change_history'):
            self._change_history.clear()

        self.logger.info( "LineEditMemory资源已清理完毕" )

    def __del__( self ):
        """析构函数，确保资源被释放"""
        try:
            self.cleanup()
        except Exception as e:
            # 仅记录错误但不抛出异常
            print( f"LineEditMemory析构时发生错误: {e}" )
            traceback.print_exc()

    def save_current_text( self, line_edit_widget: QLineEdit ) -> None:
        """
        强制保存指定输入框的当前文本
        
        Args:
            line_edit_widget: 要保存的QLineEdit对象
        """
        # 检查对象是否有效
        if sip.isdeleted( line_edit_widget ):
            self.logger.warning( "尝试保存已销毁的输入框对象的文本" )
            return

        widget_key = self._get_widget_key( line_edit_widget )
        if not widget_key:
            self.logger.warning( f"无法获取输入框的唯一键: {line_edit_widget}" )
            return

        current_text = line_edit_widget.text()
        # 检查QLineEdit是否有值
        has_value = bool( current_text and current_text.strip() )

        # 添加时间戳
        current_timestamp = time.time()

        if not has_value:
            # 如果没有值，标记为删除
            self._pending_save_data[ widget_key ] = {
                "value": None,
                "timestamp": current_timestamp,
                "has_value": False
            }
            self.logger.info( f"强制保存: 输入框 '{widget_key}' 内容为空或无有效值，将从配置中删除" )
        else:
            # 有值，保存
            self._pending_save_data[ widget_key ] = {
                "value": current_text,
                "timestamp": current_timestamp,
                "has_value": True
            }
            self.logger.info( f"强制保存: 输入框 '{widget_key}' 有值，将保存: '{current_text}'" )

        # 更新初始文本记录
        self._initial_texts[ widget_key ] = current_text

        # 启动防抖定时器
        if self._save_timer.isActive():
            self._save_timer.stop()
        self._save_timer.start( 0 )    # 立即处理

    def save_all_texts( self ) -> None:
        """
        强制保存所有输入框的当前文本
        
        示例:
            ```python
            # 手动触发保存所有文本输入框内容
            self.memory_handler.save_all_texts()
            ```
        """
        self.logger.info( "开始批量保存所有输入框内容" )

        # 遍历所有输入框
        for line_edit in self._line_edits:
            if sip.isdeleted( line_edit ):
                continue

            widget_key = self._get_widget_key( line_edit )
            if not widget_key:
                continue

            current_text = line_edit.text()
            has_value = bool( current_text and current_text.strip() )
            current_timestamp = time.time()

            if not has_value:
                self._pending_save_data[ widget_key ] = {
                    "value": None,
                    "timestamp": current_timestamp,
                    "has_value": False
                }
            else:
                self._pending_save_data[ widget_key ] = {
                    "value": current_text,
                    "timestamp": current_timestamp,
                    "has_value": True
                }

            # 更新初始文本记录
            self._initial_texts[ widget_key ] = current_text

        # 如果有数据要保存，立即处理
        if self._pending_save_data:
            self.logger.info( f"批量保存 {len(self._pending_save_data)} 个输入框内容" )
            if self._save_timer.isActive():
                self._save_timer.stop()
            self._save_timer.start( 0 )    # 立即处理

    # =================================================================================
    # 增强队列写入控制方法
    # =================================================================================

    def enable_batch_save(self, enabled: bool = True) -> None:
        """
        启用或禁用批量保存功能

        Args:
            enabled: 是否启用批量保存

        使用示例:
            ```python
            # 启用批量保存（默认）
            memory.enable_batch_save(True)

            # 禁用批量保存，使用原始的单个保存方式
            memory.enable_batch_save(False)
            ```
        """
        self._batch_save_enabled = enabled
        self.logger.info(f"批量保存功能已{'启用' if enabled else '禁用'}")

    def set_batch_config(self, batch_size: int = 10, batch_timeout: float = 0.5) -> None:
        """
        配置批量保存参数

        Args:
            batch_size: 批量保存的最大任务数量
            batch_timeout: 批量等待超时时间（秒）

        使用示例:
            ```python
            # 设置批量大小为20，超时时间为1秒
            memory.set_batch_config(batch_size=20, batch_timeout=1.0)
            ```
        """
        self._batch_size = max(1, batch_size)
        self._batch_timeout = max(0.1, batch_timeout)
        self.logger.info(f"批量保存配置已更新: 大小={self._batch_size}, 超时={self._batch_timeout}s")

    def get_queue_status(self) -> Dict[str, Any]:
        """
        获取队列状态信息

        Returns:
            Dict[str, Any]: 包含队列状态的字典

        使用示例:
            ```python
            status = memory.get_queue_status()
            print(f"队列大小: {status['queue_size']}")
            print(f"批量保存启用: {status['batch_enabled']}")
            ```
        """
        return {
            "queue_size": self._enhanced_save_queue.size(),
            "is_empty": self._enhanced_save_queue.is_empty(),
            "batch_enabled": self._batch_save_enabled,
            "batch_size": self._batch_size,
            "batch_timeout": self._batch_timeout,
            "is_saving": self._is_saving_flag,
            "pending_data_count": len(self._pending_save_data),
            "widget_timers_count": len(self._widget_timers)
        }

    def force_save_all(self, priority: SavePriority = SavePriority.HIGH) -> None:
        """
        强制保存所有待保存的数据

        Args:
            priority: 保存优先级

        使用示例:
            ```python
            # 高优先级强制保存所有数据
            memory.force_save_all(SavePriority.HIGH)

            # 关键优先级强制保存（程序退出时）
            memory.force_save_all(SavePriority.CRITICAL)
            ```
        """
        try:
            # 停止所有控件定时器
            for timer in self._widget_timers.values():
                if timer.isActive():
                    timer.stop()

            # 将所有待保存数据添加到增强队列
            current_timestamp = time.time()
            for widget_key, data in self._pending_save_data.items():
                task = SaveTask(
                    widget_key=widget_key,
                    data=data,
                    priority=priority,
                    timestamp=current_timestamp
                )
                self._enhanced_save_queue.put_task(task)

            # 清空待保存数据
            self._pending_save_data.clear()

            # 立即处理队列
            if self._batch_save_enabled:
                self._process_enhanced_queue()
            else:
                self._process_save_queue()

            self.logger.info(f"已强制保存所有待保存数据，优先级: {priority.name}")

        except Exception as e:
            self.logger.error(f"强制保存失败: {e}")

    def clear_queue(self) -> None:
        """
        清空所有队列和待保存数据

        使用示例:
            ```python
            # 清空所有队列
            memory.clear_queue()
            ```
        """
        try:
            # 停止所有定时器
            if self._save_timer.isActive():
                self._save_timer.stop()

            for timer in self._widget_timers.values():
                if timer.isActive():
                    timer.stop()

            # 清空队列和数据
            self._enhanced_save_queue.clear()
            self._pending_save_data.clear()
            self._data_to_save_queue.clear()

            self.logger.info("已清空所有队列和待保存数据")

        except Exception as e:
            self.logger.error(f"清空队列失败: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 包含详细性能统计的字典

        使用示例:
            ```python
            stats = memory.get_performance_stats()
            print(f"队列处理率: {stats['queue_stats']['processing_rate']:.2f} 任务/秒")
            print(f"成功率: {stats['queue_stats']['success_rate']:.1f}%")
            ```
        """
        try:
            # 获取队列统计
            queue_stats = self._enhanced_save_queue.get_statistics()

            # 获取基本状态
            basic_status = self.get_queue_status()

            # 计算额外的性能指标
            total_widgets = len(self._line_edits)
            active_timers = sum(1 for timer in self._widget_timers.values() if timer.isActive())

            return {
                'queue_stats': queue_stats,
                'basic_status': basic_status,
                'performance_metrics': {
                    'total_widgets_managed': total_widgets,
                    'active_timers': active_timers,
                    'timer_efficiency': (active_timers / max(1, total_widgets)) * 100,
                    'memory_usage_estimate': {
                        'pending_data_items': len(self._pending_save_data),
                        'widget_timers': len(self._widget_timers),
                        'queue_pending_tasks': queue_stats['pending_tasks']
                    }
                },
                'configuration': {
                    'batch_enabled': self._batch_save_enabled,
                    'batch_size': self._batch_size,
                    'batch_timeout': self._batch_timeout,
                    'debounce_interval_ms': self._debounce_interval_ms
                }
            }

        except Exception as e:
            self.logger.error(f"获取性能统计失败: {e}")
            return {'error': str(e)}

    def reset_performance_stats(self) -> None:
        """
        重置性能统计信息

        使用示例:
            ```python
            # 重置统计信息，开始新的监控周期
            memory.reset_performance_stats()
            ```
        """
        try:
            self._enhanced_save_queue.reset_statistics()
            self.logger.info("性能统计信息已重置")

        except Exception as e:
            self.logger.error(f"重置性能统计失败: {e}")

    def print_performance_report(self) -> None:
        """
        打印详细的性能报告

        使用示例:
            ```python
            # 打印性能报告到控制台
            memory.print_performance_report()
            ```
        """
        try:
            stats = self.get_performance_stats()

            if 'error' in stats:
                print(f"❌ 获取性能统计失败: {stats['error']}")
                return

            print("\n" + "="*60)
            print("📊 LineEditMemory 性能报告")
            print("="*60)

            # 队列统计
            queue_stats = stats['queue_stats']
            print(f"\n🔄 队列统计:")
            print(f"  当前队列大小: {queue_stats['current_size']}")
            print(f"  待处理任务: {queue_stats['pending_tasks']}")
            print(f"  总添加任务: {queue_stats['total_added']}")
            print(f"  总处理任务: {queue_stats['total_processed']}")
            print(f"  峰值队列大小: {queue_stats['peak_size']}")
            print(f"  处理率: {queue_stats['processing_rate']:.2f} 任务/秒")
            print(f"  成功率: {queue_stats['success_rate']:.1f}%")
            print(f"  运行时间: {queue_stats['uptime_seconds']:.1f} 秒")

            # 性能指标
            perf_metrics = stats['performance_metrics']
            print(f"\n⚡ 性能指标:")
            print(f"  管理的控件数量: {perf_metrics['total_widgets_managed']}")
            print(f"  活跃定时器: {perf_metrics['active_timers']}")
            print(f"  定时器效率: {perf_metrics['timer_efficiency']:.1f}%")

            # 内存使用
            memory_usage = perf_metrics['memory_usage_estimate']
            print(f"\n💾 内存使用估计:")
            print(f"  待保存数据项: {memory_usage['pending_data_items']}")
            print(f"  控件定时器: {memory_usage['widget_timers']}")
            print(f"  队列待处理任务: {memory_usage['queue_pending_tasks']}")

            # 配置信息
            config = stats['configuration']
            print(f"\n⚙️ 配置信息:")
            print(f"  批量保存: {'启用' if config['batch_enabled'] else '禁用'}")
            print(f"  批量大小: {config['batch_size']}")
            print(f"  批量超时: {config['batch_timeout']} 秒")
            print(f"  防抖间隔: {config['debounce_interval_ms']} 毫秒")

            print("\n" + "="*60)

        except Exception as e:
            print(f"❌ 打印性能报告失败: {e}")
            self.logger.error(f"打印性能报告失败: {e}")

    # =================================================================================
    # 同步检测功能增强方法
    # =================================================================================

    def _start_sync_detection(self) -> None:
        """
        启动同步检测功能

        初始化内容快照并启动定时检测器，用于检测通过同步机制更新的内容变化
        """
        if not self._enable_sync_detection:
            self.logger.info("同步检测功能已禁用")
            return

        try:
            # 初始化所有控件的内容快照
            self._initialize_content_snapshots()

            # 启动同步检测定时器
            if self._sync_detection_interval_ms > 0:
                self._sync_detection_timer.start(self._sync_detection_interval_ms)
                self.logger.info(f"同步检测已启动，检测间隔: {self._sync_detection_interval_ms}ms")
            else:
                self.logger.warning("同步检测间隔无效，同步检测未启动")

        except Exception as e:
            self.logger.error(f"启动同步检测失败: {e}")
            traceback.print_exc()

    def _initialize_content_snapshots(self) -> None:
        """
        初始化所有控件的内容快照

        为每个管理的QLineEdit控件创建初始内容快照，用于后续的变化检测
        """
        try:
            current_time = time.time()

            for line_edit in self._line_edits:
                if sip.isdeleted(line_edit):
                    continue

                widget_key = self._get_widget_key(line_edit)
                if not widget_key:
                    continue

                current_text = line_edit.text()
                content_hash = self._calculate_content_hash(current_text)

                self._content_snapshots[widget_key] = {
                    "text": current_text,
                    "timestamp": current_time,
                    "hash": content_hash
                }

            self.logger.debug(f"已初始化 {len(self._content_snapshots)} 个控件的内容快照")

        except Exception as e:
            self.logger.error(f"初始化内容快照失败: {e}")
            traceback.print_exc()

    def _calculate_content_hash(self, text: str) -> str:
        """
        计算文本内容的哈希值

        Args:
            text: 要计算哈希的文本

        Returns:
            str: 文本的哈希值
        """
        import hashlib
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _check_sync_updates(self) -> None:
        """
        检查同步更新的内容变化

        定时检查所有管理的QLineEdit控件，检测通过同步机制更新但未被常规事件捕获的内容变化
        """
        if self._sync_detection_running:
            self.logger.debug("同步检测正在运行中，跳过本次检查")
            return

        self._sync_detection_running = True

        try:
            current_time = time.time()
            changes_detected = 0

            for line_edit in self._line_edits:
                if sip.isdeleted(line_edit):
                    continue

                widget_key = self._get_widget_key(line_edit)
                if not widget_key:
                    continue

                # 检测内容变化
                if self._detect_content_change(line_edit, widget_key, current_time):
                    changes_detected += 1

            if changes_detected > 0:
                self.logger.info(f"同步检测发现 {changes_detected} 个控件内容变化")
            else:
                self.logger.debug("同步检测未发现内容变化")

        except Exception as e:
            self.logger.error(f"同步检测过程中发生错误: {e}")
            traceback.print_exc()
        finally:
            self._sync_detection_running = False

    def _detect_content_change(self, line_edit: QLineEdit, widget_key: str, current_time: float) -> bool:
        """
        检测单个控件的内容变化（增强的防重复机制）

        Args:
            line_edit: QLineEdit控件
            widget_key: 控件键名
            current_time: 当前时间戳

        Returns:
            bool: 是否检测到内容变化
        """
        try:
            # 防循环检测
            if self._sync_detection_in_progress.get(widget_key, False):
                self.logger.debug(f"控件 '{widget_key}' 正在进行同步检测，跳过")
                return False

            self._sync_detection_in_progress[widget_key] = True

            try:
                current_text = line_edit.text()
                current_hash = self._calculate_content_hash(current_text)

                # 检查是否为重复变化
                if self._is_duplicate_change(widget_key, current_hash, current_time):
                    return False

                # 获取上次的快照
                last_snapshot = self._content_snapshots.get(widget_key)

                if not last_snapshot:
                    # 首次检测，创建快照
                    self._content_snapshots[widget_key] = {
                        "text": current_text,
                        "timestamp": current_time,
                        "hash": current_hash
                    }
                    self._add_to_change_history(widget_key, current_hash, current_time)
                    return False

                # 检查内容是否发生变化
                if current_hash != last_snapshot["hash"]:
                    # 检查是否在防重复时间窗口内
                    time_diff_ms = (current_time - last_snapshot["timestamp"]) * 1000

                    if time_diff_ms < self._duplicate_prevention_window_ms:
                        self.logger.debug(f"控件 '{widget_key}' 在防重复时间窗口内，跳过记录")
                        return False

                    # 记录内容变化
                    self.logger.info(f"同步检测到控件 '{widget_key}' 内容变化: '{last_snapshot['text']}' -> '{current_text}'")

                    # 更新快照
                    self._content_snapshots[widget_key] = {
                        "text": current_text,
                        "timestamp": current_time,
                        "hash": current_hash
                    }

                    # 添加到变化历史
                    self._add_to_change_history(widget_key, current_hash, current_time)

                    # 处理内容变化
                    self._handle_sync_detected_change(line_edit, widget_key, current_text, current_time)
                    return True

                return False

            finally:
                # 确保清除防循环标志
                self._sync_detection_in_progress[widget_key] = False

        except Exception as e:
            self.logger.error(f"检测控件 '{widget_key}' 内容变化时发生错误: {e}")
            traceback.print_exc()
            # 确保清除防循环标志
            self._sync_detection_in_progress[widget_key] = False
            return False

    def _is_duplicate_change(self, widget_key: str, content_hash: str, current_time: float) -> bool:
        """
        检查是否为重复的内容变化

        Args:
            widget_key: 控件键名
            content_hash: 内容哈希值
            current_time: 当前时间戳

        Returns:
            bool: 是否为重复变化
        """
        try:
            history = self._change_history.get(widget_key, [])

            # 检查最近的变化历史
            for record in reversed(history):
                time_diff_ms = (current_time - record["timestamp"]) * 1000

                # 如果时间差超过防重复窗口，停止检查
                if time_diff_ms > self._duplicate_prevention_window_ms * 2:
                    break

                # 如果哈希值相同且在时间窗口内，认为是重复变化
                if record["hash"] == content_hash and time_diff_ms < self._duplicate_prevention_window_ms:
                    self.logger.debug(f"控件 '{widget_key}' 检测到重复变化，跳过")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检查重复变化时发生错误: {e}")
            return False

    def _add_to_change_history(self, widget_key: str, content_hash: str, timestamp: float) -> None:
        """
        添加变化记录到历史

        Args:
            widget_key: 控件键名
            content_hash: 内容哈希值
            timestamp: 时间戳
        """
        try:
            if widget_key not in self._change_history:
                self._change_history[widget_key] = []

            history = self._change_history[widget_key]

            # 添加新记录
            history.append({
                "hash": content_hash,
                "timestamp": timestamp
            })

            # 限制历史记录大小
            if len(history) > self._max_history_size:
                history.pop(0)  # 移除最旧的记录

        except Exception as e:
            self.logger.error(f"添加变化历史时发生错误: {e}")

    def _handle_sync_detected_change(self, line_edit: QLineEdit, widget_key: str, new_text: str, timestamp: float) -> None:
        """
        处理同步检测到的内容变化

        Args:
            line_edit: QLineEdit控件
            widget_key: 控件键名
            new_text: 新文本内容
            timestamp: 时间戳
        """
        try:
            # 检查是否有实际值
            has_value = bool(new_text and new_text.strip())

            # 更新初始文本记录
            self._initial_texts[widget_key] = new_text

            if not has_value:
                # 如果没有值，标记为删除
                self._pending_save_data[widget_key] = {
                    "value": None,
                    "timestamp": timestamp,
                    "has_value": False
                }
                self.logger.info(f"同步检测: 控件 '{widget_key}' 内容为空，将从配置中删除")
            else:
                # 有值，保存
                self._pending_save_data[widget_key] = {
                    "value": new_text,
                    "timestamp": timestamp,
                    "has_value": True
                }
                self.logger.info(f"同步检测: 控件 '{widget_key}' 有值，将保存: '{new_text}'")

            # 使用增强的队列写入机制
            if self._batch_save_enabled:
                self._add_to_enhanced_queue(widget_key, new_text, has_value, timestamp)
            else:
                # 启动防抖定时器
                self._save_timer.start(self._debounce_interval_ms)

        except Exception as e:
            self.logger.error(f"处理同步检测到的变化时发生错误: {e}")
            traceback.print_exc()

    def enable_sync_detection(self, enabled: bool = True) -> None:
        """
        启用或禁用同步检测功能

        Args:
            enabled: 是否启用同步检测

        使用示例:
            ```python
            # 启用同步检测
            memory.enable_sync_detection(True)

            # 禁用同步检测
            memory.enable_sync_detection(False)
            ```
        """
        try:
            self._enable_sync_detection = enabled

            if enabled:
                if not self._sync_detection_timer.isActive():
                    self._start_sync_detection()
                    self.logger.info("同步检测功能已启用")
            else:
                if self._sync_detection_timer.isActive():
                    self._sync_detection_timer.stop()
                    self.logger.info("同步检测功能已禁用")

        except Exception as e:
            self.logger.error(f"设置同步检测状态失败: {e}")

    def set_sync_detection_interval(self, interval_ms: int) -> None:
        """
        设置同步检测间隔

        Args:
            interval_ms: 检测间隔（毫秒）

        使用示例:
            ```python
            # 设置检测间隔为500毫秒
            memory.set_sync_detection_interval(500)
            ```
        """
        try:
            if interval_ms <= 0:
                self.logger.warning("同步检测间隔必须大于0")
                return

            self._sync_detection_interval_ms = interval_ms

            # 如果定时器正在运行，重新启动以应用新间隔
            if self._sync_detection_timer.isActive():
                self._sync_detection_timer.stop()
                self._sync_detection_timer.start(interval_ms)

            self.logger.info(f"同步检测间隔已设置为: {interval_ms}ms")

        except Exception as e:
            self.logger.error(f"设置同步检测间隔失败: {e}")

    def force_sync_check(self) -> int:
        """
        强制执行一次同步检测

        Returns:
            int: 检测到的变化数量

        使用示例:
            ```python
            # 强制检测一次
            changes = memory.force_sync_check()
            print(f"检测到 {changes} 个变化")
            ```
        """
        try:
            if self._sync_detection_running:
                self.logger.warning("同步检测正在运行中，无法强制执行")
                return 0

            self.logger.info("开始强制同步检测")

            current_time = time.time()
            changes_detected = 0

            for line_edit in self._line_edits:
                if sip.isdeleted(line_edit):
                    continue

                widget_key = self._get_widget_key(line_edit)
                if not widget_key:
                    continue

                # 强制检测内容变化（忽略防重复时间窗口）
                if self._force_detect_content_change(line_edit, widget_key, current_time):
                    changes_detected += 1

            self.logger.info(f"强制同步检测完成，发现 {changes_detected} 个变化")
            return changes_detected

        except Exception as e:
            self.logger.error(f"强制同步检测失败: {e}")
            traceback.print_exc()
            return 0

    def _force_detect_content_change(self, line_edit: QLineEdit, widget_key: str, current_time: float) -> bool:
        """
        强制检测单个控件的内容变化（忽略防重复时间窗口）

        Args:
            line_edit: QLineEdit控件
            widget_key: 控件键名
            current_time: 当前时间戳

        Returns:
            bool: 是否检测到内容变化
        """
        try:
            current_text = line_edit.text()
            current_hash = self._calculate_content_hash(current_text)

            # 获取上次的快照
            last_snapshot = self._content_snapshots.get(widget_key)

            if not last_snapshot:
                # 首次检测，创建快照
                self._content_snapshots[widget_key] = {
                    "text": current_text,
                    "timestamp": current_time,
                    "hash": current_hash
                }
                return False

            # 检查内容是否发生变化
            if current_hash != last_snapshot["hash"]:
                # 记录内容变化
                self.logger.info(f"强制检测到控件 '{widget_key}' 内容变化: '{last_snapshot['text']}' -> '{current_text}'")

                # 更新快照
                self._content_snapshots[widget_key] = {
                    "text": current_text,
                    "timestamp": current_time,
                    "hash": current_hash
                }

                # 处理内容变化
                self._handle_sync_detected_change(line_edit, widget_key, current_text, current_time)
                return True

            return False

        except Exception as e:
            self.logger.error(f"强制检测控件 '{widget_key}' 内容变化时发生错误: {e}")
            traceback.print_exc()
            return False

    def get_sync_detection_status(self) -> Dict[str, Any]:
        """
        获取同步检测状态信息

        Returns:
            Dict[str, Any]: 包含同步检测状态的字典

        使用示例:
            ```python
            status = memory.get_sync_detection_status()
            print(f"同步检测启用: {status['enabled']}")
            print(f"检测间隔: {status['interval_ms']}ms")
            ```
        """
        try:
            # 计算活跃的检测标志数量
            active_detection_flags = sum(1 for flag in self._sync_detection_in_progress.values() if flag)

            # 计算历史记录统计
            total_history_records = sum(len(history) for history in self._change_history.values())

            return {
                "enabled": self._enable_sync_detection,
                "interval_ms": self._sync_detection_interval_ms,
                "timer_active": self._sync_detection_timer.isActive(),
                "detection_running": self._sync_detection_running,
                "snapshots_count": len(self._content_snapshots),
                "duplicate_prevention_window_ms": self._duplicate_prevention_window_ms,
                "active_detection_flags": active_detection_flags,
                "change_history_widgets": len(self._change_history),
                "total_history_records": total_history_records,
                "max_history_size": self._max_history_size
            }
        except Exception as e:
            self.logger.error(f"获取同步检测状态失败: {e}")
            traceback.print_exc()
            return {"error": str(e)}

    def clear_sync_detection_data(self) -> None:
        """
        清空同步检测相关数据

        使用示例:
            ```python
            # 清空同步检测数据，重新开始检测
            memory.clear_sync_detection_data()
            ```
        """
        try:
            self.logger.info("开始清空同步检测数据")

            # 清空内容快照
            cleared_snapshots = len(self._content_snapshots)
            self._content_snapshots.clear()

            # 清空变化历史
            cleared_history = sum(len(history) for history in self._change_history.values())
            self._change_history.clear()

            # 重置检测标志
            active_flags = sum(1 for flag in self._sync_detection_in_progress.values() if flag)
            self._sync_detection_in_progress.clear()

            # 重新初始化内容快照
            self._initialize_content_snapshots()

            self.logger.info(f"同步检测数据清空完成: 快照({cleared_snapshots}), 历史记录({cleared_history}), 活跃标志({active_flags})")

        except Exception as e:
            self.logger.error(f"清空同步检测数据失败: {e}")
            traceback.print_exc()

    def get_sync_detection_statistics(self) -> Dict[str, Any]:
        """
        获取同步检测的详细统计信息

        Returns:
            Dict[str, Any]: 包含详细统计信息的字典

        使用示例:
            ```python
            stats = memory.get_sync_detection_statistics()
            print(f"检测效率: {stats['detection_efficiency']:.2f}%")
            ```
        """
        try:
            status = self.get_sync_detection_status()

            # 计算检测效率指标
            total_widgets = len(self._line_edits)
            monitored_widgets = status['snapshots_count']
            detection_efficiency = (monitored_widgets / max(1, total_widgets)) * 100

            # 计算平均历史记录长度
            avg_history_length = 0
            if status['change_history_widgets'] > 0:
                avg_history_length = status['total_history_records'] / status['change_history_widgets']

            # 内存使用估算
            memory_estimate = {
                "snapshots_kb": (status['snapshots_count'] * 200) / 1024,  # 估算每个快照200字节
                "history_kb": (status['total_history_records'] * 100) / 1024,  # 估算每个历史记录100字节
                "flags_kb": (len(self._sync_detection_in_progress) * 50) / 1024  # 估算每个标志50字节
            }

            return {
                **status,
                "performance_metrics": {
                    "total_widgets": total_widgets,
                    "monitored_widgets": monitored_widgets,
                    "detection_efficiency": detection_efficiency,
                    "avg_history_length": avg_history_length
                },
                "memory_usage_estimate": memory_estimate,
                "health_indicators": {
                    "timer_healthy": status['timer_active'] == status['enabled'],
                    "no_stuck_detections": status['active_detection_flags'] == 0,
                    "reasonable_history_size": avg_history_length <= self._max_history_size
                }
            }

        except Exception as e:
            self.logger.error(f"获取同步检测统计信息失败: {e}")
            traceback.print_exc()
            return {"error": str(e)}

    def diagnose_sync_detection(self) -> Dict[str, Any]:
        """
        诊断同步检测功能的健康状态

        Returns:
            Dict[str, Any]: 包含诊断结果的字典

        使用示例:
            ```python
            diagnosis = memory.diagnose_sync_detection()
            if diagnosis['overall_health'] != 'healthy':
                print(f"检测到问题: {diagnosis['issues']}")
            ```
        """
        try:
            stats = self.get_sync_detection_statistics()
            issues = []
            warnings = []

            # 检查基本配置
            if not stats.get('enabled', False):
                issues.append("同步检测功能未启用")

            if stats.get('interval_ms', 0) <= 0:
                issues.append("同步检测间隔配置无效")

            # 检查运行状态
            health = stats.get('health_indicators', {})

            if not health.get('timer_healthy', False):
                issues.append("定时器状态与启用状态不一致")

            if not health.get('no_stuck_detections', True):
                warnings.append(f"检测到 {stats.get('active_detection_flags', 0)} 个卡住的检测进程")

            if not health.get('reasonable_history_size', True):
                warnings.append("历史记录大小超出合理范围")

            # 检查性能指标
            perf = stats.get('performance_metrics', {})
            efficiency = perf.get('detection_efficiency', 0)

            if efficiency < 50:
                warnings.append(f"检测效率较低: {efficiency:.1f}%")
            elif efficiency < 80:
                warnings.append(f"检测效率中等: {efficiency:.1f}%")

            # 检查内存使用
            memory_usage = stats.get('memory_usage_estimate', {})
            total_memory_kb = sum(memory_usage.values())

            if total_memory_kb > 1024:  # 超过1MB
                warnings.append(f"内存使用较高: {total_memory_kb:.1f}KB")

            # 确定整体健康状态
            if issues:
                overall_health = "critical"
            elif warnings:
                overall_health = "warning"
            else:
                overall_health = "healthy"

            return {
                "overall_health": overall_health,
                "issues": issues,
                "warnings": warnings,
                "statistics": stats,
                "recommendations": self._generate_recommendations(issues, warnings, stats)
            }

        except Exception as e:
            self.logger.error(f"诊断同步检测功能失败: {e}")
            traceback.print_exc()
            return {
                "overall_health": "error",
                "error": str(e),
                "issues": ["诊断过程中发生错误"],
                "warnings": [],
                "recommendations": ["请检查日志以获取详细错误信息"]
            }

    def _generate_recommendations(self, issues: List[str], warnings: List[str], stats: Dict[str, Any]) -> List[str]:
        """
        根据诊断结果生成建议

        Args:
            issues: 问题列表
            warnings: 警告列表
            stats: 统计信息

        Returns:
            List[str]: 建议列表
        """
        recommendations = []

        try:
            # 针对问题的建议
            for issue in issues:
                if "未启用" in issue:
                    recommendations.append("调用 enable_sync_detection(True) 启用同步检测")
                elif "间隔配置无效" in issue:
                    recommendations.append("调用 set_sync_detection_interval() 设置有效的检测间隔")
                elif "定时器状态" in issue:
                    recommendations.append("重启同步检测功能或重新初始化 LineEditMemory")

            # 针对警告的建议
            for warning in warnings:
                if "卡住的检测进程" in warning:
                    recommendations.append("调用 clear_sync_detection_data() 清理检测状态")
                elif "历史记录大小" in warning:
                    recommendations.append("考虑减少 max_history_size 或清理历史数据")
                elif "检测效率" in warning:
                    recommendations.append("检查控件是否正确设置了 objectName")
                elif "内存使用" in warning:
                    recommendations.append("考虑增加检测间隔或清理历史数据")

            # 通用建议
            if not recommendations:
                recommendations.append("同步检测功能运行正常，无需特殊操作")

        except Exception as e:
            self.logger.error(f"生成建议时发生错误: {e}")
            recommendations.append("生成建议时发生错误，请查看日志")

        return recommendations
