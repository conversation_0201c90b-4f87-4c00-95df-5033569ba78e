[style]
# Based on which preset style (pep8, google, chromium, facebook)
based_on_style = pep8

# Column limit
column_limit = 130

# Indent width
indent_width = 4

# Whether to indent blank lines
indent_blank_lines = false

# Continuation indent width
continuation_indent_width = 4

# How to align continued lines (FIXED, SPACE)
continuation_align_style = SPACE

# Add space after colon in dictionaries
spaces_around_power_operator = true

# Place each dictionary entry on a separate line
each_dict_entry_on_separate_line = true

# Force every dictionary literal to be multi-line
force_multiline_dict = true

# Add spaces around default or named assigns
spaces_around_default_or_named_assign = true

# Do not add spaces around selected binary operators
no_spaces_around_selected_binary_operators = *,/

# Whether to split before logical operators (and, or)
split_before_logical_operator = true

# Split before the dot in a chained method call
split_before_dot = true

# Two blank lines around top-level definitions (functions or classes)
blank_lines_around_top_level_definition = 3

# Add a blank line before nested class or function definitions
blank_line_before_nested_class_or_def = true

# Number of blank lines between top-level imports and variable definitions
blank_lines_between_top_level_imports_and_variables = 2

# Allow splitting of bracketed expressions
split_all_comma_separated_values = false

# Split complex comprehensions into multiple lines
split_complex_comprehension = true

# Allow lambdas to be formatted on more than one line
allow_multiline_lambdas = true

# Dedent closing brackets to match the opening line
dedent_closing_brackets = true

# Coalesce empty brackets onto a single line
coalesce_brackets = true

# Do not add spaces inside brackets
space_inside_brackets = true

# Do not add a space between a trailing comma and a closing bracket
space_between_ending_comma_and_closing_bracket = true

# Add parentheses to clarify arithmetic precedence
arithmetic_precedence_indication = true

# Add spaces around dictionary delimiters (:)
spaces_around_dict_delimiters = true

# Add spaces around tuple delimiters (,)
spaces_around_tuple_delimiters = true

spaces_before_comment = 4