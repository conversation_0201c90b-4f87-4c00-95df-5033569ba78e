from global_tools.utils.helper import Logger, LogLevel


def my_simple_formatter( record, color, truncate_length ):
	# 创建简单的日志格式: [时间] 级别: 消息
	time_str = record[ "time" ].strftime( "%H:%M:%S" )
	# 控制台输出
	console = f"[{time_str}] {record[ 'levelname' ]}: {record[ 'message' ]}"
	# 文件输出
	file = f"[{time_str}] {record[ 'levelname' ]}: {record[ 'message' ]}"
	return console, file


Logger.setLevel( LogLevel.DEBUG )
# Logger.setFormatter(my_simple_formatter)

logger = Logger()

logger.debug( "debugfsdhjkfhkjdsfhkjdshfkjdshfkjdshfkjdshfkjdshfkjshdjf黑科技多少分可接受的合法科技大厦饭卡大沙发看哈的设计开发和四大会计发哈打卡随机发好卡手机打繁花盛开绝代风华当fdfdshjkfhdskjfhkdjsfhkjdshfjkdshfkjdshfkjsdhfkjsdhfkjsdhfkjdshfkjsdhfkjsdhfkjsdhfkjdshfkjsdhfdskjfhkjsdhfkdsjhfkjdshfkjsdhfkjdshfksjdfhkjsskjlfdskljfhjksdfhkjdshfkjdshfkjdshfkjdshfkjdshfkjdshkjfdshkjfhdskjfhdskjfhdjksfhsdkjhfk升科技发哈科技三打哈发卡机大沙发科技大厦发卡机打撒" )
logger.info( "info" )
logger.warning( "warning" )
logger.error( "error" )
