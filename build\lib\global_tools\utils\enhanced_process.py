import multiprocessing
import traceback
import time
from typing import Any, Callable, Dict, Optional, Union
import queue
import threading
import random


class ResultContainer:
    """
    用于存储中间结果的容器类。
    支持普通数据和列表数据的存储和检索。
    """

    def __init__(self):
        self._data = {}
        self._list_data = {}

    def set(self, key: str, value: Any) -> None:
        """
        设置一个键值对

        Args:
            key (str): 键名
            value (Any): 值
        """
        self._data[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取指定键的值

        Args:
            key (str): 键名
            default (Any, optional): 默认值

        Returns:
            Any: 键对应的值，如果键不存在则返回默认值
        """
        return self._data.get(key, default)

    def get_all(self) -> Dict[str, Any]:
        """
        获取所有数据

        Returns:
            Dict[str, Any]: 所有键值对
        """
        return self._data.copy()

    def append_to_list(self, key: str, value: Any) -> None:
        """
        向指定键的列表追加一个值

        Args:
            key (str): 键名
            value (Any): 要追加的值
        """
        if key not in self._list_data:
            self._list_data[key] = []
        self._list_data[key].append(value)

    def get_list(self, key: str, default: list = None) -> list:
        """
        获取指定键的列表

        Args:
            key (str): 键名
            default (list, optional): 默认值

        Returns:
            list: 键对应的列表，如果键不存在则返回默认值
        """
        return self._list_data.get(key, default)

    def get_all_lists(self) -> Dict[str, list]:
        """
        获取所有列表数据

        Returns:
            Dict[str, list]: 所有列表数据
        """
        return {k: v.copy() for k, v in self._list_data.items()}

    def clear_lists(self) -> None:
        """
        清空所有列表数据
        """
        self._list_data.clear()

    def update_lists(self, data: Dict[str, list]) -> None:
        """
        更新列表数据

        Args:
            data (Dict[str, list]): 要更新的列表数据
        """
        self._list_data.clear()
        self._list_data.update(data)

    def update_progress(self, progress: int) -> None:
        """
        更新进度

        Args:
            progress (int): 进度百分比
        """
        self._data["progress"] = f"{progress}%"
        self._data["timestamp"] = time.strftime("%H:%M:%S")
        self._data["steps"] = f"step_{progress}_completed"


class EnhancedProcess:
    """
    增强的进程类，提供更强大的进程控制和结果获取功能。

    这个类继承了原生 multiprocessing.Process 的功能，并增加了以下特性：
    1. 支持中间结果存储和获取：通过内部的 ResultContainer 实现数据存储
    2. 提供优雅的进程终止机制：可以在终止进程前获取最新状态数据
    3. 详细的错误处理和堆栈跟踪：捕获并传递子进程中的异常信息
    4. 支持任意参数传递：可以向目标函数传递任意位置参数和关键字参数
    5. 实时状态更新：通过队列机制实现父子进程间的数据交换
    6. 超时控制：各种操作都支持超时参数设置

    使用流程：
    1. 创建实例：process = EnhancedProcess(target=your_function)
    2. 启动进程：process.start(your_args)
    3. 获取中间结果：process.get_result() 或 process.get_list_data()
    4. 等待完成：process.wait_for_completion(timeout=5.0)
    5. 终止进程：process.terminate_gracefully()
    6. 清理资源：process.cleanup()

    ---------------------------------------------------------------------------
                                使用示例
    ---------------------------------------------------------------------------

    **准备工作：定义目标函数**

    所有示例都需要一个在子进程中运行的目标函数。这个函数必须接受
    `ResultContainer` 作为第一个参数。

    ```python
    import time
    import multiprocessing
    import traceback
    from global_tools.utils.enhanced_process import EnhancedProcess, ResultContainer

    # 示例目标函数：模拟一个耗时任务，并记录中间结果和进度
    def sample_task(result_container: ResultContainer, duration: int, task_name: str = "DefaultTask"):
        print(f"子进程: 任务 '{task_name}' 开始，持续 {duration} 秒。")
        total_steps = 10
        for i in range(total_steps):
            # 模拟工作
            time.sleep(duration / total_steps)

            # 更新普通数据
            result_container.set("current_step", i + 1)
            result_container.set("task_name", task_name)

            # 更新列表数据
            result_container.append_to_list("steps_completed", f"Step {i+1}")
            result_container.append_to_list(f"{task_name}_log", f"Log entry {i+1} at {time.time():.2f}")

            # 更新进度（可选，非内置功能，需自行在 get_result 中获取）
            progress = int(((i + 1) / total_steps) * 100)
            result_container.set("progress", f"{progress}%")
            result_container.set("last_update_time", time.strftime("%H:%M:%S"))

            print(f"子进程: {task_name} - 完成步骤 {i+1}/{total_steps} ({progress}%)")

        print(f"子进程: 任务 '{task_name}' 完成。")
        # 任务完成后返回最终结果
        return {"status": "completed", "total_steps": total_steps, "task_name": task_name}

    # 示例目标函数：模拟一个会出错的任务
    def failing_task(result_container: ResultContainer, fail_after: int):
        print(f"子进程: 失败任务开始，将在 {fail_after} 秒后失败。")
        result_container.set("status", "running")
        for i in range(fail_after):
            time.sleep(1)
            result_container.append_to_list("log", f"Running for {i+1} seconds")
        print("子进程: 准备抛出异常...")
        raise ValueError("任务执行失败！")
        return "这个永远不会返回" # 为了满足类型提示，实际不会执行

    # 注意：在 Windows 上使用 multiprocessing 时，通常需要保护主模块入口点
    # if __name__ == "__main__":
    #     multiprocessing.freeze_support()
    #     # 在这里运行下面的示例代码...

    ```

    **1. 初始化 (`__init__`)**

    创建 `EnhancedProcess` 实例，传入目标函数。

    ```python
    # 创建一个执行 sample_task 的进程实例
    process = EnhancedProcess(target=sample_task)

    # 创建一个执行 failing_task 的进程实例
    error_process = EnhancedProcess(target=failing_task)

    print("EnhancedProcess 实例已创建。")
    ```

    **2. 启动进程 (`start`)**

    启动子进程执行目标函数，可以传递额外的参数。

    ```python
    # 启动 sample_task，传入参数 duration=3 和 task_name="DataProcessing"
    print("启动 sample_task 进程...")
    process.start(duration=3, task_name="DataProcessing") # duration 是位置参数args[0], task_name 是kwargs['task_name']
    print("sample_task 进程已启动。")

    # 启动 failing_task，传入参数 fail_after=2
    print("启动 failing_task 进程...")
    error_process.start(fail_after=2)
    print("failing_task 进程已启动。")

    # 检查进程是否立即变为活动状态 (通常是的)
    print(f"sample_task 是否存活? {process.is_alive()}")
    print(f"failing_task 是否存活? {error_process.is_alive()}")

    # !! 注意：不能在进程运行时再次调用 start
    # process.start(duration=1) # 这会引发 RuntimeError
    ```

    **3. 检查进程状态 (`is_alive`)**

    检查子进程是否仍在运行。

    ```python
    # 等待一小段时间让进程运行起来
    time.sleep(0.5)
    print(f"sample_task 是否仍在运行? {process.is_alive()}")

    # (假设 sample_task 总共运行 3 秒)
    time.sleep(3)
    print(f"sample_task 在完成后是否仍在运行? {process.is_alive()}") # 应该为 False
    ```

    **4. 获取普通结果 (`get_result`)**

    获取子进程通过 `result_container.set()` 设置的非列表数据。

    ```python
    # 启动一个新进程用于演示 get_result
    get_result_process = EnhancedProcess(target=sample_task)
    get_result_process.start(duration=2, task_name="ResultGetter")
    print("启动 ResultGetter 进程...")

    # 在进程运行时获取中间结果 (可能不是最新的)
    time.sleep(0.6)
    print("尝试获取中间普通结果:")
    intermediate_results = get_result_process.get_result(timeout=0.1) # 短暂等待新数据
    print(f"  - 中间结果 (字典): {intermediate_results}")
    if intermediate_results:
        print(f"  - 获取特定键 'current_step': {get_result_process.get_result(key='current_step')}")
        print(f"  - 获取特定键 'progress': {get_result_process.get_result(key='progress')}")
        print(f"  - 获取不存在的键: {get_result_process.get_result(key='non_existent', default='N/A')}")

    # 等待进程完成 (确保所有数据都被发送)
    print("等待 ResultGetter 完成...")
    get_result_process.wait_for_completion(timeout=3)

    # 进程结束后获取最终的普通数据
    print("获取进程结束后的普通结果:")
    final_results = get_result_process.get_result() # 结束后无需 timeout
    print(f"  - 最终结果 (字典): {final_results}")
    print(f"  - 获取特定键 'task_name': {get_result_process.get_result(key='task_name')}")

    get_result_process.cleanup() # 清理资源
    ```

    **5. 获取列表结果 (`get_list_data`)**

    获取子进程通过 `result_container.append_to_list()` 添加的列表数据。

    ```python
    # 启动一个新进程用于演示 get_list_data
    list_data_process = EnhancedProcess(target=sample_task)
    list_data_process.start(duration=2.5, task_name="ListGenerator")
    print("启动 ListGenerator 进程...")

    # 在进程运行时获取中间列表数据
    time.sleep(1)
    print("尝试获取中间列表数据:")
    intermediate_list_data = list_data_process.get_list_data(timeout=0.1) # 等待新数据
    print(f"  - 所有列表 (字典): {intermediate_list_data}")
    if intermediate_list_data:
        print(f"  - 获取特定列表 'steps_completed': {list_data_process.get_list_data(key='steps_completed', timeout=0)}") # timeout=0 表示不等待
        print(f"  - 获取特定列表 'ListGenerator_log': {list_data_process.get_list_data(key='ListGenerator_log')}")
        print(f"  - 获取不存在的列表: {list_data_process.get_list_data(key='non_existent_list')}") # 返回 None

    # 等待进程完成
    print("等待 ListGenerator 完成...")
    list_data_process.wait_for_completion(timeout=3)

    # 进程结束后获取最终的列表数据
    print("获取进程结束后的列表数据:")
    final_list_data = list_data_process.get_list_data()
    print(f"  - 所有列表 (字典): {final_list_data}")
    if final_list_data:
        print(f"  - 获取特定列表 'steps_completed': {list_data_process.get_list_data(key='steps_completed')}")

    list_data_process.cleanup() # 清理资源
    ```

    **6. 等待进程完成 (`wait_for_completion`)**

    阻塞当前线程，直到子进程执行完毕、发生错误或超时。

    ```python
    # 重新使用之前的 process 和 error_process 实例 (假设它们已启动)
    # 注意：如果 process 实例已运行完毕，wait_for_completion 会立即返回 True (或 False 如果之前有错)

    # 示例 1: 等待正常完成的进程 (如果上面示例已跑完，这里会很快返回)
    print("等待 sample_task (可能已完成)...")
    completed_normally = process.wait_for_completion(timeout=5) # 设置超时
    print(f"sample_task 是否正常完成? {completed_normally}")
    if completed_normally:
        print(f"获取 sample_task 的最终返回值: {process._final_result}") # 注意：通常通过 get_result/get_list_data 获取数据

    # 示例 2: 等待会出错的进程
    print("等待 failing_task (应该会出错)...")
    completed_with_error = error_process.wait_for_completion(timeout=5)
    print(f"failing_task 是否正常完成? {completed_with_error}") # 应该为 False

    # 示例 3: 等待超时 (启动一个长时间任务)
    long_task_process = EnhancedProcess(target=sample_task)
    long_task_process.start(duration=10, task_name="LongRunner")
    print("启动 LongRunner 进程 (将运行10秒)...")
    print("等待 LongRunner 完成 (超时设置为 3 秒)...")
    timed_out = not long_task_process.wait_for_completion(timeout=3)
    print(f"等待 LongRunner 是否超时? {timed_out}") # 应该为 True
    print(f"即使超时，LongRunner 是否仍在运行? {long_task_process.is_alive()}") # 应该为 True

    # 对超时的进程进行清理 (通常需要终止)
    if timed_out:
        print("由于超时，优雅地终止 LongRunner...")
        long_task_process.terminate_gracefully(timeout=1)
        print(f"终止后 LongRunner 是否仍在运行? {long_task_process.is_alive()}") # 应该为 False
    long_task_process.cleanup()
    ```

    **7. 获取错误信息 (`get_error`)**

    如果在子进程执行期间发生异常，获取错误详情。

    ```python
    # 使用上面已完成（且出错）的 error_process
    if not completed_with_error: # 来自 wait_for_completion 的结果
        error_details = error_process.get_error()
        print("获取 failing_task 的错误信息:")
        if error_details:
            print(f"  - 错误类型和消息: {error_details.get('error')}")
            print(f"  - 堆栈跟踪:\n{error_details.get('traceback')}")
        else:
            print("  - 未找到错误信息 (可能进程未出错或未完成)。")
    else:
         print("failing_task 没有报告错误 (这不符合预期!)")

    # 对于正常完成的进程，get_error 返回 None
    print(f"获取 sample_task (正常完成) 的错误信息: {process.get_error()}")
    ```

    **8. 优雅终止 (`terminate_gracefully`)**

    尝试平稳地停止子进程，并在终止前尽可能获取最后的数据。

    ```python
    # 启动一个新进程用于演示终止
    terminate_demo_process = EnhancedProcess(target=sample_task)
    terminate_demo_process.start(duration=10, task_name="TerminateDemo")
    print("启动 TerminateDemo 进程 (将运行10秒)...")

    # 让进程运行一会儿
    time.sleep(2.5)
    print(f"TerminateDemo 当前进度: {terminate_demo_process.get_result(key='progress', timeout=0.1)}")
    print(f"TerminateDemo 已完成步骤: {terminate_demo_process.get_list_data(key='steps_completed', timeout=0.1)}")

    # 优雅地终止进程
    print("开始优雅终止 TerminateDemo...")
    termination_result = terminate_demo_process.terminate_gracefully(timeout=1.0) # 等待1秒让子进程响应
    print(f"优雅终止是否成功? {termination_result}")
    print(f"终止后 TerminateDemo 是否仍在运行? {terminate_demo_process.is_alive()}") # 应该为 False

    # 即使被终止，仍可以获取到终止前的最后数据
    print("获取被终止进程的最后数据:")
    final_data = terminate_demo_process.get_result()
    final_list_data = terminate_demo_process.get_list_data()
    print(f"  - 最后普通数据: {final_data}")
    print(f"  - 最后列表数据: {final_list_data}")

    # 检查状态是否反映了终止
    status_after_terminate = terminate_demo_process.get_process_status()
    print(f"终止后的状态: {status_after_terminate['status']}") # 应该是 'terminated'

    terminate_demo_process.cleanup() # 虽然 terminate_gracefully 会做一些清理，但显式调用 cleanup 是好习惯
    ```

    **9. 获取详细状态 (`get_process_status`)**

    获取一个包含进程各种状态信息的字典。

    ```python
    # 使用上面不同状态的进程实例

    # 1. 获取正在运行的进程状态 (需要重新启动一个)
    running_status_process = EnhancedProcess(target=sample_task)
    running_status_process.start(duration=5, task_name="StatusCheck")
    time.sleep(1) # 确保它在运行
    print("获取正在运行进程的状态:")
    running_status = running_status_process.get_process_status()
    # 打印部分关键状态信息
    print(f"  - Status: {running_status['status']}")             # 应该是 'running'
    print(f"  - Is Alive: {running_status['is_alive']}")         # 应该是 True
    print(f"  - Progress: {running_status.get('progress')}")    # 显示当前进度
    print(f"  - Data Keys: {running_status['data_keys']}")
    print(f"  - List Data Keys: {running_status['list_data_keys']}")
    running_status_process.terminate_gracefully() # 清理

    # 2. 获取已正常完成进程的状态 (使用之前的 'process')
    print("\n获取正常完成进程的状态:")
    completed_status = process.get_process_status()
    print(f"  - Status: {completed_status['status']}")           # 应该是 'completed'
    print(f"  - Is Alive: {completed_status['is_alive']}")       # 应该是 False
    print(f"  - Has Result: {completed_status['has_result']}")   # 应该是 True
    print(f"  - Result: {completed_status['result']}")          # 显示最终返回值
    print(f"  - Has Error: {completed_status['has_error']}")     # 应该是 False

    # 3. 获取已出错进程的状态 (使用之前的 'error_process')
    print("\n获取出错进程的状态:")
    error_status = error_process.get_process_status()
    print(f"  - Status: {error_status['status']}")               # 应该是 'error'
    print(f"  - Is Alive: {error_status['is_alive']}")           # 应该是 False
    print(f"  - Has Error: {error_status['has_error']}")         # 应该是 True
    print(f"  - Error Message: {error_status['error_info']['error']}") # 显示错误消息

    # 4. 获取已终止进程的状态 (使用之前的 'terminate_demo_process')
    print("\n获取已终止进程的状态:")
    terminated_status = terminate_demo_process.get_process_status()
    print(f"  - Status: {terminated_status['status']}")           # 应该是 'terminated'
    print(f"  - Is Alive: {terminated_status['is_alive']}")       # 应该是 False
    print(f"  - Terminated Flag: {terminated_status['terminated']}") # 应该是 True
    print(f"  - Data Count: {terminated_status['data_count']}")     # 显示终止前收集到的数据项数
    print(f"  - List Data Count: {terminated_status['list_data_count']}")

    # 5. 获取未启动进程的状态
    not_started_process = EnhancedProcess(target=sample_task)
    print("\n获取未启动进程的状态:")
    not_started_status = not_started_process.get_process_status()
    print(f"  - Status: {not_started_status['status']}")         # 应该是 'not_started'
    print(f"  - Is Alive: {not_started_status['is_alive']}")     # 应该是 False

    # 清理所有用过的进程实例
    process.cleanup()
    error_process.cleanup()
    terminate_demo_process.cleanup()
    not_started_process.cleanup()
    print("\n所有示例进程已清理。")
    ```

    **10. 清理资源 (`cleanup`)**

    显式调用以终止进程（如果仍在运行）并清理资源。通常在不再需要进程对象时调用。

    ```python
    # 创建并启动一个进程
    cleanup_process = EnhancedProcess(target=sample_task)
    cleanup_process.start(duration=5, task_name="CleanupDemo")
    print("启动 CleanupDemo 进程...")
    time.sleep(1) # 让它运行一下

    # 手动调用 cleanup
    print("手动调用 cleanup...")
    cleanup_process.cleanup()
    print("Cleanup 调用完毕。")
    print(f"CleanupDemo 是否仍在运行? {cleanup_process.is_alive()}") # 应该为 False

    # cleanup 是安全的，即使进程已结束或未启动也可以调用
    print("对已结束的进程再次调用 cleanup...")
    cleanup_process.cleanup()
    print("再次调用 cleanup 完成。")

    not_started_process = EnhancedProcess(target=sample_task)
    print("对未启动的进程调用 cleanup...")
    not_started_process.cleanup()
    print("对未启动进程调用 cleanup 完成。")
    ```

    **11. 获取结果容器 (`get_result_container`)**

    获取父进程持有的 `ResultContainer` 实例的引用。这主要用于调试或特殊场景，
    通常应使用 `get_result` 和 `get_list_data` 来获取同步后的数据。

    ```python
    access_container_process = EnhancedProcess(target=sample_task)
    access_container_process.start(duration=2, task_name="ContainerAccess")
    print("启动 ContainerAccess 进程...")

    time.sleep(1)
    # 注意：这里获取的 container 里的数据可能不是最新的，
    # 因为它只在 get_result/get_list_data/wait_for_completion
    # 处理队列消息时才从子进程同步。
    # 如果需要最新数据，应先调用上述方法之一。
    access_container_process.get_result(timeout=0.1) # 先触发一次数据同步

    container_ref = access_container_process.get_result_container()
    print("获取 ResultContainer 引用:")
    print(f"  - 容器中的普通数据: {container_ref.get_all()}")
    print(f"  - 容器中的列表数据: {container_ref.get_all_lists()}")
    print(f"  - 容器中特定的值 'current_step': {container_ref.get('current_step')}")
    print(f"  - 容器中特定的列表 'steps_completed': {container_ref.get_list('steps_completed')}")

    access_container_process.wait_for_completion()
    access_container_process.cleanup()
    ```
    """

    def __init__(self, target: Callable):
        """
        初始化增强进程实例。

        Args:
            target (Callable): 要在子进程中执行的目标函数。该函数必须接受 ResultContainer
                               作为第一个参数，用于存储结果和状态。

        Raises:
            TypeError: 如果 target 不是可调用对象。

        注意:
            目标函数的签名应为：function(result_container: ResultContainer, *args, **kwargs) -> Any
        """
        if not callable(target):
            raise TypeError("target must be a callable")

        self.callback = target
        self._process = None
        self._queue = multiprocessing.Queue()
        self._result_container = ResultContainer()
        self._final_result = None
        self._error = None
        self._terminated = False
        self._final_data_received = False

    def get_result_container(self) -> ResultContainer:
        """
        获取结果容器对象，用于直接访问存储的数据。

        此方法返回的是进程主线程中的结果容器，其中包含从子进程同步过来的最新数据。
        可以通过这个容器直接访问当前已同步的数据，而不需要通过队列通信。

        Returns:
            ResultContainer: 当前进程实例使用的结果容器对象，包含所有已同步的数据。

        注意:
            如果进程正在运行，返回的数据可能不是最新的。
            要获取最新数据，应使用 get_result() 或 get_list_data() 方法。
        """
        return self._result_container

    def start(self, *args, **kwargs) -> None:
        """
        启动进程执行目标函数。

        此方法会创建一个新的进程来执行在初始化时指定的目标函数，并传递参数。
        在启动前会重置所有状态变量，确保可以多次启动同一个进程实例（但不能同时运行）。

        Args:
            *args: 传递给目标函数的位置参数。
            **kwargs: 传递给目标函数的关键字参数。

        Raises:
            RuntimeError: 如果进程已经在运行。

        注意:
            目标函数的第一个参数总是 ResultContainer 实例，不需要在 args 中提供。
            函数签名实际上是：callback(result_container, *args, **kwargs)
        """
        if self._process and self._process.is_alive():
            raise RuntimeError("Process is already running")

        self._final_result = None
        self._error = None
        self._result_container = ResultContainer()
        self._terminated = False
        self._final_data_received = False

        self._process = multiprocessing.Process(
            target=self._run_wrapper,
            args=(self.callback, self._queue, self._result_container, args, kwargs),
        )

        self._process.start()

    @staticmethod
    def _run_wrapper(
        callback: Callable,
        queue: multiprocessing.Queue,
        result_container: ResultContainer,
        args: tuple,
        kwargs: dict,
    ) -> None:
        """内部运行包装器"""

        def send_current_state():
            """发送当前状态"""
            try:
                # 创建数据的深拷贝以避免并发问题
                data_copy = result_container.get_all()
                list_data_copy = result_container.get_all_lists()
                queue.put(("update", (data_copy, list_data_copy)))
                # 确保数据被发送
                queue.put(("sync", None))
            except Exception as e:
                print(f"发送状态时出错: {e}", flush=True)

        def check_terminate():
            """检查终止信号"""
            while not stop_event.is_set():
                try:
                    msg = queue.get_nowait()
                    if msg == "terminate":
                        terminate_event.set()
                        # 立即发送当前状态
                        send_current_state()
                        break
                except multiprocessing.queues.Empty:
                    time.sleep(0.05)  # 减少检查间隔
                except Exception as e:
                    print(f"检查终止信号时出错: {e}", flush=True)
                    break

        # 创建事件对象
        stop_event = threading.Event()
        terminate_event = threading.Event()

        # 启动状态更新线程
        def status_updater():
            """定期发送状态更新"""
            while not stop_event.is_set():
                send_current_state()
                time.sleep(0.1)  # 增加更新间隔，减少竞争

        # 创建并启动线程
        status_thread = threading.Thread(target=status_updater, daemon=True)
        terminate_thread = threading.Thread(target=check_terminate, daemon=True)
        status_thread.start()
        terminate_thread.start()

        try:
            # 执行回调函数
            result = callback(result_container, *args, **kwargs)
            # 发送最终状态
            send_current_state()
            # 发送结果
            queue.put(("result", result))
        except Exception as e:
            # 发送错误信息
            error_info = {"error": str(e), "traceback": traceback.format_exc()}
            queue.put(("error", error_info))
        finally:
            # 确保发送最后的状态
            send_current_state()
            # 等待一段时间确保数据被发送
            time.sleep(0.2)  # 增加等待时间
            # 停止状态更新
            stop_event.set()
            status_thread.join(timeout=1.0)
            terminate_thread.join(timeout=1.0)

    def terminate_gracefully(self, timeout: float = 1.0) -> bool:
        """
        优雅地终止进程，尝试保存所有中间结果。

        此方法会发送终止信号给子进程，然后等待其自然结束。在等待期间，
        会持续获取子进程发送的最新数据。如果在超时时间内进程未结束，则强制终止。
        与直接调用 Process.terminate() 相比，这种方式可以尽量保存更多的中间结果。

        Args:
            timeout (float, optional): 等待进程正常终止的最长时间（秒）。默认为1.0秒。
                                      超过这个时间后，会强制终止进程。

        Returns:
            bool: 如果进程成功终止返回 True，如果发生错误返回 False。

        注意:
            即使返回 False，进程可能已经被终止，但可能没有正确获取所有数据。
            总是建议在程序结束前调用此方法以清理资源。
        """
        if not self._process or not self._process.is_alive():
            return True

        try:
            # 先获取当前数据
            try:
                current_data = self.get_list_data(timeout=0.1)
                if current_data:
                    self._result_container._list_data.update(current_data)
            except Exception as e:
                print(f"获取当前数据时出错: {e}", flush=True)

            # 发送终止信号
            self._queue.put("terminate")

            # 等待进程自然结束
            start_time = time.time()
            while time.time() - start_time < timeout:
                if not self._process.is_alive():
                    break

                # 获取最新数据
                try:
                    msg = self._queue.get_nowait()
                    if isinstance(msg, tuple) and len(msg) == 2:
                        msg_type, msg_data = msg
                        if msg_type == "update":
                            data, list_data = msg_data
                            self._result_container._data.update(data)
                            self._result_container._list_data.update(list_data)
                        elif msg_type == "result":
                            self._final_result = msg_data
                            self._final_data_received = True
                        elif msg_type == "error":
                            self._error = msg_data
                except multiprocessing.queues.Empty:
                    time.sleep(0.1)

            # 再次尝试获取最终数据
            try:
                final_data = self.get_list_data(timeout=0.1)
                if final_data:
                    self._result_container._list_data.update(final_data)
            except Exception as e:
                print(f"获取最终数据时出错: {e}", flush=True)

            # 如果进程仍在运行，强制终止
            if self._process.is_alive():
                self._process.terminate()
                self._process.join(timeout=0.5)

                # 如果join超时后进程仍在运行，尝试更强力的终止(仅在POSIX系统有效)
                if self._process.is_alive():
                    try:
                        if hasattr(self._process, "kill"):  # Python 3.7+
                            self._process.kill()
                            self._process.join(timeout=0.5)
                        else:
                            import signal

                            try:
                                import os

                                os.kill(self._process.pid, signal.SIGKILL)
                                self._process.join(timeout=0.5)
                            except (ImportError, AttributeError, OSError):
                                print(
                                    f"无法强制终止进程: {self._process.pid}", flush=True
                                )
                    except Exception as kill_err:
                        print(f"强制终止进程时出错: {kill_err}", flush=True)

            # 清理队列资源
            try:
                # 尝试清空队列中的所有剩余数据
                while True:
                    try:
                        self._queue.get_nowait()
                    except (multiprocessing.queues.Empty, EOFError, BrokenPipeError):
                        break
                    except Exception:
                        break

                # 关闭并加入队列
                if hasattr(self._queue, "close"):
                    self._queue.close()
                if hasattr(self._queue, "join_thread"):
                    self._queue.join_thread()
                if hasattr(self._queue, "cancel_join_thread"):
                    # 在某些情况下可能需要取消等待队列线程结束
                    # 注意: 这可能导致资源泄漏，但在紧急情况下可以避免阻塞
                    try:
                        timeout_event = threading.Event()
                        timeout_thread = threading.Thread(
                            target=lambda: timeout_event.wait(0.5)
                            or self._queue.cancel_join_thread()
                        )
                        timeout_thread.daemon = True
                        timeout_thread.start()
                        timeout_thread.join(0.6)  # 稍微长一点的等待时间
                        timeout_event.set()  # 确保线程退出
                    except Exception as e:
                        print(f"取消等待队列线程时出错: {e}", flush=True)
            except Exception as queue_err:
                print(f"清理队列资源时出错: {queue_err}", flush=True)

            self._terminated = True
            return True

        except Exception as e:
            print(f"终止进程时出错: {e}", flush=True)
            # 即使出错，也尝试清理资源
            try:
                if self._process and self._process.is_alive():
                    self._process.terminate()
                    self._process.join(timeout=0.2)

                # 尝试清理队列
                if hasattr(self._queue, "close"):
                    try:
                        self._queue.close()
                    except Exception:
                        pass
            except Exception:
                pass
            return False

    def get_result(self, timeout: float = None, key: str = None) -> Any:
        """
        获取进程的普通结果数据。

        此方法尝试从子进程获取非列表类型的数据。如果进程已经结束，则直接返回
        结果容器中的数据；否则，会在指定的超时时间内等待子进程发送最新数据。

        Args:
            timeout (float, optional): 等待新数据的最长时间（秒）。如果为 None，
                                      则使用默认值 0.5 秒。
            key (str, optional): 要获取的特定键。如果为 None，则返回所有非列表数据。

        Returns:
            Any: 如果指定了 key，返回对应的值；否则返回所有非列表数据的字典。

        注意:
            即使超时，也会返回已接收到的最新数据。
            如果需要等待进程完成，应使用 wait_for_completion() 方法。
        """
        if not self._process or not self._process.is_alive():
            return (
                self._result_container.get(key)
                if key
                else self._result_container.get_all()
            )

        if timeout is None:
            timeout = 0.5

        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    msg_type, msg_data = self._queue.get(timeout=0.1)

                    if msg_type == "update":
                        # 更新容器数据
                        data, list_data = msg_data
                        self._result_container._data.update(data)
                        self._result_container._list_data.update(list_data)
                    elif msg_type == "result":
                        # 设置最终结果
                        self._final_result = msg_data
                        self._final_data_received = True
                    elif msg_type == "error":
                        # 处理错误
                        self._error = msg_data
                except multiprocessing.queues.Empty:
                    continue

        except Exception as e:
            print(f"获取结果时出错: {e}", flush=True)

        return (
            self._result_container.get(key) if key else self._result_container.get_all()
        )

    def get_error(self) -> Optional[str]:
        """
        获取进程执行过程中发生的错误信息。

        如果子进程执行过程中发生了异常，该方法将返回错误信息。
        错误信息包含异常的字符串表示和完整的堆栈跟踪。

        Returns:
            Optional[str]: 如果发生错误，返回错误信息；否则返回 None。

        注意:
            建议在 wait_for_completion() 返回 False 时调用此方法检查错误原因。
            错误信息结构为一个字典，包含 'error' 和 'traceback' 两个键。
        """
        return self._error

    def is_alive(self) -> bool:
        """
        检查进程是否仍在运行。

        此方法检查子进程是否仍在运行。它是对原生 Process.is_alive() 方法的封装，
        提供了额外的空值检查以避免异常。

        Returns:
            bool: 如果进程存在且仍在运行，返回 True；否则返回 False。

        注意:
            如果进程尚未启动（_process 为 None），也会返回 False。
        """
        return bool(self._process and self._process.is_alive())

    def cleanup(self):
        """
        清理进程相关的资源。

        此方法会优雅地终止仍在运行的进程，并清空结果容器中的列表数据。
        这是一个安全的方法，可以在任何时候调用，即使进程未启动或已结束也不会出错。

        注意:
            此方法会捕获并忽略所有异常，确保清理过程不会中断程序执行。
            在对象被销毁前会自动调用此方法（通过 __del__），但建议在不再需要进程时
            显式调用以立即释放资源。
        """
        try:
            if self._process and self._process.is_alive():
                self.terminate_gracefully()
            if self._result_container:
                self._result_container.clear_lists()
        except BaseException:
            pass

    def __del__(self):
        """
        析构函数，确保资源被正确清理。

        当 EnhancedProcess 对象被垃圾回收时，此方法会被自动调用。
        它会调用 cleanup() 方法确保所有资源被正确释放，包括终止进程和清理数据。

        注意:
            由于 Python 的垃圾回收机制，不能保证此方法在程序结束时一定会被调用，
            因此建议在不再需要进程时显式调用 cleanup() 方法。
        """
        try:
            self.cleanup()
        except BaseException:
            pass

    def get_list_data(
        self, timeout: float = None, key: str = None
    ) -> Union[Dict[str, list], list, None]:
        """
        获取进程的列表类型数据。

        此方法尝试从子进程获取列表类型的数据。如果进程已经结束，则直接返回
        结果容器中的数据；否则，会在指定的超时时间内等待子进程发送最新数据。

        Args:
            timeout (float, optional): 等待新数据的最长时间（秒）。如果为 None，
                                      则立即返回当前数据，不等待新数据。
            key (str, optional): 要获取的特定列表键。如果为 None，则返回所有列表数据。

        Returns:
            Union[Dict[str, list], list, None]: 如果指定了 key，返回对应的列表（如果key不存在则返回None）；
                                             否则返回包含所有列表数据的字典。

        注意:
            此方法专门用于获取通过 append_to_list() 方法添加的列表类型数据。
            对于非列表类型数据，应使用 get_result() 方法。
            当指定key但键不存在时，返回None而非空列表。
        """
        # 如果进程已结束或被终止，直接返回当前数据
        if not self._process or not self._process.is_alive() or self._terminated:
            data = self._result_container.get_all_lists()
            return data.get(key) if key else data

        # 如果不需要等待新数据，直接返回当前数据
        if timeout is None:
            data = self._result_container.get_all_lists()
            return data.get(key) if key else data

        try:
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    msg = self._queue.get(timeout=0.1)
                    if isinstance(msg, tuple) and len(msg) == 2:
                        msg_type, msg_data = msg
                        if msg_type == "update":
                            data, list_data = msg_data
                            self._result_container._data.update(data)
                            self._result_container._list_data.update(list_data)
                        elif msg_type == "result":
                            self._final_result = msg_data
                            self._final_data_received = True
                        elif msg_type == "error":
                            self._error = msg_data
                except multiprocessing.queues.Empty:
                    continue
                except (EOFError, BrokenPipeError, OSError, ValueError) as e:
                    # 队列可能已关闭
                    print(f"获取列表数据时队列可能已关闭: {e}", flush=True)
                    break

        except Exception as e:
            print(f"获取列表数据时出错: {e}", flush=True)

        data = self._result_container.get_all_lists()
        return data.get(key) if key else data

    def wait_for_completion(
        self, timeout: float = None, check_interval: float = 0.1
    ) -> bool:
        """
        等待进程完成执行。

        此方法会阻塞当前线程，直到子进程完成、出错或超时。在等待期间，
        会不断从队列中获取最新数据并更新结果容器。这是获取完整结果的推荐方式。

        Args:
            timeout (float, optional): 最长等待时间（秒）。如果为 None，则无限等待
                                      直到进程完成或出错。
            check_interval (float, optional): 检查进程状态的间隔时间（秒）。
                                             默认为 0.1 秒。

        Returns:
            bool: 如果进程正常完成返回 True，如果超时或出错返回 False。

        注意:
            如果返回 False，可以通过 get_error() 方法获取详细的错误信息。
            即使超时或出错，已收到的数据仍然可以通过 get_result() 或 get_list_data() 获取。
        """
        if not self._process:
            return False

        # 如果进程已被终止，直接返回成功
        if self._terminated:
            return True

        # 定义一个标志，表示队列是否关闭
        queue_closed = False

        def process_messages(wait_timeout: float = 0.1) -> Optional[bool]:
            """处理队列中的所有消息"""
            nonlocal queue_closed

            # 如果队列已关闭，直接返回
            if queue_closed:
                return None

            try:
                while not queue_closed:
                    try:
                        # 使用超时等待，确保不会错过消息
                        msg = self._queue.get(timeout=wait_timeout)
                        if isinstance(msg, tuple) and len(msg) == 2:
                            msg_type, msg_data = msg
                            if msg_type == "update":
                                data, list_data = msg_data
                                self._result_container._data.update(data)
                                self._result_container._list_data.update(list_data)
                            elif msg_type == "result":
                                self._final_result = msg_data
                                self._final_data_received = True
                            elif msg_type == "error":
                                self._error = msg_data
                                return False  # 立即返回错误状态
                            elif msg_type == "sync":
                                continue
                    except multiprocessing.queues.Empty:
                        break
                    except (EOFError, BrokenPipeError, OSError, ValueError) as e:
                        # 队列可能已关闭或无效
                        print(f"队列可能已关闭: {e}", flush=True)
                        queue_closed = True
                        return None  # 表示队列已关闭，不是错误
            except Exception as e:
                print(f"处理消息时出错: {e}", flush=True)
                return False
            return None

        start_time = time.time()
        while True:
            # 检查是否超时
            if timeout is not None and time.time() - start_time >= timeout:
                print("等待进程完成超时", flush=True)
                return False

            # 检查进程是否还在运行
            if not self._process.is_alive():
                # 如果进程已结束且已被标记为终止，视为成功完成
                if self._terminated:
                    return True

                # 尝试处理剩余的消息
                if not queue_closed:
                    try:
                        result = process_messages(wait_timeout=0.1)
                        if result is False:  # 如果有错误，返回False
                            return False
                    except Exception as e:
                        print(f"处理最终消息时出错: {e}", flush=True)
                return True

            # 检查是否已被终止
            if self._terminated:
                return True

            # 处理队列中的消息
            try:
                if not queue_closed:  # 只在队列未关闭时尝试处理消息
                    wait_time = min(
                        check_interval,
                        timeout - (time.time() - start_time) if timeout else check_interval,
                    )
                    result = process_messages(wait_timeout=wait_time)
                    if result is False:  # 如果有错误，返回False
                        return False
            except (EOFError, BrokenPipeError, OSError, ValueError) as queue_err:
                # 队列可能已关闭，这可能是因为进程被终止
                print(f"访问队列时出错，可能已关闭: {queue_err}", flush=True)
                queue_closed = True
                if not self._process.is_alive() or self._terminated:
                    return True
            except Exception as e:
                print(f"等待完成时出错: {e}", flush=True)

            # 如果队列已关闭且进程已终止，视为成功完成
            if queue_closed and (not self._process.is_alive() or self._terminated):
                return True

            # 计算剩余时间
            remaining_time = (
                timeout - (time.time() - start_time) if timeout else check_interval
            )
            if remaining_time <= 0:
                print("等待进程完成超时", flush=True)
                return False

            # 等待一段时间
            time.sleep(min(check_interval, remaining_time))

    def get_process_status(self) -> Dict[str, Any]:
        """
        获取进程的详细状态信息。

        此方法提供了进程当前状态的全面视图，包括运行状态、错误信息、结果状态等。
        它整合了来自多个内部属性的信息，以提供对进程状态的完整了解，无需调用多个方法。

        该方法具有极高的健壮性，能够处理进程处于任何状态的情况，包括未启动、
        正在运行、已完成、已出错、已终止等。所有可能导致异常的操作都有适当的错误处理。

        Returns:
            Dict[str, Any]: 包含以下键的字典：
                - status (str): 进程状态的描述性字符串，可能的值有：
                    * "not_started": 进程尚未启动
                    * "running": 进程正在运行
                    * "completed": 进程已正常完成
                    * "error": 进程执行时发生错误
                    * "terminated": 进程被手动终止
                    * "unknown": 无法确定进程状态

                - is_alive (bool): 进程是否仍在运行

                - has_error (bool): 是否有错误信息

                - error_info (Dict[str, str] | None): 如果有错误，返回错误信息字典，
                  包含 'error'（错误消息）和 'traceback'（堆栈跟踪）两个键；否则为 None

                - terminated (bool): 进程是否已被优雅终止

                - has_result (bool): 是否已接收到最终结果

                - result (Any | None): 最终结果（如果有）

                - progress (str | None): 进度信息（如果有设置）

                - data_count (int): 普通数据项的数量

                - list_data_count (int): 列表数据项的数量

                - list_data_keys (List[str]): 所有列表数据的键名

                - data_keys (List[str]): 所有普通数据的键名

        例子:
            >>> process = EnhancedProcess(target=my_function)
            >>> process.start()
            >>> time.sleep(1)  # 等待一些数据生成
            >>> status_info = process.get_process_status()
            >>> print(f"进程状态: {status_info['status']}")
            >>> print(f"进度: {status_info['progress']}")
            >>> if status_info['has_error']:
            ...     print(f"错误: {status_info['error_info']['error']}")
            >>> process.terminate_gracefully()

        注意:
            此方法不会修改进程的任何状态，是一个纯观察方法。
            它也不会阻塞等待新数据，只返回调用时刻的快照状态。
            如果需要最新数据，可以先调用 get_result() 或 get_list_data() 方法更新内部数据。
        """
        try:
            # 初始化返回字典
            status_info = {
                "status": "unknown",           # 默认为未知状态
                "is_alive": False,             # 进程是否在运行
                "has_error": False,            # 是否有错误
                "error_info": None,            # 错误信息
                "terminated": self._terminated,  # 是否已终止
                "has_result": self._final_data_received,  # 是否有结果
                "result": self._final_result,  # 最终结果
                "progress": None,              # 进度信息
                "data_count": 0,               # 普通数据数量
                "list_data_count": 0,          # 列表数据数量
                "list_data_keys": [],          # 列表数据键名
                "data_keys": []                # 普通数据键名
            }

            # 确定进程状态
            if self._process is None:
                # 进程尚未启动
                status_info["status"] = "not_started"
            else:
                # 检查进程是否在运行
                is_alive = False
                try:
                    is_alive = self._process.is_alive()
                except (AttributeError, ValueError):
                    # 在某些情况下is_alive可能会失败
                    is_alive = False

                status_info["is_alive"] = is_alive

                if is_alive:
                    # 进程正在运行
                    status_info["status"] = "running"
                elif self._terminated:
                    # 进程被终止 - 这是最优先的状态指示
                    status_info["status"] = "terminated"
                elif self._error is not None:
                    # 进程出错
                    status_info["status"] = "error"
                    status_info["has_error"] = True
                    status_info["error_info"] = self._error
                elif self._final_data_received:
                    # 进程正常完成
                    status_info["status"] = "completed"
                else:
                    # 进程不在运行，但未被标记为终止或完成
                    # 检查是否有数据来判断可能的状态
                    has_data = (len(self._result_container.get_all()) > 0 or
                                len(self._result_container.get_all_lists()) > 0)
                    if has_data:
                        # 如果有数据但没有明确的终止状态，则可能是被终止的
                        status_info["status"] = "terminated"
                    else:
                        # 无法确定确切状态
                        status_info["status"] = "unknown"

            # 获取结果容器中的数据信息
            if self._result_container:
                # 安全地获取普通数据
                try:
                    data = self._result_container.get_all()
                    status_info["data_count"] = len(data)
                    status_info["data_keys"] = list(data.keys())

                    # 尝试获取进度信息
                    if "progress" in data:
                        status_info["progress"] = data["progress"]
                except Exception as e:
                    print(f"获取普通数据信息时出错: {e}", flush=True)

                # 安全地获取列表数据
                try:
                    list_data = self._result_container.get_all_lists()
                    status_info["list_data_count"] = len(list_data)
                    status_info["list_data_keys"] = list(list_data.keys())
                except Exception as e:
                    print(f"获取列表数据信息时出错: {e}", flush=True)

            # 检查错误信息
            if self._error is not None:
                status_info["has_error"] = True
                status_info["error_info"] = self._error

            return status_info

        except Exception as e:
            # 即使在发生任何异常的情况下，也返回最小可用信息
            print(f"获取进程状态时发生未预期的错误: {e}", flush=True)
            traceback_info = traceback.format_exc()
            print(traceback_info, flush=True)

            # 返回带有错误信息的基本状态
            return {
                "status": "error_fetching_status",
                "is_alive": bool(self._process and self._process.is_alive()),
                "has_error": True,
                "error_info": {
                    "error": str(e),
                    "traceback": traceback_info
                },
                "terminated": self._terminated if hasattr(self, "_terminated") else None,
                "has_result": False,
                "result": None,
                "progress": None,
                "data_count": 0,
                "list_data_count": 0,
                "list_data_keys": [],
                "data_keys": []
            }


def test(result_container: ResultContainer):
    for i in range(100):
        result_container.append_to_list("array", f"name_1_{i}")
        result_container.append_to_list("array2", f"name_2_{i}")
        time.sleep(0.5)


def main():
    print("===============测试开始===============", flush=True)

    def thread_func(enhanced_process: EnhancedProcess):
        print("线程开始，将在2秒后终止进程", flush=True)
        time.sleep(2)
        print("开始终止进程...", flush=True)
        result = enhanced_process.terminate_gracefully()
        print(f"进程终止结果: {result}", flush=True)
        print("进程已终止", flush=True)

    enhanced_process = EnhancedProcess(target=test)
    enhanced_process.start()
    print("进程已启动，启动监控线程", flush=True)
    thread = threading.Thread(target=thread_func, args=(enhanced_process,))
    thread.daemon = True  # 确保主线程退出时线程也会退出
    thread.start()
    print("等待进程完成...", flush=True)
    wait_result = enhanced_process.wait_for_completion()
    result_data = enhanced_process.get_list_data()
    print(result_data)
    print(enhanced_process.get_process_status())
    thread.join()


if __name__ == "__main__":
    # 在 Windows 下需要保护入口点
    multiprocessing.freeze_support()
    main()
