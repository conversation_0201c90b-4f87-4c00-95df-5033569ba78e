#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL表达式验证统一接口

提供简化和详细两种验证接口，确保向后兼容性和易用性。

作者: PostgreSQL客户端修复团队
版本: 1.0.0
"""

from typing import Optional, Dict, Any
from .sql_expression_validator import (
    UniversalSQLValidator, 
    ValidationResult, 
    ValidationConfig, 
    ValidationLevel
)

# 全局验证器实例（单例模式）
_global_validator: Optional[UniversalSQLValidator] = None


def get_validator(config: Optional[ValidationConfig] = None) -> UniversalSQLValidator:
    """
    获取全局验证器实例
    
    Args:
        config (ValidationConfig, optional): 验证配置，仅在首次调用时生效
        
    Returns:
        UniversalSQLValidator: 验证器实例
    """
    global _global_validator
    if _global_validator is None:
        _global_validator = UniversalSQLValidator(config)
    return _global_validator


def is_valid_sql_expression(sql_expr: str, **kwargs) -> bool:
    """
    简化的SQL表达式验证接口

    提供向后兼容的简单验证接口，返回布尔值结果。
    使用经过验证的简化算法，专门解决IS NULL和IS NOT NULL的验证问题。

    Args:
        sql_expr (str): 要验证的SQL表达式
        **kwargs: 验证配置参数（保留兼容性，当前版本忽略）

    Returns:
        bool: True表示有效，False表示无效

    Examples:
        >>> is_valid_sql_expression('("field" IS NOT NULL)')
        True
        >>> is_valid_sql_expression('"field_name"')
        False
        >>> is_valid_sql_expression('("segmentation_data" IS NOT NULL)')
        True
        >>> is_valid_sql_expression('("id" = 1 AND "name" LIKE \'%test%\')')
        True
    """
    try:
        # 使用简化但有效的验证器
        from .simple_sql_validator import is_valid_sql_expression as simple_validator
        return simple_validator(sql_expr)

    except Exception:
        # 发生任何异常时，为了安全起见返回False
        return False


def validate_sql_expression_detailed(sql_expr: str, **kwargs) -> dict:
    """
    详细的SQL表达式验证接口

    提供完整的验证结果和诊断信息，适用于需要详细错误信息的场景。

    Args:
        sql_expr (str): 要验证的SQL表达式
        **kwargs: 验证配置参数（保留兼容性，当前版本忽略）

    Returns:
        dict: 详细的验证结果字典

    Examples:
        >>> result = validate_sql_expression_detailed('invalid_expr')
        >>> print(f"有效性: {result['is_valid']}")
        >>> print(f"错误类型: {result['error_type']}")
        >>> print(f"错误信息: {result['error_message']}")
        >>> print(f"置信度: {result['confidence_score']}")
    """
    try:
        # 使用简化但有效的验证器
        from .simple_sql_validator import validate_sql_expression_detailed as simple_detailed
        return simple_detailed(sql_expr)

    except Exception as e:
        # 发生异常时返回错误结果
        return {
            'is_valid': False,
            'error_type': 'syntax_error',
            'error_message': f"验证过程发生异常: {str(e)}",
            'confidence_score': 0.0,
            'suggestions': []
        }


def validate_sql_expression_simple(sql_expr: str) -> Dict[str, Any]:
    """
    简化的验证接口，返回字典格式结果
    
    Args:
        sql_expr (str): 要验证的SQL表达式
        
    Returns:
        Dict[str, Any]: 包含验证结果的字典
            - is_valid (bool): 是否有效
            - error_message (str): 错误信息（如果有）
            - confidence_score (float): 置信度
            
    Examples:
        >>> result = validate_sql_expression_simple('("field" IS NOT NULL)')
        >>> print(result)
        {'is_valid': True, 'error_message': '', 'confidence_score': 0.85}
    """
    detailed_result = validate_sql_expression_detailed(sql_expr)
    
    return {
        'is_valid': detailed_result.is_valid,
        'error_message': detailed_result.error_message,
        'confidence_score': detailed_result.confidence_score
    }


def reset_validator():
    """
    重置全局验证器实例
    
    清空缓存并重新初始化验证器，用于测试或配置更改后的重置。
    """
    global _global_validator
    if _global_validator:
        _global_validator.clear_cache()
    _global_validator = None


def get_validator_info() -> Dict[str, Any]:
    """
    获取验证器信息
    
    Returns:
        Dict[str, Any]: 验证器信息
            - version (str): 版本号
            - cache_size (int): 缓存大小
            - supported_operators (dict): 支持的运算符
    """
    validator = get_validator()
    
    return {
        'version': '1.0.0',
        'cache_size': len(validator._validation_cache),
        'supported_operators': list(validator.config.supported_operators.keys()),
        'strictness': validator.config.strictness.value
    }


# 向后兼容的别名
validate_sql = is_valid_sql_expression
check_sql_expression = is_valid_sql_expression
