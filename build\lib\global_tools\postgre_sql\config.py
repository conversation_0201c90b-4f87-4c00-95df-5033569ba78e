"""
PostgreSQL数据库配置管理模块

提供灵活的配置管理功能，支持从多种来源加载配置：
1. 默认配置
2. 配置文件（JSON、INI等）
3. 环境变量
4. 程序参数

配置优先级：程序参数 > 环境变量 > 配置文件 > 默认配置
"""

import os
import json
import logging
import configparser
import traceback
from typing import Dict, Any, Optional, Union

from .exceptions import ConfigurationError


class DBConfig:
    """数据库配置管理类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 连接配置
        "host": "localhost",
        "port": 5432,
        "user": "postgres",
        "password": "",
        "database": "postgres",
        
        # 连接池配置
        "min_connections": 1,
        "max_connections": 10,
        "connection_timeout": 30,  # 连接超时时间(秒)
        "pool_recycle": 3600,      # 连接回收时间(秒)
        
        # 执行配置
        "query_timeout": 60,       # 查询超时时间(秒)
        "statement_timeout": 60,   # SQL语句超时时间(秒)
        "retry_count": 3,          # 重试次数
        "retry_delay": 1,          # 重试延迟(秒)
        
        # 日志配置
        "log_level": "INFO",
        "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "log_sql": False,          # 是否记录SQL语句
        
        # 其他配置
        "application_name": "PostgreSQLClient",
        "schema": "public",
    }
    
    # 环境变量前缀
    ENV_PREFIX = "PGDB_"
    
    def __init__(self, config_path: Optional[str] = None, **kwargs):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，可选
            **kwargs: 直接指定的配置参数
        """
        # 初始化配置字典，从默认配置开始
        self.config = self.DEFAULT_CONFIG.copy()
        
        # 从配置文件加载
        if config_path:
            self._load_from_file(config_path)
            
        # 从环境变量加载
        self._load_from_env()
        
        # 从程序参数加载
        if kwargs:
            self.config.update(self._normalize_config(kwargs))
            
        # 验证必要的配置项
        self._validate_config()
        
    def _load_from_file(self, path: str) -> None:
        """
        从配置文件加载配置
        
        Args:
            path: 配置文件路径
            
        Raises:
            ConfigurationError: 如果配置文件格式不支持或读取失败
        """
        if not os.path.exists(path):
            raise ConfigurationError(f"配置文件不存在: {path}")
            
        ext = os.path.splitext(path)[1].lower()
        
        try:
            if ext == '.json':
                with open(path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(self._normalize_config(file_config))
            elif ext in ('.ini', '.cfg'):
                parser = configparser.ConfigParser()
                parser.read(path, encoding='utf-8')
                
                # 尝试读取database或postgresql部分
                for section in ['database', 'postgresql', 'db']:
                    if section in parser:
                        section_dict = {k: self._parse_value(v) for k, v in parser[section].items()}
                        self.config.update(self._normalize_config(section_dict))
                        break
            elif ext in ('.yaml', '.yml'):
                try:
                    import yaml
                    with open(path, 'r', encoding='utf-8') as f:
                        file_config = yaml.safe_load(f)
                        # 尝试读取database或postgresql部分
                        for section in ['database', 'postgresql', 'db']:
                            if section in file_config:
                                self.config.update(self._normalize_config(file_config[section]))
                                break
                        else:
                            # 如果没有特定部分，使用整个文件
                            self.config.update(self._normalize_config(file_config))
                except ImportError:
                    traceback.print_exc()  # 打印详细异常堆栈
                    raise ConfigurationError("需要安装PyYAML库以支持YAML配置文件")
            else:
                raise ConfigurationError(f"不支持的配置文件格式: {ext}")
        except Exception as e:
            traceback.print_exc()  # 打印详细异常堆栈
            raise ConfigurationError(f"读取配置文件失败: {str(e)}")
            
    def _load_from_env(self) -> None:
        """从环境变量加载配置"""
        env_config = {}
        
        # 查找所有以前缀开头的环境变量
        for key, value in os.environ.items():
            if key.startswith(self.ENV_PREFIX):
                # 移除前缀并转换为小写
                config_key = key[len(self.ENV_PREFIX):].lower()
                env_config[config_key] = self._parse_value(value)
                
        # 更新配置
        if env_config:
            self.config.update(self._normalize_config(env_config))
            
    def _parse_value(self, value: str) -> Any:
        """
        解析配置值，尝试转换为适当的类型
        
        Args:
            value: 配置值字符串
            
        Returns:
            解析后的配置值
        """
        # 尝试解析布尔值
        if value.lower() in ('true', 'yes', 'on', '1'):
            return True
        elif value.lower() in ('false', 'no', 'off', '0'):
            return False
            
        # 尝试解析数字
        try:
            # 尝试解析为整数
            return int(value)
        except ValueError:
            try:
                # 尝试解析为浮点数
                return float(value)
            except ValueError:
                traceback.print_exc()  # 打印详细异常堆栈
                # 保持为字符串
                return value
                
    def _normalize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        规范化配置键名，将键名转换为小写
        
        Args:
            config: 配置字典
            
        Returns:
            规范化后的配置字典
        """
        return {k.lower(): v for k, v in config.items()}
        
    def _validate_config(self) -> None:
        """
        验证必要的配置项
        
        Raises:
            ConfigurationError: 如果缺少必要的配置项
        """
        # 检查必要的配置项
        required_fields = ['host', 'port', 'database']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        
        if missing_fields:
            raise ConfigurationError(f"缺少必要的配置项: {', '.join(missing_fields)}")
            
        # 验证端口是整数
        try:
            self.config['port'] = int(self.config['port'])
        except (ValueError, TypeError):
            traceback.print_exc()  # 打印详细异常堆栈
            raise ConfigurationError(f"端口必须是整数: {self.config['port']}")
            
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            配置字典
        """
        return self.config.copy()
        
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取指定配置项
        
        Args:
            key: 配置项名称
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        return self.config.get(key.lower(), default)
        
    def get_connection_params(self) -> Dict[str, Any]:
        """
        获取数据库连接参数
        
        Returns:
            连接参数字典
        """
        # 提取连接相关的参数
        params = {
            'host': self.config['host'],
            'port': self.config['port'],
            'database': self.config['database'],
            'user': self.config['user'],
            'password': self.config['password'],
        }
        
        # 添加应用名称
        if 'application_name' in self.config:
            params['application_name'] = self.config['application_name']
            
        return params
        
    def get_pool_params(self) -> Dict[str, Any]:
        """
        获取连接池参数
        
        Returns:
            连接池参数字典
        """
        return {
            'minconn': self.config['min_connections'],
            'maxconn': self.config['max_connections'],
        }
        
    def get_log_level(self) -> int:
        """
        获取日志级别
        
        Returns:
            logging模块定义的日志级别
        """
        level_str = str(self.config['log_level']).upper()
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        return level_map.get(level_str, logging.INFO)
        
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        # 创建配置副本，隐藏密码
        config_copy = self.config.copy()
        if 'password' in config_copy:
            config_copy['password'] = '******'
            
        return f"DBConfig({json.dumps(config_copy, indent=2)})"
        
    def __repr__(self) -> str:
        """返回配置的字符串表示"""
        return self.__str__() 