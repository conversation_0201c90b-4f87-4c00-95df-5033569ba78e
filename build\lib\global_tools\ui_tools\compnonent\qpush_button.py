import traceback
import threading
from typing import Callable, Dict, List, Optional, Any, Tuple
from PyQt5.QtWidgets import (
    QPushButton,
    QGraphicsOpacityEffect,
)
from PyQt5.QtCore import (
    QRegExp,
    QThread,
    pyqtSignal,
    QObject,
    Qt,
    QTimer,
    QEvent,
    QTime,
)
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5 import sip
from PyQt5.QtWidgets import *
import re
from typing import *
import logging
from functools import partial
from global_tools.utils import ClassInstanceManager,Logger


class QPushButtonManager(QObject):
    """
    QPushButtonManager 是一个功能强大的工具类，用于管理多个QPushButton控件，
    增强其功能和交互体验。提供了信号连接、样式切换、动画效果等丰富功能（单例模式）。

    本类是对QPushButtonHelper的增强版本，支持同时管理多个QPushButton控件，
    通过按钮名称（objectName或文本）进行访问。

    ===== 单例模式说明 =====
    该类实现了线程安全的单例模式：
    - 首次创建时需要传入按钮参数
    - 后续调用可以不传参数，直接返回已创建的实例
    - 支持重置单例实例，重新创建新的管理器
    - 在多线程环境下保证线程安全

    ===== 单例模式使用示例 =====

    1. 创建和初始化（单例模式）:
       ```python
       # 创建多个QPushButton
       save_btn = QPushButton("保存")
       save_btn.setObjectName("save")

       cancel_btn = QPushButton("取消")
       cancel_btn.setObjectName("cancel")

       submit_btn = QPushButton("提交")
       submit_btn.setObjectName("submit")

       # 方式1：直接创建管理器（首次创建）
       manager = QPushButtonManager([save_btn, cancel_btn, submit_btn])

       # 方式2：使用推荐的类方法创建（首次创建）
       manager = QPushButtonManager.get_instance([save_btn, cancel_btn, submit_btn])

       # 方式3：后续获取已创建的实例（无需参数）
       manager = QPushButtonManager.get_instance()
       # 或者
       manager = QPushButtonManager()
       ```

    2. 按钮管理和操作:
       ```python
       # 设置按钮状态
       manager.set_enabled("save", True)
       manager.set_visible("cancel", False)

       # 设置按钮样式和特效
       manager.enable_auto_style("save", True)
       manager.add_hover_opacity_effect("submit", 1.0, 0.8, 200)

       # 连接信号处理
       def on_button_clicked(name, checked):
           print(f"按钮 '{name}' 被点击了，状态: {checked}")

       manager.button_clicked.connect(on_button_clicked)
       ```

    3. 单例模式管理:
       ```python
       # 检查实例是否已创建
       if QPushButtonManager.is_instance_created():
           manager = QPushButtonManager.get_instance()
       else:
           # 首次创建
           manager = QPushButtonManager.get_instance([btn1, btn2])

       # 获取实例信息
       info = QPushButtonManager.get_instance_info()
       if info:
           print(f"管理的按钮数量: {info['button_count']}")

       # 重置单例实例（在需要重新初始化时）
       QPushButtonManager.reset_instance()
       new_manager = QPushButtonManager.get_instance([new_btn1, new_btn2])
       ```

    使用示例集锦：

    1. 基本初始化和使用:
    ```python
    from PyQt5.QtWidgets import QPushButton
    from global_tools.ui_tools.compnonent.qpush_button import QPushButtonManager

    # 创建多个按钮
    button1 = QPushButton("按钮1")
    button1.setObjectName("btn1")
    button2 = QPushButton("按钮2")
    button2.setObjectName("btn2")

    # 创建按钮管理器，传入多个按钮
    manager = QPushButtonManager([button1, button2], parent=main_window)

    # 监听按钮点击信号
    manager.button_clicked.connect(lambda name, checked: print(f"按钮 {name} 被点击了: {checked}"))
    ```

    2. 按钮状态和样式管理:
    ```python
    # 禁用特定按钮(会自动应用禁用样式，如果启用了auto_style)
    manager.set_enabled("btn1", False)

    # 设置特定按钮的自定义禁用样式
    manager.set_disabled_style("btn1", '''
        QPushButton:disabled {
            color: #666666;
            background-color: #EEEEEE;
            border: 1px solid #AAAAAA;
        }
    ''')

    # 启用特定按钮的自动样式切换功能
    manager.enable_auto_style("btn1", True)

    # 临时重置特定按钮为原始样式
    manager.reset_to_original_style("btn1")
    ```

    3. 按钮外观设置:
    ```python
    # 设置按钮文本
    manager.set_text("btn1", "新按钮文本")

    # 设置工具提示
    manager.set_tooltip("btn2", "这是一个按钮提示")

    # 设置图标
    from PyQt5.QtGui import QIcon
    from PyQt5.QtCore import QSize
    icon = QIcon("path/to/icon.png")
    manager.set_icon("btn1", icon, QSize(16, 16))
    ```

    4. 信号连接管理:
    ```python
    # 定义按钮点击处理函数
    def on_button1_clicked():
        print("按钮1被点击")

    def on_button2_clicked():
        print("按钮2被点击")

    # 连接特定按钮的点击信号
    manager.connect_clicked_signal("btn1", on_button1_clicked)
    manager.connect_clicked_signal("btn2", on_button2_clicked)

    # 断开特定函数的连接
    manager.disconnect_clicked_signal("btn1", on_button1_clicked)

    # 断开按钮的所有连接
    manager.disconnect_clicked_signal("btn2")
    ```

    5. 高级效果:
    ```python
    # 为特定按钮添加悬停透明度动画效果
    manager.add_hover_opacity_effect("btn1",
        start_opacity=1.0,  # 正常状态下不透明
        end_opacity=0.8,    # 悬停时轻微透明
        duration=200        # 200毫秒完成过渡
    )

    # 为特定按钮添加点击防抖动(避免短时间内多次点击触发多次事件)
    manager.enable_debounce("btn2", interval_ms=300)
    ```

    6. 按钮管理:
    ```python
    # 获取所有按钮名称
    button_names = manager.get_all_button_names()
    print(f"管理的按钮: {', '.join(button_names)}")

    # 检查特定按钮是否存在
    if manager.has_button("btn3"):
        print("按钮3已存在")
    else:
        print("按钮3不存在")

    # 添加新按钮
    new_button = QPushButton("新按钮")
    manager.add_button(new_button)

    # 移除按钮
    manager.remove_button("btn1")
    ```

    7. 批量操作:
    ```python
    # 禁用所有按钮
    results = manager.set_all_enabled(False)

    # 为所有按钮启用自动样式
    manager.enable_all_auto_style(True)

    # 监听批量启用/禁用信号
    manager.all_enabled_changed.connect(lambda enabled, count: print(f"已{'启用' if enabled else '禁用'} {count} 个按钮"))

    # 批量启用/禁用信号的高级用法
    def on_all_buttons_state_changed(enabled, count):
        # 可以在这里执行UI更新或其他操作
        status_bar.showMessage(f"已{'启用' if enabled else '禁用'} {count} 个按钮", 3000)
        if not enabled:
            # 例如，在禁用所有按钮后，显示一个加载指示器
            progress_indicator.setVisible(True)

    # 连接信号到自定义处理函数
    manager.all_enabled_changed.connect(on_all_buttons_state_changed)
    ```

    8. 信号和槽机制的使用:
    ```python
    # 监听单个按钮启用状态变化
    manager.enabled_changed.connect(lambda name, enabled: print(f"按钮 {name} 已{'启用' if enabled else '禁用'}"))

    # 监听按钮文本变化
    manager.text_changed.connect(lambda name, text: print(f"按钮 {name} 文本已更改为: {text}"))

    # 监听按钮可见性变化
    manager.visibility_changed.connect(lambda name, visible: print(f"按钮 {name} 已{'显示' if visible else '隐藏'}"))
    ```

    9. 焦点控制:
    ```python
    # 启用焦点保持功能，防止按钮启用/禁用时导致QLineEdit自动获取焦点
    manager.set_preserve_focus(True)

    # 检查焦点保持功能是否启用
    if manager.is_preserve_focus_enabled():
        print("焦点保持功能已启用")
    else:
        print("焦点保持功能已禁用")

    # 启用禁用按钮后焦点转移到父容器功能
    manager.set_focus_to_parent_on_disable(True)

    # 设置禁用按钮后焦点转移到父容器的焦点策略
    from PyQt5.QtCore import Qt
    manager.set_parent_focus_policy(Qt.StrongFocus)  # 使用强焦点策略
    ```
    """

    # =================================================================================
    # 单例模式相关类变量
    # =================================================================================
    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _initialized = False  # 初始化标志
    _cached_args = None  # 缓存的构造参数
    _cached_kwargs = None  # 缓存的关键字参数

    # --- 自定义信号 ---
    # 当按钮被点击时发出，参数为按钮名称和按钮的checked状态
    button_clicked = pyqtSignal(str, bool)
    # 当按钮的启用状态变化时发出，参数为按钮名称和新的启用状态
    enabled_changed = pyqtSignal(str, bool)
    # 当请求更改按钮启用状态时发出，参数为按钮名称和目标启用状态
    # enable_button_requested = pyqtSignal(str, bool)
    # 当按钮的可见性变化时发出，参数为按钮名称和新的可见性状态
    visibility_changed = pyqtSignal(str, bool)
    # 当按钮的文本变化时发出，参数为按钮名称和新的文本
    text_changed = pyqtSignal(str, str)
    # 当按钮的工具提示变化时发出，参数为按钮名称和新的工具提示
    tooltip_changed = pyqtSignal(str, str)
    # 当按钮的图标变化时发出，参数为按钮名称
    icon_changed = pyqtSignal(str)
    # 当按钮的可检查状态变化时发出，参数为按钮名称和新的可检查状态
    checkable_changed = pyqtSignal(str, bool)
    # 当按钮的选中状态变化时发出，参数为按钮名称和新的选中状态
    checked_changed = pyqtSignal(str, bool)
    # 当所有按钮的启用状态被批量设置时发出，参数为设置的启用状态(bool)和操作成功的按钮数量(int)
    # 可用于在批量操作后执行UI更新或其他相关操作
    all_enabled_changed = pyqtSignal(bool, int)

    # =================================================================================
    # Internal Signals for Thread-Safe UI Updates
    # ---------------------------------------------------------------------------------
    # 以下信号用于在内部实现线程安全的UI更新。
    # 公共方法（如 set_text）会发射这些信号，而不是直接修改QPushButton控件。
    # 信号会被连接到相应的私有槽函数，Qt的事件循环机制会确保这些槽函数总是在主GUI线程中执行，
    # 从而避免跨线程操作UI导致的崩溃。
    # =================================================================================
    # 请求安全地设置按钮文本 (button_name, text)
    __set_text_signal = pyqtSignal(str, str)
    # 请求安全地设置按钮工具提示 (button_name, tooltip)
    __set_tooltip_signal = pyqtSignal(str, str)
    # 请求安全地设置按钮图标 (button_name, icon, size)
    __set_icon_signal = pyqtSignal(str, QIcon, object)
    # 请求安全地设置按钮的启用/禁用状态 (button_name, enabled)
    __set_enabled_signal = pyqtSignal(str, bool)
    # 请求安全地设置按钮的可见性 (button_name, visible)
    __set_visible_signal = pyqtSignal(str, bool)
    # 请求安全地设置按钮是否可检查 (button_name, checkable)
    __set_checkable_signal = pyqtSignal(str, bool)
    # 请求安全地设置按钮的选中状态 (button_name, checked)
    __set_checked_signal = pyqtSignal(str, bool)
    # 请求安全地设置按钮的禁用样式 (button_name, stylesheet)
    __set_disabled_style_signal = pyqtSignal(str, str)
    # 请求安全地将按钮样式重置为原始状态 (button_name)
    __reset_to_original_style_signal = pyqtSignal(str)
    # 请求安全地启用/禁用自动样式功能 (button_name, enable)
    __enable_auto_style_signal = pyqtSignal(str, bool)
    # 请求安全地添加悬停透明度效果 (button_name, start_opacity, end_opacity, duration)
    __add_hover_opacity_effect_signal = pyqtSignal(str, float, float, int)
    # 请求安全地启用/禁用点击防抖动 (button_name, interval_ms)
    __enable_debounce_signal = pyqtSignal(str, int)
    # 请求安全地设置是否保持焦点 (preserve)
    __set_preserve_focus_signal = pyqtSignal(bool)
    # 请求安全地设置禁用时是否将焦点转移到父容器 (enable)
    __set_focus_to_parent_on_disable_signal = pyqtSignal(bool)
    # 请求安全地设置父容器的焦点策略 (policy)
    __set_parent_focus_policy_signal = pyqtSignal(object)

    def __new__(cls, buttons=None, parent: Optional[QObject] = None, auto_style: bool = False):
        """
        单例模式的 __new__ 方法

        实现线程安全的单例模式：
        - 如果实例不存在，创建新实例
        - 如果实例已存在且有效，直接返回现有实例
        - 如果实例已存在但无效（被删除），重新创建实例

        Args:
            buttons: 要管理的QPushButton列表，可以是单个按钮、按钮列表或None（首次创建时需要）
            parent (Optional[QObject]): 父对象（可选）
            auto_style (bool): 是否为所有按钮启用自动样式，默认为False

        Returns:
            QPushButtonManager: 单例实例
        """
        # 双重检查锁定模式，确保线程安全
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建新实例
                    cls._instance = super(QPushButtonManager, cls).__new__(cls)
                    cls._initialized = False
                    # 缓存参数供后续使用
                    cls._cached_args = buttons
                    cls._cached_kwargs = {'parent': parent, 'auto_style': auto_style}
        else:
            # 检查现有实例是否仍然有效
            try:
                if sip.isdeleted(cls._instance):
                    # 实例已被删除，重新创建
                    with cls._lock:
                        if sip.isdeleted(cls._instance):
                            cls._instance = super(QPushButtonManager, cls).__new__(cls)
                            cls._initialized = False
                            # 如果没有传入新参数，使用缓存的参数
                            if buttons is None and cls._cached_args:
                                cls._cached_args = cls._cached_args
                                cls._cached_kwargs = cls._cached_kwargs
                            else:
                                cls._cached_args = buttons
                                cls._cached_kwargs = {'parent': parent, 'auto_style': auto_style}
            except RuntimeError:
                # 处理可能的运行时错误
                with cls._lock:
                    cls._instance = super(QPushButtonManager, cls).__new__(cls)
                    cls._initialized = False
                    cls._cached_args = buttons if buttons is not None else cls._cached_args
                    cls._cached_kwargs = {'parent': parent, 'auto_style': auto_style}

        return cls._instance

    def __init__(self, buttons=None, parent: Optional[QObject] = None, auto_style: bool = False):
        """
        初始化QPushButtonManager（单例模式）

        Args:
            buttons: 要管理的QPushButton列表，可以是单个按钮、按钮列表或None
                    首次创建时必须提供，后续调用可以为空（使用缓存的参数）
            parent: Qt父对象，默认为None
            auto_style: 是否为所有按钮启用自动样式，默认为False

        注意：
            - 如果实例已经初始化过，此方法会直接返回，不会重复初始化
            - 如果没有传入参数且有缓存参数，会使用缓存的参数进行初始化
            - 可以通过 reset_instance() 方法重置单例实例

        示例:
        ```python
        # 方式1：直接创建管理器（首次创建）
        button1 = QPushButton("按钮1")
        button1.setObjectName("btn1")
        button2 = QPushButton("按钮2")
        button2.setObjectName("btn2")
        manager = QPushButtonManager([button1, button2])

        # 方式2：使用推荐的类方法创建（首次创建）
        manager = QPushButtonManager.get_instance([button1, button2])

        # 方式3：后续获取已创建的实例（无需参数）
        manager = QPushButtonManager.get_instance()
        # 或者
        manager = QPushButtonManager()
        ```
        """
        # 检查是否已经初始化过
        if self.__class__._initialized:
            return

        # 如果没有传入参数，尝试使用缓存的参数
        if buttons is None and self.__class__._cached_args:
            buttons = self.__class__._cached_args
            if self.__class__._cached_kwargs:
                parent = self.__class__._cached_kwargs.get('parent', parent)
                auto_style = self.__class__._cached_kwargs.get('auto_style', auto_style)

        # 如果仍然没有参数，记录警告但继续初始化
        if buttons is None:
            # 延迟获取logger，避免循环依赖
            try:
                logger = ClassInstanceManager.get_instance(key="ui_logger")
                if logger:
                    logger.warning("QPushButtonManager 初始化时未提供按钮，将创建空的管理器")
            except:
                pass  # 如果获取logger失败，忽略警告

        super().__init__(parent)

        # 初始化日志记录器
        # self.__logger = self.__get_logger()
        self.__logger:Logger = ClassInstanceManager.get_instance(key="ui_logger") # type: ignore

        # -------------------------------------------------
        # 连接内部信号到槽函数，以实现线程安全的UI更新
        # -------------------------------------------------
        self.__set_text_signal.connect(self.__on_set_text)
        self.__set_tooltip_signal.connect(self.__on_set_tooltip)
        self.__set_icon_signal.connect(self.__on_set_icon)
        self.__set_enabled_signal.connect(self.__on_set_enabled)
        self.__set_visible_signal.connect(self.__on_set_visible)
        self.__set_checkable_signal.connect(self.__on_set_checkable)
        self.__set_checked_signal.connect(self.__on_set_checked)
        self.__set_disabled_style_signal.connect(self.__on_set_disabled_style)
        self.__reset_to_original_style_signal.connect(self.__on_reset_to_original_style)
        self.__enable_auto_style_signal.connect(self.__on_enable_auto_style)
        self.__add_hover_opacity_effect_signal.connect(self.__on_add_hover_opacity_effect)
        self.__enable_debounce_signal.connect(self.__on_enable_debounce)
        self.__set_preserve_focus_signal.connect(self.__on_set_preserve_focus)
        self.__set_focus_to_parent_on_disable_signal.connect(self.__on_set_focus_to_parent_on_disable)
        self.__set_parent_focus_policy_signal.connect(self.__on_set_parent_focus_policy)

        # 存储按钮和相关状态的字典
        self.__buttons: Dict[str, QPushButton] = {}  # 存储按钮实例 {name: QPushButton}
        self.__opacity_effects: Dict[str, QGraphicsOpacityEffect] = {}  # 存储透明度效果 {name: QGraphicsOpacityEffect}
        self.__opacity_animations_in = {}  # 存储鼠标进入动画 {name: QPropertyAnimation}
        self.__opacity_animations_out = {}  # 存储鼠标离开动画 {name: QPropertyAnimation}
        self.__debounce_timers = {}  # 存储防抖动定时器 {name: QTimer}
        self.__debounce_intervals = {}  # 存储防抖动间隔 {name: int}
        self.__last_emit_times = {}  # 存储上次信号发出时间 {name: int}
        self.__original_stylesheets = {}  # 存储原始样式表 {name: str}
        self.__disabled_stylesheets = {}  # 存储禁用样式表 {name: str}
        self.__auto_style_enabled = {}  # 存储自动样式状态 {name: bool}
        self.__is_watching_enabled = {}  # 存储是否监听启用状态 {name: bool}
        self.__internal_enabled_change = {}  # 存储是否由内部方法触发启用状态变化 {name: bool}
        self.__preserve_focus = True  # 是否在启用/禁用按钮时保持当前焦点
        self.__focus_to_parent = True  # 是否在禁用按钮时将焦点设置到父容器
        self.__parent_focus_policy = Qt.StrongFocus  # type: ignore # 设置父容器的焦点策略

        # 初始化默认禁用样式表
        self.__default_disabled_stylesheet = """
            QPushButton:disabled {
                color: #888888;
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
            }
        """

        # 处理输入的按钮列表
        if buttons is not None:
            # 如果传入的是单个按钮，转为列表
            if isinstance(buttons, QPushButton):
                buttons = [buttons]
            # 添加所有按钮
            for button in buttons:
                if isinstance(button, QPushButton):
                    self.add_button(button, auto_style)
                elif isinstance(button, (list, tuple)):
                    # 处理嵌套列表
                    for btn in button:
                        if isinstance(btn, QPushButton):
                            self.add_button(btn, auto_style)

        # 标记为已初始化
        self.__class__._initialized = True
        self.__logger.debug(f"QPushButtonManager 已初始化，管理 {len(self.__buttons)} 个按钮")

    @classmethod
    def __get_logger(cls) -> logging.Logger:
        """
        获取或创建类的内部日志记录器。
        这是一个类方法，确保所有实例共享同一个日志记录器。

        Returns:
            logging.Logger: 配置好的日志记录器实例
        """
        logger = logging.getLogger("QPushButtonManager")
        if not logger.handlers:
            # 如果需要特定配置且没有已存在的处理器，可以添加处理器
            pass
        return logger

    def add_button(self, button: QPushButton, auto_style: bool = False) -> bool:
        """
        添加一个按钮到管理器。

        Args:
            button (QPushButton): 要添加的QPushButton实例
            auto_style (bool): 是否为该按钮启用自动样式，默认为False

        Returns:
            bool: 添加成功返回True，失败返回False

        示例:
        ```python
        button = QPushButton("新按钮")
        button.setObjectName("new_btn")
        success = manager.add_button(button)
        if success:
            print("按钮添加成功")
        ```
        """
        if not isinstance(button, QPushButton):
            self.__logger.error(f"添加按钮失败：传入的对象不是QPushButton实例")
            return False

        # 使用objectName作为键，如果没有则使用按钮文本
        button_name = button.objectName() or button.text()
        if not button_name:
            self.__logger.error(f"添加按钮失败：按钮没有objectName或文本")
            return False

        # 检查按钮名称是否已存在
        if button_name in self.__buttons:
            self.__logger.warning(f"按钮名称 '{button_name}' 已存在，将覆盖原有按钮")

        try:
            # 存储按钮及其状态
            self.__buttons[button_name] = button
            self.__original_stylesheets[button_name] = button.styleSheet()
            self.__disabled_stylesheets[button_name] = self.__default_disabled_stylesheet
            self.__auto_style_enabled[button_name] = False
            self.__is_watching_enabled[button_name] = False
            self.__debounce_intervals[button_name] = 0
            self.__last_emit_times[button_name] = 0
            self.__internal_enabled_change[button_name] = False

            # 设置内部连接
            self.__setup_internal_connections(button_name)

            # 如果指定了自动启用样式切换，则启用它
            if auto_style:
                self.enable_auto_style(button_name, True)
                self.__logger.debug(f"按钮 '{button_name}' 在添加时启用了自动样式功能")

            self.__logger.debug(f"按钮 '{button_name}' 已添加到管理器")
            return True
        except Exception as e:
            self.__logger.error(f"添加按钮 '{button_name}' 时发生错误: {e}")
            traceback.print_exc()
            return False

    def remove_button(self, button_name: str) -> bool:
        """
        从管理器中移除指定名称的按钮。

        Args:
            button_name (str): 要移除的按钮名称

        Returns:
            bool: 移除成功返回True，失败返回False

        示例:
        ```python
        success = manager.remove_button("temp_btn")
        if success:
            print("临时按钮已移除")
        ```
        """
        if not self.has_button(button_name):
            self.__logger.warning(f"移除按钮失败：按钮 '{button_name}' 不存在")
            return False

        try:
            button = self.__buttons[button_name]

            # 移除事件过滤器
            if not sip.isdeleted(button):
                button.removeEventFilter(self)

            # 清理图形效果
            if button_name in self.__opacity_effects and self.__opacity_effects[button_name]:
                if not sip.isdeleted(button):
                    button.setGraphicsEffect(None)

            # 停止动画和定时器
            if button_name in self.__opacity_animations_in and self.__opacity_animations_in[button_name]:
                self.__opacity_animations_in[button_name].stop()

            if button_name in self.__opacity_animations_out and self.__opacity_animations_out[button_name]:
                self.__opacity_animations_out[button_name].stop()

            if button_name in self.__debounce_timers and self.__debounce_timers[button_name]:
                self.__debounce_timers[button_name].stop()

            # 从所有字典中移除数据
            for dictionary in [
                self.__buttons, self.__opacity_effects, self.__opacity_animations_in,
                self.__opacity_animations_out, self.__debounce_timers, self.__debounce_intervals,
                self.__last_emit_times, self.__original_stylesheets, self.__disabled_stylesheets,
                self.__auto_style_enabled, self.__is_watching_enabled, self.__internal_enabled_change
            ]:
                if button_name in dictionary:
                    del dictionary[button_name]

            self.__logger.debug(f"按钮 '{button_name}' 已从管理器移除")
            return True
        except Exception as e:
            self.__logger.error(f"移除按钮 '{button_name}' 时发生错误: {e}")
            traceback.print_exc()
            return False

    def has_button(self, button_name: str) -> bool:
        """
        检查指定名称的按钮是否存在于管理器中。

        Args:
            button_name (str): 要检查的按钮名称

        Returns:
            bool: 如果按钮存在返回True，否则返回False

        示例:
        ```python
        if manager.has_button("submit_btn"):
            # 对提交按钮进行操作
            manager.set_text("submit_btn", "提交表单")
        ```
        """
        return button_name in self.__buttons

    def get_button(self, button_name: str) -> Optional[QPushButton]:
        """
        获取指定名称的按钮实例。

        Args:
            button_name (str): 要获取的按钮名称

        Returns:
            Optional[QPushButton]: 如果按钮存在返回QPushButton实例，否则返回None

        示例:
        ```python
        button = manager.get_button("custom_btn")
        if button:
            # 直接对按钮进行操作
            button.setCheckable(True)
        ```
        """
        if not self.has_button(button_name):
            self.__logger.warning(f"获取按钮失败：按钮 '{button_name}' 不存在")
            return None
        return self.__buttons[button_name]

    def get_all_button_names(self) -> List[str]:
        """
        获取管理器中所有按钮的名称列表。

        Returns:
            List[str]: 按钮名称列表

        示例:
        ```python
        all_buttons = manager.get_all_button_names()
        print(f"管理的按钮: {', '.join(all_buttons)}")
        ```
        """
        return list(self.__buttons.keys())

    def get_all_buttons(self) -> Dict[str, QPushButton]:
        """
        获取管理器中所有按钮的字典副本。

        Returns:
            Dict[str, QPushButton]: 按钮字典，键为按钮名称，值为按钮实例

        示例:
        ```python
        all_buttons_dict = manager.get_all_buttons()
        for name, button in all_buttons_dict.items():
            print(f"按钮 {name}: {button.text()}")
        ```
        """
        return self.__buttons.copy()

    def __setup_internal_connections(self, button_name: str) -> None:
        """
        为指定按钮设置内部信号连接，包括点击信号和事件过滤器。

        Args:
            button_name: 按钮名称
        """
        if not self.has_button(button_name):
            return

        button = self.__buttons[button_name]

        # 连接点击信号
        button.clicked.connect(lambda checked: self.__on_button_clicked(button_name, checked))

        # 连接选中状态变化信号
        if button.isCheckable():
            button.toggled.connect(lambda checked: self.checked_changed.emit(button_name, checked))

        # 安装事件过滤器，用于捕获启用状态变化、可见性变化和悬停事件
        button.installEventFilter(self)

        self.__logger.debug(f"已为按钮 '{button_name}' 设置内部信号连接")

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        """
        事件过滤器，用于监听按钮的各种事件，如启用状态变化和鼠标悬停。

        Args:
            watched: 被监视的对象
            event: 事件对象

        Returns:
            bool: 如果事件被处理返回True，否则返回False
        """
        # 查找这个对象对应的按钮名称
        button_name = None
        for name, button in self.__buttons.items():
            if button is watched:
                button_name = name
                break

        if button_name is None:
            # 如果找不到对应的按钮名称，让事件继续传播
            return super().eventFilter(watched, event)

        # 处理启用状态变化事件
        if event.type() == QEvent.EnabledChange: # type: ignore
            is_enabled = watched.isEnabled() # type: ignore
            self.__logger.debug(f"按钮 '{button_name}' 启用状态变为: {'启用' if is_enabled else '禁用'}")

            # 如果启用了自动样式功能，应用相应的样式
            if self.__auto_style_enabled.get(button_name, False) and self.__is_watching_enabled.get(button_name, False):
                self.__apply_state_style(button_name, is_enabled)

            # 检查是否由内部方法触发的变更
            # 如果不是由内部方法触发的，说明是外部直接调用了QPushButton的setEnabled方法
            # 此时需要发出信号，因为set_enabled方法未被调用
            if not self.__internal_enabled_change.get(button_name, False):
                self.__logger.debug(f"按钮 '{button_name}' 启用状态变化由外部触发，发出enabled_changed信号")
                self.enabled_changed.emit(button_name, is_enabled)

        # 处理可见性变化事件
        elif event.type() in (QEvent.ShowToParent, QEvent.HideToParent): # type: ignore
            is_visible = event.type() == QEvent.ShowToParent # type: ignore
            self.__logger.debug(f"按钮 '{button_name}' 可见性状态变为: {'显示' if is_visible else '隐藏'}")

            # 发出可见性状态变化信号
            self.visibility_changed.emit(button_name, is_visible)

        # 处理鼠标悬停事件（如果设置了悬停效果）
        elif button_name in self.__opacity_animations_in and self.__opacity_animations_in[button_name]:
            if event.type() == QEvent.Enter: # type: ignore
                # 鼠标进入按钮，开始进入动画
                self.__opacity_animations_in[button_name].start()
            elif event.type() == QEvent.Leave: # type: ignore
                # 鼠标离开按钮，开始离开动画
                self.__opacity_animations_out[button_name].start()

        # 让事件继续传播
        return super().eventFilter(watched, event)

    def __on_button_clicked(self, button_name: str, checked: bool) -> None:
        """
        内部槽函数，处理按钮点击事件。
        如果启用了防抖动，将进行防抖处理。

        Args:
            button_name: 按钮名称
            checked: 按钮的选中状态
        """
        # 如果启用了防抖动功能
        if self.__debounce_intervals.get(button_name, 0) > 0:
            current_time = int(QTime.currentTime().msecsSinceStartOfDay())
            if current_time - self.__last_emit_times.get(button_name, 0) < self.__debounce_intervals[button_name]:
                self.__logger.debug(f"按钮 '{button_name}' 点击被防抖动机制过滤")
                return

            self.__last_emit_times[button_name] = current_time

            # 如果定时器存在且活跃，重启它
            if button_name in self.__debounce_timers and self.__debounce_timers[button_name] and self.__debounce_timers[button_name].isActive():
                self.__debounce_timers[button_name].stop()
                self.__debounce_timers[button_name].start(self.__debounce_intervals[button_name])

        # 发出自定义信号
        self.__logger.debug(f"按钮 '{button_name}' 被点击，发出button_clicked信号，checked={checked}")
        self.button_clicked.emit(button_name, checked)

    # --- 按钮属性操作方法 ---

    def set_text(self, button_name: str, text: str) -> bool:
        """
        设置指定按钮的文本。

        Args:
            button_name (str): 按钮名称
            text (str): 要设置的文本

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        success = manager.set_text("submit_btn", "提交")
        if success:
            print("按钮文本已更新")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_text_signal.emit(button_name, text)
        return True

    def set_tooltip(self, button_name: str, text: str) -> bool:
        """
        设置指定按钮的工具提示。

        Args:
            button_name (str): 按钮名称
            text (str): 要设置的工具提示文本

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        success = manager.set_tooltip("help_btn", "点击获取帮助")
        if success:
            print("工具提示已设置")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_tooltip_signal.emit(button_name, text)
        return True

    def set_icon(self, button_name: str, icon: QIcon, size: Optional[QSize] = None) -> bool:
        """
        设置指定按钮的图标。

        Args:
            button_name (str): 按钮名称
            icon (QIcon): 要设置的图标对象
            size (Optional[QSize]): 可选，图标大小

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        from PyQt5.QtGui import QIcon
        from PyQt5.QtCore import QSize

        icon = QIcon("path/to/icon.png")
        success = manager.set_icon("save_btn", icon, QSize(16, 16))
        if success:
            print("图标已设置")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_icon_signal.emit(button_name, icon, size)
        return True

    def set_enabled(self, button_name: str, enabled: bool) -> bool:
        """
        启用或禁用指定按钮。

        Args:
            button_name (str): 按钮名称
            enabled (bool): True表示启用，False表示禁用

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 禁用按钮
        manager.set_enabled("delete_btn", False)

        # 启用按钮
        manager.set_enabled("save_btn", True)
        ```
        """
        if not self.has_button(button_name):
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_enabled_signal.emit(button_name, enabled)
        return True

    def __reset_internal_flag(self, button_name: str) -> None:
        """
        重置内部变更标记，用于跟踪启用状态变化的来源。

        Args:
            button_name (str): 按钮名称
        """
        if button_name in self.__internal_enabled_change:
            self.__internal_enabled_change[button_name] = False

    def __preserve_focus_during_operation(self, callback: Callable) -> Any:
        """
        在执行操作期间保存和恢复焦点，防止操作导致焦点意外转移。

        Args:
            callback (Callable): 要执行的操作函数

        Returns:
            Any: 回调函数的返回值

        该方法用于解决在启用/禁用按钮时可能导致的焦点自动转移问题，
        特别是防止QLineEdit控件自动获取焦点的情况。
        """
        if not self.__preserve_focus:
            return callback()

        try:
            # 获取当前焦点控件
            focus_widget = QApplication.focusWidget()

            # 执行操作
            result = callback()

            # 恢复焦点，但如果设置了焦点转移到父容器且当前操作是禁用按钮，则不恢复焦点
            # 这个检查在_handle_enable_button_request中进行，这里不需要特殊处理
            if focus_widget and not sip.isdeleted(focus_widget) and focus_widget.isEnabled():
                focus_widget.setFocus()

            return result
        except Exception as e:
            self.__logger.error(f"在保存和恢复焦点期间发生错误: {e}")
            traceback.print_exc()
            # 出错时仍然执行原操作，但不管理焦点
            return callback()

    def _handle_enable_button_request(self, button_name: str, enabled: bool) -> None:
        """
        (已废弃) 内部槽函数，用于处理按钮启用状态变更请求。
        此方法已被新的线程安全槽函数 __on_set_enabled 取代。
        """
        self.__logger.warning("方法 _handle_enable_button_request 已被废弃，请不要直接调用。")
        # 实际逻辑已迁移到 __on_set_enabled
        self.__on_set_enabled(button_name, enabled)

    def set_preserve_focus(self, preserve: bool) -> None:
        """
        设置是否在启用/禁用按钮时保持当前焦点。

        当启用此功能时，即使禁用/启用按钮，QLineEdit控件也不会自动获取焦点。
        这解决了在复杂表单中禁用按钮可能导致意外焦点转移的问题。

        Args:
            preserve (bool): True表示保持焦点，False表示不进行焦点处理

        示例:
        ```python
        # 启用焦点保持功能，避免QLineEdit自动获取焦点
        manager.set_preserve_focus(True)

        # 禁用焦点保持功能，恢复Qt默认的焦点处理行为
        manager.set_preserve_focus(False)
        ```
        """
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_preserve_focus_signal.emit(preserve)

    def is_preserve_focus_enabled(self) -> bool:
        """
        检查是否启用了焦点保持功能。

        Returns:
            bool: 如果启用了焦点保持功能返回True，否则返回False

        示例:
        ```python
        if manager.is_preserve_focus_enabled():
            print("焦点保持功能已启用")
        else:
            print("焦点保持功能已禁用")
        ```
        """
        return self.__preserve_focus

    def __apply_state_style(self, button_name: str, enabled: bool) -> None:
        """
        根据按钮启用状态应用相应的样式。

        Args:
            button_name (str): 按钮名称
            enabled (bool): True应用启用状态样式，False应用禁用状态样式
        """
        if not self.has_button(button_name):
            return

        button = self.__buttons[button_name]
        try:
            if enabled:
                # 恢复原始样式表
                original_style = self.__original_stylesheets.get(button_name, "")
                button.setStyleSheet(original_style)
                self.__logger.debug(f"按钮 '{button_name}' 已应用原始样式表（启用状态）")
            else:
                # 应用组合样式表 (原始样式 + 禁用样式)
                original_style = self.__original_stylesheets.get(button_name, "")
                disabled_style = self.__disabled_stylesheets.get(button_name, self.__default_disabled_stylesheet)
                combined_style = original_style + disabled_style
                button.setStyleSheet(combined_style)
                self.__logger.debug(f"按钮 '{button_name}' 已应用禁用样式表（禁用状态）")
        except Exception as e:
            self.__logger.error(f"为按钮 '{button_name}' 应用{'启用' if enabled else '禁用'}样式时发生错误: {e}")
            traceback.print_exc()

    def __start_watching_enabled_state(self, button_name: str) -> None:
        """
        开始监听指定按钮的启用状态变化。
        当按钮状态变化时，会自动应用相应的样式。

        Args:
            button_name (str): 按钮名称
        """
        if not self.has_button(button_name):
            return

        # 如果已经在监听，则不再重复设置
        if self.__is_watching_enabled.get(button_name, False):
            return

        self.__is_watching_enabled[button_name] = True

        # 立即应用当前状态的样式
        button = self.__buttons[button_name]
        current_enabled = button.isEnabled()
        self.__apply_state_style(button_name, current_enabled)

        self.__logger.debug(f"开始监听按钮 '{button_name}' 的启用状态变化，当前状态: {'启用' if current_enabled else '禁用'}")

    def __stop_watching_enabled_state(self, button_name: str) -> None:
        """
        停止监听按钮的启用状态变化。
        停止监听后，按钮的样式不会随状态自动变化。

        Args:
            button_name (str): 按钮名称
        """
        if not self.has_button(button_name):
            return

        # 如果没有在监听，则不执行操作
        if not self.__is_watching_enabled.get(button_name, False):
            return

        self.__is_watching_enabled[button_name] = False
        self.__logger.debug(f"停止监听按钮 '{button_name}' 的启用状态变化")

    def enable_auto_style(self, button_name: str, enable: bool = True) -> bool:
        """
        为指定按钮启用或禁用自动样式功能。

        启用后，按钮在禁用状态下会自动应用禁用样式，启用状态下恢复原始样式。

        Args:
            button_name (str): 按钮名称
            enable (bool): True表示启用自动样式功能，False表示禁用。默认为True

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 启用按钮的自动样式
        manager.enable_auto_style("form_btn", True)

        # 禁用按钮的自动样式
        manager.enable_auto_style("plain_btn", False)
        ```
        """
        if not self.has_button(button_name):
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__enable_auto_style_signal.emit(button_name, enable)
        return True

    def is_auto_style_enabled(self, button_name: str) -> Optional[bool]:
        """
        检查指定按钮是否启用了自动样式功能。

        Args:
            button_name (str): 按钮名称

        Returns:
            Optional[bool]: 如果按钮存在且启用了自动样式返回True，
                           如果按钮存在但未启用自动样式返回False，
                           如果按钮不存在则返回None

        示例:
        ```python
        auto_style = manager.is_auto_style_enabled("themed_btn")
        if auto_style:
            print("按钮已启用自动样式")
        elif auto_style is False:  # 注意这里的判断方式
            print("按钮未启用自动样式")
        else:
            print("按钮不存在")
        ```
        """
        if not self.has_button(button_name):
            return None

        return self.__auto_style_enabled.get(button_name, False)

    def enable_all_auto_style(self, enable: bool = True) -> Dict[str, bool]:
        """
        为所有按钮启用或禁用自动样式功能。

        Args:
            enable (bool): True表示启用自动样式功能，False表示禁用。默认为True

        Returns:
            Dict[str, bool]: 操作结果字典，键为按钮名称，值为该按钮操作是否成功

        示例:
        ```python
        # 为所有按钮启用自动样式
        results = manager.enable_all_auto_style(True)

        # 检查结果
        if all(results.values()):
            print("所有按钮已启用自动样式")
        ```
        """
        results = {}
        for button_name in self.get_all_button_names():
            results[button_name] = self.enable_auto_style(button_name, enable)

        success_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        self.__logger.debug(f"已为 {success_count}/{total_count} 个按钮{'启用' if enable else '禁用'}自动样式")

        return results

    # --- 特效方法 ---

    def add_hover_opacity_effect(self, button_name: str, start_opacity: float = 1.0,
                                 end_opacity: float = 0.7, duration: int = 150) -> bool:
        """
        为指定按钮添加鼠标悬停透明度效果。

        Args:
            button_name (str): 按钮名称
            start_opacity (float): 初始不透明度，范围0.0-1.0。默认为1.0
            end_opacity (float): 悬停时不透明度，范围0.0-1.0。默认为0.7
            duration (int): 动画持续时间，单位毫秒。默认为150ms

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        success = manager.add_hover_opacity_effect("fancy_btn",
                                               start_opacity=1.0,
                                               end_opacity=0.8,
                                               duration=200)
        if success:
            print("悬停效果已添加")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__add_hover_opacity_effect_signal.emit(button_name, start_opacity, end_opacity, duration)
        return True

    def enable_debounce(self, button_name: str, interval_ms: int = 200) -> bool:
        """
        为指定按钮启用点击防抖动功能。

        防抖动功能可以防止用户短时间内多次点击按钮触发多次事件。

        Args:
            button_name (str): 按钮名称
            interval_ms (int): 防抖动间隔，单位毫秒。设置为0表示禁用防抖动。默认为200ms

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 启用300毫秒的防抖动
        manager.enable_debounce("submit_btn", 300)

        # 禁用防抖动
        manager.enable_debounce("quick_btn", 0)
        ```
        """
        if not self.has_button(button_name):
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__enable_debounce_signal.emit(button_name, interval_ms)
        return True

    # --- 信号连接方法 ---

    def connect_clicked_signal(self, button_name: str, slot: Callable[[], Any],
                               connection_type: Qt.ConnectionType = Qt.AutoConnection) -> bool: # type: ignore
        """
        连接指定按钮的clicked信号到槽函数。

        Args:
            button_name (str): 按钮名称
            slot (Callable[[], Any]): 槽函数，当按钮被点击时调用
            connection_type (Qt.ConnectionType): 连接类型，默认为Qt.AutoConnection

        Returns:
            bool: 连接成功返回True，失败返回False

        示例:
        ```python
        def on_save_clicked():
            print("保存按钮被点击")

        success = manager.connect_clicked_signal("save_btn", on_save_clicked)
        if success:
            print("点击信号已连接")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        func_name = getattr(slot, "__name__", repr(slot))

        try:
            button.clicked.connect(slot, connection_type) # type: ignore
            self.__logger.debug(f"按钮 '{button_name}' 的clicked信号已成功连接到槽函数 '{func_name}'")
            return True
        except TypeError as te:
            self.__logger.error(f"连接按钮 '{button_name}' 的clicked信号时发生类型错误: {te}")
            traceback.print_exc()
            return False
        except Exception as e:
            self.__logger.error(f"连接按钮 '{button_name}' 的clicked信号时发生未知错误: {e}")
            traceback.print_exc()
            return False

    def disconnect_clicked_signal(self, button_name: str, slot: Optional[Callable[[], Any]] = None) -> bool:
        """
        断开指定按钮的clicked信号连接。

        Args:
            button_name (str): 按钮名称
            slot (Optional[Callable[[], Any]]): 要断开的槽函数，如果为None则断开所有连接。默认为None

        Returns:
            bool: 断开连接成功返回True，失败返回False

        示例:
        ```python
        # 断开特定槽函数
        manager.disconnect_clicked_signal("save_btn", on_save_clicked)

        # 断开所有连接
        manager.disconnect_clicked_signal("close_btn")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        try:
            if slot is None:
                # 断开所有连接
                success = button.clicked.disconnect()
                if success:
                    self.__logger.debug(f"已断开按钮 '{button_name}' 的所有clicked信号连接")
                return bool(success)
            else:
                # 断开特定槽函数
                button.clicked.disconnect(slot)
                func_name = getattr(slot, "__name__", repr(slot))
                self.__logger.debug(f"已断开按钮 '{button_name}' 的clicked信号与槽函数 '{func_name}' 的连接")
                return True
        except TypeError as te:
            self.__logger.error(f"断开按钮 '{button_name}' 的clicked信号连接时发生类型错误: {te}")
            traceback.print_exc()
            return False
        except RuntimeError as re:
            # 断开不存在的连接时会抛出RuntimeError
            self.__logger.warning(f"断开按钮 '{button_name}' 的clicked信号连接失败: {re}")
            return False
        except Exception as e:
            self.__logger.error(f"断开按钮 '{button_name}' 的clicked信号连接时发生未知错误: {e}")
            traceback.print_exc()
            return False

    def set_visible(self, button_name: str, visible: bool) -> bool:
        """
        设置指定按钮的可见性。

        Args:
            button_name (str): 按钮名称
            visible (bool): True表示显示按钮，False表示隐藏按钮

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 隐藏按钮
        manager.set_visible("advanced_btn", False)

        # 显示按钮
        manager.set_visible("basic_btn", True)
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_visible_signal.emit(button_name, visible)
        return True

    def is_visible(self, button_name: str) -> Optional[bool]:
        """
        检查指定按钮是否可见。

        Args:
            button_name (str): 按钮名称

        Returns:
            Optional[bool]: 如果按钮存在且可见则返回True，
                           如果按钮存在但不可见则返回False，
                           如果按钮不存在则返回None

        示例:
        ```python
        visible = manager.is_visible("option_btn")
        if visible:
            print("选项按钮可见")
        elif visible is False:  # 注意这里的判断方式
            print("选项按钮不可见")
        else:
            print("选项按钮不存在")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return None
        return button.isVisible()

    def set_all_visible(self, visible: bool) -> Dict[str, bool]:
        """
        设置所有按钮的可见性。

        Args:
            visible (bool): True表示显示所有按钮，False表示隐藏所有按钮

        Returns:
            Dict[str, bool]: 操作结果字典，键为按钮名称，值为该按钮操作是否成功

        示例:
        ```python
        # 隐藏所有按钮
        results = manager.set_all_visible(False)

        # 检查是否所有按钮都成功隐藏
        all_success = all(results.values())
        if all_success:
            print("所有按钮已隐藏")
        else:
            # 找出失败的按钮
            failed_buttons = [name for name, success in results.items() if not success]
            print(f"以下按钮隐藏失败: {', '.join(failed_buttons)}")
        ```
        """
        results = {}
        for button_name in self.get_all_button_names():
            results[button_name] = self.set_visible(button_name, visible)

        success_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        self.__logger.debug(f"已{'显示' if visible else '隐藏'} {success_count}/{total_count} 个按钮")

        return results

    def set_checkable(self, button_name: str, checkable: bool) -> bool:
        """
        设置指定按钮是否可检查（可选中）。

        Args:
            button_name (str): 按钮名称
            checkable (bool): True表示按钮可检查，False表示按钮不可检查

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 设置按钮可检查（类似于切换按钮）
        manager.set_checkable("toggle_btn", True)
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_checkable_signal.emit(button_name, checkable)
        return True

    def is_checkable(self, button_name: str) -> Optional[bool]:
        """
        检查指定按钮是否可检查（可选中）。

        Args:
            button_name (str): 按钮名称

        Returns:
            Optional[bool]: 如果按钮存在且可检查则返回True，
                          如果按钮存在但不可检查则返回False，
                          如果按钮不存在则返回None
        """
        button = self.get_button(button_name)
        if not button:
            return None
        return button.isCheckable()

    def set_checked(self, button_name: str, checked: bool) -> bool:
        """
        设置指定按钮的选中状态（按钮必须先设置为可检查）。

        Args:
            button_name (str): 按钮名称
            checked (bool): True表示选中按钮，False表示取消选中

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        # 先设置按钮为可检查
        manager.set_checkable("option_btn", True)

        # 选中按钮
        manager.set_checked("option_btn", True)
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_checked_signal.emit(button_name, checked)
        return True

    def is_checked(self, button_name: str) -> Optional[bool]:
        """
        检查指定按钮是否被选中。

        Args:
            button_name (str): 按钮名称

        Returns:
            Optional[bool]: 如果按钮存在且被选中则返回True，
                          如果按钮存在但未被选中则返回False，
                          如果按钮不存在则返回None
        """
        button = self.get_button(button_name)
        if not button:
            return None
        return button.isChecked()

    def __del__(self):
        """
        析构函数，负责清理资源。
        """
        try:
            self.__logger.debug("QPushButtonManager 开始清理资源")

            # 停止所有动画和定时器
            for button_name in list(self.__buttons.keys()):
                # 移除事件过滤器
                button = self.__buttons.get(button_name)
                if button and not sip.isdeleted(button):
                    button.removeEventFilter(self)

                # 停止动画
                if button_name in self.__opacity_animations_in and self.__opacity_animations_in[button_name]:
                    self.__opacity_animations_in[button_name].stop()

                if button_name in self.__opacity_animations_out and self.__opacity_animations_out[button_name]:
                    self.__opacity_animations_out[button_name].stop()

                # 停止定时器
                if button_name in self.__debounce_timers and self.__debounce_timers[button_name]:
                    self.__debounce_timers[button_name].stop()

                # 移除效果
                if button and not sip.isdeleted(button) and button_name in self.__opacity_effects and self.__opacity_effects[button_name]:
                    button.setGraphicsEffect(None)

            # 清空所有字典
            self.__buttons.clear()
            self.__opacity_effects.clear()
            self.__opacity_animations_in.clear()
            self.__opacity_animations_out.clear()
            self.__debounce_timers.clear()
            self.__debounce_intervals.clear()
            self.__last_emit_times.clear()
            self.__original_stylesheets.clear()
            self.__disabled_stylesheets.clear()
            self.__auto_style_enabled.clear()
            self.__is_watching_enabled.clear()
            self.__internal_enabled_change.clear()

            self.__logger.debug("QPushButtonManager 资源清理完成")
        except Exception as e:
            self.__logger.error(f"QPushButtonManager 清理资源时发生错误: {e}")
            traceback.print_exc()

    def is_enabled(self, button_name: str) -> Optional[bool]:
        """
        检查指定按钮是否启用。

        Args:
            button_name (str): 按钮名称

        Returns:
            Optional[bool]: 如果按钮存在且启用则返回True，
                           如果按钮存在但禁用则返回False，
                           如果按钮不存在则返回None

        示例:
        ```python
        enabled = manager.is_enabled("submit_btn")
        if enabled:
            print("提交按钮已启用")
        elif enabled is False:  # 注意这里的判断方式
            print("提交按钮已禁用")
        else:
            print("提交按钮不存在")
        ```
        """
        button = self.get_button(button_name)
        if not button:
            return None
        return button.isEnabled()

    def set_all_enabled(self, enabled: bool) -> Dict[str, bool]:
        """
        设置所有按钮的启用状态。

        Args:
            enabled (bool): True表示启用所有按钮，False表示禁用所有按钮

        Returns:
            Dict[str, bool]: 操作结果字典，键为按钮名称，值为该按钮操作是否成功

        示例:
        ```python
        # 禁用所有按钮
        results = manager.set_all_enabled(False)

        # 检查是否所有按钮都成功禁用
        all_success = all(results.values())
        if all_success:
            print("所有按钮已禁用")
        else:
            # 找出失败的按钮
            failed_buttons = [name for name, success in results.items() if not success]
            print(f"以下按钮禁用失败: {', '.join(failed_buttons)}")
        ```
        """
        results = {}
        for button_name in self.get_all_button_names():
            results[button_name] = self.set_enabled(button_name, enabled)

        success_count = sum(1 for result in results.values() if result)
        total_count = len(results)
        self.__logger.debug(f"已{'启用' if enabled else '禁用'} {success_count}/{total_count} 个按钮")

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.all_enabled_changed.emit(enabled, success_count)

        return results

    # --- 样式操作方法 ---

    def set_disabled_style(self, button_name: str, style_sheet: str) -> bool:
        """
        设置指定按钮禁用状态的样式表。

        Args:
            button_name (str): 按钮名称
            style_sheet (str): 禁用状态的CSS样式表

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        disabled_style = '''
            QPushButton:disabled {
                color: #888888;
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
            }
        '''
        success = manager.set_disabled_style("important_btn", disabled_style)
        if success:
            print("禁用样式已设置")
        ```
        """
        if not self.has_button(button_name):
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_disabled_style_signal.emit(button_name, style_sheet)
        return True

    def reset_to_original_style(self, button_name: str) -> bool:
        """
        重置指定按钮样式到初始状态。

        Args:
            button_name (str): 按钮名称

        Returns:
            bool: 操作成功返回True，失败返回False

        示例:
        ```python
        success = manager.reset_to_original_style("themed_btn")
        if success:
            print("按钮样式已重置为原始状态")
        ```
        """
        if not self.has_button(button_name):
            return False

        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__reset_to_original_style_signal.emit(button_name)
        return True

    def set_focus_to_parent_on_disable(self, enable: bool) -> None:
        """
        设置是否在禁用按钮时将焦点转移到父容器。

        当启用此功能时，禁用按钮后会自动将焦点设置到按钮的父容器上，
        这在复杂表单中可以提供更好的用户体验，避免焦点丢失。

        Args:
            enable (bool): True表示启用焦点转移到父容器功能，False表示禁用

        示例:
        ```python
        # 启用禁用按钮后焦点转移到父容器的功能
        button_manager = QPushButtonManager([submit_button, cancel_button])
        button_manager.set_focus_to_parent_on_disable(True)

        # 禁用按钮，焦点会自动转移到父容器
        button_manager.set_enabled("submit_button", False)
        ```
        """
        self.__set_focus_to_parent_on_disable_signal.emit(enable)

    def is_focus_to_parent_enabled(self) -> bool:
        """
        检查是否启用了禁用按钮时焦点转移到父容器的功能。

        Returns:
            bool: 如果启用了焦点转移到父容器功能返回True，否则返回False

        示例:
        ```python
        if button_manager.is_focus_to_parent_enabled():
            print("禁用按钮时焦点会自动转移到父容器")
        else:
            print("禁用按钮时焦点不会自动转移")
        ```
        """
        return self.__focus_to_parent

    def set_parent_focus_policy(self, policy: Qt.FocusPolicy) -> None:
        """
        设置父容器在接收焦点时使用的焦点策略。

        当父容器原本无法接收焦点(focusPolicy为Qt.NoFocus)时，
        会临时使用此策略使其能够接收焦点，焦点设置完成后恢复原策略。

        Args:
            policy (Qt.FocusPolicy): 要使用的焦点策略
                - Qt.NoFocus: 不接收焦点
                - Qt.TabFocus: 只通过Tab键接收焦点
                - Qt.ClickFocus: 只通过鼠标点击接收焦点
                - Qt.StrongFocus: 通过Tab键和鼠标点击接收焦点
                - Qt.WheelFocus: 通过Tab键、鼠标点击和鼠标滚轮接收焦点

        示例:
        ```python
        from PyQt5.QtCore import Qt

        # 设置使用强焦点策略
        button_manager.set_parent_focus_policy(Qt.StrongFocus)

        # 设置使用完全焦点策略（包括滚轮）
        button_manager.set_parent_focus_policy(Qt.WheelFocus)
        ```
        """
        self.__set_parent_focus_policy_signal.emit(policy)

    # =================================================================================
    # Private Slots for Thread-Safe UI Updates
    # =================================================================================

    def __on_set_text(self, button_name: str, text: str) -> None:
        """(私有槽) 在GUI线程中安全地设置按钮文本。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            if button.text() != text:
                button.setText(text)
                self.__logger.debug(f"按钮 '{button_name}' 的文本已设置为: '{text}'")
                self.text_changed.emit(button_name, text)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 文本时发生错误: {e}")
            traceback.print_exc()

    def __on_set_tooltip(self, button_name: str, text: str) -> None:
        """(私有槽) 在GUI线程中安全地设置工具提示。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            if button.toolTip() != text:
                button.setToolTip(text)
                if text:
                    self.__logger.debug(f"按钮 '{button_name}' 的工具提示已设置为: '{text}'")
                else:
                    self.__logger.debug(f"按钮 '{button_name}' 的工具提示已被移除")
                self.tooltip_changed.emit(button_name, text)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 工具提示时发生错误: {e}")
            traceback.print_exc()

    def __on_set_icon(self, button_name: str, icon: QIcon, size: Optional[QSize]) -> None:
        """(私有槽) 在GUI线程中安全地设置图标。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            button.setIcon(icon)
            log_message = f"按钮 '{button_name}' 的图标已设置"
            if size is not None and isinstance(size, QSize):
                button.setIconSize(size)
                log_message += f"，图标尺寸设置为 {size.width()}x{size.height()}"
            self.__logger.debug(log_message)
            self.icon_changed.emit(button_name)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 图标时发生错误: {e}")
            traceback.print_exc()

    def __on_set_enabled(self, button_name: str, enabled: bool) -> None:
        """(私有槽) 在GUI线程中安全地处理按钮启用状态变更请求。"""
        button = self.get_button(button_name)
        if not button:
            self.__logger.warning(f"处理按钮 '{button_name}' 启用状态变更请求失败：按钮不存在")
            return

        if button.isEnabled() == enabled:
            return

        try:
            self.__internal_enabled_change[button_name] = True
            if self.__auto_style_enabled.get(button_name, False) and self.__is_watching_enabled.get(button_name, False):
                self.__apply_state_style(button_name, enabled)

            def set_button_enabled():
                button.setEnabled(enabled)
                state = "启用" if enabled else "禁用"
                self.__logger.debug(f"按钮 '{button_name}' 的状态已通过槽函数设置为: {state}")
                self.enabled_changed.emit(button_name, enabled)

                if not enabled and self.__focus_to_parent:
                    try:
                        parent_widget = button.parentWidget()
                        if parent_widget:
                            original_policy = parent_widget.focusPolicy()
                            if original_policy == Qt.FocusPolicy.NoFocus:
                                parent_widget.setFocusPolicy(self.__parent_focus_policy)
                            parent_widget.setFocus(Qt.FocusReason.OtherFocusReason)
                            self.__logger.debug(f"按钮 '{button_name}' 被禁用后，焦点已设置到父容器")
                            if original_policy == Qt.FocusPolicy.NoFocus:
                                parent_widget.setFocusPolicy(original_policy)
                    except Exception as e:
                        self.__logger.error(f"设置焦点到父容器时发生错误: {e}")
            
            self.__preserve_focus_during_operation(set_button_enabled)

        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 启用状态时发生错误: {e}")
            traceback.print_exc()
        finally:
            QTimer.singleShot(0, lambda: self.__reset_internal_flag(button_name))

    def __on_set_visible(self, button_name: str, visible: bool) -> None:
        """(私有槽) 在GUI线程中安全地设置按钮可见性。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            if button.isVisible() != visible:
                button.setVisible(visible)
                state = "显示" if visible else "隐藏"
                self.__logger.debug(f"按钮 '{button_name}' 的可见性已设置为: {state}")
                self.visibility_changed.emit(button_name, visible)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 可见性时发生错误: {e}")
            traceback.print_exc()

    def __on_set_checkable(self, button_name: str, checkable: bool) -> None:
        """(私有槽) 在GUI线程中安全地设置按钮是否可检查。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            if button.isCheckable() != checkable:
                button.setCheckable(checkable)
                state = "可检查" if checkable else "不可检查"
                self.__logger.debug(f"按钮 '{button_name}' 设置为: {state}")
                self.checkable_changed.emit(button_name, checkable)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 可检查状态时发生错误: {e}")
            traceback.print_exc()

    def __on_set_checked(self, button_name: str, checked: bool) -> None:
        """(私有槽) 在GUI线程中安全地设置按钮的选中状态。"""
        button = self.get_button(button_name)
        if not button:
            return
        try:
            if not button.isCheckable():
                self.__logger.warning(f"设置按钮 '{button_name}' 选中状态失败：按钮不可检查")
                return
            if button.isChecked() != checked:
                button.setChecked(checked)
                state = "选中" if checked else "取消选中"
                self.__logger.debug(f"按钮 '{button_name}' 的状态已设置为: {state}")
                self.checked_changed.emit(button_name, checked)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 选中状态时发生错误: {e}")
            traceback.print_exc()

    def __on_set_disabled_style(self, button_name: str, style_sheet: str) -> None:
        """(私有槽) 在GUI线程中安全地设置禁用样式。"""
        if not self.has_button(button_name):
            return
        try:
            self.__disabled_stylesheets[button_name] = style_sheet
            self.__logger.debug(f"按钮 '{button_name}' 的禁用样式表已更新")
            button = self.__buttons[button_name]
            if self.__auto_style_enabled.get(button_name, False) and not button.isEnabled():
                self.__apply_state_style(button_name, False)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置按钮 '{button_name}' 禁用样式时发生错误: {e}")
            traceback.print_exc()

    def __on_reset_to_original_style(self, button_name: str) -> None:
        """(私有槽) 在GUI线程中安全地重置按钮样式。"""
        button = self.get_button(button_name)
        if not button or button_name not in self.__original_stylesheets:
            return
        try:
            button.setStyleSheet(self.__original_stylesheets[button_name])
            self.__logger.debug(f"按钮 '{button_name}' 样式已重置为原始状态")
        except Exception as e:
            self.__logger.error(f"在槽函数中重置按钮 '{button_name}' 样式时发生错误: {e}")
            traceback.print_exc()

    def __on_enable_auto_style(self, button_name: str, enable: bool) -> None:
        """(私有槽) 在GUI线程中安全地启用或禁用自动样式。"""
        if not self.has_button(button_name):
            return
        try:
            if self.__auto_style_enabled.get(button_name, False) != enable:
                self.__auto_style_enabled[button_name] = enable
                if enable:
                    self.__logger.debug(f"按钮 '{button_name}' 启用了自动样式功能")
                    self.__start_watching_enabled_state(button_name)
                else:
                    self.__logger.debug(f"按钮 '{button_name}' 禁用了自动样式功能")
                    self.__stop_watching_enabled_state(button_name)
                    button = self.__buttons[button_name]
                    button.setStyleSheet(self.__original_stylesheets.get(button_name, ""))
        except Exception as e:
            self.__logger.error(f"在槽函数中为按钮 '{button_name}' 设置自动样式功能时发生错误: {e}")
            traceback.print_exc()

    def __on_add_hover_opacity_effect(self, button_name: str, start_opacity: float, end_opacity: float, duration: int) -> None:
        """(私有槽) 在GUI线程中安全地添加悬停透明度效果。"""
        button = self.get_button(button_name)
        if not button:
            return
        if button_name in self.__opacity_effects and self.__opacity_effects[button_name]:
            return
        if not (0.0 <= start_opacity <= 1.0 and 0.0 <= end_opacity <= 1.0 and duration >= 0):
            self.__logger.error(f"为按钮 '{button_name}' 添加悬停效果失败：参数无效")
            return
        try:
            effect = QGraphicsOpacityEffect(button)
            button.setGraphicsEffect(effect)
            effect.setOpacity(start_opacity)
            animation_in = QPropertyAnimation(effect, b"opacity", self)
            animation_in.setDuration(duration)
            animation_in.setStartValue(start_opacity)
            animation_in.setEndValue(end_opacity)
            animation_in.setEasingCurve(QEasingCurve.InOutQuad)
            animation_out = QPropertyAnimation(effect, b"opacity", self)
            animation_out.setDuration(duration)
            animation_out.setStartValue(end_opacity)
            animation_out.setEndValue(start_opacity)
            animation_out.setEasingCurve(QEasingCurve.InOutQuad)
            self.__opacity_effects[button_name] = effect
            self.__opacity_animations_in[button_name] = animation_in
            self.__opacity_animations_out[button_name] = animation_out
            self.__logger.debug(f"为按钮 '{button_name}' 成功添加了悬停透明度效果")
        except Exception as e:
            self.__logger.error(f"在槽函数中为按钮 '{button_name}' 添加悬停效果时发生错误: {e}")
            traceback.print_exc()

    def __on_enable_debounce(self, button_name: str, interval_ms: int) -> None:
        """(私有槽) 在GUI线程中安全地设置防抖动。"""
        if not self.has_button(button_name):
            return
        try:
            self.__debounce_intervals[button_name] = interval_ms
            if interval_ms <= 0:
                if button_name in self.__debounce_timers and self.__debounce_timers[button_name]:
                    self.__debounce_timers[button_name].stop()
                    del self.__debounce_timers[button_name]
                self.__logger.debug(f"按钮 '{button_name}' 已禁用防抖动功能")
            else:
                if button_name not in self.__debounce_timers or not self.__debounce_timers.get(button_name):
                    self.__debounce_timers[button_name] = QTimer(self)
                    self.__debounce_timers[button_name].setSingleShot(True)
                self.__logger.debug(f"按钮 '{button_name}' 已启用防抖动功能，间隔设置为{interval_ms}ms")
        except Exception as e:
            self.__logger.error(f"在槽函数中为按钮 '{button_name}' 设置防抖动功能时发生错误: {e}")
            traceback.print_exc()

    def __on_set_preserve_focus(self, preserve: bool) -> None:
        """(私有槽) 在GUI线程中安全地设置焦点保持策略。"""
        self.__preserve_focus = preserve
        self.__logger.debug(f"焦点保持功能已{'启用' if preserve else '禁用'}")

    def __on_set_focus_to_parent_on_disable(self, enable: bool) -> None:
        """(私有槽) 在GUI线程中安全地设置禁用时焦点转移策略。"""
        self.__focus_to_parent = enable
        self.__logger.debug(f"禁用按钮时焦点转移到父容器功能已{'启用' if enable else '禁用'}")

    def __on_set_parent_focus_policy(self, policy: Qt.FocusPolicy) -> None:
        """(私有槽) 在GUI线程中安全地设置父容器焦点策略。"""
        self.__parent_focus_policy = policy
        self.__logger.debug(f"父容器焦点策略已设置为: {policy}")
    # =================================================================================
    # 单例模式管理方法
    # =================================================================================

    @classmethod
    def get_instance(cls, buttons=None, parent: Optional[QObject] = None, auto_style: bool = False) -> 'QPushButtonManager':
        """
        获取QPushButtonManager的单例实例

        这是推荐的获取实例的方法，相比直接调用构造函数更加明确。

        Args:
            buttons: 要管理的QPushButton列表，可以是单个按钮、按钮列表或None（首次创建时需要）
            parent (Optional[QObject]): 父对象（可选）
            auto_style (bool): 是否为所有按钮启用自动样式，默认为False

        Returns:
            QPushButtonManager: 单例实例

        使用示例:
            ```python
            # 首次创建，需要传入按钮列表
            button1 = QPushButton("按钮1")
            button1.setObjectName("btn1")
            button2 = QPushButton("按钮2")
            button2.setObjectName("btn2")
            manager = QPushButtonManager.get_instance([button1, button2])

            # 后续获取，可以不传参数
            manager = QPushButtonManager.get_instance()
            ```
        """
        return cls(buttons, parent=parent, auto_style=auto_style)

    @classmethod
    def reset_instance(cls) -> None:
        """
        重置单例实例

        清理当前实例并重置单例状态，下次调用时会创建新的实例。
        这在需要重新初始化管理器或在测试中很有用。

        使用示例:
            ```python
            # 重置单例实例
            QPushButtonManager.reset_instance()

            # 下次调用会创建新的实例
            new_manager = QPushButtonManager.get_instance([new_button1, new_button2])
            ```
        """
        with cls._lock:
            if cls._instance is not None:
                try:
                    # 尝试清理现有实例
                    if not sip.isdeleted(cls._instance):
                        # 清理所有资源
                        cls._instance.__cleanup_all_resources()
                except Exception as e:
                    # 如果清理失败，记录错误但继续重置
                    try:
                        logger = ClassInstanceManager.get_instance(key="ui_logger")
                        if logger:
                            logger.warning(f"重置QPushButtonManager实例时清理失败: {str(e)}")
                    except:
                        pass

            # 重置所有单例相关的类变量
            cls._instance = None
            cls._initialized = False
            cls._cached_args = None
            cls._cached_kwargs = None

    @classmethod
    def is_instance_created(cls) -> bool:
        """
        检查单例实例是否已创建且有效

        Returns:
            bool: 如果实例已创建且有效返回True，否则返回False

        使用示例:
            ```python
            if QPushButtonManager.is_instance_created():
                manager = QPushButtonManager.get_instance()
            else:
                manager = QPushButtonManager.get_instance([button1, button2])
            ```
        """
        if cls._instance is None:
            return False

        try:
            return not sip.isdeleted(cls._instance) and cls._initialized
        except RuntimeError:
            return False

    @classmethod
    def get_instance_info(cls) -> Optional[Dict[str, Any]]:
        """
        获取单例实例的状态信息

        Returns:
            Optional[Dict[str, Any]]: 包含实例信息的字典，如果实例不存在则返回None

        返回的字典包含以下键：
            - instance: 实例对象
            - initialized: 是否已初始化
            - cached_args: 缓存的参数
            - cached_kwargs: 缓存的关键字参数
            - button_count: 管理的按钮数量
            - button_names: 按钮名称列表

        使用示例:
            ```python
            info = QPushButtonManager.get_instance_info()
            if info:
                print(f"管理的按钮数量: {info['button_count']}")
                print(f"按钮名称: {info['button_names']}")
            ```
        """
        if cls._instance is None:
            return None

        try:
            if sip.isdeleted(cls._instance):
                return None

            return {
                "instance": cls._instance,
                "initialized": cls._initialized,
                "cached_args": cls._cached_args,
                "cached_kwargs": cls._cached_kwargs,
                "button_count": len(cls._instance.__buttons) if hasattr(cls._instance, '_QPushButtonManager__buttons') else 0,
                "button_names": list(cls._instance.__buttons.keys()) if hasattr(cls._instance, '_QPushButtonManager__buttons') else []
            }
        except (RuntimeError, AttributeError):
            return None

    @classmethod
    def get_cached_args(cls) -> Tuple[Any, Optional[Dict[str, Any]]]:
        """
        获取缓存的构造参数

        Returns:
            Tuple[Any, Optional[Dict[str, Any]]]: 缓存的按钮参数和关键字参数

        使用示例:
            ```python
            buttons, kwargs = QPushButtonManager.get_cached_args()
            print(f"缓存的按钮参数: {buttons}")
            print(f"缓存的关键字参数: {kwargs}")
            ```
        """
        return (cls._cached_args, cls._cached_kwargs)

    def __cleanup_all_resources(self) -> None:
        """
        清理所有资源（私有方法，用于实例重置时）
        """
        try:
            # 清理所有动画
            for animation in self.__opacity_animations_in.values():
                if animation:
                    animation.stop()
                    animation.deleteLater()
            for animation in self.__opacity_animations_out.values():
                if animation:
                    animation.stop()
                    animation.deleteLater()

            # 清理所有定时器
            for timer in self.__debounce_timers.values():
                if timer:
                    timer.stop()
                    timer.deleteLater()

            # 移除所有事件过滤器
            for button in self.__buttons.values():
                if button and not sip.isdeleted(button):
                    button.removeEventFilter(self)

            # 清理所有字典
            self.__buttons.clear()
            self.__opacity_effects.clear()
            self.__opacity_animations_in.clear()
            self.__opacity_animations_out.clear()
            self.__debounce_timers.clear()
            self.__debounce_intervals.clear()
            self.__last_emit_times.clear()
            self.__original_stylesheets.clear()
            self.__disabled_stylesheets.clear()
            self.__auto_style_enabled.clear()
            self.__is_watching_enabled.clear()
            self.__internal_enabled_change.clear()

        except Exception as e:
            # 如果清理过程中出现错误，记录但不抛出异常
            try:
                self.__logger.warning(f"清理资源时出现错误: {str(e)}")
            except:
                pass

