import threading
import time
from typing import Any, List, Union, Optional, Callable, Dict, Tuple
import logging
import traceback
from PyQt5.QtCore import (
    QObject,
    Qt,
    QTimer,
    QPropertyAnimation,
    QEasingCurve,
    pyqtSignal,
    pyqtSlot,
    Q_ARG,
    QMetaObject,
    QThread,
    QAbstractAnimation,
)
from PyQt5.QtWidgets import QProgressBar, QApplication
from PyQt5 import sip
from global_tools.utils import ClassInstanceManager, Logger

class QProgressBarHelper(QObject):
    """
    QProgressBarHelper 是 ProgressBarManager 的改进版，用于管理一组 QProgressBar 控件，
    提供便捷的交互接口、动画效果和信号通知。

    此类使用 QProgressBar 的 objectName 作为键值，直接接收一组 QProgressBar 控件作为参数。
    所有操作都支持通过控件名称快速访问和修改 QProgressBar 的属性。

    核心特性:
    - 接收一组 QProgressBar 控件作为初始化参数
    - 通过控件名称快速访问和操作 QProgressBar
    - 提供平滑的动画效果
    - 发送信号通知状态变化
    - 支持多线程环境下的安全更新

    信号:
    - value_changed(object_name: str, new_value: int): 当进度条值通过 set_value 成功改变时发出。
    - range_changed(object_name: str, min_value: int, max_value: int): 当进度条范围通过 set_range 成功改变时发出。
    - format_changed(object_name: str, new_format: str): 当进度条格式通过 set_format 成功改变时发出。
    - text_visibility_changed(object_name: str, is_visible: bool): 当文本可见性通过 set_text_visible 成功改变时发出。
    - orientation_changed(object_name: str, orientation: Qt.Orientation): 当方向通过 set_orientation 成功改变时发出。
    - animation_started(object_name: str): 当值动画开始时发出。
    - animation_finished(object_name: str): 当值动画完成时发出。

    使用示例:
    ---------
    ```python
    # 初始化方式一：直接传入 QProgressBar 实例列表
    progress1 = QProgressBar()
    progress1.setObjectName("taskProgress")
    progress2 = QProgressBar()
    progress2.setObjectName("overallProgress")

    # 创建管理器，传入进度条实例
    helper = QProgressBarHelper([progress1, progress2])

    # 初始化方式二：逐个添加 QProgressBar
    helper = QProgressBarHelper()
    helper.add_progress_bar(progress1)
    helper.add_progress_bar(progress2)

    # 设置值（带动画）
    helper.set_value("taskProgress", 50)

    # 设置值（不带动画）
    helper.set_value("overallProgress", 20, animate=False)

    # 设置自定义动画
    helper.set_value("taskProgress", 75, duration_ms=1000, easing_curve=QEasingCurve.OutBounce)

    # 设置范围
    helper.set_range("taskProgress", 0, 100)

    # 多线程安全更新示例
    def worker_thread():
        # 在工作线程中更新UI
        for i in range(100):
            QMetaObject.invokeMethod(
                helper,
                "set_value",
                Qt.QueuedConnection,
                Q_ARG(str, "taskProgress"),
                Q_ARG(int, i)
            )
            time.sleep(0.1)

    thread = threading.Thread(target=worker_thread)
    thread.start()

    # 监听信号
    def on_progress_change(name, value):
        print(f"进度条 {name} 值变为: {value}")

    helper.value_changed.connect(on_progress_change)
    ```
    """
    # --- 信号定义 ---
    value_changed = pyqtSignal(str, int)
    range_changed = pyqtSignal(str, int, int)
    format_changed = pyqtSignal(str, str)
    text_visibility_changed = pyqtSignal(str, bool)
    orientation_changed = pyqtSignal(str, Qt.Orientation)
    animation_started = pyqtSignal(str)
    animation_finished = pyqtSignal(str)

    # 用于跨线程安全调用__set_value_impl的自定义信号
    __set_value_signal = pyqtSignal(str, int, bool, object, object, object)

    # --- 默认动画参数 ---
    __DEFAULT_ANIMATION_DURATION = 300  # 默认动画时长 (毫秒)
    __DEFAULT_EASING_CURVE = QEasingCurve.InOutQuad  # 默认缓动曲线

    def __init__(self, progress_bars: List[QProgressBar] = None, parent: QObject = None):
        """
        初始化 QProgressBarHelper。

        Args:
            progress_bars (List[QProgressBar], optional): 要管理的 QProgressBar 列表。
                                                      每个 QProgressBar 必须设置了唯一的 objectName。
                                                      默认为 None。
            parent (QObject, optional): 父对象。默认为 None。
        """
        super().__init__(parent)
        self.__progress_bars: Dict[str, QProgressBar] = {}
        self.__animations: Dict[str, QPropertyAnimation] = {}
        # 确保logger正确初始化
        # self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__logger:Logger = ClassInstanceManager.get_instance(key="ui_logger") # type: ignore
        self.__logger.debug("初始化 QProgressBarHelper...")

        # 为线程安全添加一个锁
        self.__lock = threading.RLock()

        # 连接跨线程信号到槽函数
        self.__set_value_signal.connect(self.__set_value_impl_slot)

        # 处理传入的进度条
        if progress_bars:
            for pbar in progress_bars:
                self.add_progress_bar(pbar)

        self.__logger.debug(f"QProgressBarHelper 初始化完成，共管理 {len(self.__progress_bars)} 个进度条: {list(self.__progress_bars.keys())}")

    def add_progress_bar(self, progress_bar: QProgressBar) -> bool:
        """
        添加一个 QProgressBar 到管理器中。

        Args:
            progress_bar (QProgressBar): 要添加的 QProgressBar 对象，必须设置了 objectName。

        Returns:
            bool: 添加成功返回 True，失败返回 False。
        """
        if sip.isdeleted(progress_bar):
            self.__logger.warning(f"尝试添加一个已被删除的 QProgressBar，已跳过。")
            return False

        object_name = progress_bar.objectName()

        if not object_name:
            self.__logger.error(f"尝试添加一个未设置 objectName 的 QProgressBar，已跳过。请为所有管理的 QProgressBar 设置唯一的 objectName。")
            return False

        if object_name in self.__progress_bars:
            self.__logger.warning(f"尝试添加具有重复 objectName ('{object_name}') 的 QProgressBar，已跳过。确保 objectName 唯一。")
            return False

        self.__progress_bars[object_name] = progress_bar
        self.__logger.debug(f"成功添加 QProgressBar '{object_name}' 到管理器。")
        return True

    def __get_progress_bar(self, name: str) -> Optional[QProgressBar]:
        """
        (私有) 通过 objectName 获取 QProgressBar 实例。
        如果发现进度条已被删除，会清理相关的动画数据并将其从管理器中移除。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[QProgressBar]: 找到的 QProgressBar 实例，如果未找到或已被删除则返回 None。
        """
        self.__logger.debug(f"尝试获取名称为 '{name}' 的 QProgressBar 实例。")
        pbar = self.__progress_bars.get(name)
        if pbar is None:
            self.__logger.warning(f"未在管理器中找到名称为 '{name}' 的 QProgressBar。")
            # 确保如果进度条不在字典中，也没有对应的动画残留
            if name in self.__animations:
                self.__stop_animation(name)  # 会处理删除
            return None
        if sip.isdeleted(pbar):
            self.__logger.warning(f"名称为 '{name}' 的 QProgressBar 已被删除，将从管理器中移除。")
            del self.__progress_bars[name]
            # 确保清理动画
            if name in self.__animations:
                self.__stop_animation(name)  # 会处理删除
            return None
        return pbar

    def __stop_animation(self, name: str) -> None:
        """ (私有) 停止并清理指定名称进度条的当前动画。"""
        if name in self.__animations:
            animation = self.__animations[name]
            if not sip.isdeleted(animation):
                if animation.state() == QAbstractAnimation.State.Running:
                    self.__logger.debug(f"停止进度条 '{name}' 的当前动画。")
                    animation.stop()
                # 安全地断开连接并删除动画对象
                try:
                    animation.finished.disconnect()
                except TypeError:
                    pass  # 没有连接，忽略
            # 从字典中移除引用
            del self.__animations[name]
            self.__logger.debug(f"已清理进度条 '{name}' 的动画引用。")

    def __on_animation_finished(self, name: str) -> None:
        """(私有) 动画完成时的回调函数。"""
        self.__logger.debug(f"进度条 '{name}' 的动画已完成。")
        self.animation_finished.emit(name)
        # 不需要停止动画，因为动画完成后会自动停止
        # 清理不会立即执行，保持引用以便在信号处理完后再清理
        QTimer.singleShot(0, lambda: self.__cleanup_animation_after_finish(name))

    def __cleanup_animation_after_finish(self, name: str) -> None:
        """(私有) 动画完成后清理动画对象的引用。"""
        if name in self.__animations:
            self.__logger.debug(f"清理进度条 '{name}' 完成后的动画资源。")
            del self.__animations[name]

    # --- 公共接口方法 ---

    def get_progress_bar(self, name: str) -> Optional[QProgressBar]:
        """
        通过 objectName 获取 QProgressBar 实例。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[QProgressBar]: 找到的 QProgressBar 实例，如果未找到或已被删除则返回 None。

        示例:
            ```python
            # 获取名为 "taskProgress" 的进度条
            pbar = helper.get_progress_bar("taskProgress")
            if pbar:
                # 直接操作进度条对象
                pbar.setInvertedAppearance(True)
            ```
        """
        return self.__get_progress_bar(name)

    def get_all_progress_bar_names(self) -> List[str]:
        """
        获取当前管理器中所有 QProgressBar 的 objectName 列表。
        会过滤掉已被删除的进度条。

        Returns:
            List[str]: 包含所有有效 objectName 的列表。

        示例:
            ```python
            # 获取所有管理的进度条名称
            names = helper.get_all_progress_bar_names()
            print(f"当前管理的进度条: {names}")
            ```
        """
        self.__logger.debug("获取所有管理的进度条名称列表。")
        valid_names = []
        # 使用 list(self.__progress_bars.keys()) 复制键列表以允许在迭代期间删除
        for name in list(self.__progress_bars.keys()):
            pbar = self.__progress_bars.get(name)
            if pbar and not sip.isdeleted(pbar):
                valid_names.append(name)
            elif pbar:  # 存在但已被删除
                self.__logger.warning(f"发现受管进度条 '{name}' 已被删除，将从管理器移除。")
                del self.__progress_bars[name]
                self.__stop_animation(name)  # 清理动画
        return valid_names

    @pyqtSlot(str, int)
    def set_value(self, name: str, value: int, animate: bool = True,
                  duration_ms: Optional[int] = None,
                  easing_curve: Optional[QEasingCurve] = None) -> bool:
        """
        设置指定 QProgressBar 的值，可选动画效果。
        此方法支持多线程环境下的调用，可以安全地从工作线程中调用。

        如果从非主线程调用，将自动使用Qt的信号槽机制确保UI更新在主线程中执行。

        Args:
            name (str): QProgressBar 的 objectName。
            value (int): 要设置的新值。
            animate (bool, optional): 是否使用动画。默认为 True。
            duration_ms (Optional[int], optional): 动画时长（毫秒）。
                                                 如果为 None 或 animate=False，则使用默认值或不使用。
                                                 默认为 None (使用 __DEFAULT_ANIMATION_DURATION)。
            easing_curve (Optional[QEasingCurve], optional): 动画缓动曲线。
                                                          如果为 None 或 animate=False，则使用默认值或不使用。
                                                          默认为 None (使用 __DEFAULT_EASING_CURVE)。

        Returns:
            bool: 如果成功找到进度条并设置值（或启动动画），则返回 True，否则返回 False。

        示例:
            ```python
            # 设置带动画的值
            helper.set_value("taskProgress", 50)

            # 设置不带动画的值
            helper.set_value("taskProgress", 75, animate=False)

            # 自定义动画效果
            helper.set_value("taskProgress", 100,
                            duration_ms=2000,
                            easing_curve=QEasingCurve.OutElastic)

            # 在工作线程中使用 - 现在可以直接调用，无需手动使用QMetaObject
            def worker_thread():
                for i in range(100):
                    helper.set_value("taskProgress", i)
                    time.sleep(0.1)

            threading.Thread(target=worker_thread).start()
            ```
        """
        # 检查是否在主线程中运行
        try:
            import traceback  # 确保traceback导入可用
            from PyQt5.QtCore import QThread
            current_thread = QThread.currentThread()
            app_instance = QApplication.instance()
            # 添加防御性检查，app_instance可能为None
            if app_instance is None:
                self.__logger.warning("无法获取QApplication实例，假定在主线程中执行")
                main_thread = current_thread  # 假定当前线程就是主线程
            else:
                main_thread = app_instance.thread()
            in_main_thread = (current_thread == main_thread)

            # 如果不在主线程中，使用信号-槽机制确保UI更新在主线程中执行
            if not in_main_thread:
                self.__logger.debug(f"从工作线程调用set_value('{name}', {value})，使用信号-槽机制确保线程安全")
                try:
                    # 创建一个事件循环等待结果返回
                    result_list = []
                    wait_event = threading.Event()

                    # 定义回调处理函数
                    def handle_result(result_val):
                        result_list.append(result_val)
                        wait_event.set()

                    # 使用自定义信号触发主线程中的槽函数
                    self.__set_value_signal.emit(name, value, animate, duration_ms, easing_curve, handle_result)

                    # 等待结果返回，最多等待3秒
                    if wait_event.wait(3.0):
                        return result_list[0]
                    else:
                        self.__logger.error(f"等待set_value结果超时: {name}, {value}")
                        return False

                except Exception as e:
                    traceback.print_exc()
                    self.__logger.error(f"通过信号-槽机制调用set_value时出错: {str(e)}")
                    return False
            else:
                # 在主线程中，直接调用实现方法
                self.__logger.debug(f"在主线程中直接调用set_value('{name}', {value})")
                return self.__set_value_impl(name, value, animate, duration_ms, easing_curve)  # type: ignore
        except Exception as e:
            # 如果在确定线程状态时出错，记录日志并继续用常规方法执行
            import traceback
            traceback.print_exc()
            self.__logger.error(f"确定线程状态时出错: {str(e)}，继续使用常规方法")
            # 出现异常时，假定在主线程中，直接调用实现方法
            return self.__set_value_impl(name, value, animate, duration_ms, easing_curve)  # type: ignore

    @pyqtSlot(str, int, bool, object, object, object)
    def __set_value_impl_slot(self, name: str, value: int, animate: bool,
                              duration_ms: Optional[int],
                              easing_curve: Optional[QEasingCurve],
                              callback_fn: Optional[Callable[[bool], None]] = None) -> None:
        """
        接收跨线程信号并调用__set_value_impl的槽函数。
        此方法总是在UI线程中执行。

        Args:
            name: 进度条名称
            value: 要设置的值
            animate: 是否使用动画
            duration_ms: 动画持续时间
            easing_curve: 动画曲线
            callback_fn: 可选的回调函数，用于将结果返回给调用线程
        """
        try:
            result = self.__set_value_impl(name, value, animate, duration_ms, easing_curve) # type: ignore
            # 如果提供了回调函数，调用它返回结果
            if callback_fn is not None and callable(callback_fn):
                callback_fn(result)
        except Exception as e:
            self.__logger.error(f"在UI线程中执行__set_value_impl_slot时出错: {str(e)}")
            traceback.print_exc()
            # 如果提供了回调函数，通知失败
            if callback_fn is not None and callable(callback_fn):
                callback_fn(False)

    @pyqtSlot(str, int, bool, object, object, object)
    def __set_value_impl(self, name: str, value: int, animate: bool = True,
                         duration_ms: Optional[int] = None,
                         easing_curve: Optional[QEasingCurve] = None,
                         result: Optional[List[bool]] = None) -> bool:
        """
        set_value方法的实际实现，设置指定QProgressBar的值。
        此方法应当只在主线程中调用，由set_value方法根据当前线程自动选择调用方式。

        Args:
            与set_value方法相同，外加一个result参数用于线程间返回值传递
            result: 可选的结果列表，如果提供，结果将存储在result[0]中

        Returns:
            bool: 操作是否成功
        """
        with self.__lock:
            self.__logger.debug(f"请求设置进度条 '{name}' 的值为 {value} (动画: {animate})")
            pbar = self.__get_progress_bar(name)
            if not pbar:
                if result is not None:
                    result[0] = False
                return False

            # 验证值范围
            min_val = pbar.minimum()
            max_val = pbar.maximum()
            clamped_value = max(min_val, min(value, max_val))
            if clamped_value != value:
                self.__logger.warning(f"请求的值 {value} 超出范围 [{min_val}, {max_val}]，已修正为 {clamped_value}。")
                value = clamped_value

            original_value = pbar.value()

            # 总是先停止旧动画
            self.__stop_animation(name)

            try:
                if animate:
                    # 如果值没有变化，则不执行动画
                    if original_value == value:
                        self.__logger.debug(f"进度条 '{name}' 的值已经是 {value}，跳过动画。")
                        if result is not None:
                            result[0] = True
                        return True  # 视为成功，因为值已经是目标值

                    # --- 设置动画 ---
                    anim = QPropertyAnimation(pbar, b"value", self)  # 使用 self 作为父对象方便管理
                    current_duration = duration_ms if duration_ms is not None else self.__DEFAULT_ANIMATION_DURATION
                    current_curve = easing_curve if easing_curve is not None else self.__DEFAULT_EASING_CURVE

                    anim.setDuration(current_duration)
                    anim.setEasingCurve(current_curve)
                    anim.setStartValue(original_value)  # 从当前实际值开始
                    anim.setEndValue(value)

                    # --- 连接信号 ---
                    # 使用 lambda 捕获 name
                    anim.finished.connect(lambda: self.__on_animation_finished(name))

                    # --- 存储和启动 ---
                    self.__animations[name] = anim
                    anim.start(QPropertyAnimation.DeleteWhenStopped)  # type: ignore # 动画结束后自动删除
                    self.__logger.debug(f"进度条 '{name}' 的值动画已启动: {original_value} -> {value}, 时长: {current_duration}ms, 曲线: {current_curve}")
                    self.animation_started.emit(name)  # 发射动画开始信号

                    # 为了保持一致性，值改变时就触发信号
                    if original_value != value:
                        self.value_changed.emit(name, value)  # 触发值改变信号（目标值）

                else:
                    # --- 直接设置值 ---
                    if original_value != value:
                        pbar.setValue(value)
                        self.__logger.debug(f"已直接设置进度条 '{name}' 的值为 {value}。")
                        self.value_changed.emit(name, value)  # 发射信号
                    else:
                        self.__logger.debug(f"进度条 '{name}' 的值已经是 {value}，无需直接设置。")

                if result is not None:
                    result[0] = True
                return True

            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 值时发生错误: {e}")
                traceback.print_exc()
                if result is not None:
                    result[0] = False
                return False

    def get_value(self, name: str) -> Optional[int]:
        """
        获取指定 QProgressBar 的当前值。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[int]: 当前值，如果找不到进度条则返回 None。

        示例:
            ```python
            # 获取进度条当前值
            value = helper.get_value("taskProgress")
            print(f"当前进度: {value}%")
            ```
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的当前值。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.value()
        else:
            return None

    @pyqtSlot(str, int, int)
    def set_range(self, name: str, minimum: int, maximum: int) -> bool:
        """
        设置指定 QProgressBar 的范围（最小值和最大值）。
        此方法支持多线程环境下的调用，可以安全地从工作线程中通过Qt的信号槽机制调用。

        Args:
            name (str): QProgressBar 的 objectName。
            minimum (int): 新的最小值。
            maximum (int): 新的最大值。

        Returns:
            bool: 如果成功找到进度条并设置范围，则返回 True，否则返回 False。

        示例:
            ```python
            # 设置进度条范围为 0-200
            helper.set_range("taskProgress", 0, 200)

            # 在工作线程中使用
            QMetaObject.invokeMethod(
                helper,
                "set_range",
                Qt.QueuedConnection,
                Q_ARG(str, "taskProgress"),
                Q_ARG(int, 0),
                Q_ARG(int, 200)
            )
            ```
        """
        with self.__lock:
            self.__logger.debug(f"尝试设置进度条 '{name}' 的范围为 [{minimum}, {maximum}]。")
            pbar = self.__get_progress_bar(name)
            if pbar:
                try:
                    original_min = pbar.minimum()
                    original_max = pbar.maximum()
                    if original_min != minimum or original_max != maximum:
                        pbar.setRange(minimum, maximum)
                        self.__logger.debug(f"成功设置进度条 '{name}' 的范围。")
                        self.range_changed.emit(name, minimum, maximum)  # 发射信号
                        return True
                    else:
                        self.__logger.debug(f"进度条 '{name}' 的范围已经是 [{minimum}, {maximum}]，无需更改。")
                        return True
                except Exception as e:
                    self.__logger.error(f"设置进度条 '{name}' 范围时发生错误: {e}")
                    traceback.print_exc()
                    return False
            else:
                return False

    def get_range(self, name: str) -> Optional[Tuple[int, int]]:
        """
        获取指定 QProgressBar 的范围（最小值和最大值）。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[Tuple[int, int]]: 包含 (最小值, 最大值) 的元组，如果找不到进度条则返回 None。

        示例:
            ```python
            # 获取进度条范围
            min_val, max_val = helper.get_range("taskProgress")
            print(f"进度范围: {min_val} - {max_val}")
            ```
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的范围。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return (pbar.minimum(), pbar.maximum())
        else:
            return None

    def get_minimum(self, name: str) -> Optional[int]:
        """
        获取指定 QProgressBar 的最小值。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[int]: 最小值，如果找不到进度条则返回 None。
        """
        pbar = self.__get_progress_bar(name)
        return pbar.minimum() if pbar else None

    def get_maximum(self, name: str) -> Optional[int]:
        """
        获取指定 QProgressBar 的最大值。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[int]: 最大值，如果找不到进度条则返回 None。
        """
        pbar = self.__get_progress_bar(name)
        return pbar.maximum() if pbar else None

    def set_format(self, name: str, format_str: str) -> bool:
        """
        设置指定 QProgressBar 的显示格式字符串。

        Args:
            name (str): QProgressBar 的 objectName。
            format_str (str): 新的格式字符串 (例如 "%p%", "%v/%m")。

        Returns:
            bool: 如果成功找到进度条并设置格式，则返回 True，否则返回 False。

        示例:
            ```python
            # 设置进度条显示格式
            helper.set_format("taskProgress", "当前进度: %p%")
            ```
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的格式为: '{format_str}'")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_format = pbar.format()
                if original_format != format_str:
                    pbar.setFormat(format_str)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的格式。")
                    self.format_changed.emit(name, format_str)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的格式已经是 '{format_str}'，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 格式时发生错误: {e}")
                traceback.print_exc()
                return False
        else:
            return False

    def get_format(self, name: str) -> Optional[str]:
        """
        获取指定 QProgressBar 的当前显示格式字符串。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[str]: 当前格式字符串，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的格式。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.format()
        else:
            return None

    def set_text_visible(self, name: str, visible: bool) -> bool:
        """
        设置指定 QProgressBar 的文本是否可见。

        Args:
            name (str): QProgressBar 的 objectName。
            visible (bool): True 表示可见，False 表示隐藏。

        Returns:
            bool: 如果成功找到进度条并设置可见性，则返回 True，否则返回 False。

        示例:
            ```python
            # 隐藏进度条文本
            helper.set_text_visible("taskProgress", False)
            ```
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的文本可见性为: {visible}")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_visible = pbar.isTextVisible()
                if original_visible != visible:
                    pbar.setTextVisible(visible)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的文本可见性。")
                    self.text_visibility_changed.emit(name, visible)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的文本可见性已经是 {visible}，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 文本可见性时发生错误: {e}")
                traceback.print_exc()
                return False
        else:
            return False

    def is_text_visible(self, name: str) -> Optional[bool]:
        """
        检查指定 QProgressBar 的文本是否可见。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[bool]: 如果文本可见返回 True，否则返回 False。如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的文本可见性。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.isTextVisible()
        else:
            return None

    def set_orientation(self, name: str, orientation: Qt.Orientation) -> bool:
        """
        设置指定 QProgressBar 的方向。

        Args:
            name (str): QProgressBar 的 objectName。
            orientation (Qt.Orientation): 新的方向 (Qt.Horizontal 或 Qt.Vertical)。

        Returns:
            bool: 如果成功找到进度条并设置方向，则返回 True，否则返回 False。

        示例:
            ```python
            # 设置为垂直方向
            helper.set_orientation("taskProgress", Qt.Vertical)
            ```
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的方向为: {orientation}")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_orientation = pbar.orientation()
                if original_orientation != orientation:
                    pbar.setOrientation(orientation)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的方向。")
                    self.orientation_changed.emit(name, orientation)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的方向已经是 {orientation}，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 方向时发生错误: {e}")
                traceback.print_exc()
                return False
        else:
            return False

    def get_orientation(self, name: str) -> Optional[Qt.Orientation]:
        """
        获取指定 QProgressBar 的当前方向。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[Qt.Orientation]: 当前方向，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的方向。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.orientation()
        else:
            return None

    def reset(self, name: str, animate: bool = False, **kwargs) -> bool:
        """
        将指定 QProgressBar 的值重置为其最小值。

        Args:
            name (str): QProgressBar 的 objectName。
            animate (bool, optional): 是否使用动画重置。默认为 False。
            **kwargs: 传递给 set_value 的其他动画参数 (duration_ms, easing_curve)。

        Returns:
            bool: 操作是否成功 (取决于 set_value 的返回值)。

        示例:
            ```python
            # 重置进度条到最小值
            helper.reset("taskProgress")

            # 使用动画重置
            helper.reset("taskProgress", animate=True, duration_ms=500)
            ```
        """
        self.__logger.debug(f"尝试重置进度条 '{name}' 的值 (动画: {animate})。")
        pbar = self.__get_progress_bar(name)
        if not pbar:
            return False

        min_val = pbar.minimum()
        # 显式类型标注以避免linter误判
        set_value_func = self.set_value  # type: Callable
        return set_value_func(name, min_val, animate=animate, **kwargs)

    def stop_animation(self, name: str) -> None:
        """
        停止指定 QProgressBar 上当前正在进行的动画。

        Args:
            name (str): QProgressBar 的 objectName。

        示例:
            ```python
            # 停止进度条动画
            helper.stop_animation("taskProgress")
            ```
        """
        self.__logger.debug(f"请求停止进度条 '{name}' 的动画。")
        self.__stop_animation(name)

    def remove_progress_bar(self, name: str) -> bool:
        """
        从管理器中移除指定的 QProgressBar。

        Args:
            name (str): 要移除的 QProgressBar 的 objectName。

        Returns:
            bool: 如果成功移除则返回 True，如果找不到指定名称的进度条则返回 False。

        示例:
            ```python
            # 从管理器中移除进度条
            helper.remove_progress_bar("taskProgress")
            ```
        """
        self.__logger.debug(f"尝试从管理器中移除进度条 '{name}'。")
        if name in self.__progress_bars:
            # 先停止动画
            self.__stop_animation(name)
            # 移除进度条
            del self.__progress_bars[name]
            self.__logger.debug(f"成功从管理器中移除进度条 '{name}'。")
            return True
        else:
            self.__logger.warning(f"尝试移除不存在的进度条 '{name}'。")
            return False

    def cleanup(self) -> None:
        """
        清理 QProgressBarHelper，停止所有动画并清除内部引用。
        建议在父对象销毁前调用，或者依赖父子关系自动清理。

        示例:
            ```python
            # 清理所有资源
            helper.cleanup()
            ```
        """
        self.__logger.debug("开始清理 QProgressBarHelper...")
        # 停止所有动画
        # 使用 list 复制键，因为 __stop_animation 会修改字典
        names_to_stop = list(self.__animations.keys())
        for name in names_to_stop:
            self.__stop_animation(name)
        self.__animations.clear()

        # 清除进度条引用
        self.__progress_bars.clear()
        self.__logger.debug("QProgressBarHelper 清理完成。")

    def batch_update(self, values: Dict[str, int], animate: bool = True, **kwargs) -> Dict[str, bool]:
        """
        批量更新多个进度条的值。

        Args:
            values (Dict[str, int]): 字典，键为进度条名称，值为要设置的新值。
            animate (bool, optional): 是否使用动画。默认为 True。
            **kwargs: 传递给 set_value 的其他动画参数 (duration_ms, easing_curve)。

        Returns:
            Dict[str, bool]: 字典，键为进度条名称，值为更新是否成功。

        示例:
            ```python
            # 批量更新多个进度条
            results = helper.batch_update({
                "taskProgress": 50,
                "overallProgress": 75
            }, animate=True, duration_ms=800)

            # 检查结果
            for name, success in results.items():
                print(f"进度条 {name} 更新{'成功' if success else '失败'}")
            ```
        """
        self.__logger.debug(f"批量更新进度条值: {values}")
        results = {}
        # 显式类型标注以避免linter误判
        set_value_func = self.set_value  # type: Callable
        for name, value in values.items():
            results[name] = set_value_func(name, value, animate=animate, **kwargs)
        return results
