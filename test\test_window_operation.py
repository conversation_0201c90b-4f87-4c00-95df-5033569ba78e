import sys
import os

# 将项目根目录添加到Python的模块搜索路径中，以解决模块导入问题
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from global_tools.window_operation.utils import get_random_point_in_polygon,get_polygon_center

import unittest

# shapely is required for robust verification of the test results.
try:
    from shapely.geometry import Point, Polygon
    from shapely.affinity import scale
    SHAPELY_AVAILABLE = True
except ImportError:
    SHAPELY_AVAILABLE = False

#
# @unittest.skipIf(not SHAPELY_AVAILABLE, "shapely library not found, skipping geometric tests.")
# class TestGetRandomPointInPolygon(unittest.TestCase):
#     """
#     为 get_random_point_in_polygon 函数提供单元测试。
#     """
#
#     def setUp(self):
#         """在每个测试方法运行前设置好测试所需的数据。"""
#         self.poly_coords_tuples = [(100, 50), (400, 50), (450, 250), (350, 350), (150, 300)]
#         self.poly_coords_flat = [100, 50, 400, 50, 450, 250, 350, 350, 150, 300]
#         self.original_polygon = Polygon(self.poly_coords_tuples)
#         print(f"\nRunning test: {self._testMethodName}")
#
#     def test_valid_point_with_tuple_list_and_default_margin(self):
#         """测试基本功能：使用元组列表和默认15%的边距。"""
#         margin = 0.15
#         point = get_random_point_in_polygon(self.poly_coords_tuples, margin=margin)
#
#         self.assertIsNotNone(point, "函数不应返回 None")
#         self.assertIsInstance(point, tuple, "返回值应为元组")
#         self.assertEqual(len(point), 2, "元组应包含x, y两个坐标")
#
#         # 验证点是否在内缩后的多边形内
#         min_x, min_y, max_x, max_y = self.original_polygon.bounds
#         offset = min(max_x - min_x, max_y - min_y) * margin
#         inner_polygon = self.original_polygon.buffer(-offset)
#         self.assertTrue(inner_polygon.contains(Point(point)), "生成的点不在正确的内缩区域内")
#         print(" -> Success: Point is within the scaled polygon.")
#
#     def test_valid_point_with_flat_list_and_custom_margin(self):
#         """测试替代功能：使用扁平列表和自定义10%的边距。"""
#         margin = 0.1
#         point = get_random_point_in_polygon(self.poly_coords_flat, margin=margin)
#
#         self.assertIsNotNone(point)
#
#         min_x, min_y, max_x, max_y = self.original_polygon.bounds
#         offset = min(max_x - min_x, max_y - min_y) * margin
#         inner_polygon = self.original_polygon.buffer(-offset)
#         self.assertTrue(inner_polygon.contains(Point(point)), "生成的点不在正确的内缩区域内")
#         print(" -> Success: Point is within the custom scaled polygon.")
#
#     def test_valid_point_with_zero_margin(self):
#         """测试零边距：点应该在原始多边形内部。"""
#         point = get_random_point_in_polygon(self.poly_coords_tuples, margin=0)
#
#         self.assertIsNotNone(point)
#         self.assertTrue(self.original_polygon.contains(Point(point)), "生成的点不在原始多边形内")
#         print(" -> Success: Point is within the original polygon.")
#
#     def test_large_margin_returns_none(self):
#         """测试边界情况：过大的边距应该导致返回 None。"""
#         # 对于这个特定的多边形，0.49的边距足够大以致于内缩后的图形为空
#         point = get_random_point_in_polygon(self.poly_coords_tuples, margin=0.49)
#         self.assertIsNone(point, "当边距过大时，应返回 None")
#         print(" -> Success: Correctly returned None for an excessive margin.")
#
#     def test_invalid_margin_raises_value_error(self):
#         """测试错误处理：超出[0, 1)范围的边距应抛出 ValueError。"""
#         with self.assertRaises(ValueError, msg="大于等于1的边距应引发错误"):
#             get_random_point_in_polygon(self.poly_coords_tuples, margin=1.1)
#
#         with self.assertRaises(ValueError, msg="负边距应引发错误"):
#             get_random_point_in_polygon(self.poly_coords_tuples, margin=-0.1)
#         print(" -> Success: Correctly raised ValueError for invalid margins.")
#
#     def test_insufficient_points_raises_value_error(self):
#         """测试错误处理：少于3个顶点应抛出 ValueError。"""
#         with self.assertRaises(ValueError, msg="少于3个顶点应引发错误"):
#             get_random_point_in_polygon([(0, 0), (100, 100)])
#         print(" -> Success: Correctly raised ValueError for insufficient points.")
#
#     def test_flat_list_with_odd_length_raises_value_error(self):
#         """测试错误处理：长度为奇数的扁平列表应抛出 ValueError。"""
#         with self.assertRaises(ValueError, msg="奇数长度的扁平列表应引发错误"):
#             get_random_point_in_polygon([10, 20, 30, 40, 50])
#         print(" -> Success: Correctly raised ValueError for odd-length flat list.")
#

if __name__ == '__main__':
    # 使脚本可以直接运行
    # unittest.main(verbosity=2)
    # r = get_random_point_in_polygon([
    #     (
    #       349.7368421052632,
    #       327.5438596491228
    #     ),
    #     (
    #       435.70175438596493,
    #       323.6842105263158
    #     ),
    #     (
    #       454.29824561403507,
    #       342.6315789473684
    #     ),
    #     (
    #       461.66666666666663,
    #       325.7894736842105
    #     ),
    #     (
    #       477.8070175438596,
    #       311.7543859649123
    #     ),
    #     (
    #       493.59649122807014,
    #       342.6315789473684
    #     ),
    #     (
    #       516.4035087719299,
    #       326.4912280701754
    #     ),
    #     (
    #       605.5263157894736,
    #       327.5438596491228
    #     ),
    #     (
    #       579.2105263157895,
    #       369.29824561403507
    #     ),
    #     (
    #       509.38596491228066,
    #       381.2280701754386
    #     ),
    #     (
    #       490.7894736842105,
    #       382.6315789473684
    #     ),
    #     (
    #       499.9122807017544,
    #       403.3333333333333
    #     ),
    #     (
    #       472.1929824561404,
    #       422.2807017543859
    #     ),
    #     (
    #       458.859649122807,
    #       400.1754385964912
    #     ),
    #     (
    #       463.0701754385965,
    #       375.6140350877193
    #     ),
    #     (
    #       406.57894736842104,
    #       377.36842105263156
    #     ),
    #     (
    #       365.17543859649123,
    #       360.1754385964912
    #     ),
    #     (
    #       349.7368421052632,
    #       339.4736842105263
    #     )
    #   ])
    # print(r)

    r = get_polygon_center( [
        [
          442.36842105263156,
          253.50877192982458
        ],
        [
          484.1228070175439,
          265.43859649122805
        ],
        [
          507.280701754386,
          291.4035087719298
        ],
        [
          509.7368421052631,
          326.14035087719293
        ],
        [
          512.8947368421052,
          341.57894736842104
        ],
        [
          525.1754385964912,
          365.0877192982456
        ],
        [
          508.68421052631584,
          372.8070175438596
        ],
        [
          498.859649122807,
          417.0175438596491
        ],
        [
          480.2631578947369,
          444.0350877192982
        ],
        [
          445.1754385964912,
          452.45614035087715
        ],
        [
          430.43859649122805,
          451.4035087719298
        ],
        [
          455.0,
          413.1578947368421
        ],
        [
          471.8421052631579,
          360.8771929824561
        ],
        [
          444.47368421052636,
          348.9473684210526
        ],
        [
          438.50877192982455,
          337.36842105263156
        ],
        [
          439.9122807017544,
          328.59649122807014
        ],
        [
          474.29824561403507,
          334.91228070175436
        ],
        [
          488.68421052631584,
          316.31578947368416
        ],
        [
          472.1929824561404,
          287.89473684210526
        ]
      ])
    print(r)




