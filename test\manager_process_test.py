from global_tools.utils.manager_process.mannger_process import SharedDataManager, ProcessPoolManager


# 使用示例:

def process_task(shared_data_manager: SharedDataManager, item, *args, **kwargs):
    # 在这里处理数据项
    print(f"处理数据项: {item}")
    
    lock = shared_data_manager.get_lock()
    with lock:
        # 使用共享数据管理器
        shared_data_manager.increment("processed_count")

        # 添加处理结果到共享列表
        shared_data_manager.append_to_list("results", f"处理结果: {item}")

    # 其他处理逻辑...


def main():
    # 创建进程池管理器
    data_items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    max_processes = 4
    manager = ProcessPoolManager(data_items, max_processes, process_task)

    # 启动处理
    manager.start()

    # 等待所有进程完成
    manager.wait_completion()

    # 获取处理结果
    results = manager.get_shared_data("results", [])
    processed_count = manager.get_shared_data("processed_count", 0)
    print(f"已处理 {processed_count} 个项目")
    print(f"处理结果: {results}")


if __name__ == "__main__":
    main()
