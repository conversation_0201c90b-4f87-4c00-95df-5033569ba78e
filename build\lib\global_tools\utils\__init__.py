from .colors import Colors
from .helper import (
    Lo<PERSON>,
    Singleton,
    LogLevel,
    find_same_elements_groups,
    OptimalSorter,
    flatten_preserve_types,
    flatten_preserve_types_no_dict,
    clean_directory,
    delete_files,
    get_file_info,
    list_directory_files,
    split_list_evenly,
    find_latest,
    find_latest_created_folder,
    ClassInstanceManager
)
from .event import EventEmitter
from .manager_process3 import ManagedMultiProcess, SharedDataManager, ProcessEventManager
from .enhanced_process import EnhancedProcess, ProcessLogger


__all__ = [
    # ------- 终端输出与日志 -------
    'Colors',                   # 类: 用于在终端输出中添加颜色。
    'Logger',                   # 类: 提供日志记录功能。
    "LogLevel",

    # ------- 设计模式与工具类 -------
    'Singleton',                # 类: 实现单例设计模式的基类。
    'OptimalSorter',            # 类: 提供某种优化排序的功能。
    'EventEmitter',             # 类: 实现事件发射器模式，用于事件驱动编程。

    # ------- 列表和容器操作 -------
    'find_same_elements_groups',  # 函数: 查找列表中相同元素的分组。
    'flatten_preserve_types',   # 函数: 展平嵌套列表/元组，保留原始数据类型（包括字典）。
    'flatten_preserve_types_no_dict',  # 函数: 展平嵌套列表/元组，保留原始数据类型（不处理字典）。
    'split_list_evenly',        # 函数: 将列表尽可能均匀地分割成指定数量的部分。

    # ------- 文件系统操作 -------
    'clean_directory',          # 函数: 清理指定目录的内容。
    'delete_files',             # 函数: 删除指定的文件。
    'get_file_info',            # 函数: 获取文件的信息（如大小、修改时间等）。
    "list_directory_files",     # 函数: 列出指定目录下的所有文件。
    "find_latest",              # 函数: 在一组文件或目录中查找最新的一个（基于修改时间）。
    "find_latest_created_folder",  # 函数: 在指定路径下查找最新创建的文件夹。

    # ------- 进程管理 -------
    "EnhancedProcess",          # 类: 对原生进程进行增强，可能包含额外的控制或通信功能。
    "ProcessLogger",            # 进程日志记录器，提供给子进程用于手动记录日志，并传递到主进程。。
    "ManagedMultiProcess",      # 类: 高级多进程任务处理管理器，支持数据共享、事件监听等。
    "SharedDataManager",        # 类: 用于ManagedMultiProcess内部，管理进程间共享的数据。
    "ProcessEventManager",      # 类: 用于ManagedMultiProcess内部，管理进程相关的事件。

    # ------- 其他 -------
    "ClassInstanceManager",     # 类: 用于管理类实例。
]
