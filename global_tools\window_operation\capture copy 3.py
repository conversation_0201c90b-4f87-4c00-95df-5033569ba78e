import os
import sys
import time
import logging
import numpy as np
import cv2
import ctypes
from ctypes import wintypes
from ctypes import POINTER, byref, windll, Structure, WINFUNCTYPE, c_void_p, c_int, c_uint, c_bool, c_float
from typing import Tuple, Optional, Union, Dict, Any, List



# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WindowsGraphicsCapture")

# Windows API constants
user32 = ctypes.windll.user32
gdi32 = ctypes.windll.gdi32
kernel32 = ctypes.windll.kernel32

# Windows API constants for multi-monitor support
SM_CXVIRTUALSCREEN = 78  # Width of virtual screen
SM_CYVIRTUALSCREEN = 79  # Height of virtual screen
SM_XVIRTUALSCREEN = 76   # Left coordinate of virtual screen
SM_YVIRTUALSCREEN = 77   # Top coordinate of virtual screen

# Define necessary Windows API structures
class RECT(Structure):
    _fields_ = [("left", c_int),
                ("top", c_int),
                ("right", c_int),
                ("bottom", c_int)]

class MONITORINFO(Structure):
    _fields_ = [
        ("cbSize", c_uint),
        ("rcMonitor", RECT),
        ("rcWork", RECT),
        ("dwFlags", c_uint)
    ]

class WindowsGraphicsCapture:
    """
    A high-performance screenshot capture class that uses DXcam for screen capture.
    This class provides methods to capture screenshots from specified window handles
    with coordinates relative to the window's top-left corner.
    
    DXcam utilizes the Desktop Duplication API, which provides high-performance 
    screen capture capabilities in Windows.
    
    Attributes:
        __dpi_scale (float): The current DPI scaling factor.
        __last_capture_time (float): Timestamp of the last capture operation.
    """

    def __init__(self):
        """
        Initialize the WindowsGraphicsCapture class.
        Sets up DXcam for screen capture.
        """
        self.__dpi_scale = self.__get_dpi_scale()
        self.__last_capture_time = 0.0
        self.__dxcam_instance = None
        # Store screen dimensions for region validation
        self.__screen_width = user32.GetSystemMetrics(0)  # SM_CXSCREEN
        self.__screen_height = user32.GetSystemMetrics(1)  # SM_CYSCREEN
        self.__initialize_dxcam()
        logger.info("WindowsGraphicsCapture initialized with DXcam backend")
        logger.info(f"Screen resolution detected: {self.__screen_width}x{self.__screen_height}")
        # Maximum number of retries for capture attempts
        self.__max_retries = 3
        # Minimum delay between captures to avoid overwhelming DXcam
        self.__min_delay_between_captures = 0.01  # 10ms

    def __initialize_dxcam(self) -> None:
        """
        Initialize DXcam for screen capture.
        """
        try:
            # Import dxcam dynamically to avoid direct dependency
            import importlib.util
            spec = importlib.util.find_spec("dxcam")
            if spec is not None:
                dxcam = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(dxcam)
                # Create DXcam instance with RGB output color
                self.__dxcam_instance = dxcam.create(output_color="RGB")
                logger.info("DXcam initialized successfully")
            else:
                raise ImportError("DXcam module not found")
        except ImportError as e:
            logger.error(f"Error importing DXcam: {str(e)}")
            logger.error("Please install DXcam using: pip install dxcam")
            raise ImportError("DXcam is required for WindowsGraphicsCapture")

    def __get_dpi_scale(self) -> float:
        """
        Gets the current DPI scaling factor.

        Returns:
            float: The DPI scaling factor.
        """
        try:
            # Check if Windows 8.1 or later
            if hasattr(windll, "shcore"):
                awareness = ctypes.c_int()
                windll.shcore.GetProcessDpiAwareness(0, ctypes.byref(awareness))
                
                # If the process is DPI aware, get the DPI scaling
                if awareness.value != 0:  # 0 means DPI unaware
                    dpi = ctypes.c_uint()
                    windll.shcore.GetDpiForSystem(ctypes.byref(dpi))
                    return dpi.value / 96.0
            return 1.0
        except Exception:
            # Fallback to default scale if unable to determine
            return 1.0

    def __get_window_rect(self, hwnd: int) -> Tuple[int, int, int, int]:
        """
        Gets the rectangle coordinates of a window.

        Args:
            hwnd (int): The window handle.

        Returns:
            Tuple[int, int, int, int]: A tuple containing (left, top, right, bottom) coordinates.
        """
        rect = RECT()
        if not user32.GetClientRect(hwnd, byref(rect)):
            raise ctypes.WinError()
        
        # Convert client coordinates to screen coordinates
        pt = wintypes.POINT(rect.left, rect.top)
        user32.ClientToScreen(hwnd, byref(pt))
        rect.left, rect.top = pt.x, pt.y
        
        pt = wintypes.POINT(rect.right, rect.bottom)
        user32.ClientToScreen(hwnd, byref(pt))
        rect.right, rect.bottom = pt.x, pt.y
        
        return (rect.left, rect.top, rect.right, rect.bottom)

    def __validate_window(self, hwnd: int) -> bool:
        """
        Validates that a window handle is valid and visible.

        Args:
            hwnd (int): The window handle.

        Returns:
            bool: True if the window is valid and visible, False otherwise.
        """
        if not user32.IsWindow(hwnd):
            logger.warning(f"Invalid window handle: {hwnd}")
            return False
        
        if not user32.IsWindowVisible(hwnd):
            logger.warning(f"Window {hwnd} is not visible")
            return False
        
        # Check if the window is minimized
        if user32.IsIconic(hwnd):
            logger.warning(f"Window {hwnd} is minimized")
            return False
            
        return True

    def __validate_and_adjust_region(self, 
                                    window_rect: Tuple[int, int, int, int], 
                                    region: Optional[Tuple[int, int, int, int]] = None) -> Tuple[int, int, int, int]:
        """
        Validates and adjusts a capture region to ensure it's within screen bounds.

        Args:
            window_rect (Tuple[int, int, int, int]): The window rectangle (left, top, right, bottom).
            region (Optional[Tuple[int, int, int, int]]): The region to validate (x, y, right, bottom).

        Returns:
            Tuple[int, int, int, int]: The adjusted region (left, top, right, bottom).
        """
        if region is None:
            # Use the entire window
            left, top, right, bottom = window_rect
        else:
            # Calculate absolute screen coordinates based on window-relative region
            left = window_rect[0] + region[0]
            top = window_rect[1] + region[1]
            right = window_rect[0] + region[2] 
            bottom = window_rect[1] + region[3]
        
        # Ensure the region is within the screen bounds
        left = max(0, min(left, self.__screen_width - 1))
        top = max(0, min(top, self.__screen_height - 1))
        right = max(left + 1, min(right, self.__screen_width))
        bottom = max(top + 1, min(bottom, self.__screen_height))
        
        # Ensure minimum dimensions (DXcam needs at least 1x1 region)
        if right - left < 1:
            right = left + 1
        if bottom - top < 1:
            bottom = top + 1
            
        logger.debug(f"Adjusted region: ({left}, {top}, {right}, {bottom})")
        return (left, top, right, bottom)

    def capture(self, 
                hwnd: int, 
                region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Captures a screenshot from the specified window handle using DXcam.
        
        Args:
            hwnd (int): The window handle to capture.
            region (Optional[Tuple[int, int, int, int]]): Optional region to capture (x, y, right, bottom)
                                                         relative to window's top-left corner.

        Returns:
            np.ndarray: Captured image as a numpy array in RGB format.
            
        Example:
            ```python
            # Create a WindowsGraphicsCapture instance
            capture = WindowsGraphicsCapture()
            
            # Get a window handle (example using window title)
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("Notepad")
            
            # Capture the entire window
            img = capture.capture(hwnd)
            
            # Capture a specific region (100x100 pixels from top-left)
            region_img = capture.capture(hwnd, region=(0, 0, 100, 100))
            
            # Display the captured image
            cv2.imshow("Captured", img)
            cv2.waitKey(0)
            ```
        """
        start_time = time.time()
        last_error = None
        
        # Check if DXcam is available
        if self.__dxcam_instance is None:
            raise RuntimeError("DXcam is not initialized")
        
        # Validate the window handle
        if not self.__validate_window(hwnd):
            raise ValueError(f"Window handle {hwnd} is not valid or visible")
        
        # Add a small delay if captures happen too quickly
        elapsed_since_last = time.time() - (start_time - self.__last_capture_time / 1000.0)
        if elapsed_since_last < self.__min_delay_between_captures:
            time.sleep(self.__min_delay_between_captures - elapsed_since_last)
        
        # Retry mechanism for more robust capturing
        for retry in range(self.__max_retries):
            try:
                # Get window coordinates
                window_rect = self.__get_window_rect(hwnd)
                
                # Validate and adjust the region to ensure it's within bounds
                dxcam_region = self.__validate_and_adjust_region(window_rect, region)
                
                # Perform the capture using DXcam
                frame = self.__dxcam_instance.grab(region=dxcam_region)
                
                if frame is None:
                    logger.warning(f"DXcam returned None on attempt {retry+1}/{self.__max_retries}")
                    last_error = RuntimeError("Failed to capture frame with DXcam")
                    # Wait a bit before retrying
                    time.sleep(0.05 * (retry + 1))
                    continue
                
                # Record metrics
                elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                self.__last_capture_time = elapsed_time
                
                # Log performance data
                logger.debug(f"Capture completed in {elapsed_time:.2f} ms using DXcam (attempt {retry+1})")
                
                # DXcam returns BGR format despite output_color="RGB" setting, so convert it
                # This ensures colors are displayed correctly (yellow as yellow, not blue)
                if frame.shape[2] >= 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                return frame
                
            except Exception as e:
                last_error = e
                logger.warning(f"Capture attempt {retry+1}/{self.__max_retries} failed: {str(e)}")
                if retry < self.__max_retries - 1:
                    # Wait longer between retries with exponential backoff
                    time.sleep(0.05 * (2 ** retry))
        
        # If we got here, all capture attempts failed
        logger.error(f"All {self.__max_retries} capture attempts failed")
        if last_error:
            raise last_error
        else:
            raise RuntimeError("Failed to capture screenshot after multiple attempts")

    def get_last_capture_time(self) -> float:
        """
        Returns the time in milliseconds that the last capture operation took.
        
        Returns:
            float: Time in milliseconds of the last capture operation.
            
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            print(f"Capture took {capture.get_last_capture_time():.2f} ms")
            ```
        """
        return self.__last_capture_time
    
    def get_screen_size(self) -> Tuple[int, int]:
        """
        Returns the primary screen resolution.
        
        Returns:
            Tuple[int, int]: Width and height of the primary screen.
        """
        return (self.__screen_width, self.__screen_height)
    
    @staticmethod
    def get_window_handle_by_title(title: str) -> int:
        """
        Gets a window handle by its title.
        
        Args:
            title (str): The window title to search for.
            
        Returns:
            int: Window handle if found, 0 otherwise.
            
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("Notepad")
            if hwnd:
                img = capture.capture(hwnd)
            ```
        """
        return user32.FindWindowW(None, title)
    
    @staticmethod
    def list_windows() -> List[Dict[str, Any]]:
        """
        Lists all visible windows in the system.
        
        Returns:
            List[Dict[str, Any]]: List of dictionaries containing window information.
            
        Example:
            ```python
            windows = WindowsGraphicsCapture.list_windows()
            for window in windows:
                print(f"Window: {window['title']} (Handle: {window['hwnd']})")
            ```
        """
        windows = []
        
        def enum_windows_proc(hwnd, lparam):
            if user32.IsWindowVisible(hwnd):
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    rect = RECT()
                    user32.GetWindowRect(hwnd, byref(rect))
                    
                    windows.append({
                        "hwnd": hwnd,
                        "title": buffer.value,
                        "rect": (rect.left, rect.top, rect.right, rect.bottom),
                        "width": rect.right - rect.left,
                        "height": rect.bottom - rect.top
                    })
            return True
        
        enum_windows_proc_type = WINFUNCTYPE(c_bool, c_int, c_int)
        enum_windows_proc_callback = enum_windows_proc_type(enum_windows_proc)
        user32.EnumWindows(enum_windows_proc_callback, 0)
        
        return windows
    
    def release(self) -> None:
        """
        Releases resources used by the capture instance.
        
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            capture.release()
            ```
        """
        if self.__dxcam_instance is not None:
            if hasattr(self.__dxcam_instance, 'release'):
                self.__dxcam_instance.release()
            
            self.__dxcam_instance = None
            
        logger.info("Resources released")
