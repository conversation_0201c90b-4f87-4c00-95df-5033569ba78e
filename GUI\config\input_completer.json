{"test_input_1": ["1213213", "测试项目1", "测试项目2", "另一个测试项目", "Python编程", "PyQt5开发", "自动补全功能"], "test_input_2": ["1232131231231", "第二个输入框的历史1", "第二个输入框的历史2", "独立的历史记录", "不同的补全项"], "detailed_test_input": ["新的测试文本", "测试项目1", "测试项目2", "测试项目3", "Python编程", "PyQt5开发"], "line_edit_2133961201056": ["测试项目1", "测试项目2", "测试项目3"], "line_edit_2133891324016": ["JetBrains Mono 字体测试", "Python Programming 编程", "微软雅黑 Microsoft YaHei", "Code Editor 代码编辑器", "Font Test 字体测试", "中英文混合显示效果", "InputCompleter 自动补全", "UI Components 界面组件"], "line_edit_1404357024720": ["Python Programming 编程", "InputCompleter 自动补全功能", "Font Test 字体测试", "JetBrains Mono 字体", "微软雅黑 Microsoft YaHei", "Consolas 控制台字体", "SimHei 黑体", "Courier New 新宋体", "中英文混合显示效果测试", "UI Components 界面组件开发"]}