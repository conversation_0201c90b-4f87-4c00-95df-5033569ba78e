"""
PostgreSQL SQL条件解析模块

提供SQL条件解析功能，将Python风格的条件表达式转换为SQL WHERE子句。
支持各种操作符和函数，如比较、逻辑、模式匹配等。
"""

import re
import logging
import traceback
from typing import Any, Dict, List, Optional, Tuple, Union, Set

class SQLConditionParser:
    """
    SQL条件解析器，将Python风格的条件表达式转换为SQL WHERE子句
    """
    
    def __init__(self):
        """初始化解析器"""
        self.tokens = []
        self.current_token_index = 0
        self.logger = logging.getLogger("SQLConditionParser")
        
    def parse(self, condition: str) -> str:
        """
        解析条件表达式，转换为SQL WHERE子句
        
        Args:
            condition: Python风格的条件表达式
            
        Returns:
            SQL WHERE子句
        """
        try:
            self.tokens = self.tokenize(condition)
            self.current_token_index = 0
            ast = self.parse_expression()
            if self.current_token_index != len(self.tokens):
                # Check if the remaining tokens are just a trailing semicolon, which can be ignored
                remaining_tokens = self.tokens[self.current_token_index:]
                if not (len(remaining_tokens) == 1 and remaining_tokens[0] == ';'):
                    self.logger.error(f"解析错误: 未预期的token '{remaining_tokens}' 在位置 {self.current_token_index}")
                    raise ValueError(f"解析错误: 未预期的token '{remaining_tokens}' 在位置 {self.current_token_index}")
            return self.generate_sql(ast)
        except Exception as e:
            self.logger.error(f"解析条件表达式失败: {str(e)}")
            self.logger.error(f"条件表达式: {condition}")
            self.logger.error(f"词法单元: {self.tokens}")
            self.logger.error(f"当前位置: {self.current_token_index}")
            traceback.print_exc()  # 打印详细异常堆栈
            raise ValueError(f"解析条件表达式失败: {str(e)}")

    def tokenize(self, condition: str) -> list:
        """
        词法分析，将条件字符串转换为词法单元列表，增强对字符串、转义、分号、反斜杠等边缘情况的处理。
        支持：
            - 单引号/双引号字符串，内部可包含转义（如 it''s, C:\\folder\\file.txt）
            - 忽略末尾分号
            - 空格、括号、逗号、操作符等分割
        """
        tokens = []
        i = 0
        n = len(condition)
        while i < n:
            c = condition[i]
            # 跳过空白
            if c.isspace():
                i += 1
                continue
            # 跳过分号
            if c == ';':
                i += 1
                continue
            # 优先识别多字符操作符
            if condition[i:i+3] == '->>':
                tokens.append('->>')
                i += 3
                continue
            if condition[i:i+2] == '->':
                tokens.append('->')
                i += 2
                continue
            if condition[i:i+2] == '::':
                tokens.append('::')
                i += 2
                continue
            # 字符串字面量（单引号，支持SQL标准转义：两个单引号表示一个单引号）
            if c == "'":
                val = "'"
                i += 1
                while i < n:
                    if condition[i] == "'":
                        # 判断是否为转义单引号（两个单引号，SQL标准）
                        if i + 1 < n and condition[i+1] == "'":
                            val += "''"
                            i += 2
                            continue
                        else:
                            val += "'"
                            i += 1
                            break
                    else:
                        val += condition[i]
                        i += 1
                tokens.append(val)
                continue
            # 字符串字面量（双引号）
            if c == '"':
                val = '"'
                i += 1
                while i < n:
                    if condition[i] == '"':
                        # 判断是否为转义双引号（两个双引号）
                        if i + 1 < n and condition[i+1] == '"':
                            val += '""'
                            i += 2
                            continue
                        else:
                            val += '"'
                            i += 1
                            break
                    else:
                        val += condition[i]
                        i += 1
                tokens.append(val)
                continue
            # 括号、逗号、操作符
            if c in '(),':
                tokens.append(c)
                i += 1
                continue
            # 多字符操作符
            if condition[i:i+2] in ('==', '!=', '>=', '<='):
                tokens.append(condition[i:i+2])
                i += 2
                continue
            if c in '><':
                tokens.append(c)
                i += 1
                continue
            # 关键字、标识符、数字
            if c.isalpha() or c == '_':
                start = i
                while i < n and (condition[i].isalnum() or condition[i] == '_'):
                    i += 1
                word = condition[start:i]
                # 关键字小写
                if word.lower() in ('and','or','not','between','is','null','in','like','ilike','regexp','exists'):
                    tokens.append(word.lower())
                else:
                    tokens.append(word)
                continue
            # 数字（整数或浮点）
            if c.isdigit():
                start = i
                has_dot = False
                while i < n and (condition[i].isdigit() or (condition[i] == '.' and not has_dot)):
                    if condition[i] == '.':
                        has_dot = True
                    i += 1
                tokens.append(condition[start:i])
                continue
            # 其它字符（如%等非法操作符，直接作为token，后续语法检查会报错）
            tokens.append(c)
            i += 1
        self.logger.debug(f"词法分析结果: {tokens}")
        return tokens

    def parse_expression(self):
        # expression ::= factor (("and" | "or") factor)*
        left = self.parse_factor()
        while self.current_token() in ("and", "or"):
            operator = self.current_token() # 获取操作符
            self.advance()                  # 消耗操作符
            right = self.parse_factor()
            left = (left, operator, right)
        return left

    def parse_factor(self):
        # factor ::= "not" factor | comparison
        current = self.current_token()
        if current == "not":
            operator = self.current_token() # 获取 "not"
            self.advance()                  # 消耗 "not"
            # "not" applies to the result of the subsequent factor (which will resolve to a comparison or primary)
            return (operator, self.parse_factor()) # 使用获取到的 "not"
        else:
            return self.parse_comparison()

    def parse_comparison(self):
        # comparison ::= primary (comp_operator primary | "is" "null" | "is" "not" "null" | "between" primary "and" primary | "in" "(" primary ("," primary)* ")")?
        left = self.parse_primary()
        
        current = self.current_token()
        comparison_ops_standard = ("==", "!=", ">=", "<=", ">", "<", "like", "ilike", "regexp")

        if current == "is":
            is_op = self.current_token() # 获取 "is"
            self.advance()               # 消耗 "is"
            if self.current_token() == "not":
                not_op = self.current_token() # 获取 "not"
                self.advance()                # 消耗 "not"
                # 处理is not null、is not true、is not false
                if self.current_token() in ("null", "true", "false"):
                    value = self.current_token()
                    self.advance()
                    return (left, is_op, (not_op, value)) # 使用获取到的 "is" 和 "not"
                else:
                    self.logger.error(f"解析错误: 'is not' 后期望 'null'、'true' 或 'false'，但得到 '{self.current_token()}'")
                    raise ValueError(f"解析错误: 'is not' 后期望 'null'、'true' 或 'false'，但得到 '{self.current_token()}'")
            else:
                # 处理is null、is true、is false
                if self.current_token() in ("null", "true", "false"):
                    value = self.current_token()
                    self.advance()
                    return (left, is_op, value) # 使用获取到的 "is"
                else:
                    self.logger.error(f"解析错误: 'is' 后期望 'null'、'true' 或 'false'，但得到 '{self.current_token()}'")
                    raise ValueError(f"解析错误: 'is' 后期望 'null'、'true' 或 'false'，但得到 '{self.current_token()}'")
        elif current in comparison_ops_standard:
            operator = self.current_token() # 获取操作符
            self.advance()                  # 消耗操作符
            right = self.parse_primary()
            return (left, operator, right) # 使用获取到的操作符
        elif current == "between":
            operator = self.current_token() # 获取 "between"
            self.advance()                  # 消耗 "between"
            low = self.parse_primary()
            self.expect_token("and")
            high = self.parse_primary()
            return (left, operator, low, high) # 使用获取到的 "between"
        elif current == "in":
            operator = self.current_token() # 获取 "in"
            self.advance()                  # 消耗 "in"
            self.expect_token("(")
            values = []
            if self.current_token() != ")":
                values.append(self.parse_primary())
                while self.current_token() == ",":
                    self.advance()
                    values.append(self.parse_primary())
            self.expect_token(")")
            return (left, operator, tuple(values))
        
        return left # If no operator follows, it's just a primary

    def parse_primary(self):
        # primary ::= IDENTIFIER | STRING_LITERAL | NUMBER_LITERAL | "(" expression ")" | "null"
        current = self.current_token()
        if current is None:
            self.logger.error("解析错误: 未预期的输入结束")
            raise ValueError("解析错误: 未预期的输入结束")

        # 1. 先解析一个基本primary
        if current == "(":
            self.advance() # consume "("
            expr = self.parse_expression()
            self.expect_token(")") # consume ")"
            node = expr
        elif current == "null":
            null_val = self.current_token() # 获取 "null"
            self.advance()                  # 消耗 "null"
            node = null_val # Represent SQL NULL as a string "null" in AST for simplicity
        elif (current.startswith("'") and current.endswith("'")) or \
             (current.startswith('"') and current.endswith('"')):
            token_val = self.current_token() # 获取 token
            self.advance()                   # 消耗 token
            node = token_val
        elif re.fullmatch(r"-?\d+\.?\d*", current) or \
             re.fullmatch(r"\b[a-zA-Z_][a-zA-Z0-9_]*", current): # String, Number, or Identifier
            token_val = self.current_token() # 获取 token
            self.advance()                   # 消耗 token
            node = token_val
        else:
            self.logger.error(f"解析错误: 未预期的token '{current}' (在parse_primary中)")
            raise ValueError(f"解析错误: 未预期的token '{current}'")

        # 2. 递归处理 JSONB 操作符链 ->, ->>, ::type
        while True:
            next_token = self.current_token()
            if next_token == '->':
                self.advance()
                right = self.current_token()
                if right is None:
                    self.logger.error("解析错误: '->' 后缺少字段名")
                    raise ValueError("解析错误: '->' 后缺少字段名")
                self.advance()
                node = ('->', node, right)
            elif next_token == '->>':
                self.advance()
                right = self.current_token()
                if right is None:
                    self.logger.error("解析错误: '->>' 后缺少字段名或下标")
                    raise ValueError("解析错误: '->>' 后缺少字段名或下标")
                self.advance()
                node = ('->>', node, right)
            elif next_token == '::':
                self.advance()
                type_token = self.current_token()
                if type_token is None:
                    self.logger.error("解析错误: '::' 后缺少类型名")
                    raise ValueError("解析错误: '::' 后缺少类型名")
                self.advance()
                node = ('::', node, type_token)
            else:
                break
        return node

    def is_identifier(self, token: str) -> bool:
        """
        判断词法单元是否为标识符
        
        Args:
            token: 词法单元
            
        Returns:
            是否为标识符
        """
        return re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", token) is not None and token.lower() not in (
            "and", "or", "not", "between", "is", "null", "in", "like", "ilike", "regexp", "exists")

    def is_string(self, token: str) -> bool:
        """
        判断词法单元是否为字符串
        
        Args:
            token: 词法单元
            
        Returns:
            是否为字符串
        """
        return (token.startswith("'") and token.endswith("'")) or (token.startswith('"') and token.endswith('"'))

    def is_number(self, token: str) -> bool:
        """判断一个token是否为数字"""
        try:
            float(token)
            return True
        except ValueError:
            traceback.print_exc()  # 打印详细异常堆栈
            return False

    def current_token(self) -> Optional[str]:
        """
        获取当前词法单元
        
        Returns:
            当前词法单元，如果已经到达末尾则返回None
        """
        if 0 <= self.current_token_index < len(self.tokens):
            return self.tokens[self.current_token_index]
        else:
            return None

    def advance(self) -> None:
        """前进到下一个词法单元"""
        self.current_token_index += 1

    def expect_token(self, expected_token: str) -> None:
        """
        期望当前词法单元是指定值
        
        Args:
            expected_token: 期望的词法单元值
            
        Raises:
            ValueError: 如果当前词法单元不符合期望
        """
        current = self.current_token()
        if current is not None and current == expected_token:
            self.advance()
        else:
            self.logger.error(f"解析错误: 期望token '{expected_token}' 但得到 '{current}'")
            raise ValueError(f"解析错误: 期望token '{expected_token}' 但得到 '{current}'")

    def generate_sql(self, ast: Any) -> str:
        if isinstance(ast, str):
            # 处理标识符、字符串字面量、数字（如果词法分析器返回它们为字符串）和 "null"
            if ast.lower() == "null":
                return "NULL"
            # 处理布尔值字面量
            elif ast.lower() == "true":
                return "TRUE"
            elif ast.lower() == "false":
                return "FALSE"
            # 字符串字面量处理
            if ast.startswith("'") and ast.endswith("'"):
                content = ast[1:-1]  # 剥离外部单引号
                content = content.replace("\\\\", "\\ ").replace("\\'", "''").replace("\\ ", "\\\\") # 中间步骤避免 \\\' -> \'
                return f"'{content}'"
            elif ast.startswith('"') and ast.endswith('"'):
                content = ast[1:-1]  # 剥离外部双引号
                content = content.replace("\\\\", "\\ ").replace("\\\"", '"').replace("\\ ", "\\\\")
                content = content.replace("'", "''")
                return f"'{content}'"
            if ast.replace('.', '', 1).isdigit() or (ast.startswith('-') and ast.replace('.', '', 1).lstrip('-').isdigit()):
                return ast
            return f'"{ast}"' # PostgreSQL中标准做法是对标识符加双引号以保留大小写或特殊字符

        elif isinstance(ast, tuple):
            # JSONB 操作符链递归处理
            if len(ast) == 3 and ast[0] in ('->', '->>'):
                left_sql = self.generate_sql(ast[1])
                right_sql = self.generate_sql(ast[2])
                return f"({left_sql} {ast[0]} {right_sql})"
            if len(ast) == 3 and ast[0] == '::':
                left_sql = self.generate_sql(ast[1])
                type_sql = ast[2]
                return f"({left_sql})::{type_sql}"
            # ("not", operand)
            if len(ast) == 2 and ast[0] == "not":
                return f"(NOT {self.generate_sql(ast[1])})"
            # (left, operator, right) 或 (left, operator, complex_right for IS [NOT] NULL)
            elif len(ast) == 3:
                left_sql = self.generate_sql(ast[0])
                operator_token = ast[1]
                if not isinstance(operator_token, str):
                    self.logger.error(f"generate_sql: 操作符令牌必须是字符串，但得到: {type(operator_token)} in AST: {ast}")
                    raise ValueError(f"内部错误: AST操作符令牌不是字符串: {operator_token}")
                if operator_token.lower() == "is":
                    if ast[2] == "null":
                        return f"({left_sql} IS NULL)"
                    elif ast[2] == "true":
                        return f"({left_sql} IS TRUE)"
                    elif ast[2] == "false":
                        return f"({left_sql} IS FALSE)"
                    elif isinstance(ast[2], tuple) and len(ast[2]) == 2 and ast[2][0] == "not":
                        if ast[2][1] == "null":
                            return f"({left_sql} IS NOT NULL)"
                        elif ast[2][1] == "true":
                            return f"({left_sql} IS NOT TRUE)"
                        elif ast[2][1] == "false":
                            return f"({left_sql} IS NOT FALSE)"
                        else:
                            self.logger.error(f"generate_sql: 'is not' 操作符后跟了非预期的值: {ast[2][1]} in AST: {ast}")
                            raise ValueError("内部错误: 'is not' 操作符结构错误")
                    else:
                        self.logger.error(f"generate_sql: 'is' 操作符后跟了非预期的结构: {ast[2]} in AST: {ast}")
                        raise ValueError("内部错误: 'is' 操作符结构错误")
                op_sql = self.map_operator(operator_token)
                if operator_token.lower() == "in":
                    if isinstance(ast[2], tuple):
                        if not ast[2]:
                            self.logger.warning(f"generate_sql: IN 操作符的列表为空: {ast}")
                            return "(1=0)"
                        values_sql = ", ".join([self.generate_sql(v) for v in ast[2]])
                        return f"({left_sql} {op_sql} ({values_sql}))"
                    else:
                        self.logger.error(f"generate_sql: IN 操作符的右侧期待元组，但得到: {type(ast[2])} in AST: {ast}")
                        raise ValueError("内部错误: IN 操作符结构错误")
                else:
                    right_sql = self.generate_sql(ast[2])
                    return f"({left_sql} {op_sql} {right_sql})"
            # BETWEEN 操作符, AST: (ident, "between", low, high)
            elif len(ast) == 4 and isinstance(ast[1], str) and ast[1].lower() == "between":
                left_sql = self.generate_sql(ast[0])
                op_sql = self.map_operator(ast[1]) # "BETWEEN"
                low_sql = self.generate_sql(ast[2])
                high_sql = self.generate_sql(ast[3])
                return f"({left_sql} {op_sql} {low_sql} AND {high_sql})"
            else:
                self.logger.error(f"generate_sql: 无法处理的AST元组结构 (长度 {len(ast)}): {ast}")
                raise ValueError(f"内部错误: 无法处理的AST元组结构")
        elif isinstance(ast, (int, float)):
            return str(ast)
        else:
            self.logger.error(f"generate_sql: 未知的AST节点顶层类型: {type(ast)}, value: {ast}")
            raise ValueError(f"内部错误: 未知的AST节点类型 {type(ast)}")

    def map_operator(self, operator: str) -> str:
        # 确保 operator 是字符串，这应该由调用者 (generate_sql) 保证
        op_lower = operator.lower() # 现在可以安全调用 .lower()
        mapping = {
            "==": "=", 
            "!=": "!=", 
            ">": ">", 
            "<": "<", 
            ">=": ">=", 
            "<=": "<=",
            "and": "AND", 
            "or": "OR", 
            # "not" 由 generate_sql 中的 ("not", expr) 结构处理，不通过 map_operator
            "is": "IS", # "is null", "is not null" 由 generate_sql 特殊处理其右操作数
            "like": "ILIKE", # 修复点：所有LIKE自动转为ILIKE
            "ilike": "ILIKE", # PostgreSQL specific
            "regexp": "~",    # PostgreSQL REGEXP operator is ~ or ~* for case-insensitive
            "between": "BETWEEN", # "between" 由 generate_sql 特殊处理其结构
            "in": "IN"            # "in" 由 generate_sql 特殊处理其结构
        }
        # 如果操作符不在映射中，假设它是一个有效的SQL操作符并返回其大写形式
        # 或者，可以更严格，只允许已知的操作符
        return mapping.get(op_lower, op_lower.upper())


# 创建全局解析器实例
parser = SQLConditionParser()

# 导出便捷函数
def parse_condition(condition: str) -> str:
    """
    解析条件表达式，转换为SQL WHERE子句
    
    Args:
        condition: Python风格的条件表达式
        
    Returns:
        SQL WHERE子句
    """
    return parser.parse(condition) 