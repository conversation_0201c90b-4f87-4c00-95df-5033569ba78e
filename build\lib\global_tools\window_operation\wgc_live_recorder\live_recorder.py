import ctypes
import os
import time
import sys
from tkinter import N, NO
import numpy as np
from numpy.lib.stride_tricks import as_strided
import cv2
import uuid
import traceback
import random
import threading

# ===================================================================================
# WGC.dll 的 Python ctypes 定义
# ===================================================================================

# --- 基本类型和枚举 ---

c_bool = ctypes.c_bool
HWND = ctypes.c_void_p
HMONITOR = ctypes.c_void_p
LiveRecorderHandle = ctypes.c_void_p


class WGCEncoderQuality(ctypes.c_int):
    """ 编码质量枚举 """
    LOW = 0
    MEDIUM = 1
    HIGH = 2

# --- 结构体定义 ---


class FrameData_C(ctypes.Structure):
    """ C 语言 FrameData_C 结构体的 ctypes 映射 """
    _fields_ = [
        ("pixels", ctypes.POINTER(ctypes.c_uint8)),
        ("width", ctypes.c_uint),
        ("height", ctypes.c_uint),
        ("stride", ctypes.c_uint),
    ]


class LiveRecorderOptions_C(ctypes.Structure):
    """ C 语言 LiveRecorderOptions_C 结构体的 ctypes 映射 """
    _fields_ = [
        ("targetWindow", HWND),
        ("monitorIndex", ctypes.c_int),
        ("outputFile", ctypes.c_wchar_p),
        ("frameRate", ctypes.c_uint),
        ("bitrate", ctypes.c_uint),
        ("quality", WGCEncoderQuality),
        ("recordDurationMs", ctypes.c_longlong),
        ("includeCursor", c_bool),
        ("useHardwareEncoding", c_bool),
    ]

# --- 回调函数原型 ---


# 定义将从C++调用的Python回调函数原型
# 它必须返回一个布尔值 (在C中表现为int的0或1), 告诉录制器是否继续
PyFrameCallback = ctypes.CFUNCTYPE(c_bool, ctypes.POINTER(FrameData_C), ctypes.c_void_p)


# ===================================================================================
# DLL 加载器和 API 封装类
# ===================================================================================

class WGC_API:
    """ WGC.dll 的 Python 封装类 """

    def __init__(self, dll_path):
        try:
            self.dll = ctypes.CDLL(dll_path)
        except OSError as e:
            print(f"致命错误: 从路径加载 WGC.dll 失败: {dll_path}")
            print(f"错误详情: {e}")
            print("\n请确保您已成功运行 build.bat 脚本。")
            sys.exit(1)

        self._define_api_signatures()

    def _define_api_signatures(self):
        """ 设置所有DLL函数的参数类型和返回类型 """
        # --- 全局函数 ---
        self.dll.WGC_Initialize.restype = c_bool
        self.dll.WGC_Cleanup.restype = None
        self.dll.WGC_GetLastError.restype = ctypes.c_char_p
        self.dll.WGC_GetVersion.restype = ctypes.c_char_p

        # --- LiveRecorder 函数 ---
        self.dll.WGC_LiveRecorder_Create.argtypes = [ctypes.POINTER(LiveRecorderOptions_C)]
        self.dll.WGC_LiveRecorder_Create.restype = LiveRecorderHandle

        self.dll.WGC_LiveRecorder_Destroy.argtypes = [LiveRecorderHandle]
        self.dll.WGC_LiveRecorder_Destroy.restype = None

        self.dll.WGC_LiveRecorder_Start.argtypes = [LiveRecorderHandle]
        self.dll.WGC_LiveRecorder_Start.restype = c_bool

        self.dll.WGC_LiveRecorder_Stop.argtypes = [LiveRecorderHandle]
        self.dll.WGC_LiveRecorder_Stop.restype = None

        self.dll.WGC_LiveRecorder_IsRecording.argtypes = [LiveRecorderHandle]
        self.dll.WGC_LiveRecorder_IsRecording.restype = c_bool

        self.dll.WGC_LiveRecorder_SetFrameCallback.argtypes = [LiveRecorderHandle, PyFrameCallback, ctypes.c_void_p]
        self.dll.WGC_LiveRecorder_SetFrameCallback.restype = None

        self.dll.WGC_LiveRecorder_GetLastError.argtypes = [LiveRecorderHandle]
        self.dll.WGC_LiveRecorder_GetLastError.restype = ctypes.c_char_p

# ===================================================================================
# Python 高级封装类
# ===================================================================================


class LiveRecorder:
    """
    一个高级Python封装器，用于WGC.dll的LiveRecorder功能。
    它通过上下文管理器（'with'语句）自动处理资源的初始化、创建、销毁和清理，
    并提供一个简单易用的接口来开始、停止和监控录制。
    """

    # 使用类级别变量来确保整个Python进程中只初始化/清理一次WGC库
    _wgc_api = None
    _is_lib_initialized = False
    _library_version = "N/A"

    def __init__(self, dll_path, **kwargs):
        """
        构造函数。加载DLL(如果尚未加载)，并根据提供的选项创建一个LiveRecorder实例。

        Args:
            dll_path (str): WGC.dll的绝对或相对路径。
            **kwargs: 录制器选项，直接映射到C++的LiveRecorderOptions_C结构体。
                - targetWindow (int): 目标窗口句柄。如果提供，则优先捕获窗口。
                - monitorIndex (int): 目标显示器索引(-1为主显示器)。仅当targetWindow未提供时生效。
                - outputFile (str): 输出的.mp4文件路径。如果为None，则不保存视频。
                - frameRate (int): 帧率 (默认30)。
                - bitrate (int): 比特率 (默认8,000,000 bps)。
                - quality (WGCEncoderQuality): 编码质量 (默认 WGCEncoderQuality.HIGH)。
                - recordDurationMs (int): 录制时长(毫秒, 0为无限长)。
                - includeCursor (bool): 是否包含光标 (默认True)。
                - useHardwareEncoding (bool): 是否使用硬件编码 (默认True)。

        Raises:
            FileNotFoundError: 如果找不到指定的dll_path。
            RuntimeError: 如果WGC库初始化失败或创建录制器实例失败。
        """
        # --- 步骤 1: 单次初始化WGC库 ---
        if not LiveRecorder._is_lib_initialized:
            if not os.path.exists(dll_path):
                raise FileNotFoundError(f"在 '{dll_path}' 未找到 DLL。请先运行 build.bat 脚本来编译 DLL。")

            print(f"首次加载: 正在从以下路径加载 DLL: {dll_path}")
            LiveRecorder._wgc_api = WGC_API(dll_path)

            if not LiveRecorder._wgc_api.dll.WGC_Initialize():
                last_error = LiveRecorder._wgc_api.dll.WGC_GetLastError().decode('utf-8', 'ignore')
                raise RuntimeError(f"WGC_Initialize 失败: {last_error}")

            LiveRecorder._library_version = LiveRecorder._wgc_api.dll.WGC_GetVersion().decode('utf-8')
            print(f"WGC 库已初始化。版本号: {LiveRecorder._library_version}")
            LiveRecorder._is_lib_initialized = True

        # 将对API实例的引用存储在实例变量中
        self.wgc = LiveRecorder._wgc_api
        assert self.wgc is not None, "内部错误: WGC API 对象不应为None"

        self.handle = None
        self._internal_callback_ref = None  # 持有对内部ctypes回调的引用
        self.__user_callback = None         # 持有用户提供的Python回调
        self.__crop_region = None           # 持有(x, y, w, h)的裁剪区域元组

        # --- 步骤 2: 配置并创建LiveRecorder实例 ---
        print("\n正在配置 LiveRecorder...")
        options = LiveRecorderOptions_C(
            targetWindow=kwargs.get("targetWindow", None),
            monitorIndex=kwargs.get("monitorIndex", -1),
            outputFile=kwargs.get("outputFile", None),
            frameRate=kwargs.get("frameRate", 30),
            bitrate=kwargs.get("bitrate", 8000000),
            quality=kwargs.get("quality", WGCEncoderQuality.HIGH),
            recordDurationMs=kwargs.get("recordDurationMs", 0),
            includeCursor=kwargs.get("includeCursor", True),
            useHardwareEncoding=kwargs.get("useHardwareEncoding", True)
        )

        self.handle = self.wgc.dll.WGC_LiveRecorder_Create(ctypes.byref(options))
        if not self.handle:
            last_error = self.get_last_error()
            raise RuntimeError(f"创建 LiveRecorder 失败: {last_error}")

        print("LiveRecorder 实例已创建。")

    def _internal_frame_callback(self, frame_data_ptr, user_data):
        """
        [内部回调] 这个函数由C++的后台线程直接调用。
        它负责：
        1. 将C指针数据转换为OpenCV图像。
        2. 如果设置了裁剪区域，则进行裁剪。
        3. 调用用户提供的回调函数。
        4. 将用户回调的返回值（True/False）传递回去，以控制录制流程。
        """
        try:
            # 步骤1: 将原始帧数据转换为OpenCV图像 (BGR格式)
            frame = frame_data_ptr.contents
            np_bgra_frame = as_strided(
                np.ctypeslib.as_array(frame.pixels, shape=(frame.height * frame.stride,)),
                shape=(frame.height, frame.width, 4),
                strides=(frame.stride, 4, 1)
            )
            processed_frame = cv2.cvtColor(np_bgra_frame, cv2.COLOR_BGRA2BGR)

            # 步骤2: 如果设置了裁剪区域，则应用裁剪
            if self.__crop_region:
                x, y, w, h = self.__crop_region
                # 检查裁剪尺寸的有效性，防止越界
                if x >= 0 and y >= 0 and w > 0 and h > 0 and x + w <= processed_frame.shape[1] and y + h <= processed_frame.shape[0]:
                    processed_frame = processed_frame[y:y + h, x:x + w]
                else:
                    # 如果裁剪区域无效，可以打印警告并使用原始帧
                    print(f"警告: 无效的裁剪区域 {self.__crop_region}，将忽略本次裁剪。")

            # 步骤3: 调用用户注册的回调函数
            if self.__user_callback:
                # 将处理后的（可能已裁剪的）OpenCV图像传递给用户
                return self.__user_callback(processed_frame)

            # 如果没有设置用户回调，默认继续录制
            return True

        except Exception as e:
            traceback.print_exc()
            # 当回调中发生任何异常时，安全起见，返回False以停止捕re cording
            return False

    def set_crop_region(self, x1, y1, x2, y2):
        """
        设置一个矩形区域，用于裁剪后续的每一帧视频。
        使用左上角和右下角坐标 (x1, y1, x2, y2)。
        应在调用 start() 之前设置此项。

        Args:
            x1 (int): 裁剪区域左上角的X坐标。
            y1 (int): 裁剪区域左上角的Y坐标。
            x2 (int): 裁剪区域右下角的X坐标。
            y2 (int): 裁剪区域右下角的Y坐标。

        Raises:
            ValueError: 如果提供的参数不是非负整数，或者坐标无效 (例如 x1 >= x2)。
        """
        if not all(isinstance(i, int) and i >= 0 for i in [x1, y1, x2, y2]):
            raise ValueError("裁剪区域的所有坐标参数必须是非负整数。")
        if x1 >= x2 or y1 >= y2:
            raise ValueError(f"无效的裁剪坐标：右下角 ({x2},{y2}) 必须大于左上角 ({x1},{y1})。")

        width = x2 - x1
        height = y2 - y1
        self.__crop_region = (x1, y1, width, height)
        print(f"裁剪区域已设置为: (x={x1}, y={y1}, w={width}, h={height}) from rect ({x1},{y1},{x2},{y2})")

    def set_frame_handler(self, callback):
        """
        注册一个回调函数，用于处理经过内部转换和裁剪后的OpenCV视频帧。

        Args:
            callback (function): 用户提供的回调函数。
                该函数应接收一个参数（OpenCV的numpy.ndarray图像），
                并返回一个布尔值（True表示继续，False表示停止）。

        Raises:
            TypeError: 如果提供的参数不是一个可调用对象。
        """
        if not callable(callback):
            raise TypeError("提供的回调必须是一个可调用函数。")
        self.__user_callback = callback
        print("用户帧处理回调已设置。")

    def start(self):
        """
        开始录制。
        在调用此方法之前，应通过 set_frame_handler() 设置回调。

        Raises:
            RuntimeError: 如果启动录制失败或未设置帧处理回调。
        """
        if not self.__user_callback:
            raise RuntimeError("启动失败：请先通过 set_frame_handler() 设置帧处理回调函数。")

        if not self.wgc:
            raise RuntimeError("WGC API尚未初始化。")

        # 将内部回调方法转换为C函数指针，并存储其引用以防被垃圾回收
        self._internal_callback_ref = PyFrameCallback(self._internal_frame_callback)
        self.wgc.dll.WGC_LiveRecorder_SetFrameCallback(self.handle, self._internal_callback_ref, None)
        print("内部帧回调函数已设置。")

        print("\n开始录制...")
        if not self.wgc.dll.WGC_LiveRecorder_Start(self.handle):
            last_error = self.get_last_error()
            raise RuntimeError(f"开始录制失败: {last_error}")

    def stop(self):
        """立即请求停止录制。这是一个异步操作。"""
        if self.handle and self.wgc:
            self.wgc.dll.WGC_LiveRecorder_Stop(self.handle)

    def is_recording(self):
        """
        检查C++后端当前是否处于录制状态。

        Returns:
            bool: 如果正在录制则返回True，否则返回False。
        """
        if self.handle and self.wgc:
            return self.wgc.dll.WGC_LiveRecorder_IsRecording(self.handle)
        return False

    def get_last_error(self):
        """
        获取与此录制器实例相关的最后一个错误信息。

        Returns:
            str: 错误信息字符串。
        """
        # LiveRecorder的错误函数需要传递句柄
        if self.wgc:
            error_bytes = self.wgc.dll.WGC_LiveRecorder_GetLastError(self.handle)
            return error_bytes.decode('utf-8', 'ignore')
        return "WGC API尚未初始化。"

    def destroy(self):
        """
        销毁C++中的录制器实例并释放相关资源。
        通常通过'with'语句自动调用。
        """
        if self.handle and self.wgc:
            print("正在销毁 LiveRecorder 实例...")
            self.wgc.dll.WGC_LiveRecorder_Destroy(self.handle)
            self.handle = None

    @classmethod
    def cleanup_library(cls):
        """
        清理整个WGC库的全局资源。应在程序退出前调用。
        """
        if cls._is_lib_initialized and cls._wgc_api:
            print("正在清理 WGC 库...")
            cls._wgc_api.dll.WGC_Cleanup()
            cls._is_lib_initialized = False
            cls._wgc_api = None

    def __enter__(self):
        """上下文管理器入口点。"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出点，确保资源被销毁。"""
        self.destroy()

# ===================================================================================
# 使用示例
# ===================================================================================


if __name__ == "__main__":
    print("--- WGC LiveRecorder Python 测试脚本 (高级API版) ---")

    # --- 1. 定义路径和全局变量 ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    dll_path = os.path.abspath(os.path.join(script_dir, "Release", "WGC.dll"))

    frame_count = 0
    start_time = time.perf_counter()

    def process_frame_callback(opencv_frame):
        """
        用户级别的帧处理回调函数。
        这个函数接收的已经是处理好的OpenCV图像。
        """
        global frame_count, start_time
        frame_count += 1

        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        start_time = end_time

        print(f"Python 用户回调: 收到第 {frame_count} 帧 "
              f"| 尺寸: {opencv_frame.shape[1]}x{opencv_frame.shape[0]} "
              f"| 每帧处理耗时: {duration_ms:.2f} ms")
        # time.sleep(2)
        return True

    # --- 2. 创建并启动录制器 ---
    recorder = None
    try:
        recorder = LiveRecorder(
            targetWindow=264604,
            dll_path=dll_path,
            outputFile=None,
            frameRate=30,
            quality=WGCEncoderQuality.HIGH,
            includeCursor=True,
            recordDurationMs=0,
        )

        # --- 3. 配置录制器 ---
        recorder.set_crop_region(100, 100, 600, 600)  # x1, y1, x2, y2
        recorder.set_frame_handler(process_frame_callback)

        # --- 4. 开始录制 ---
        recorder.start()
        print("\n录制已开始。将自动处理100帧或按 Ctrl+C 停止...")

        # --- 5. 等待录制结束 ---
        # 主线程等待，直到is_recording变为False（由回调函数或用户中断触发）
        while recorder.is_recording():
            time.sleep(0.1)

    except (KeyboardInterrupt, SystemExit):
        print("\n\n检测到用户中断。正在停止录制...")
        if recorder:
            recorder.stop()
    except (FileNotFoundError, RuntimeError, TypeError, ValueError) as e:
        print(f"\n发生致命错误: {e}")
        traceback.print_exc()
    except Exception as e:
        print(f"\n发生未知异常: {e}")
        traceback.print_exc()
    finally:
        # --- 6. 最终清理 ---
        # 确保无论如何都销毁C++对象并清理库
        if recorder:
            recorder.destroy()
        LiveRecorder.cleanup_library()
        print("\n测试脚本已结束。")
