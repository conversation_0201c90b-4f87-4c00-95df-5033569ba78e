"""
PostgreSQL数据库类型转换模块

提供Python数据类型与PostgreSQL数据类型之间的双向转换功能，支持：
1. 基本数据类型转换（整数、浮点数、字符串、布尔值等）
2. 复杂数据类型转换（列表、字典、日期时间等）
3. 特殊类型处理（UUID、Decimal、二进制数据等）
4. 自定义类型注册和扩展

通过统一的类型转换接口，确保数据在Python应用和PostgreSQL数据库之间安全、准确地传输。
"""

import json
import datetime
import decimal
import uuid
import traceback
import base64
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, Set, Tuple

from .logger import get_logger

# 类型别名定义
T = TypeVar('T')
ConversionFunc = Callable[[Any], Any]

# 获取日志记录器
logger = get_logger("DBTypeConverter")


class DBTypeConverter:
    """
    数据库类型转换器，用于Python数据类型与PostgreSQL数据类型之间的双向转换
    """
    
    # 类型转换注册表
    _py_to_pg_converters = {}
    _pg_to_py_converters = {}
    
    def __init__(self):
        """初始化类型转换器"""
        # 注册默认转换器
        self._register_default_converters()
        
    def _register_default_converters(self):
        """注册默认的类型转换器"""
        # Python到PostgreSQL的转换器
        self.register_py_to_pg_converter(type(None), self._none_to_pg)
        self.register_py_to_pg_converter(bool, self._bool_to_pg)
        self.register_py_to_pg_converter(int, self._int_to_pg)
        self.register_py_to_pg_converter(float, self._float_to_pg)
        self.register_py_to_pg_converter(str, self._str_to_pg)
        self.register_py_to_pg_converter(bytes, self._bytes_to_pg)
        self.register_py_to_pg_converter(bytearray, self._bytes_to_pg)
        self.register_py_to_pg_converter(list, self._list_to_pg)
        self.register_py_to_pg_converter(tuple, self._list_to_pg)
        self.register_py_to_pg_converter(set, self._list_to_pg)
        self.register_py_to_pg_converter(dict, self._dict_to_pg)
        self.register_py_to_pg_converter(datetime.date, self._date_to_pg)
        self.register_py_to_pg_converter(datetime.datetime, self._datetime_to_pg)
        self.register_py_to_pg_converter(datetime.time, self._time_to_pg)
        self.register_py_to_pg_converter(decimal.Decimal, self._decimal_to_pg)
        self.register_py_to_pg_converter(uuid.UUID, self._uuid_to_pg)
        
        # PostgreSQL到Python的转换器
        # 这些将在后续版本中实现
        
    @classmethod
    def register_py_to_pg_converter(cls, python_type: type, converter_func: ConversionFunc) -> None:
        """
        注册Python到PostgreSQL的类型转换器
        
        Args:
            python_type: Python类型
            converter_func: 转换函数，接受Python值并返回PostgreSQL兼容值
        """
        cls._py_to_pg_converters[python_type] = converter_func
        
    @classmethod
    def register_pg_to_py_converter(cls, pg_type_name: str, converter_func: ConversionFunc) -> None:
        """
        注册PostgreSQL到Python的类型转换器
        
        Args:
            pg_type_name: PostgreSQL类型名称
            converter_func: 转换函数，接受PostgreSQL值并返回Python值
        """
        cls._pg_to_py_converters[pg_type_name] = converter_func
        
    @classmethod
    def py_to_pg(cls, value: Any, for_array: bool = False) -> Any:
        """
        将Python值转换为PostgreSQL兼容值
        
        Args:
            value: 要转换的Python值
            for_array: 是否用于PostgreSQL数组
            
        Returns:
            转换后的PostgreSQL兼容值
        """
        if value is None:
            return None
            
        # 查找直接匹配的转换器
        converter = cls._py_to_pg_converters.get(type(value))
        if converter:
            try:
                return converter(value, for_array=for_array)
            except Exception as e:
                logger.warning(f"使用注册的转换器转换 {type(value)} 失败: {str(e)}")
                traceback.print_exc()  # 打印详细异常堆栈
                
        # 查找继承类型的转换器
        for py_type, converter in cls._py_to_pg_converters.items():
            if isinstance(value, py_type):
                try:
                    return converter(value, for_array=for_array)
                except Exception as e:
                    logger.warning(f"使用继承类型转换器转换 {type(value)} 失败: {str(e)}")
                    traceback.print_exc()  # 打印详细异常堆栈
                    
        # 回退到字符串表示
        try:
            return str(value)
        except Exception as e:
            logger.warning(f"转换 {type(value)} 为字符串失败: {str(e)}")
            traceback.print_exc()  # 打印详细异常堆栈
            return None
            
    @classmethod
    def pg_to_py(cls, value: Any, pg_type: str = None) -> Any:
        """
        将PostgreSQL值转换为Python值
        
        Args:
            value: 要转换的PostgreSQL值
            pg_type: PostgreSQL类型名称
            
        Returns:
            转换后的Python值
        """
        # 这个方法将在后续版本中实现
        return value
        
    # 以下是各种类型的转换函数
    
    @staticmethod
    def _none_to_pg(value: None, **kwargs) -> None:
        """None值转换"""
        return None
        
    @staticmethod
    def _bool_to_pg(value: bool, **kwargs) -> bool:
        """布尔值转换"""
        return value
        
    @staticmethod
    def _int_to_pg(value: int, **kwargs) -> int:
        """整数转换"""
        return value
        
    @staticmethod
    def _float_to_pg(value: float, **kwargs) -> float:
        """浮点数转换"""
        return value
        
    @staticmethod
    def _str_to_pg(value: str, for_array: bool = False, **kwargs) -> str:
        """字符串转换
        for_array=True时，PostgreSQL数组字符串元素内部的单引号应写为两个单引号''，不加反斜杠。
        
        # 已删除所有 print('[DEBUG _str_to_pg] ...') 调试输出，防止类型转换过程产生多余日志。
        """
        if for_array:
            out = value.replace("'", "''")
            return out
        return value
        
    @staticmethod
    def _bytes_to_pg(value: Union[bytes, bytearray], **kwargs) -> bytes:
        """二进制数据直接返回 bytes，不做 base64 编码"""
        return bytes(value)
        
    @classmethod
    def _list_to_pg(cls, value: Union[List, Tuple, Set], for_array: bool = False, **kwargs) -> Union[str, List]:
        """列表、元组、集合转换"""
        # 空列表转换为空数组
        if not value:
            return "{}" if for_array else []
            
        # 只在拼接时处理字符串，不在递归时处理，防止内容丢失
        all_numbers = all(isinstance(x, (int, float)) for x in value)
        all_strings = all(isinstance(x, str) for x in value)
        if for_array:
            if all_numbers:
                return "{" + ",".join(str(x) for x in value) + "}"
            elif all_strings:
                return "{" + ",".join('"' + cls._str_to_pg(item, for_array=True) + '"' for item in value) + "}"
            else:
                # 混合类型，递归处理
                converted_items = [cls.py_to_pg(item, for_array=False) for item in value]
                return json.dumps(converted_items)
        else:
            # 递归处理
            converted_items = [cls.py_to_pg(item, for_array=False) for item in value]
            return converted_items
                
    @classmethod
    def _dict_to_pg(cls, value: Dict, **kwargs) -> str:
        """字典转换为JSON字符串（只对最外层做 json.dumps，不递归处理 value）"""
        try:
            return json.dumps(value)
        except Exception as e:
            logger.error(f"字典转换失败: {str(e)}")
            traceback.print_exc()  # 打印详细异常堆栈
            # 回退到字符串表示
            return str(value)
            
    @staticmethod
    def _date_to_pg(value: datetime.date, **kwargs) -> str:
        """日期转换为ISO格式字符串"""
        return value.isoformat()
        
    @staticmethod
    def _datetime_to_pg(value: datetime.datetime, **kwargs) -> str:
        """日期时间转换为ISO格式字符串"""
        return value.isoformat()
        
    @staticmethod
    def _time_to_pg(value: datetime.time, **kwargs) -> str:
        """时间转换为ISO格式字符串"""
        return value.isoformat()
        
    @staticmethod
    def _decimal_to_pg(value: decimal.Decimal, **kwargs) -> float:
        """Decimal转换为浮点数"""
        return float(value)
        
    @staticmethod
    def _uuid_to_pg(value: uuid.UUID, **kwargs) -> str:
        """UUID转换为字符串"""
        return str(value)
        
    @classmethod
    def adapt_value_for_db(cls, value: Any) -> Any:
        """
        将Python数据类型转换为PostgreSQL可接受的数据类型。
        
        Args:
            value: 任何Python数据类型
            
        Returns:
            转换后的值，适合PostgreSQL使用
        """
        return cls.py_to_pg(value)
        
    @classmethod
    def convert_to_pg_type(cls, value: Any, logger=None, recursive: bool = True, for_array: bool = False) -> Any:
        """
        将Python数据类型递归转换为PostgreSQL可接受的数据类型。
        
        Args:
            value: 任何Python数据类型的值
            logger: 可选的日志记录器对象，用于记录警告和错误
            recursive: 是否递归处理复合数据类型（列表、字典）中的元素，默认为True
            for_array: 内部参数，指示当前值是否用于PostgreSQL数组，影响字符串转义
            
        Returns:
            转换后的值，适合PostgreSQL使用
            
        示例:
            >>> DBTypeConverter.convert_to_pg_type(None)
            None
            >>> DBTypeConverter.convert_to_pg_type([1, "text", {"key": "value"}])  # 递归转换列表中的元素
            >>> DBTypeConverter.convert_to_pg_type({"id": 1, "data": [1, 2, 3]})   # 递归转换字典中的值
        """
        try:
            # 处理None值
            if value is None:
                return None
                
            # 处理布尔类型
            if isinstance(value, bool):
                return value
                
            # 处理数值类型
            if isinstance(value, (int, float)):
                return value
                
            # 处理Decimal类型
            if isinstance(value, decimal.Decimal):
                return float(value)
                
            # 处理UUID类型
            if isinstance(value, uuid.UUID):
                return str(value)
                
            # 处理日期时间类型
            if isinstance(value, (datetime.date, datetime.datetime)):
                return value.isoformat()
                
            # 处理字符串类型
            if isinstance(value, str):
                if for_array:
                    # 如果是用于数组，需要转义双引号和单引号
                    return value.replace('"', '\\"').replace("'", "\\'")
                return value
                
            # 处理列表类型
            if isinstance(value, list):
                # 空列表转换为空数组
                if not value:
                    return "{}" if for_array else []
                    
                # 递归处理列表中的每个元素
                if recursive:
                    try:
                        converted_items = [
                            cls.convert_to_pg_type(
                                item, logger, recursive, True)
                            for item in value
                        ]
                    except Exception as list_e:
                        # 打印详细堆栈信息
                        error_msg = f"列表元素转换失败: {str(list_e)}"
                        if logger:
                            logger.error(error_msg)
                        else:
                            print(error_msg)
                        traceback.print_exc()  # 打印详细异常堆栈
                        # 回退到非递归处理
                        converted_items = value
                else:
                    converted_items = value
                    
                # 检查转换后的列表是否为纯数字或纯字符串
                try:
                    all_numbers = all(isinstance(x, (int, float))
                                      for x in converted_items)
                    all_strings = all(isinstance(x, str)
                                      for x in converted_items)
                                      
                    # 将列表转换为PostgreSQL数组格式或JSON
                    if for_array or not recursive:
                        if all_numbers:
                            return "{" + ",".join(str(x) for x in converted_items) + "}"
                        elif all_strings:
                            return "{" + ",".join(f'"{x}"' for x in converted_items) + "}"
                        else:
                            # 混合类型，转换为JSON字符串
                            return json.dumps(converted_items)
                    else:
                        # 返回转换后的列表，后续处理（如insert_data）可能会再次处理它
                        return converted_items
                except Exception as format_e:
                    # 打印详细堆栈信息
                    error_msg = f"列表格式化失败: {str(format_e)}"
                    if logger:
                        logger.error(error_msg)
                    else:
                        print(error_msg)
                    traceback.print_exc()  # 打印详细异常堆栈
                    # 尝试直接JSON序列化
                    try:
                        return json.dumps(value)
                    except:
                        traceback.print_exc()  # 打印详细异常堆栈
                        return str(value)
                        
            # 处理字典类型
            if isinstance(value, dict):
                try:
                    if recursive:
                        # 递归处理字典中的每个值
                        converted_dict = {}
                        for k, v in value.items():
                            try:
                                converted_dict[k] = cls.convert_to_pg_type(
                                    v, logger, recursive)
                            except Exception as key_e:
                                # 记录单个键值对转换失败
                                error_msg = f"字典键 '{k}' 的值转换失败: {str(key_e)}"
                                if logger:
                                    logger.warning(error_msg)
                                else:
                                    print(error_msg)
                                traceback.print_exc()  # 打印详细异常堆栈
                                # 使用字符串表示作为回退
                                try:
                                    converted_dict[k] = str(v)
                                except:
                                    traceback.print_exc()  # 打印详细异常堆栈
                                    converted_dict[k] = None
                        # 转换为JSON字符串
                        return json.dumps(converted_dict)
                    else:
                        # 不递归处理，直接转换为JSON
                        return json.dumps(value)
                except Exception as dict_e:
                    # 打印详细堆栈信息
                    error_msg = f"字典转换失败: {str(dict_e)}"
                    if logger:
                        logger.error(error_msg)
                    else:
                        print(error_msg)
                    traceback.print_exc()  # 打印详细异常堆栈
                    # 回退到字符串表示
                    return str(value)
                    
            # 处理可迭代对象（非字符串/列表/字典）
            if hasattr(value, "__iter__") and not isinstance(value, (str, list, dict)):
                try:
                    # 尝试转换为列表后处理
                    return cls.convert_to_pg_type(list(value), logger, recursive, for_array)
                except Exception as iter_e:
                    error_msg = f"无法将可迭代对象转换为列表: {str(iter_e)}"
                    if logger:
                        logger.warning(error_msg)
                    else:
                        print(error_msg)
                    traceback.print_exc()  # 打印详细异常堆栈
                    # 回退到字符串表示
                    try:
                        return str(value)
                    except:
                        traceback.print_exc()  # 打印详细异常堆栈
                        return None
                        
            # 处理其他类型
            try:
                # 尝试转换为字符串
                return str(value)
            except Exception as e:
                error_msg = f"无法转换类型 {type(value)} 的值: {value}，将返回None。错误: {str(e)}"
                if logger:
                    logger.warning(error_msg)
                else:
                    print(error_msg)
                traceback.print_exc()  # 打印详细异常堆栈
                return None
                
        except Exception as e:
            # 主异常处理
            error_msg = f"数据类型转换失败: {str(e)}"
            if logger:
                logger.error(error_msg)
            else:
                print(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈
            return None


# 创建全局转换器实例
converter = DBTypeConverter()

# 导出便捷函数
def adapt_value_for_db(value: Any) -> Any:
    """将Python值转换为PostgreSQL兼容值"""
    return DBTypeConverter.adapt_value_for_db(value)

def convert_to_pg_type(value: Any, logger=None, recursive: bool = True, for_array: bool = False) -> Any:
    """将Python值递归转换为PostgreSQL兼容值"""
    return DBTypeConverter.convert_to_pg_type(value, logger, recursive, for_array) 