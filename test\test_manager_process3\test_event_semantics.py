#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试事件语义的正确性
验证 COMPLETED 和 STOPPED 事件的互斥性
"""

import sys
import os
import time
import unittest
import threading

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager


def test_worker(shared_data_manager, item, *args, **kwargs):
    """测试工作函数"""
    import time
    time.sleep(0.5)  # 模拟一些工作
    shared_data_manager.append_to_list("results", f"processed_{item}")
    return f"result_{item}"


class TestEventSemantics(unittest.TestCase):
    """测试事件语义的正确性"""
    
    def setUp(self):
        """设置测试"""
        self.events_triggered = []
        self.event_lock = threading.Lock()
        self.manager = None
    
    def tearDown(self):
        """清理测试"""
        if self.manager:
            try:
                self.manager.stop_all(immediate=True)
            except:
                pass
    
    def on_event(self, event_name):
        """事件回调函数工厂"""
        def callback(mp_instance, *args, **kwargs):
            with self.event_lock:
                self.events_triggered.append(event_name)
            print(f"🎉 事件触发: {event_name}")
        return callback
    
    def wait_for_events(self, expected_events, timeout=10.0):
        """等待特定事件触发"""
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            with self.event_lock:
                if all(event in self.events_triggered for event in expected_events):
                    return True
            time.sleep(0.1)
        return False
    
    def test_natural_completion_events(self):
        """测试自然完成场景的事件语义"""
        print("=" * 80)
        print("测试自然完成场景：应该触发 COMPLETED 事件，不触发 STOPPED 事件")
        print("=" * 80)
        
        # 创建测试数据（少量数据，确保能快速完成）
        test_data = [f"item_{i}" for i in range(2)]
        print(f"测试数据: {test_data}")
        
        # 创建 ManagedMultiProcess 实例
        self.manager = ManagedMultiProcess(
            input_data=test_data,
            callback_func=test_worker,
            num_processes=2,
            max_queue_size=10
        )
        
        # 注册所有相关事件
        events_to_monitor = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
        ]
        
        for event in events_to_monitor:
            self.manager.listen_event(event, self.on_event(event))
        
        print("\n启动多进程处理...")
        self.manager.run()
        
        print("等待自然完成...")
        # 等待足够长时间让任务自然完成
        time.sleep(3.0)
        
        print(f"触发的事件: {self.events_triggered}")
        
        # 验证事件语义
        with self.event_lock:
            # 应该触发的事件
            self.assertIn(ProcessEventManager.PROCESS_CREATED, self.events_triggered, 
                         "自然完成场景应该触发 PROCESS_CREATED")
            self.assertIn(ProcessEventManager.PROCESS_COMPLETED, self.events_triggered, 
                         "自然完成场景应该触发 PROCESS_COMPLETED")
            self.assertIn(ProcessEventManager.PROCESS_COMPLETED_WITH_DATA, self.events_triggered, 
                         "自然完成场景应该触发 PROCESS_COMPLETED_WITH_DATA")
            
            # 不应该触发的事件
            self.assertNotIn(ProcessEventManager.PROCESS_STOPPED, self.events_triggered, 
                           "自然完成场景不应该触发 PROCESS_STOPPED")
            self.assertNotIn(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, self.events_triggered, 
                           "自然完成场景不应该触发 PROCESS_STOPPED_WITH_DATA")
        
        print("✅ 自然完成场景事件语义正确")
    
    def test_stop_all_events(self):
        """测试 stop_all 场景的事件语义"""
        print("\n" + "=" * 80)
        print("测试 stop_all 场景：应该触发 STOPPED 事件，不触发 COMPLETED 事件")
        print("=" * 80)
        
        # 重置事件列表
        with self.event_lock:
            self.events_triggered.clear()
        
        # 创建测试数据（较多数据，确保在 stop_all 时还在执行）
        test_data = [f"item_{i}" for i in range(5)]
        print(f"测试数据: {test_data}")
        
        # 创建 ManagedMultiProcess 实例
        self.manager = ManagedMultiProcess(
            input_data=test_data,
            callback_func=test_worker,
            num_processes=2,
            max_queue_size=10
        )
        
        # 注册所有相关事件
        events_to_monitor = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
        ]
        
        for event in events_to_monitor:
            self.manager.listen_event(event, self.on_event(event))
        
        print("\n启动多进程处理...")
        self.manager.run()
        
        print("等待一段时间后调用 stop_all...")
        time.sleep(1.0)  # 让任务开始执行但不完成
        
        print("调用 stop_all()...")
        self.manager.stop_all(immediate=False)
        
        print("等待 stop 相关事件...")
        time.sleep(2.0)
        
        print(f"触发的事件: {self.events_triggered}")
        
        # 验证事件语义
        with self.event_lock:
            # 应该触发的事件
            self.assertIn(ProcessEventManager.PROCESS_CREATED, self.events_triggered, 
                         "stop_all 场景应该触发 PROCESS_CREATED")
            self.assertIn(ProcessEventManager.PROCESS_STOPPED, self.events_triggered, 
                         "stop_all 场景应该触发 PROCESS_STOPPED")
            self.assertIn(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, self.events_triggered, 
                         "stop_all 场景应该触发 PROCESS_STOPPED_WITH_DATA")
            
            # 不应该触发的事件
            self.assertNotIn(ProcessEventManager.PROCESS_COMPLETED, self.events_triggered, 
                           "stop_all 场景不应该触发 PROCESS_COMPLETED")
            self.assertNotIn(ProcessEventManager.PROCESS_COMPLETED_WITH_DATA, self.events_triggered, 
                           "stop_all 场景不应该触发 PROCESS_COMPLETED_WITH_DATA")
        
        print("✅ stop_all 场景事件语义正确")


def main():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main()
