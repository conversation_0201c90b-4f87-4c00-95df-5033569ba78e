[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 1379] 2025-05-12 22:52:38.336 错误:	获取锁代理时发生意外错误: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.365 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.365 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.367 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.367 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.369 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.369 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.371 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.371 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.374 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.374 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.376 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.376 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.378 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.378 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.380 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.380 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.382 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.383 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.384 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.385 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.439 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.443 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.443 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.443 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.445 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.445 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.445 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.447 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.448 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.448 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.450 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.450 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.450 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.453 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.454 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.454 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.457 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.457 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.458 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.459 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.460 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.460 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.463 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.464 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.464 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.465 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.466 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.466 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.468 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.468 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:38.487 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.488 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2396] 2025-05-12 22:52:38.488 调试:	获取通知队列数据时连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.488 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:38.489 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2441] 2025-05-12 22:52:38.491 调试:	数据实时变化监听线程已停止[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.492 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.492 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.521 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.523 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.525 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.527 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.529 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.530 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.533 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.537 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.538 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.540 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.540 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.541 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.542 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.544 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.546 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.548 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.550 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.554 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.556 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.558 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.569 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.570 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.570 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.572 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.573 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.574 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.595 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.595 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.598 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.599 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.644 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.647 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.660 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.662 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.674 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.677 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.677 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.677 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.679 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.680 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.700 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.700 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.704 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.704 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.750 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.752 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.766 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.768 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:38.775 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:38.777 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.781 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.783 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.783 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.784 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.786 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.786 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.789 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.792 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.794 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.796 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.797 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.799 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.802 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:38.806 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.806 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.807 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:38.807 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.808 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.808 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:38.809 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:38.810 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.810 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.810 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.812 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.812 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.814 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.814 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.816 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.819 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.821 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.823 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.824 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.826 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.828 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.854 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.857 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.870 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.872 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.886 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.888 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.889 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.889 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.892 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.892 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.916 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.916 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.917 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.918 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.918 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.919 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.932 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.934 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.960 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.962 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.975 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.977 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.992 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.994 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.995 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:38.995 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...


[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.997 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:38.997 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.022 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.022 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.023 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.025 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.025 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.025 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.037 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.039 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:39.059 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:39.060 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.063 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.066 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.079 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:39.080 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:39.083 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.083 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:39.084 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:39.085 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.099 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.100 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.100 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.101 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.102 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.102 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.128 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.129 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.129 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.131 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.131 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.132 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.141 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.144 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.168 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.170 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.185 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.187 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.207 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.207 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.207 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.210 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.210 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.210 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.234 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.236 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.236 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.238 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.239 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.239 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.247 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.249 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.272 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.274 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.290 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.291 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.313 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.314 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.314 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.316 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.317 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.317 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:39.319 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:39.320 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.340 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.342 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.343 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.344 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.352 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.354 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.378 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.380 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.394 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.396 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.405 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.408 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.420 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.420 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.420 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...


[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.424 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.424 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.424 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.446 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.446 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.450 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.450 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.458 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.460 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.483 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.485 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.499 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.502 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.510 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.511 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.526 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.526 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.527 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...


[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.529 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.529 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.529 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.552 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.553 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.554 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.555 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.562 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.565 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.587 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.590 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.605 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.607 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:39.607 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:39.609 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.614 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.617 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.633 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.634 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.634 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.636 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.637 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.637 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:39.640 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:39.642 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:39.643 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:39.644 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.658 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.659 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.661 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.662 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.668 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.670 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.693 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.695 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.709 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.711 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.720 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.723 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.739 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.741 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.741 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.742 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.742 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.743 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.763 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.765 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.767 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.768 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.772 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.774 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.797 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.800 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.815 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.818 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.825 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.829 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.848 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.848 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.848 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.851 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.851 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.851 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.870 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.872 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.874 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.874 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.877 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.880 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.903 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.905 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:39.920 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.920 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:39.923 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.924 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.930 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.934 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:39.943 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:39.944 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:39.945 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:39.946 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.954 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.955 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.955 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.957 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.957 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.957 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.977 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.977 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.981 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.981 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:39.984 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:39.986 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.007 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.010 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.027 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.031 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.036 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.039 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.061 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.062 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.062 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.065 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.065 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.065 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.083 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.083 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.086 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.086 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.089 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.092 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.112 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.114 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.133 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.135 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.140 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.143 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.168 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.168 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.169 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.170 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.172 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.172 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.188 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.188 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.191 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.191 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.193 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.195 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.217 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:40.218 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:40.219 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.220 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.238 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.240 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:40.245 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.245 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:40.246 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:40.247 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.248 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:40.249 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.274 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.275 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.276 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.278 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.278 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.279 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.294 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.294 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.296 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.297 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.299 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.302 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.322 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.324 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.343 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.345 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.350 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.352 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.381 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.381 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.383 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.383 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.384 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.385 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.399 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.400 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.402 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.403 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.405 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.407 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.427 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.429 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.449 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.450 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.455 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.458 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.487 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.494 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.494 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.496 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.498 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.502 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.514 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.514 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.515 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.519 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.520 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.520 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.536 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.539 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.553 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.555 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.560 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.563 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:40.569 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:40.570 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.599 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.600 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.602 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.602 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.606 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.608 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:40.613 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:40.614 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:40.615 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:40.617 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.621 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.622 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.623 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.625 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.627 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.627 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.642 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.646 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.661 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.665 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.666 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.669 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.705 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.706 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.709 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.709 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.711 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.713 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.728 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.730 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.731 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.731 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.733 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.734 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.748 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.750 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.767 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.770 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.770 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.773 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.812 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.813 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.815 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.815 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.815 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.817 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.834 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.836 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.836 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.836 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.838 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.838 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.852 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.855 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.872 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.874 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.874 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.878 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:40.914 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:40.917 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.917 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.918 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.921 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.921 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.922 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.924 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.939 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.940 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.942 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.942 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 832] 2025-05-12 22:52:40.943 调试:	SharedDataManager.shutdown - 开始关闭...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.944 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.944 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 856] 2025-05-12 22:52:40.945 调试:	  关闭 Manager...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 858] 2025-05-12 22:52:40.946 调试:	  Manager 已关闭。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 864] 2025-05-12 22:52:40.947 调试:	SharedDataManager.shutdown - 完成。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.957 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.959 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.978 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.981 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:40.982 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:40.983 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.023 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.024 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.026 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.026 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.026 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.028 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.045 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.048 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.050 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.050 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.051 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.051 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.064 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.066 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.083 错误:	获取实时通知队列失败: 
---------------------------------------------------------------------------
Traceback (most recent call last):
  File "H:\Python\Miniconda\envs\YOLO\Lib\multiprocessing\managers.py", line 260, in serve_client
    self.id_to_local_proxy_obj[ident]
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
Ke...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.086 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.086 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.089 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.128 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.128 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.131 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.132 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.132 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.134 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.152 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.155 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.155 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。

[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.156 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.158 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.158 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.168 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.170 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 781] 2025-05-12 22:52:41.179 调试:	变更队列连接断开: ，线程即将退出
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\shared_data_manager.py", line 823] 2025-05-12 22:52:41.181 调试:	数据变化通知处理线程已停止
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.190 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.194 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.234 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.234 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.238 错误:	获取实时通知队列失败: [WinError 10053] 你的主机中的软件中止了一个已建立的连接。[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.238 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.238 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...


[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.240 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2362] 2025-05-12 22:52:41.246 错误:	获取实时通知队列失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
[File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2364] 2025-05-12 22:52:41.249 错误:	Traceback (most recent call last):
  File "K:\yolo11\custom-module\global\global_tools\utils\manager_process3\managed_multi_process.py", line 2356, in __process_realtime_notifications
    notify_queue = self.__shared_data_manager_proxy.get_realtime_notify_queue()
                   ^^^^^^^^^^^^^^^^^...