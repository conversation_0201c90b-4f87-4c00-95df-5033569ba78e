import numpy as np
import cv2
import dxcam
import time
from typing import Optional, Tuple, List, Union
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DXCamCore")

class DXCamCore:
    """
    使用DXcam库对指定显示器进行图像捕获并转换为OpenCV格式的工具类
    """
    
    def __init__(self, 
                 monitor_index: int = 0, 
                 output_color: str = "BGR",
                 region: Optional[Tuple[int, int, int, int]] = None):
        """
        初始化DXCamCore类
        
        Args:
            monitor_index (int): 显示器索引，0表示主显示器
            output_color (str): 输出图像的颜色格式，可选值为"BGR"(OpenCV默认)或"RGB"
            region (tuple, optional): 捕获区域 (left, top, right, bottom)，None表示整个显示器
            
        示例:
            # 创建一个捕获主显示器的DXCamCore实例
            capture = DXCamCore(monitor_index=0)
            
            # 创建一个捕获第二个显示器特定区域的DXCamCore实例
            capture = DXCamCore(monitor_index=1, region=(100, 100, 500, 500))
        """
        self.__monitor_index = monitor_index
        self.__output_color = output_color
        self.__region = region
        self.__camera = None
        self.__monitors_info = []
        
        # 初始化DXcam
        try:
            # 获取设备和输出信息
            device_info_str = dxcam.device_info()
            output_info_str = dxcam.output_info()
            
            if not device_info_str or not output_info_str:
                raise ValueError("无法获取显示器信息")
            
            # 解析输出信息以获取显示器列表
            self.__parse_output_info(output_info_str)
            
            logger.info(f"检测到 {len(self.__monitors_info)} 个显示器")
            
            # 验证显示器索引是否有效
            if monitor_index >= len(self.__monitors_info) or monitor_index < 0:
                raise ValueError(f"无效的显示器索引: {monitor_index}，可用范围为 0-{len(self.__monitors_info)-1}")
            
            # 获取当前显示器信息
            target_monitor = self.__monitors_info[monitor_index]
            logger.info(f"使用显示器 {monitor_index}: {target_monitor}")
            
            # 如果指定了区域，则使用指定区域，否则使用整个显示器
            region_to_use = self.__region
            if region_to_use is None:
                # 使用整个显示器
                width = target_monitor["width"]
                height = target_monitor["height"]
                left = target_monitor["left"]
                top = target_monitor["top"]
                region_to_use = (left, top, left + width, top + height)
            
            # 创建相机实例
            self.__camera = dxcam.create(device_idx=target_monitor["device_idx"], 
                                       output_idx=target_monitor["output_idx"],
                                       output_color=output_color,
                                       region=region_to_use)
            
            logger.info(f"DXcam 初始化成功，区域: {region_to_use}")
            
        except Exception as e:
            logger.error(f"DXcam 初始化失败: {str(e)}")
            raise
    
    def __parse_output_info(self, output_info_str: str) -> None:
        """
        解析dxcam.output_info()返回的字符串，提取显示器信息
        
        Args:
            output_info_str (str): dxcam.output_info()返回的字符串
            
        格式示例:
        'Device[0] Output[0]: Res:(1920, 1080) Rot:0 Primary:True\nDevice[0] Output[1]: Res:(1920, 1080) Rot:0 Primary:False\n'
        """
        self.__monitors_info = []
        
        # 正则表达式匹配输出信息
        pattern = r'Device\[(\d+)\] Output\[(\d+)\]: Res:\((\d+), (\d+)\) Rot:(\d+) Primary:(True|False)'
        matches = re.finditer(pattern, output_info_str)
        
        for match in matches:
            device_idx = int(match.group(1))
            output_idx = int(match.group(2))
            width = int(match.group(3))
            height = int(match.group(4))
            rotation = int(match.group(5))
            is_primary = match.group(6) == "True"
            
            # 计算left和top（这可能需要根据实际情况调整）
            # 简单假设主显示器在(0,0)，非主显示器在右侧
            left = 0
            top = 0
            
            # 将解析结果添加到显示器列表
            monitor_info = {
                "device_idx": device_idx,
                "output_idx": output_idx,
                "width": width,
                "height": height,
                "rotation": rotation, 
                "is_primary": is_primary,
                "left": left,
                "top": top
            }
            
            self.__monitors_info.append(monitor_info)
    
    def get_monitors_info(self) -> List[dict]:
        """
        获取所有可用显示器的信息
        
        Returns:
            List[dict]: 显示器信息列表，每个字典包含显示器的详细信息
            
        示例:
            # 获取所有显示器信息
            monitors = capture.get_monitors_info()
            for i, monitor in enumerate(monitors):
                print(f"显示器 {i}: {monitor}")
        """
        if not self.__monitors_info:
            return []
        return self.__monitors_info
    
    def get_current_monitor_info(self) -> dict:
        """
        获取当前使用的显示器信息
        
        Returns:
            dict: 当前显示器的详细信息
            
        示例:
            # 获取当前使用的显示器信息
            monitor_info = capture.get_current_monitor_info()
            print(f"当前使用的显示器: {monitor_info}")
        """
        if not self.__monitors_info or self.__monitor_index >= len(self.__monitors_info):
            logger.error("无法获取当前显示器信息：无效的显示器索引")
            return {}
        return self.__monitors_info[self.__monitor_index]
    
    def capture(self, max_retries: int = 3) -> Optional[np.ndarray]:
        """
        捕获当前显示器的图像并返回OpenCV格式的图像数据
        
        Args:
            max_retries (int): 捕获失败时的最大重试次数
            
        Returns:
            np.ndarray: OpenCV格式的图像数据，如果捕获失败则返回None
            
        示例:
            # 捕获一帧图像
            frame = capture.capture()
            if frame is not None:
                # 使用OpenCV处理图像
                cv2.imshow("Captured Frame", frame)
                cv2.waitKey(0)
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return None
        
        # 尝试捕获，最多重试max_retries次
        img = None
        retry_count = 0
        
        while img is None and retry_count < max_retries:
            try:
                # 捕获图像
                img = self.__camera.grab()
                
                if img is None:
                    retry_count += 1
                    logger.warning(f"捕获图像失败，尝试重试 ({retry_count}/{max_retries})")
                    continue
                
                # 检查图像是否为空
                if img.size == 0:
                    retry_count += 1
                    logger.warning(f"捕获的图像为空，尝试重试 ({retry_count}/{max_retries})")
                    img = None
                    continue
                
                # DXcam默认已转换为指定的颜色格式(在创建时通过output_color参数设置)
                
            except Exception as e:
                retry_count += 1
                logger.error(f"捕获图像时出错: {str(e)}, 尝试重试 ({retry_count}/{max_retries})")
                img = None
        
        if img is None:
            logger.error(f"在 {max_retries} 次尝试后仍无法捕获图像")
        
        return img
    
    def capture_with_timeout(self, timeout_ms: int = 1000) -> Optional[np.ndarray]:
        """
        在指定的超时时间内捕获图像
        
        Args:
            timeout_ms (int): 超时时间，单位为毫秒
            
        Returns:
            np.ndarray: OpenCV格式的图像数据，如果超时或捕获失败则返回None
            
        示例:
            # 尝试在500毫秒内捕获图像
            frame = capture.capture_with_timeout(timeout_ms=500)
            if frame is not None:
                cv2.imshow("Captured Frame", frame)
                cv2.waitKey(0)
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return None
        
        try:
            # 使用DXcam的超时功能捕获图像
            img = self.__camera.grab(timeout=timeout_ms)
            
            if img is None:
                logger.warning(f"在 {timeout_ms}ms 内未能捕获图像")
                return None
            
            # 检查图像是否为空
            if img.size == 0:
                logger.warning("捕获的图像为空")
                return None
            
            return img
            
        except Exception as e:
            logger.error(f"捕获图像时出错: {str(e)}")
            return None
    
    def capture_multiple(self, num_frames: int = 3, interval_ms: int = 33) -> List[np.ndarray]:
        """
        捕获多帧图像
        
        Args:
            num_frames (int): 捕获的帧数
            interval_ms (int): 每帧之间的时间间隔，单位为毫秒
            
        Returns:
            List[np.ndarray]: OpenCV格式的图像列表，如果某帧捕获失败，则对应位置为None
            
        示例:
            # 捕获3帧图像，间隔33毫秒(约30fps)
            frames = capture.capture_multiple(num_frames=3, interval_ms=33)
            for i, frame in enumerate(frames):
                if frame is not None:
                    cv2.imshow(f"Frame {i}", frame)
            cv2.waitKey(0)
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return []
        
        try:
            # 使用循环和休眠来手动捕获多帧图像（因为DXcam.grab_multiple在新版本可能不可用）
            result = []
            for _ in range(num_frames):
                frame = self.__camera.grab()
                result.append(frame)
                # 休眠指定的间隔时间
                if _ < num_frames - 1:  # 最后一帧不需要休眠
                    time.sleep(interval_ms / 1000.0)
            
            return result
            
        except Exception as e:
            logger.error(f"捕获多帧图像时出错: {str(e)}")
            return []
    
    def set_capture_region(self, region: Tuple[int, int, int, int]) -> bool:
        """
        设置捕获区域
        
        Args:
            region (tuple): 捕获区域 (left, top, right, bottom)
            
        Returns:
            bool: 是否设置成功
            
        示例:
            # 设置捕获区域为屏幕左上角的100x100像素区域
            success = capture.set_capture_region((0, 0, 100, 100))
            if success:
                frame = capture.capture()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return False
        
        try:
            # 设置新的捕获区域
            self.__camera.stop()
            self.__camera = None
            
            # 获取当前显示器信息
            current_monitor = self.__monitors_info[self.__monitor_index]
            
            # 重新创建相机实例，使用新的区域
            self.__region = region
            self.__camera = dxcam.create(
                device_idx=current_monitor["device_idx"],
                output_idx=current_monitor["output_idx"],
                output_color=self.__output_color,
                region=region
            )
            
            logger.info(f"捕获区域已更新为: {region}")
            return True
            
        except Exception as e:
            logger.error(f"设置捕获区域时出错: {str(e)}")
            return False
    
    def start_recording(self, target_fps: int = 30) -> bool:
        """
        开始录制模式
        
        Args:
            target_fps (int): 目标帧率
            
        Returns:
            bool: 是否成功开始录制
            
        示例:
            # 开始以30fps的速率录制
            if capture.start_recording(target_fps=30):
                # 获取一些帧
                for _ in range(10):
                    frame = capture.get_latest_frame()
                    # 处理帧...
                # 停止录制
                capture.stop_recording()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return False
        
        try:
            self.__camera.start(target_fps=target_fps)
            logger.info(f"开始录制，目标帧率: {target_fps} fps")
            return True
            
        except Exception as e:
            logger.error(f"开始录制时出错: {str(e)}")
            return False
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """
        在录制模式下获取最新的一帧
        
        Returns:
            np.ndarray: 最新的一帧图像，如果未在录制或获取失败则返回None
            
        示例:
            # 获取录制模式下的最新帧
            frame = capture.get_latest_frame()
            if frame is not None:
                cv2.imshow("Latest Frame", frame)
                cv2.waitKey(1)
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return None
        
        try:
            img = self.__camera.get_latest_frame()
            
            if img is None:
                return None
            
            # 检查图像是否为空
            if img.size == 0:
                logger.warning("获取的最新帧为空")
                return None
            
            return img
            
        except Exception as e:
            logger.error(f"获取最新帧时出错: {str(e)}")
            return None
    
    def stop_recording(self) -> None:
        """
        停止录制模式
        
        示例:
            # 停止之前开始的录制
            capture.stop_recording()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return
        
        try:
            self.__camera.stop()
            logger.info("录制已停止")
            
        except Exception as e:
            logger.error(f"停止录制时出错: {str(e)}")
    
    def release(self) -> None:
        """
        释放相机资源
        
        示例:
            # 使用完毕后释放资源
            capture.release()
        """
        if self.__camera is not None:
            try:
                self.__camera.stop()
                self.__camera = None
                logger.info("相机资源已释放")
                
            except Exception as e:
                logger.error(f"释放相机资源时出错: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口方法，用于支持with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出方法，确保资源被正确释放"""
        self.release()
