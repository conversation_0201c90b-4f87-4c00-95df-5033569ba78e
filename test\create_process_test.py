import sys
import os
# sys.path.insert( 0, os.path.abspath( os.path.join( os.path.dirname( __file__ ), '..' ) ) )

import threading
import logging
import random
import time

from global_tools.utils.manager_process3 import ManagedMultiProcess, SharedDataManager, \
	ProcessEventManager

# 添加日志设置
from global_tools.utils import Logger, LogLevel

logger = Logger()


# from global_tools.utils.manager_process.create_process import ManagedMultiProcess, SharedDataManager, ProcessEventManager

# 将示例工作函数移到顶层，以便子进程可以找到它


def simple_worker_example( shared_manager_proxy: SharedDataManager, task_item: int, prefix: str = "Task" ):
	'''
	一个简单的工作函数示例，使用 SharedDataManager 代理。
	它处理一个数字，并将结果存储在共享数据中。
	使用超时锁避免可能的死锁问题。

	Args:
	shared_manager_proxy: SharedDataManager代理对象，用于访问共享数据
	task_item: 要处理的任务项（整数）
	prefix: 任务前缀，默认为"Task"

	Returns:
	None: 结果通过共享数据管理器存储
	'''
	for i in range( 10 ):
		try:
			lock = shared_manager_proxy.get_lock()

			with lock:
				num = shared_manager_proxy.get_value( "num", default=0 )
				new_num = num + 1
				shared_manager_proxy.add_value( "num", new_num )
				shared_manager_proxy.append_to_list( "list", new_num )
				print( f"id {os.getpid()} - {i}" )

			# 模拟处理时间
			time.sleep( random.uniform( 0.01, 0.15 ) )

		except Exception as e:
			# 确保异常被捕获，避免子进程崩溃
			print( f"进程 {os.getpid()} 处理任务 {i} 时出错: {e}" )
			# 出错时短暂暂停，避免大量错误日志
			time.sleep( 0.5 )


def on_num_changed( keys, old_value, new_value, *args, **kwargs ):
	pass
	print( f"共享数据 {keys} 已更改: {old_value} -> {new_value}" )
	# time.sleep( random.uniform( 0.2, 0.35 ) )
	pass


def main():
	# 创建一个简单的处理任务列表
	tasks = list( range( 3 ) )

	# 1. 创建并配置 ManagedMultiProcess 实例
	mp = ManagedMultiProcess(
		input_data=tasks,
		callback_func=simple_worker_example,  # 使用示例工作函数
		num_processes=3,
		prefix="Demo"  # 传递给示例工作函数的参数
	)

	mp.watch_shared_data( "num", on_num_changed )
	mp.run()
	mp.wait_all()

	status = mp.check_and_release_lock( force=True )

	after_num = mp.get_shared_value( key='num' )
	after_list = mp.get_shared_list( key='list' )

	# 验证关闭前后数据是否一致
	print( f"数据一致性检查: num一致={after_num}, list一致={after_list}" )

	# 关闭共享资源
	mp.close_shared_data_resources()


if __name__ == "__main__":
	main()
