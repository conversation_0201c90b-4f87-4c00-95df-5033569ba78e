import sys
import os

# Add parent directory to module path if running directly
if __name__ == "__main__" and not __package__:
	sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from global_tools.window_operation.capture import WindowsGraphicsCapture
import cv2
import time

if __name__ == "__main__":
	# 创建截图实例
	capture = WindowsGraphicsCapture()

	# 获取窗口句柄（示例使用窗口标题）
	hwnd = 135134

	index = capture.get_monitor_index_for_window(hwnd)
	region_img = capture.capture(hwnd, region=(0,0,1000,800))
	cv2.imshow("title", region_img)
	cv2.waitKey(0)
	
	# for i in range(test_count):
	# 	try:
	# 		# 每次截图间添加适当的延迟
	# 		if i > 0:
	# 			time.sleep(0.05)  # 添加50ms延迟
				
	# 		# 轮流使用不同大小的区域
	# 		region = regions[i % len(regions)]
			
	# 		# 捕获指定区域
	# 		region_img = capture.capture(hwnd, region=(0,0,1000,1500))
			
	# 		# 查看截图操作的性能
	# 		print(f"第 {i+1} 次截图完成，区域: {region}，耗时: {capture.get_last_capture_time():.2f} 毫秒，图像大小: {region_img.shape}")
	# 		success_count += 1

		
	# 	except Exception as e:
	# 		print(f"第 {i+1} 次截图失败，区域: {region}，错误: {str(e)}")

	# print(f"\n测试完成: 成功 {success_count}/{test_count} 次 ({success_count/test_count*100:.1f}%)")
