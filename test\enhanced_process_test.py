import os
import sys

# 将当前项目根目录添加到Python模块路径，确保使用本地代码而非已安装的包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from global_tools.utils.enhanced_process import EnhancedProcess, ProcessLogger
import time
import asyncio
import threading
from global_tools.utils import Colors, ClassInstanceManager, Logger, LogLevel


# 全局变量，用于存储测试结果
test_4_received_logs = []


def init_logger():
    """初始化日志设置"""
    logger = ClassInstanceManager.get_instance("EnhancedProcessLogger")
    if logger:
        logger.set_instance_level(LogLevel.DEBUG)
        return True
    return False


# 测试4的回调函数 - 测试字典日志
async def test_4_log_callback(log_entry):
    await asyncio.sleep(0.01)  # 模拟一些异步处理操作
    test_4_received_logs.append(log_entry)
    if 'message' in log_entry:
        print(f"主进程收到字典日志: {log_entry['message']}", log_entry.get("data"))
    else:
        print(f"主进程收到日志: {log_entry}")


# 测试4的子进程函数 - 测试字典日志
def test_4_worker(shared_data_proxy, data_queue, process_logger, *args, **kwargs):
    # 1. 标准方式记录日志
    name = kwargs.get("name")
    print(name)
    process_logger.log("标准日志", level="INFO")
    
    # 2. 字典方式记录日志 - 发送多条日志测试异步处理
    for i in range(100):
        process_logger.log({
            'message': f'字典日志 #{i+1}',
            'level': 'INFO',
            'custom_field': f'自定义字段-{i}',
            'data': {'count': i, 'timestamp': time.time()}
        })
        time.sleep(0.05)  # 间隔发送
    
    # 3. 完全自定义格式的日志
    process_logger.log({
        'operation': 'USER_LOGIN',  # 不使用标准的message字段
        'status': 'SUCCESS',        # 不使用标准的level字段
        'user_id': 12345,
        'ip_address': '***********',
        'login_time': time.time()
    })
    
    return "测试完成"


def test_dict_logger():
    """测试ProcessLogger的字典日志功能"""
    global test_4_received_logs
    test_4_received_logs = []
    
    print("\n===== 测试字典日志功能(异步回调) =====")
    
    # 创建增强进程
    process = EnhancedProcess(target=test_4_worker)
    process.set_log_callback(test_4_log_callback)

    # 创建终止回调函数
    def on_terminate():
        print("\n==== 同步终止回调已执行 ====")
        print(f"共接收到 {len(test_4_received_logs)} 条日志")
    
    async def async_on_terminate():
        await asyncio.sleep(0.1)  # 模拟异步操作
        print("\n==== 异步终止回调已执行 ====")
        print(f"共接收到 {len(test_4_received_logs)} 条日志")

    def func():
        time.sleep(3)
        # 使用同步回调函数终止进程
        process.terminate_gracefully(callback=async_on_terminate)

    threading.Thread(target=func).start()
    process.start(name="LXL")

    # 等待进程完成
    time.sleep(5)  # 给足够的时间让回调执行完成
    print("\n测试完成!")


def run_all_tests():
    """运行测试用例"""
    print("\n========== 开始测试字典日志异步回调功能 ==========")
    
    # 初始化日志
    if init_logger():
        print("日志初始化成功")
    else:
        print("警告: 日志初始化可能失败")
    
    try:
        # 只运行test_4相关测试
        test_dict_logger()
        
        print("\n========== 测试用例执行完毕 ==========")
        print("✓ 测试通过")
        return True
    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()



