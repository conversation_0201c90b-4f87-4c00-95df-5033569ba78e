#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ManagedMultiProcess 综合功能测试
验证修复后的核心功能是否正常工作
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_worker(shared_data_manager, item, *args, **kwargs):
    """
    测试工作函数
    
    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        *args, **kwargs: 额外参数
        
    Returns:
        处理结果
    """
    import os
    import time
    
    pid = os.getpid()
    print(f"[PID {pid}] 处理项目: {item}")
    
    # 模拟一些工作
    time.sleep(0.1)
    
    # 使用锁保证原子性操作
    lock = shared_data_manager.get_lock()
    with lock:
        # 更新计数器
        counter = shared_data_manager.get_value("processed_count", 0)
        shared_data_manager.add_value("processed_count", counter + 1)
        
        # 记录处理的PID
        pids = shared_data_manager.get_list("worker_pids", [])
        if pid not in pids:
            shared_data_manager.append_to_list("worker_pids", pid)
    
    # 添加处理结果
    shared_data_manager.append_to_list("results", f"processed_{item}_by_{pid}")
    
    return f"result_{item}"


def test_basic_functionality():
    """测试基础多进程功能"""
    print("=" * 80)
    print("测试基础多进程功能")
    print("=" * 80)
    
    # 创建测试数据
    test_data = [f"item_{i}" for i in range(8)]
    print(f"测试数据: {test_data}")
    
    # 创建 ManagedMultiProcess 实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=test_worker,
        num_processes=3,
        max_queue_size=20
    )
    
    try:
        # 启动处理
        print("启动多进程处理...")
        results = manager.run()
        
        # 等待完成
        print("等待任务完成...")
        manager.wait_all(timeout=10)
        
        # 获取结果
        print("获取最终结果...")
        final_results = manager.get_results()
        
        # 验证结果
        print("\n验证结果:")
        print(f"处理计数: {final_results.get('processed_count', 0)} / {len(test_data)}")
        print(f"工作进程PID: {final_results.get('worker_pids', [])}")
        print(f"结果数量: {len(final_results.get('results', []))}")
        
        # 检查是否所有任务都被处理
        processed_count = final_results.get('processed_count', 0)
        results_count = len(final_results.get('results', []))
        
        success = (processed_count == len(test_data) and 
                  results_count == len(test_data))
        
        print(f"基础功能测试: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


def test_single_process_mode():
    """测试单进程模式"""
    print("\n" + "=" * 80)
    print("测试单进程模式")
    print("=" * 80)
    
    # 创建测试数据
    test_data = [f"single_{i}" for i in range(3)]
    print(f"测试数据: {test_data}")
    
    # 创建单进程模式实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=test_worker,
        num_processes=1,  # 单进程模式
        max_queue_size=10
    )
    
    try:
        # 启动处理
        print("启动单进程处理...")
        results = manager.run()  # 单进程模式会直接返回结果
        
        # 验证结果
        print("\n验证结果:")
        print(f"处理计数: {results.get('processed_count', 0)} / {len(test_data)}")
        print(f"结果数量: {len(results.get('results', []))}")
        
        # 检查是否所有任务都被处理
        processed_count = results.get('processed_count', 0)
        results_count = len(results.get('results', []))
        
        success = (processed_count == len(test_data) and 
                  results_count == len(test_data))
        
        print(f"单进程模式测试: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"单进程模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


def test_empty_data():
    """测试空数据场景"""
    print("\n" + "=" * 80)
    print("测试空数据场景")
    print("=" * 80)
    
    # 创建空数据
    test_data = []
    print(f"测试数据: {test_data}")
    
    # 创建实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=test_worker,
        num_processes=2,
        max_queue_size=10
    )
    
    try:
        # 启动处理
        print("启动空数据处理...")
        results = manager.run()
        
        # 验证结果
        print("\n验证结果:")
        print(f"返回结果: {results}")
        
        # 空数据场景应该正常处理
        success = True  # 只要不抛异常就算成功
        
        print(f"空数据场景测试: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except Exception as e:
        print(f"空数据场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


def main():
    """主测试函数"""
    print("开始 ManagedMultiProcess 综合功能测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("基础多进程功能", test_basic_functionality),
        ("单进程模式", test_single_process_mode),
        ("空数据场景", test_empty_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有综合功能测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
