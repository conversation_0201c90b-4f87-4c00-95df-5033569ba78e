# -*- coding: utf-8 -*-
"""
窗口操作相关的实用工具函数。
"""

import random
from typing import List, Tuple, Union

# 为了实现最优的几何计算，我们依赖 `shapely` 库。
# 请确保您已安装此库: pip install shapely
try:
    from shapely.geometry import Point, Polygon
    from shapely.affinity import scale
except ImportError:
    raise ImportError("此模块需要 'shapely' 库。请通过 'pip install shapely' 进行安装。")


def __normalize_polygon_coords(
    polygon_coords: List[Union[Tuple[float, float], float]]
) -> List[Tuple[float, float]]:
    """
    【内部辅助函数】标准化并验证多边形坐标。

    此函数将两种支持的坐标格式统一转换为 `[(x, y), ...]` 的元组列表格式，
    并执行基本的数据有效性检查。

    Args:
        polygon_coords: 原始坐标，支持元组列表或扁平数值列表。

    Raises:
        ValueError: 如果坐标格式无效（如扁平列表长度为奇数）或顶点数少于3。

    Returns:
        一个标准化的 `[(x, y), ...]` 格式的坐标列表。
    """
    if not polygon_coords:
        raise ValueError("输入的坐标列表不能为空。")

    normalized_coords: List[Tuple[float, float]]

    # 检查并转换格式
    if isinstance(polygon_coords[0], (int, float)):
        # 假定为 [x1, y1, x2, y2, ...] 格式
        if len(polygon_coords) % 2 != 0:
            raise ValueError("扁平坐标列表的长度必须是偶数。")
        it = iter(polygon_coords)
        normalized_coords = list(zip(it, it))
    elif isinstance(polygon_coords[0], (list, tuple)):
        # 假定为 [(x1, y1), ...] 或 [[x1, y1], ...] 格式
        normalized_coords = [tuple(p) for p in polygon_coords]
    else:
        raise ValueError("不支持的坐标格式。")

    # 校验顶点数量
    if len(normalized_coords) < 3:
        raise ValueError("一个多边形至少需要3个顶点。")

    return normalized_coords


def get_polygon_center(
    polygon_coords: List[Union[Tuple[float, float], float]],
    center_type: str = 'centroid'
) -> Union[Tuple[float, float], None]:
    """
    计算并返回多边形的中心点坐标。

    此函数利用 `shapely` 库的高性能几何计算能力，可以根据要求
    获取两种不同类型的中心点。

    Args:
        polygon_coords (List[Union[Tuple[float, float], float]]):
            多边形的顶点坐标列表。支持以下格式:
            1. 元组/列表的列表: `[(x1, y1), [x2, y2], ...]`
            2. 扁平数值列表: `[x1, y1, x2, y2, ...]`
        center_type (str, optional):
            定义计算中心点的模式。默认为 `'centroid'`。
            - `'centroid'`: 几何质心（质量中心）。对于凹多边形，
              质心可能位于多边形外部。
            - `'representative'`: 代表点。一个保证始终位于多边形
              内部的点，对于在复杂形状上放置标签等场景非常有用。

    Returns:
        Union[Tuple[float, float], None]:
            - 如果成功，返回一个 `(x, y)` 格式的中心点坐标元组。
            - 如果输入的坐标无法构成一个有效的多边形，则返回 `None`。

    Raises:
        ValueError: 如果 `center_type` 不是 `'centroid'` 或 `'representative'`，
                    或者输入的坐标数据无效。
    """
    # =========================================================================
    # 使用示例:
    #
    # # 准备一个常规多边形和一个C形凹多边形
    # regular_poly = [(0, 0), (10, 0), (10, 10), (0, 10)]
    # c_shape_poly = [(0, 0), (10, 0), (10, 2), (2, 2), (2, 8), (10, 8), (10, 10), (0, 10)]
    #
    # # 1. 获取常规多边形的质心 (centroid)
    # center_1 = get_polygon_center(regular_poly, center_type='centroid')
    # print(f"示例 1: 常规多边形的质心: {center_1}") # 预期: (5.0, 5.0)
    #
    # # 2. 获取常规多边形的代表点 (结果应与质心非常接近)
    # center_2 = get_polygon_center(regular_poly, center_type='representative')
    # print(f"示例 2: 常规多边形的代表点: {center_2}")
    #
    # # 3. 获取C形多边形的质心 (注意：质心可能在多边形外部)
    # center_3 = get_polygon_center(c_shape_poly, 'centroid')
    # print(f"示例 3: C形多边形的质心: {center_3}")
    #
    # # 4. 获取C形多边形的代表点 (保证在多边形内部)
    # center_4 = get_polygon_center(c_shape_poly, 'representative')
    # print(f"示例 4: C形多边形的代表点: {center_4}")
    #
    # # 5. 使用扁平列表作为输入
    # flat_poly = [0, 0, 10, 0, 10, 10, 0, 10]
    # center_5 = get_polygon_center(flat_poly)
    # print(f"示例 5: 使用扁平列表获取质心: {center_5}")
    #
    # # 6. 错误处理
    # try:
    #     get_polygon_center(regular_poly, center_type='invalid_type')
    # except ValueError as e:
    #     print(f"示例 6: 捕捉到错误 - {e}")
    # =========================================================================

    # --- 步骤 1: 参数校验 ---
    if center_type not in ['centroid', 'representative']:
        raise ValueError("参数 'center_type' 必须是 'centroid' 或 'representative'。")

    # --- 步骤 2: 标准化坐标 ---
    try:
        normalized_coords = __normalize_polygon_coords(polygon_coords)
    except ValueError as e:
        raise e  # 将底层错误直接抛出

    # --- 步骤 3: 创建多边形并计算中心 ---
    try:
        polygon = Polygon(normalized_coords)
        if not polygon.is_valid:
            # 对于无效多边形（如自相交），尝试用 buffer(0) 修复
            polygon = polygon.buffer(0)
        
        if polygon.is_empty or not isinstance(polygon, Polygon):
            return None  # 无法形成有效多边形

        if center_type == 'centroid':
            center_point = polygon.centroid
        else:  # representative
            center_point = polygon.representative_point()

        return (center_point.x, center_point.y)

    except Exception:
        # shapely 可能因坐标问题创建失败
        return None


def get_random_point_in_polygon(
    polygon_coords: List[Union[Tuple[float, float], float]],
    margin: float = 0.15
) -> Union[Tuple[float, float], None]:
    """
    在指定多边形内部生成一个随机点，该点与边缘保持一定的安全距离。

    此函数首先根据提供的 `margin` (边距)比例向内缩放原始多边形，
    创建一个"安全区域"。然后，它使用高效的拒绝采样法在这个
    内缩后的多边形中找到一个位置均匀分布的随机点。

    此方法依赖 `shapely` 库来处理复杂的几何运算，确保了结果的
    准确性和高性能。

    Args:
        polygon_coords (List[Union[Tuple[float, float], float]]):
            多边形的顶点坐标列表。支持两种常见的格式:
            1. 元组列表: `[(x1, y1), (x2, y2), (x3, y3), ...]`
            2. 扁平数值列表: `[x1, y1, x2, y2, x3, y3, ...]`
        margin (float, optional):
            内缩的安全边距比例，取值范围应在 `[0, 1)` 之间。
            例如，0.15 表示安全区域的边界距离原始多边形边界约15%的
            距离。默认为 0.15。

    Returns:
        Union[Tuple[float, float], None]:
            - 如果成功找到点，返回一个 `(x, y)` 格式的坐标元组。
            - 如果 `margin` 过大导致无法形成有效的内部安全区域，
              或者输入坐标无法构成有效多边形，则返回 `None`。

    Raises:
        ValueError: 如果 `margin` 参数不在 `[0, 1)` 的有效范围内，
                    或者输入的坐标点少于3个。

    """
    # =========================================================================
    # 使用示例:
    # 
    # # 准备一个多边形坐标
    # poly_coords_tuples = [(100, 50), (400, 50), (450, 250), (350, 350), (150, 300)]
    # poly_coords_flat = [100, 50, 400, 50, 450, 250, 350, 350, 150, 300]
    # 
    # # 1. 基本用法 (使用元组列表和默认边距)
    # random_point_1 = get_random_point_in_polygon(poly_coords_tuples)
    # if random_point_1:
    #     print(f"示例 1: 在默认边距(15%)内找到的随机点: {random_point_1}")
    # 
    # # 2. 自定义边距 (使用扁平列表和10%的边距)
    # random_point_2 = get_random_point_in_polygon(poly_coords_flat, margin=0.1)
    # if random_point_2:
    #     print(f"示例 2: 在10%边距内找到的随机点: {random_point_2}")
    # 
    # # 3. 无边距 (在整个多边形内随机)
    # random_point_3 = get_random_point_in_polygon(poly_coords_tuples, margin=0)
    # if random_point_3:
    #     print(f"示例 3: 在整个多边形内找到的随机点: {random_point_3}")
    #
    # # 4. 边距过大的情况 (可能返回 None)
    # random_point_4 = get_random_point_in_polygon(poly_coords_tuples, margin=0.4)
    # if random_point_4:
    #     print(f"示例 4: 在40%边距内找到的随机点: {random_point_4}")
    # else:
    #     print("示例 4: 边距过大，无法生成有效的内部多边形，未找到点。")
    #
    # # 5. 错误处理：无效的边距参数
    # try:
    #     get_random_point_in_polygon(poly_coords_tuples, margin=1.1)
    # except ValueError as e:
    #     print(f"示例 5: 捕捉到错误 - {e}")
    # =========================================================================

    # --- 步骤 1: 参数校验 ---
    if not 0 <= margin < 1:
        raise ValueError("参数 'margin' 必须在 [0, 1) 范围内。")

    # --- 步骤 2: 标准化输入坐标 ---
    try:
        normalized_coords = __normalize_polygon_coords(polygon_coords)
    except ValueError as e:
        raise e

    # --- 步骤 3: 创建多边形对象 ---
    try:
        original_polygon = Polygon(normalized_coords)
        if not original_polygon.is_valid:
            # 对于无效的多边形（如自相交），尝试用buffer(0)修复
            original_polygon = original_polygon.buffer(0)
            if not original_polygon.is_valid or original_polygon.is_empty:
                return None # 修复后仍然无效或为空
    except Exception:
        return None # 无法从坐标创建多边形

    # --- 步骤 4: 创建内缩的安全区域 ---
    if margin == 0:
        inner_polygon = original_polygon
    else:
        # 使用 buffer 操作创建一个向内收缩的、等距的内部多边形。
        # 这种方法比基于质心的缩放更稳健，尤其适用于不规则形状。
        
        # 1. 计算一个合适的绝对收缩距离
        min_x, min_y, max_x, max_y = original_polygon.bounds
        width = max_x - min_x
        height = max_y - min_y
        
        # 使用边界框的短边作为基准，以防止在细长多边形上过度收缩
        offset_distance = min(width, height) * margin

        # 2. 向内收缩
        inner_polygon = original_polygon.buffer(-offset_distance)

    # --- 步骤 5: 检查内缩结果并生成点 ---
    if inner_polygon.is_empty or not isinstance(inner_polygon, Polygon):
        # 如果边距过大导致多边形消失或变成线/点，则无法生成点
        return None

    min_x, min_y, max_x, max_y = inner_polygon.bounds

    # --- 步骤 6: 拒绝采样 ---
    # 在内缩多边形的边界框内随机生成点，直到找到一个在多边形内的点
    while True:
        random_point = Point(
            random.uniform(min_x, max_x),
            random.uniform(min_y, max_y)
        )
        # `contains` 是一个高度优化的操作
        if inner_polygon.contains(random_point):
            return (random_point.x, random_point.y)
