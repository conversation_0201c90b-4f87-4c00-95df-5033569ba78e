#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试 PROCESS_STOPPED_WITH_DATA 事件问题
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def simple_worker(shared_data_manager, item, *args, **kwargs):
    """简单的工作函数"""
    import time
    time.sleep(0.5)  # 模拟一些工作
    shared_data_manager.append_to_list("results", f"processed_{item}")
    return f"result_{item}"


def test_stopped_with_data_event():
    """测试 PROCESS_STOPPED_WITH_DATA 事件"""
    print("=" * 80)
    print("调试 PROCESS_STOPPED_WITH_DATA 事件")
    print("=" * 80)
    
    # 创建测试数据
    test_data = [f"item_{i}" for i in range(5)]
    print(f"测试数据: {test_data}")
    
    # 事件触发记录
    events_triggered = []
    
    def on_event(mp_instance, *args, **kwargs):
        event_name = kwargs.get('event_name', 'UNKNOWN')
        events_triggered.append(event_name)
        print(f"🎉 事件触发: {event_name}")
    
    # 创建 ManagedMultiProcess 实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=simple_worker,
        num_processes=2,
        max_queue_size=10
    )
    
    try:
        # 注册所有事件
        events_to_monitor = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
        ]
        
        for event in events_to_monitor:
            manager.listen_event(event, lambda mp, *a, **k: on_event(mp, event_name=event, *a, **k))
        
        print("\n启动多进程处理...")
        manager.run()
        
        print("等待一段时间后停止...")
        time.sleep(2.0)  # 让一些任务完成
        
        print("调用 stop_all()...")
        manager.stop_all(immediate=False)
        
        print("等待事件触发...")
        time.sleep(3.0)  # 等待事件触发
        
        print(f"\n触发的事件: {events_triggered}")
        
        # 检查结果
        results = manager.get_results()
        print(f"最终结果: {results}")
        
        # 检查是否触发了 PROCESS_STOPPED_WITH_DATA 事件
        if ProcessEventManager.PROCESS_STOPPED_WITH_DATA in [e for e in events_triggered]:
            print("✅ PROCESS_STOPPED_WITH_DATA 事件已触发")
        else:
            print("❌ PROCESS_STOPPED_WITH_DATA 事件未触发")
            print("可能的原因:")
            print("1. 数据队列未为空")
            print("2. 自动监控线程已停止")
            print("3. 事件触发条件不满足")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


if __name__ == "__main__":
    test_stopped_with_data_event()
