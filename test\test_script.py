import asyncio
import time
import threading
from global_tools.utils.enhanced_process import EnhancedProcess
import tqdm


def worker(shared_data_proxy, enqueue, *args, **kwargs):
    for i in range(5):
        enqueue.put(f"子进程数据: {i}")
    print("完成")


# 主进程异步消费回调
async def consume_cb(data):
    print(f"主进程消费到: {data}")
    await asyncio.sleep(1)


def main():
    print("===============测试开始===============", flush=True)

    enhanced_process = EnhancedProcess(target=worker, use_shared_queue_manager=True)
    enhanced_process.set_data_consume_callback(consume_cb)
    enhanced_process.start()
    enhanced_process.wait_for_completion()


if __name__ == "__main__":
    import multiprocessing
    multiprocessing.freeze_support()
    main()
