"""
PostgreSQL数据库日志管理模块

提供统一的日志记录功能，支持多种日志输出方式：
1. 控制台输出
2. 文件输出
3. 自定义处理器

支持配置日志级别、格式和过滤器，方便进行日志管理和问题排查。
"""

import os
import sys
import logging
import functools
import traceback
from typing import Optional, Dict, Any, Callable, Union, List

try:
    # 尝试导入彩色日志支持
    import colorama
    colorama.init()
    HAS_COLORAMA = True
except ImportError:
    HAS_COLORAMA = False


class LoggerManager:
    """日志管理器，提供统一的日志记录功能"""
    
    # 日志级别映射
    LEVEL_MAP = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    # 颜色映射
    if HAS_COLORAMA:
        from colorama import Fore, Style
        COLOR_MAP = {
            'DEBUG': Fore.CYAN,
            'INFO': Fore.GREEN,
            'WARNING': Fore.YELLOW,
            'ERROR': Fore.RED,
            'CRITICAL': Fore.RED + Style.BRIGHT,
            'RESET': Style.RESET_ALL
        }
    else:
        COLOR_MAP = {
            'DEBUG': '',
            'INFO': '',
            'WARNING': '',
            'ERROR': '',
            'CRITICAL': '',
            'RESET': ''
        }
    
    def __init__(
        self,
        name: str = "PostgreSQLClient",
        level: Union[str, int] = logging.INFO,
        format_str: Optional[str] = None,
        log_file: Optional[str] = None,
        use_colors: bool = True,
        log_sql: bool = False
    ):
        """
        初始化日志管理器
        
        Args:
            name: 日志记录器名称
            level: 日志级别，可以是字符串或整数
            format_str: 日志格式字符串，None表示使用默认格式
            log_file: 日志文件路径，None表示不输出到文件
            use_colors: 是否在控制台输出中使用颜色
            log_sql: 是否记录SQL语句
        """
        self.name = name
        self.log_sql = log_sql
        self.use_colors = use_colors and HAS_COLORAMA and sys.stdout.isatty()
        
        # 创建日志记录器
        self.logger = logging.getLogger(name)
        
        # 设置日志级别
        if isinstance(level, str):
            level = self.LEVEL_MAP.get(level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 清除现有处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 默认日志格式
        if format_str is None:
            if self.use_colors:
                format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            else:
                format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 设置格式化器
        if self.use_colors:
            formatter = self._get_colored_formatter(format_str)
        else:
            formatter = logging.Formatter(format_str)
        
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 添加文件处理器
        if log_file:
            try:
                # 确保日志目录存在
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                    
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(level)
                file_handler.setFormatter(logging.Formatter(format_str))
                self.logger.addHandler(file_handler)
            except Exception as e:
                self.logger.error(f"创建日志文件处理器失败: {str(e)}")
                
        # SQL日志过滤器
        if not self.log_sql:
            self.logger.addFilter(self._sql_filter)
    
    def _get_colored_formatter(self, format_str: str) -> logging.Formatter:
        """
        创建带颜色的日志格式化器
        
        Args:
            format_str: 日志格式字符串
            
        Returns:
            带颜色的日志格式化器
        """
        class ColoredFormatter(logging.Formatter):
            def format(self, record):
                levelname = record.levelname
                # 添加颜色
                if levelname in LoggerManager.COLOR_MAP:
                    record.levelname = f"{LoggerManager.COLOR_MAP[levelname]}{levelname}{LoggerManager.COLOR_MAP['RESET']}"
                    record.msg = f"{LoggerManager.COLOR_MAP[levelname]}{record.msg}{LoggerManager.COLOR_MAP['RESET']}"
                return super().format(record)
                
        return ColoredFormatter(format_str)
    
    def _sql_filter(self, record: logging.LogRecord) -> bool:
        """
        SQL日志过滤器，过滤掉SQL语句日志
        
        Args:
            record: 日志记录
            
        Returns:
            是否保留该日志记录
        """
        return not (hasattr(record, 'sql') or 'SQL:' in str(record.msg))
    
    def get_logger(self) -> logging.Logger:
        """
        获取日志记录器
        
        Returns:
            日志记录器
        """
        return self.logger
    
    def set_level(self, level: Union[str, int]) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别，可以是字符串或整数
        """
        if isinstance(level, str):
            level = self.LEVEL_MAP.get(level.upper(), logging.INFO)
            
        self.logger.setLevel(level)
        for handler in self.logger.handlers:
            handler.setLevel(level)
    
    def enable_sql_logging(self, enable: bool = True) -> None:
        """
        启用或禁用SQL日志记录
        
        Args:
            enable: 是否启用SQL日志记录
        """
        self.log_sql = enable
        
        # 移除现有过滤器
        for f in self.logger.filters[:]:
            if f == self._sql_filter:
                self.logger.removeFilter(f)
                
        # 如果禁用SQL日志，添加过滤器
        if not enable:
            self.logger.addFilter(self._sql_filter)
    
    def log_method_call(self, level: Union[str, int] = logging.DEBUG) -> Callable:
        """
        方法调用日志装饰器
        
        Args:
            level: 日志级别
            
        Returns:
            装饰器函数
        """
        if isinstance(level, str):
            level = self.LEVEL_MAP.get(level.upper(), logging.DEBUG)
            
        def decorator(func):
            @functools.wraps(func)
            def wrapper(self_or_cls, *args, **kwargs):
                # 获取日志记录器
                logger = getattr(self_or_cls, 'logger', self.logger)
                
                # 记录方法调用
                if logger.isEnabledFor(level):
                    # 格式化参数，避免过长
                    args_str = self._format_args(args)
                    kwargs_str = self._format_kwargs(kwargs)
                    logger.log(level, f"调用 {func.__name__}({args_str}{', ' if args_str and kwargs_str else ''}{kwargs_str})")
                
                # 执行方法
                try:
                    result = func(self_or_cls, *args, **kwargs)
                    return result
                except Exception as e:
                    # 记录异常
                    logger.error(f"{func.__name__} 执行失败: {str(e)}")
                    logger.debug(f"异常堆栈: {traceback.format_exc()}")
                    raise
                    
            return wrapper
        return decorator
    
    def _format_args(self, args: tuple) -> str:
        """
        格式化位置参数，避免过长
        
        Args:
            args: 位置参数元组
            
        Returns:
            格式化后的参数字符串
        """
        if not args:
            return ""
            
        args_str = []
        for arg in args:
            args_str.append(self._format_value(arg))
            
        return ", ".join(args_str)
    
    def _format_kwargs(self, kwargs: Dict[str, Any]) -> str:
        """
        格式化关键字参数，避免过长
        
        Args:
            kwargs: 关键字参数字典
            
        Returns:
            格式化后的参数字符串
        """
        if not kwargs:
            return ""
            
        kwargs_str = []
        for key, value in kwargs.items():
            kwargs_str.append(f"{key}={self._format_value(value)}")
            
        return ", ".join(kwargs_str)
    
    def _format_value(self, value: Any) -> str:
        """
        格式化值，避免过长
        
        Args:
            value: 要格式化的值
            
        Returns:
            格式化后的值字符串
        """
        # 处理None
        if value is None:
            return "None"
            
        # 处理字符串
        if isinstance(value, str):
            if len(value) > 50:
                return f"'{value[:47]}...'"
            return f"'{value}'"
            
        # 处理列表和元组
        if isinstance(value, (list, tuple)):
            if len(value) > 5:
                return f"{type(value).__name__}([{', '.join(self._format_value(v) for v in value[:3])}..., {len(value)-3} more])"
            return f"{type(value).__name__}({[self._format_value(v) for v in value]})"
            
        # 处理字典
        if isinstance(value, dict):
            if len(value) > 3:
                items = list(value.items())[:3]
                formatted_items = [f"{self._format_value(k)}: {self._format_value(v)}" for k, v in items]
                return f"{{{', '.join(formatted_items)}, ... {len(value)-3} more}}"
            return str(value)
            
        # 其他类型
        return str(value)


# 创建默认日志管理器
default_logger_manager = LoggerManager()
default_logger = default_logger_manager.get_logger()


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称，None表示使用默认名称
        
    Returns:
        日志记录器
    """
    if name:
        return logging.getLogger(name)
    return default_logger
    

def set_log_level(level: Union[str, int]) -> None:
    """
    设置默认日志记录器的日志级别
    
    Args:
        level: 日志级别，可以是字符串或整数
    """
    default_logger_manager.set_level(level)
    

def enable_sql_logging(enable: bool = True) -> None:
    """
    启用或禁用默认日志记录器的SQL日志记录
    
    Args:
        enable: 是否启用SQL日志记录
    """
    default_logger_manager.enable_sql_logging(enable)


def configure_logger(
    name: str = "PostgreSQLClient",
    level: Union[str, int] = logging.INFO,
    format_str: Optional[str] = None,
    log_file: Optional[str] = None,
    use_colors: bool = True,
    log_sql: bool = False
) -> logging.Logger:
    """
    配置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别，可以是字符串或整数
        format_str: 日志格式字符串，None表示使用默认格式
        log_file: 日志文件路径，None表示不输出到文件
        use_colors: 是否在控制台输出中使用颜色
        log_sql: 是否记录SQL语句
        
    Returns:
        配置好的日志记录器
    """
    global default_logger_manager, default_logger
    
    # 创建新的日志管理器
    logger_manager = LoggerManager(
        name=name,
        level=level,
        format_str=format_str,
        log_file=log_file,
        use_colors=use_colors,
        log_sql=log_sql
    )
    
    # 更新默认日志管理器和记录器
    default_logger_manager = logger_manager
    default_logger = logger_manager.get_logger()
    
    # ========== 关键修复：强制logger和所有handler的level为level，移除所有旧handler再重新添加 ==========
    logger = logging.getLogger(name)
    logger.setLevel(level)
    # 先移除所有handler，防止多次添加导致级别混乱
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    # 重新添加控制台handler
    if format_str is None:
        format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(logging.Formatter(format_str))
    logger.addHandler(console_handler)
    # 文件handler
    if log_file:
        try:
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(level)
            file_handler.setFormatter(logging.Formatter(format_str))
            logger.addHandler(file_handler)
        except Exception as e:
            logger.error(f"创建日志文件处理器失败: {str(e)}")
    # SQL日志过滤器
    if not log_sql:
        logger.addFilter(logger_manager._sql_filter)
    # 再次同步所有handler的level
    for handler in logger.handlers:
        handler.setLevel(level)
    return logger


def log_method_call(level: Union[str, int] = logging.DEBUG) -> Callable:
    """
    方法调用日志装饰器
    
    Args:
        level: 日志级别
        
    Returns:
        装饰器函数
    """
    return default_logger_manager.log_method_call(level)


# 导出日志级别常量
DEBUG = logging.DEBUG
INFO = logging.INFO
WARNING = logging.WARNING
ERROR = logging.ERROR
CRITICAL = logging.CRITICAL 