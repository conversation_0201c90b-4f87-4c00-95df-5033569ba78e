# VSCode Conda 环境自动激活配置说明

## 配置概述

本配置实现了在 VSCode 中每次打开 PowerShell 终端时自动激活 conda 环境的功能。

## 配置文件说明

### 1. `.vscode/settings.json`
- **功能**: VSCode 工作区设置
- **主要配置**:
  - `python.terminal.activateEnvironment: true` - 启用 Python 环境自动激活
  - `terminal.integrated.defaultProfile.windows: "PowerShell (YOLO)"` - 设置默认终端为带 YOLO 环境的 PowerShell
  - 定义了多个终端配置文件：
    - `PowerShell (YOLO)` - 自动激活 YOLO 环境
    - `PowerShell (Base)` - 自动激活 base 环境
    - `PowerShell` - 普通 PowerShell（不自动激活环境）

### 2. `conda_init.ps1`
- **功能**: Conda 环境初始化脚本
- **主要功能**:
  - 检查 conda 是否可用
  - 初始化 conda for PowerShell
  - 优先激活 YOLO 环境，失败时回退到 base 环境
  - 显示当前环境信息
  - 提供详细的错误处理和状态提示

## 使用方法

### 自动激活（推荐）
1. 在 VSCode 中按 `Ctrl + Shift + `` 打开新终端
2. 系统会自动运行初始化脚本并激活 YOLO 环境
3. 终端提示符会显示当前激活的环境名称

### 手动选择终端类型
1. 在 VSCode 中按 `Ctrl + Shift + P` 打开命令面板
2. 输入 "Terminal: Select Default Profile"
3. 选择以下选项之一：
   - `PowerShell (YOLO)` - 自动激活 YOLO 环境
   - `PowerShell (Base)` - 自动激活 base 环境
   - `PowerShell` - 普通 PowerShell

## 全局 Conda 配置

已设置 `conda config --set auto_activate_base true`，确保 conda 在所有新的 shell 会话中自动激活 base 环境。

## 故障排除

### 问题1: 脚本执行策略错误
**解决方案**: 配置中已包含 `-ExecutionPolicy Bypass` 参数

### 问题2: conda 命令未找到
**解决方案**: 
1. 确保 Anaconda/Miniconda 已正确安装
2. 检查 conda 是否在系统 PATH 中
3. 重启 VSCode

### 问题3: YOLO 环境不存在
**解决方案**: 脚本会自动回退到 base 环境

### 问题4: 环境激活失败
**解决方案**: 
1. 检查 conda 环境是否存在：`conda env list`
2. 手动激活测试：`conda activate YOLO`
3. 查看脚本输出的错误信息

## 自定义配置

### 更改默认环境
修改 `conda_init.ps1` 中的环境名称：
```powershell
conda activate YOUR_ENV_NAME
```

### 添加新的终端配置
在 `.vscode/settings.json` 的 `terminal.integrated.profiles.windows` 中添加新配置。

## 验证配置

1. 重启 VSCode
2. 打开新终端
3. 检查是否显示环境激活信息
4. 验证终端提示符是否显示环境名称（如 `(YOLO) PS>`）
