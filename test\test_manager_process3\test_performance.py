#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能测试：验证优化效果
"""

import sys
import os
import time
import threading
import psutil
import gc

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager


def simple_worker(shared_data_manager, item, *args, **kwargs):
    """简单的工作函数"""
    import time
    time.sleep(0.1)  # 模拟工作
    shared_data_manager.append_to_list("results", f"processed_{item}")
    return f"result_{item}"


def measure_cpu_usage(duration=5.0):
    """测量CPU使用率"""
    process = psutil.Process()
    cpu_percentages = []
    
    start_time = time.time()
    while time.time() - start_time < duration:
        cpu_percent = process.cpu_percent(interval=0.1)
        cpu_percentages.append(cpu_percent)
    
    return {
        'avg_cpu': sum(cpu_percentages) / len(cpu_percentages),
        'max_cpu': max(cpu_percentages),
        'min_cpu': min(cpu_percentages),
        'samples': len(cpu_percentages)
    }


def measure_memory_usage():
    """测量内存使用"""
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        'rss': memory_info.rss / 1024 / 1024,  # MB
        'vms': memory_info.vms / 1024 / 1024,  # MB
    }


def test_performance():
    """性能测试"""
    print("=" * 80)
    print("ManagedMultiProcess 性能测试")
    print("=" * 80)
    
    # 测试数据
    test_data = [f"item_{i}" for i in range(20)]
    print(f"测试数据量: {len(test_data)} 项")
    
    # 事件计数器
    events_triggered = []
    event_lock = threading.Lock()
    
    def on_event(event_name):
        def callback(mp_instance, *args, **kwargs):
            with event_lock:
                events_triggered.append((event_name, time.time()))
            print(f"🎉 事件触发: {event_name}")
        return callback
    
    # 创建 ManagedMultiProcess 实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=simple_worker,
        num_processes=3,
        max_queue_size=10
    )
    
    try:
        # 注册事件
        events_to_monitor = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
        ]
        
        for event in events_to_monitor:
            manager.listen_event(event, on_event(event))
        
        print("\n开始性能测试...")
        
        # 测量启动前的内存
        gc.collect()  # 强制垃圾回收
        memory_before = measure_memory_usage()
        print(f"启动前内存使用: RSS={memory_before['rss']:.2f}MB, VMS={memory_before['vms']:.2f}MB")
        
        # 启动处理
        start_time = time.time()
        manager.run()
        startup_time = time.time() - start_time
        print(f"启动时间: {startup_time:.3f}s")
        
        # 测量运行期间的CPU使用率
        print("测量运行期间CPU使用率...")
        cpu_stats = measure_cpu_usage(duration=3.0)
        print(f"CPU使用率 - 平均: {cpu_stats['avg_cpu']:.2f}%, 最大: {cpu_stats['max_cpu']:.2f}%, 最小: {cpu_stats['min_cpu']:.2f}%")
        
        # 等待完成
        print("等待任务完成...")
        time.sleep(5.0)
        
        # 测量完成后的内存
        memory_after = measure_memory_usage()
        print(f"完成后内存使用: RSS={memory_after['rss']:.2f}MB, VMS={memory_after['vms']:.2f}MB")
        print(f"内存增长: RSS={memory_after['rss'] - memory_before['rss']:.2f}MB")
        
        # 分析事件触发时间
        print(f"\n事件触发分析:")
        print(f"总触发事件数: {len(events_triggered)}")
        
        if len(events_triggered) >= 2:
            first_event_time = events_triggered[0][1]
            last_event_time = events_triggered[-1][1]
            total_event_time = last_event_time - first_event_time
            print(f"事件触发总时间: {total_event_time:.3f}s")
            
            for i, (event_name, event_time) in enumerate(events_triggered):
                relative_time = event_time - first_event_time
                print(f"  {i+1}. {event_name}: +{relative_time:.3f}s")
        
        # 获取最终结果
        results = manager.get_results()
        print(f"\n处理结果:")
        print(f"成功处理项目数: {len(results)}")
        print(f"预期项目数: {len(test_data)}")
        print(f"处理成功率: {len(results)/len(test_data)*100:.1f}%")
        
        # 性能总结
        print(f"\n性能总结:")
        print(f"✅ 启动时间: {startup_time:.3f}s")
        print(f"✅ 平均CPU使用率: {cpu_stats['avg_cpu']:.2f}%")
        print(f"✅ 内存增长: {memory_after['rss'] - memory_before['rss']:.2f}MB")
        print(f"✅ 事件响应性: {len(events_triggered)} 个事件正确触发")
        
        # 性能评估
        if cpu_stats['avg_cpu'] < 10.0:
            print("🎉 CPU使用率优秀 (<10%)")
        elif cpu_stats['avg_cpu'] < 20.0:
            print("✅ CPU使用率良好 (<20%)")
        else:
            print("⚠️ CPU使用率较高 (>20%)")
        
        if memory_after['rss'] - memory_before['rss'] < 50:
            print("🎉 内存使用优秀 (<50MB)")
        elif memory_after['rss'] - memory_before['rss'] < 100:
            print("✅ 内存使用良好 (<100MB)")
        else:
            print("⚠️ 内存使用较高 (>100MB)")
        
    except Exception as e:
        print(f"性能测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


if __name__ == "__main__":
    test_performance()
