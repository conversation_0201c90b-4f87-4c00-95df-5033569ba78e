from global_tools.utils import ClassInstanceManager, Logger, LogLevel


ClassInstanceManager.create_instance( key = "ui_logger", class_obj = Logger )
logger: Logger = ClassInstanceManager.get_instance( key = "ui_logger" )    # type: ignore
logger.set_instance_level( LogLevel.OFF )
# 我的
from .helper import (
    # ManagerWorkerThread,
    # WorkerThread,
    # LineEditControl,
    # CheckBoxManager,
    # ProgressBarManager,
    LogOutput,
    # SignalSlotManager,
    # SignalSlotThread,
    # QPushButtonHelper,
    NestedScrollAreaFixer,
    # LabelManager,
)
from .compnonent.input_completer import InputCompleter, InputCompleterCache    # 输入自动补全组件
from .compnonent.compnonent import ButtonPressEffectEnhancer    # 按钮按下效果增强器
from .compnonent.qpush_button import QPushButtonManager    # 按钮管理器
from .compnonent.qlabel import QLabelManager    # 标签管理器
from .compnonent.qline_edit import LineEditManager, QLineEditSynchronizer    # 输入框管理器
from .compnonent.qprogress_bar import QProgressBarHelper    # 进度条管理器
from .compnonent.line_edit import LineEditMemory    # 输入框记忆管理器
from .compnonent.qcheckbox import CheckBoxManager, CheckBoxStateManager, SelectAllCheckBoxManager, QCheckBoxSynchronizer
from .compnonent.qtext_edit import TextEditMemory, TextEditManager    # 文本框记忆管理器

import global_tools.ui_tools.compnonent.constant as constant
from .compnonent.common import get_widgets_from_layout


__all__ = [
    # ------- 线程管理 -------
    # "ManagerWorkerThread",     # 类: 管理多个工作线程的控制器，提供统一的线程创建、启动和监控功能
    # "WorkerThread",            # 类: 基础工作线程类，可在后台执行耗时任务，避免UI阻塞
    # "SignalSlotThread",        # 类: 支持Qt信号槽机制的线程类，用于在线程与UI之间安全通信

    # ------- UI控件管理 -------
    # "LineEditControl",         # 类: 文本输入框控制器，提供验证、自动格式化、状态管理等功能
    "CheckBoxManager",    # 类: 复选框管理器，支持批量操作、状态同步和联动控制
    # "ProgressBarManager",      # 类: 进度条管理器，简化进度更新、动画效果和状态显示
    # "QPushButtonHelper",       # 类: 按钮辅助工具，提供动态启用/禁用、样式切换和批量控制功能
    # "LabelManager",            # 类: 标签管理器，用于动态更新、格式化和批量控制标签显示
    "ButtonPressEffectEnhancer",    # 类: 按钮按下效果增强器，提供按钮按下效果增强功能
    "QPushButtonManager",    # 类: 按钮管理器，支持批量操作、状态同步和联动控制
    "QLabelManager",    # 类: 标签管理器，用于动态更新、格式化和批量控制标签显示
    "LineEditManager",    # 类: 输入框管理器，支持批量操作、状态同步和联动控制
    "QProgressBarHelper",    # 类: 进度条管理器，支持批量操作、状态同步和联动控制
    "CheckBoxManager",    # 管理多个容器中的复选框，符合 PyQt5 信号槽规范
    "CheckBoxStateManager",    # 管理PyQt5容器中QCheckBox控件的状态，支持状态持久化和恢复。
    "SelectAllCheckBoxManager",    # 管理一个或多个容器内 "全选/取消全选" 复选框与其余 "子项" 复选框之间的联动逻辑。
    "get_widgets_from_layout",    # 获取布局中的所有控件
    "TextEditMemory",    # 类: 文本框记忆管理器，提供文本框记忆功能
    "QLineEditSynchronizer",    # 类: 输入框同步管理器，提供输入框同步功能
    "QCheckBoxSynchronizer",    # 类: 复选框同步管理器，提供复选框同步功能
    "TextEditManager",    # 类: 多行文本框批量管理器，提供多行文本框批量操作功能

    # ------- 高级UI组件 -------
    "InputCompleter",    # 类: 输入自动补全组件，提供智能提示和历史记录功能
    "InputCompleterCache",    # 类: 输入自动补全缓存管理器，提供历史记录持久化、自动补全和缓存管理
    "NestedScrollAreaFixer",    # 类: 嵌套滚动区域修复工具，解决Qt中嵌套滚动区域的滚动事件传播问题
    "LineEditMemory",    # 类: 输入框记忆管理器，提供输入框记忆功能
    "constant",    # 常量: 包含UI组件的常量定义

    # ------- 信号和日志工具 -------
    # "SignalSlotManager",       # 类: 信号槽管理器，简化Qt信号槽的连接、断开和管理过程
    "LogOutput",    # 类: 日志输出工具，将日志信息重定向到UI组件，支持格式化和过滤
]
