import os
import sys
import time
import logging
import numpy as np
import cv2
import ctypes
from ctypes import wintypes
from ctypes import POINTER, byref, windll, Structure, WINFUNCTYPE, c_void_p, c_int, c_uint, c_bool, c_float
from typing import Tuple, Optional, Union, Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WindowsGraphicsCapture")

# Windows API constants
user32 = ctypes.windll.user32
gdi32 = ctypes.windll.gdi32
kernel32 = ctypes.windll.kernel32

# Windows API constants for multi-monitor support
SM_CXVIRTUALSCREEN = 78  # Width of virtual screen
SM_CYVIRTUALSCREEN = 79  # Height of virtual screen
SM_XVIRTUALSCREEN = 76   # Left coordinate of virtual screen
SM_YVIRTUALSCREEN = 77   # Top coordinate of virtual screen

# Define necessary Windows API structures
class RECT(Structure):
    _fields_ = [("left", c_int),
                ("top", c_int),
                ("right", c_int),
                ("bottom", c_int)]

class MONITORINFO(Structure):
    _fields_ = [
        ("cbSize", c_uint),
        ("rcMonitor", RECT),
        ("rcWork", RECT),
        ("dwFlags", c_uint)
    ]

class WindowsGraphicsCapture:
    """
    A high-performance screenshot capture class that uses DXcam for screen capture.
    This class provides methods to capture screenshots from specified window handles
    with coordinates relative to the window's top-left corner.
    
    DXcam utilizes the Desktop Duplication API, which provides high-performance 
    screen capture capabilities in Windows.
    
    Attributes:
        __dpi_scale (float): The current DPI scaling factor.
        __last_capture_time (float): Timestamp of the last capture operation.
    """

    def __init__(self):
        """
        Initialize the WindowsGraphicsCapture class.
        Sets up DXcam for screen capture.
        """
        self.__dpi_scale = self.__get_dpi_scale()
        self.__last_capture_time = 0.0
        self.__dxcam_instance = None
        self.__initialize_dxcam()
        logger.info("WindowsGraphicsCapture initialized with DXcam backend")

    def __initialize_dxcam(self) -> None:
        """
        Initialize DXcam for screen capture.
        """
        try:
            # Import dxcam dynamically to avoid direct dependency
            import importlib.util
            spec = importlib.util.find_spec("dxcam")
            if spec is not None:
                dxcam = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(dxcam)
                self.__dxcam_instance = dxcam.create()
                logger.info("DXcam initialized successfully")
            else:
                raise ImportError("DXcam module not found")
        except ImportError as e:
            logger.error(f"Error importing DXcam: {str(e)}")
            logger.error("Please install DXcam using: pip install dxcam")
            raise ImportError("DXcam is required for WindowsGraphicsCapture")

    def __get_dpi_scale(self) -> float:
        """
        Gets the current DPI scaling factor.

        Returns:
            float: The DPI scaling factor.
        """
        try:
            # Check if Windows 8.1 or later
            if hasattr(windll, "shcore"):
                awareness = ctypes.c_int()
                windll.shcore.GetProcessDpiAwareness(0, ctypes.byref(awareness))
                
                # If the process is DPI aware, get the DPI scaling
                if awareness.value != 0:  # 0 means DPI unaware
                    dpi = ctypes.c_uint()
                    windll.shcore.GetDpiForSystem(ctypes.byref(dpi))
                    return dpi.value / 96.0
            return 1.0
        except Exception:
            # Fallback to default scale if unable to determine
            return 1.0

    def __get_window_rect(self, hwnd: int) -> Tuple[int, int, int, int]:
        """
        Gets the rectangle coordinates of a window.

        Args:
            hwnd (int): The window handle.

        Returns:
            Tuple[int, int, int, int]: A tuple containing (left, top, right, bottom) coordinates.
        """
        rect = RECT()
        if not user32.GetClientRect(hwnd, byref(rect)):
            raise ctypes.WinError()
        
        # Convert client coordinates to screen coordinates
        pt = wintypes.POINT(rect.left, rect.top)
        user32.ClientToScreen(hwnd, byref(pt))
        rect.left, rect.top = pt.x, pt.y
        
        pt = wintypes.POINT(rect.right, rect.bottom)
        user32.ClientToScreen(hwnd, byref(pt))
        rect.right, rect.bottom = pt.x, pt.y
        
        return (rect.left, rect.top, rect.right, rect.bottom)

    def capture(self, 
                hwnd: int, 
                region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Captures a screenshot from the specified window handle using DXcam.
        
        Args:
            hwnd (int): The window handle to capture.
            region (Optional[Tuple[int, int, int, int]]): Optional region to capture (x, y, right, bottom)
                                                         relative to window's top-left corner.

        Returns:
            np.ndarray: Captured image as a numpy array in BGR format (OpenCV default).
            
        Example:
            ```python
            # Create a WindowsGraphicsCapture instance
            capture = WindowsGraphicsCapture()
            
            # Get a window handle (example using window title)
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("Notepad")
            
            # Capture the entire window
            img = capture.capture(hwnd)
            
            # Capture a specific region (100x100 pixels from top-left)
            region_img = capture.capture(hwnd, region=(0, 0, 100, 100))
            
            # Display the captured image
            cv2.imshow("Captured", img)
            cv2.waitKey(0)
            ```
        """
        start_time = time.time()
        
        # Check if DXcam is available
        if self.__dxcam_instance is None:
            raise RuntimeError("DXcam is not initialized")
        
        # Check if the window handle is valid
        if not user32.IsWindow(hwnd):
            raise ValueError(f"Invalid window handle: {hwnd}")
        
        try:
            # Get window coordinates
            window_rect = self.__get_window_rect(hwnd)
            
            if region is None:
                # Capture the entire window
                left, top, right, bottom = window_rect
            else:
                # Calculate absolute screen coordinates based on window-relative region
                left = window_rect[0] + region[0]
                top = window_rect[1] + region[1]
                right = window_rect[0] + region[2] 
                bottom = window_rect[1] + region[3]
            
            # DXcam uses (left, top, right, bottom) format for region
            dxcam_region = (left, top, right, bottom)
            
            # Perform the capture using DXcam
            frame = self.__dxcam_instance.grab(region=dxcam_region)
            
            if frame is None:
                raise RuntimeError("Failed to capture frame with DXcam")
            
            # Record metrics
            elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self.__last_capture_time = elapsed_time
            
            # Log performance data
            logger.debug(f"Capture completed in {elapsed_time:.2f} ms using DXcam")
            
            # Convert from BGR to RGB for correct color display
            # DXcam returns BGR format, but we need RGB for proper color display
            if frame.shape[2] >= 3:  # Check if we have enough color channels
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            return frame
            
        except Exception as e:
            logger.error(f"Error during capture with DXcam: {str(e)}")
            raise

    def get_last_capture_time(self) -> float:
        """
        Returns the time in milliseconds that the last capture operation took.
        
        Returns:
            float: Time in milliseconds of the last capture operation.
            
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            print(f"Capture took {capture.get_last_capture_time():.2f} ms")
            ```
        """
        return self.__last_capture_time
    
    @staticmethod
    def get_window_handle_by_title(title: str) -> int:
        """
        Gets a window handle by its title.
        
        Args:
            title (str): The window title to search for.
            
        Returns:
            int: Window handle if found, 0 otherwise.
            
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("Notepad")
            if hwnd:
                img = capture.capture(hwnd)
            ```
        """
        return user32.FindWindowW(None, title)
    
    @staticmethod
    def list_windows() -> List[Dict[str, Any]]:
        """
        Lists all visible windows in the system.
        
        Returns:
            List[Dict[str, Any]]: List of dictionaries containing window information.
            
        Example:
            ```python
            windows = WindowsGraphicsCapture.list_windows()
            for window in windows:
                print(f"Window: {window['title']} (Handle: {window['hwnd']})")
            ```
        """
        windows = []
        
        def enum_windows_proc(hwnd, lparam):
            if user32.IsWindowVisible(hwnd):
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    rect = RECT()
                    user32.GetWindowRect(hwnd, byref(rect))
                    
                    windows.append({
                        "hwnd": hwnd,
                        "title": buffer.value,
                        "rect": (rect.left, rect.top, rect.right, rect.bottom),
                        "width": rect.right - rect.left,
                        "height": rect.bottom - rect.top
                    })
            return True
        
        enum_windows_proc_type = WINFUNCTYPE(c_bool, c_int, c_int)
        enum_windows_proc_callback = enum_windows_proc_type(enum_windows_proc)
        user32.EnumWindows(enum_windows_proc_callback, 0)
        
        return windows
    
    def release(self) -> None:
        """
        Releases resources used by the capture instance.
        
        Example:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            capture.release()
            ```
        """
        if self.__dxcam_instance is not None:
            if hasattr(self.__dxcam_instance, 'release'):
                self.__dxcam_instance.release()
            
            self.__dxcam_instance = None
            
        logger.info("Resources released")
