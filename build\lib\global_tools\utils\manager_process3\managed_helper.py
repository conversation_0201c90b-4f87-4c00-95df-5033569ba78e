"""
进程管理辅助模块

此模块提供了用于监控和管理多进程的辅助工具，主要功能包括：
1. 进程状态监控
2. 进程状态摘要生成
3. 监控线程的启动和停止

核心组件是 ProcessMonitor 类，它封装了所有进程监控相关的功能，方便集中管理。
"""

import time
import threading
import traceback
import logging
from typing import Dict, List, Any, Optional
from multiprocessing import Process
from global_tools.utils import Logger, LogLevel, ClassInstanceManager

# 日志
logger: Logger = ClassInstanceManager.get_instance(key="Logger")


class ProcessMonitor:
    """
    进程监控器类，封装了进程状态监控的所有功能。

    此类提供了监控工作进程状态、获取状态摘要、启动和停止监控线程等功能。
    它使用后台线程持续检查进程状态，并在状态变化时记录详细信息。
    现在支持在所有进程完成时执行回调函数，无论进程是正常完成还是异常终止。

    使用示例:
    ---------
    ```python
    # 1. 基本用法 - 作为独立组件使用
    from multiprocessing import Process
    import time

    # 创建一些测试进程
    def worker_func(sleep_time):
        time.sleep(sleep_time)

    processes = [Process(target=worker_func, args=(i,)) for i in range(1, 4)]
    for p in processes:
        p.start()

    # 创建进程监控器并启动监控
    monitor = ProcessMonitor()
    monitor.start_process_monitor(processes)

    # 等待一段时间，让进程完成
    time.sleep(5)

    # 获取进程状态摘要
    status_summary = monitor.get_process_status_summary()
    print(f"进程状态统计: {status_summary['summary']}")

    # 停止监控
    monitor.stop_process_monitor()

    # 2. 使用回调函数 - 在所有进程完成时通知
    def on_all_completed(monitor):
        print("所有进程已完成!")
        status = monitor.get_process_status_summary()
        print(f"最终状态: {status['summary']}")

    # 创建带回调的监控器
    monitor = ProcessMonitor(completion_callback=on_all_completed)
    monitor.start_process_monitor(processes)

    # 主线程可以做其他工作
    # ...
    # 回调会在后台线程中执行，不会阻塞主线程

    # 可以通过以下方法手动设置或清除回调
    monitor.set_completion_callback(lambda m: print("新回调"))
    # 或者
    monitor.set_completion_callback(None)  # 清除回调

    # 3. 与ManagedMultiProcess结合使用
    from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess

    # 定义工作函数
    def process_task(shared_manager, task_item):
        # 处理任务...
        time.sleep(1)

    # 创建任务列表
    tasks = ["task1", "task2", "task3"]

    # 创建多进程管理器
    mp = ManagedMultiProcess(
        input_data=tasks,
        callback_func=process_task,
        num_processes=2
    )

    # 启动处理
    mp.run()

    # 内部使用ProcessMonitor监控进程
    # 获取进程状态摘要
    status_summary = mp.get_process_status_summary()
    print(f"进程状态统计: {status_summary['summary']}")

    # 等待完成并检查监控结果
    mp.wait_all()
    final_summary = mp.get_process_status_summary()
    print(f"最终状态: {final_summary['summary']}")

    # 清理资源
    mp.stop_all()
    ```

    属性:
        PROCESS_STATUS_UNKNOWN (str): 未知状态标识
        PROCESS_STATUS_RUNNING (str): 运行中状态标识
        PROCESS_STATUS_COMPLETED (str): 正常完成状态标识
        PROCESS_STATUS_STOPPED (str): 已停止状态标识
        PROCESS_STATUS_FAILED (str): 失败状态标识
        PROCESS_STATUS_TERMINATED (str): 被终止状态标识
        PROCESS_STATUS_FORCED_TERMINATED (str): 强制终止状态标识
        PROCESS_STATUS_GRACEFUL_STOPPED (str): 优雅停止状态标识
    """

    # 进程状态常量
    PROCESS_STATUS_UNKNOWN = "未知"
    PROCESS_STATUS_RUNNING = "运行中"
    PROCESS_STATUS_COMPLETED = "已完成"
    PROCESS_STATUS_STOPPED = "已停止"
    PROCESS_STATUS_FAILED = "失败"
    PROCESS_STATUS_TERMINATED = "被终止"
    # 新增进程状态常量，用于区分不同的进程停止方式
    PROCESS_STATUS_FORCED_TERMINATED = "强制终止"  # 进程被强制终止（如通过SIGKILL）
    PROCESS_STATUS_GRACEFUL_STOPPED = "优雅停止"   # 进程收到停止信号后主动退出

    def __init__(self, completion_callback=None):
        """
        初始化进程监控器。

        创建必要的内部属性用于跟踪和管理进程状态监控。

        使用示例:
        ```python
        # 1. 基本用法 - 不带回调函数
        monitor = ProcessMonitor()

        # 2. 使用回调函数 - 在所有进程完成时通知
        def on_all_processes_completed(monitor):
            print("所有进程已完成!")
            status = monitor.get_process_status_summary()
            print(f"进程状态统计: {status['summary']}")

        monitor = ProcessMonitor(completion_callback=on_all_processes_completed)

        # 3. 启动监控并等待回调执行
        monitor.start_process_monitor(processes)
        # 回调会在所有进程完成时自动执行
        ```

        Args:
            completion_callback: 可选的回调函数，在所有被监控进程完成时执行。
                                回调函数接收当前ProcessMonitor实例作为参数。
        """

        # 进程状态追踪字典 {pid: {status, exit_code, start_time, end_time, process_name, duration, stop_method}}
        self.__process_status_tracker = {}

        # 用于同步访问状态追踪器的锁
        self.__process_monitor_lock = threading.RLock()

        # 控制监控线程运行的事件
        self.__process_monitor_running = threading.Event()

        # 监控线程对象
        self.__process_monitor_thread = None

        # 回调函数相关
        self.__completion_callback = completion_callback
        self.__callback_lock = threading.Lock()
        self.__all_processes_completed = False  # 标记是否已经触发过回调

        # 进程终止方式记录字典 {pid: stop_method}
        # stop_method 可以是 "normal"(正常退出), "signal"(收到信号优雅退出), "force"(强制终止)
        self.__process_stop_methods = {}

        logger.debug("ProcessMonitor 实例已初始化")

    def monitor_worker_processes(self, worker_processes: List[Process]) -> None:
        """
        异步监控工作进程的状态变化。

        此方法在单独的线程中运行，持续检查工作进程的状态，无论它们是正常完成还是异常终止。
        对每个工作进程的状态变化进行记录和标记，便于后续分析和处理。
        当所有进程都完成时，会调用完成回调函数（如果已设置）。

        具体功能：
        1. 持续检查所有工作进程的状态
        2. 记录进程完成时间和退出码
        3. 标记进程状态（运行中、已完成、已停止、异常终止等）
        4. 线程安全地更新进程状态追踪器
        5. 定期输出监控日志
        6. 在所有进程完成时执行回调函数

        Args:
            worker_processes: 工作进程列表

        Returns:
            None
        """
        logger.debug("进程状态监控线程已启动")

        # 用于控制监控频率的参数
        min_interval = 0.1   # 最小检查间隔（秒）
        max_interval = 0.5   # 最大检查间隔（秒）
        current_interval = min_interval
        start_time = time.time()
        last_log_time = start_time
        log_interval = 2.0   # 状态日志输出间隔（秒）

        try:
            # 确保进程列表有效
            if not isinstance(worker_processes, list):
                logger.warning("工作进程列表无效，监控线程退出")
                # 即使进程列表无效，也检查是否应该执行回调
                self.__execute_completion_callback()
                return

            # 初始记录所有活动进程
            with self.__process_monitor_lock:
                for p in worker_processes:
                    if isinstance(p, Process) and p.is_alive():
                        pid = p.pid
                        self.__process_status_tracker[pid] = {
                            "status": self.PROCESS_STATUS_RUNNING,
                            "exit_code": None,
                            "start_time": time.time(),
                            "end_time": None,
                            "process_name": p.name,
                            "duration": 0.0,
                            "stop_method": None
                        }
                        logger.debug(f"初始记录进程 {pid} ({p.name}) 状态为运行中")

            # 持续监控循环
            while self.__process_monitor_running.is_set():
                alive_processes = []
                current_time = time.time()

                # 遍历所有工作进程检查状态
                for p in worker_processes[:]:  # 使用副本避免迭代过程中列表变化
                    try:
                        if not isinstance(p, Process):
                            continue

                        pid = p.pid
                        # 检查进程是否在跟踪器中
                        if pid not in self.__process_status_tracker:
                            with self.__process_monitor_lock:
                                self.__process_status_tracker[pid] = {
                                    "status": self.PROCESS_STATUS_UNKNOWN,
                                    "exit_code": None,
                                    "start_time": time.time(),
                                    "end_time": None,
                                    "process_name": p.name,
                                    "duration": 0.0,
                                    "stop_method": None
                                }

                        # 检查进程状态变化
                        if p.is_alive():
                            alive_processes.append(p)
                            # 更新运行时长
                            with self.__process_monitor_lock:
                                if self.__process_status_tracker[pid]["status"] != self.PROCESS_STATUS_RUNNING:
                                    self.__process_status_tracker[pid]["status"] = self.PROCESS_STATUS_RUNNING
                                    logger.debug(f"进程 {pid} ({p.name}) 状态更新为运行中")
                                self.__process_status_tracker[pid]["duration"] = current_time - self.__process_status_tracker[pid]["start_time"]
                        else:
                            # 进程已结束，记录状态
                            with self.__process_monitor_lock:
                                previous_status = self.__process_status_tracker[pid]["status"]
                                exit_code = p.exitcode

                                # 根据退出码确定进程状态
                                if exit_code == 0:
                                    new_status = self.PROCESS_STATUS_COMPLETED
                                    stop_method = "normal"
                                elif exit_code is not None and exit_code < 0:
                                    new_status = self.PROCESS_STATUS_TERMINATED  # 负值通常表示进程被信号终止
                                    stop_method = "signal"
                                else:
                                    new_status = self.PROCESS_STATUS_FAILED      # 正值表示进程异常退出
                                    stop_method = "force"

                                # 只有状态变化时才更新和记录日志
                                if previous_status != new_status:
                                    self.__process_status_tracker[pid]["status"] = new_status
                                    self.__process_status_tracker[pid]["exit_code"] = exit_code
                                    self.__process_status_tracker[pid]["end_time"] = current_time
                                    self.__process_status_tracker[pid]["duration"] = current_time - self.__process_status_tracker[pid]["start_time"]
                                    self.__process_status_tracker[pid]["stop_method"] = stop_method

                                    logger.info(f"进程 {pid} ({p.name}) 状态从 {previous_status} 变为 {new_status}，退出码: {exit_code}，"
                                                f"停止方式: {stop_method}，运行时长: {self.__process_status_tracker[pid]['duration']:.2f}秒")

                    except Exception as e:
                        logger.error(f"监控进程 {getattr(p, 'pid', 'unknown')} 状态时出错: {e}")
                        logger.error(traceback.format_exc())

                # 定期输出监控状态日志
                if current_time - last_log_time >= log_interval:
                    with self.__process_monitor_lock:
                        running = sum(1 for info in self.__process_status_tracker.values() if info["status"] == self.PROCESS_STATUS_RUNNING)
                        completed = sum(1 for info in self.__process_status_tracker.values() if info["status"] == self.PROCESS_STATUS_COMPLETED)
                        failed = sum(1 for info in self.__process_status_tracker.values() if info["status"] == self.PROCESS_STATUS_FAILED)
                        terminated = sum(1 for info in self.__process_status_tracker.values() if info["status"] == self.PROCESS_STATUS_TERMINATED)

                        logger.debug(f"进程监控状态 - 运行中: {running}, 已完成: {completed}, 失败: {failed}, 被终止: {terminated}, "
                                     f"监控时长: {current_time - start_time:.2f}秒")
                    last_log_time = current_time

                # 动态调整检查间隔，减少CPU占用
                if current_time - start_time > 5.0:  # 5秒后开始延长间隔
                    factor = min(1.0, (current_time - start_time) / 30.0)  # 30秒后达到最大间隔
                    current_interval = min_interval + factor * (max_interval - min_interval)

                # 如果没有活动进程，可以提前退出并检查是否应执行回调
                if not alive_processes:
                    logger.info("所有进程已结束，检查是否需要执行完成回调")
                    self.__execute_completion_callback()
                    logger.info("监控线程即将退出")
                    break

                # 短暂休眠后继续检查
                time.sleep(current_interval)

        except Exception as e:
            logger.error(f"进程监控线程发生异常: {e}")
            logger.error(traceback.format_exc())
        finally:
            # 确保在任何情况下都检查是否应该执行回调
            self.__execute_completion_callback()
            logger.debug("进程状态监控线程已结束")

    def get_process_status_summary(self) -> Dict[str, Any]:
        """
        获取所有工作进程的状态摘要。

        此方法提供一个安全的方式来访问进程监控器收集的状态信息，返回一个包含
        进程状态统计和详细信息的字典。该方法是线程安全的，可以从任何线程调用。

        使用示例:
        ```python
        # 1. 基本用法 - 获取状态摘要并分析
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 等待一段时间后获取状态
        time.sleep(2)
        status = monitor.get_process_status_summary()

        # 分析状态摘要
        print(f"进程状态统计: {status['summary']}")
        print(f"监控是否活动: {status['monitoring_active']}")

        # 分析每个进程的详细状态
        for pid, info in status['processes'].items():
            print(f"进程 {pid}: 状态={info['status']}, 退出码={info['exit_code']}")
            if info['stop_method']:
                print(f"  终止方式: {info['stop_method']}")
            if info['end_time']:
                duration = info['end_time'] - info['start_time']
                print(f"  运行时长: {duration:.2f}秒")

        # 2. 与ManagedMultiProcess结合使用
        mp = ManagedMultiProcess(tasks, process_task, 3)
        mp.run()

        # 周期性检查进程状态
        while mp.is_running():
            status = mp.get_process_status_summary()
            running = status['summary'][ProcessMonitor.PROCESS_STATUS_RUNNING]
            completed = status['summary'][ProcessMonitor.PROCESS_STATUS_COMPLETED]
            print(f"运行中: {running}, 已完成: {completed}")
            time.sleep(1)

        # 分析最终状态
        final_status = mp.get_process_status_summary()
        failed = final_status['summary'][ProcessMonitor.PROCESS_STATUS_FAILED]
        if failed > 0:
            print(f"警告: {failed}个进程执行失败")

            # 查看失败进程的详细信息
            for pid, info in final_status['processes'].items():
                if info['status'] == ProcessMonitor.PROCESS_STATUS_FAILED:
                    print(f"进程 {pid} ({info['process_name']}) 失败，退出码: {info['exit_code']}")
                    if info['stop_method']:
                        print(f"  终止方式: {info['stop_method']}")

        # 3. 查看强制终止的进程
        forced_terminated = final_status['summary'].get(ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED, 0)
        if forced_terminated > 0:
            print(f"有 {forced_terminated} 个进程被强制终止")
            # 查看这些进程的详细信息
            for pid, info in final_status['processes'].items():
                if info['status'] == ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED:
                    print(f"进程 {pid} ({info['process_name']}) 被强制终止，运行时长: {info['duration']:.2f}秒")

        # 4. 分析进程终止方式
        stop_methods = final_status['stop_methods_summary']
        print(f"正常完成: {stop_methods['normal']}个进程")
        print(f"信号终止: {stop_methods['signal']}个进程")
        print(f"强制终止: {stop_methods['force']}个进程")
        ```

        Returns:
            Dict[str, Any]: 包含进程状态摘要的字典，具有以下结构：
                {
                    "summary": {
                        # 各种状态的进程数量计数
                        "运行中": 0,       # PROCESS_STATUS_RUNNING 状态的进程数
                        "已完成": 0,       # PROCESS_STATUS_COMPLETED 状态的进程数
                        "失败": 0,         # PROCESS_STATUS_FAILED 状态的进程数 (进程非零退出码)
                        "被终止": 0,       # PROCESS_STATUS_TERMINATED 状态的进程数
                        "已停止": 0,       # PROCESS_STATUS_STOPPED 状态的进程数
                        "未知": 0,         # PROCESS_STATUS_UNKNOWN 状态的进程数
                        "强制终止": 0,     # PROCESS_STATUS_FORCED_TERMINATED 状态的进程数
                        "优雅停止": 0      # PROCESS_STATUS_GRACEFUL_STOPPED 状态的进程数
                    },
                    "processes": {
                        # 每个进程的详细信息，键为进程PID
                        pid1: {
                            "status": "运行中",      # 进程状态，使用PROCESS_STATUS_*常量值
                            "exit_code": None,       # 进程退出码，运行中为None，已结束则为整数值
                            "start_time": 1627381050.123, # 进程开始时间戳(单位:秒)
                            "end_time": None,        # 进程结束时间戳(单位:秒)，运行中为None
                            "process_name": "Process-1",  # 进程名称，默认为"Process-{pid}"
                            "duration": 0.0,         # 进程持续时间(单位:秒)，运行中为0
                            "stop_method": None      # 进程终止方式，可能的值:
                                                    # - None: 未终止或未知
                                                    # - "normal": 正常完成
                                                    # - "signal": 通过信号终止(如SIGTERM)
                                                    # - "force": 强制终止(如SIGKILL)
                        },
                        pid2: { ... },
                        # 更多进程...
                    },
                    "monitoring_active": True,   # 监控线程是否处于活动状态
                    "stop_methods_summary": {
                        "normal": 0,  # 正常完成的进程数量
                        "signal": 0,  # 通过信号终止的进程数量
                        "force": 0    # 强制终止的进程数量
                    }
                }

                使用说明：
                1. "summary" 键下是各种状态的进程计数，可用于快速了解整体状态分布
                2. "processes" 键下包含每个进程的详细信息，按PID索引
                3. "monitoring_active" 表示监控线程是否正在运行
                4. "stop_methods_summary" 提供了按终止方式分类的进程数统计

                典型用途：
                - 监控进程执行状态
                - 检测异常终止的进程
                - 分析进程执行时间
                - 确定进程的终止方式
                - 收集执行统计信息用于报告和分析
        """
        result = {
            "summary": {
                self.PROCESS_STATUS_RUNNING: 0,
                self.PROCESS_STATUS_COMPLETED: 0,
                self.PROCESS_STATUS_FAILED: 0,
                self.PROCESS_STATUS_TERMINATED: 0,
                self.PROCESS_STATUS_STOPPED: 0,
                self.PROCESS_STATUS_UNKNOWN: 0,
                self.PROCESS_STATUS_FORCED_TERMINATED: 0,
                self.PROCESS_STATUS_GRACEFUL_STOPPED: 0
            },
            "processes": {},
            "monitoring_active": self.__process_monitor_thread is not None and self.__process_monitor_thread.is_alive()
        }

        # 安全地复制进程状态数据
        with self.__process_monitor_lock:
            for pid, info in self.__process_status_tracker.items():
                # 更新状态摘要计数
                status = info.get("status", self.PROCESS_STATUS_UNKNOWN)
                result["summary"][status] = result["summary"].get(status, 0) + 1

                # 复制进程详细信息
                result["processes"][pid] = {
                    "status": status,
                    "exit_code": info.get("exit_code"),
                    "start_time": info.get("start_time"),
                    "end_time": info.get("end_time"),
                    "process_name": info.get("process_name", f"Process-{pid}"),
                    "duration": info.get("duration", 0.0),
                    "stop_method": info.get("stop_method")
                }

            # 添加终止方式统计
            result["stop_methods_summary"] = {
                "normal": sum(1 for info in self.__process_status_tracker.values() if info.get("stop_method") == "normal"),
                "signal": sum(1 for info in self.__process_status_tracker.values() if info.get("stop_method") == "signal"),
                "force": sum(1 for info in self.__process_status_tracker.values() if info.get("stop_method") == "force")
            }

        return result

    def start_process_monitor(self, worker_processes: List[Process]) -> bool:
        """
        启动异步进程状态监控线程。

        此方法会创建并启动一个后台线程，用于持续监控工作进程的状态变化。
        如果监控线程已经在运行，则不会重复启动。

        使用示例:
        ```python
        # 1. 基本用法 - 启动监控并检查结果
        from multiprocessing import Process
        import time

        # 创建一些测试进程
        def worker_func(sleep_time):
            time.sleep(sleep_time)

        processes = [Process(target=worker_func, args=(i,)) for i in range(1, 4)]
        for p in processes:
            p.start()

        # 创建监控器并启动监控
        monitor = ProcessMonitor()
        success = monitor.start_process_monitor(processes)

        if success:
            print("监控已成功启动")

            # 检查监控是否活动
            if monitor.is_monitoring_active():
                print("监控线程正在运行")
        else:
            print("监控启动失败")

        # 2. 异常处理示例
        try:
            # 传入无效的进程列表
            invalid_processes = "not a list"
            success = monitor.start_process_monitor(invalid_processes)
            print(f"使用无效进程列表启动监控: {'成功' if success else '失败'}")
        except Exception as e:
            print(f"启动监控时出错: {e}")

        # 3. 与ManagedMultiProcess结合使用
        # ManagedMultiProcess内部调用start_process_monitor:
        #
        # def __start_process_monitor(self):
        #     # ...
        #     self.__process_monitor.start_process_monitor(self.worker_processes)
        #     # ...

        # 直接从ManagedMultiProcess实例中获取状态
        mp = ManagedMultiProcess(tasks, process_task, 3)
        mp.run()

        # 此时mp已内部启动了进程监控
        status = mp.get_process_status_summary()
        if status['monitoring_active']:
            print("ManagedMultiProcess已启动进程监控")
        ```

        Args:
            worker_processes: 要监控的工作进程列表

        Returns:
            bool: 成功启动则返回True，否则返回False
        """
        # 确保参数有效
        if not isinstance(worker_processes, list) or not worker_processes:
            logger.warning("工作进程列表为空或无效，不启动监控")
            return False

        # 如果监控线程已经在运行，则不重复启动
        if self.__process_monitor_thread is not None and self.__process_monitor_thread.is_alive():
            logger.debug("监控线程已经在运行，不重复启动")
            return True

        # 设置运行标志并启动监控线程
        self.__process_monitor_running.set()

        try:
            self.__process_monitor_thread = threading.Thread(
                target=self.monitor_worker_processes,
                args=(worker_processes,),
                daemon=True,
                name="ProcessMonitor"
            )
            self.__process_monitor_thread.start()
            logger.debug(f"进程监控线程已启动: {self.__process_monitor_thread.name}")
            return True
        except Exception as e:
            logger.error(f"启动进程监控线程失败: {e}")
            self.__process_monitor_running.clear()
            return False

    def stop_process_monitor(self, timeout: float = 2.0) -> bool:
        """
        停止进程状态监控线程。

        此方法会安全地停止监控线程，首先清除运行标志，然后等待线程完成。
        如果线程在超时时间内未结束，则返回False但不强制终止线程。
        在监控线程结束后，会检查是否应该执行回调函数。

        Args:
            timeout: 等待线程结束的超时时间（秒）

        Returns:
            bool: 线程成功停止返回True，超时或其他错误返回False
        """
        if self.__process_monitor_thread is None:
            logger.debug("监控线程不存在，无需停止")
            # 确认是否应该执行回调
            self.__execute_completion_callback()
            return True

        if not self.__process_monitor_thread.is_alive():
            logger.debug("监控线程已不活动，无需停止")
            # 确认是否应该执行回调
            self.__execute_completion_callback()
            return True

        try:
            # 清除运行标志
            logger.debug("正在停止进程监控线程...")
            self.__process_monitor_running.clear()

            # 等待线程结束
            start_time = time.time()
            self.__process_monitor_thread.join(timeout)

            if self.__process_monitor_thread.is_alive():
                logger.warning(f"进程监控线程在 {timeout} 秒内未结束")
                # 即使线程未结束，也检查是否应该执行回调
                self.__execute_completion_callback()
                return False
            else:
                logger.debug(f"进程监控线程已停止，耗时: {time.time() - start_time:.2f}秒")
                self.__process_monitor_thread = None  # 清除线程引用
                # 确认是否应该执行回调
                self.__execute_completion_callback()
                return True
        except Exception as e:
            logger.error(f"停止进程监控线程时出错: {e}")
            logger.error(traceback.format_exc())
            # 即使发生错误，也检查是否应该执行回调
            self.__execute_completion_callback()
            return False

    def is_monitoring_active(self) -> bool:
        """
        检查进程监控是否处于活动状态。

        使用示例:
        ```python
        # 1. 基本用法 - 检查监控状态
        monitor = ProcessMonitor()

        # 初始状态应为非活动
        if not monitor.is_monitoring_active():
            print("监控未启动")

        # 启动监控
        monitor.start_process_monitor(worker_processes)

        # 检查监控是否已激活
        if monitor.is_monitoring_active():
            print("监控已成功启动并处于活动状态")

        # 2. 监控状态变化
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 初始状态应为活动
        print(f"监控状态: {'活动' if monitor.is_monitoring_active() else '非活动'}")

        # 停止监控
        monitor.stop_process_monitor()

        # 停止后状态应为非活动
        print(f"停止后监控状态: {'活动' if monitor.is_monitoring_active() else '非活动'}")

        # 3. 与ManagedMultiProcess结合使用
        mp = ManagedMultiProcess(tasks, process_task, 3)

        # 运行前检查
        status = mp.get_process_status_summary()
        print(f"启动前监控状态: {'活动' if status['monitoring_active'] else '非活动'}")

        # 启动处理
        mp.run()

        # 运行后检查
        status = mp.get_process_status_summary()
        print(f"启动后监控状态: {'活动' if status['monitoring_active'] else '非活动'}")

        # 停止后检查
        mp.stop_all()
        status = mp.get_process_status_summary()
        print(f"停止后监控状态: {'活动' if status['monitoring_active'] else '非活动'}")
        ```

        Returns:
            bool: 如果监控线程存在且处于活动状态则返回True，否则返回False
        """
        return (self.__process_monitor_thread is not None and
                self.__process_monitor_thread.is_alive() and
                self.__process_monitor_running.is_set())

    def clear_process_status(self) -> None:
        """
        清除所有进程状态记录并重置回调状态。

        此方法会重置状态追踪器，删除所有记录的进程状态，并将完成回调状态重置，
        使得回调可以在下一轮监控中再次触发。在开始新一轮监控前调用此方法可以避免旧数据的干扰。

        使用示例:
        ```python
        # 1. 基本用法 - 清除状态记录
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 等待一段时间收集数据
        time.sleep(3)

        # 获取当前状态
        before_clear = monitor.get_process_status_summary()
        print(f"清除前的进程数: {len(before_clear['processes'])}")

        # 清除状态记录和回调状态
        monitor.clear_process_status()

        # 验证记录已清除
        after_clear = monitor.get_process_status_summary()
        print(f"清除后的进程数: {len(after_clear['processes'])}")

        # 2. 重新启动监控前清除旧数据
        monitor = ProcessMonitor()

        # 第一轮监控
        monitor.start_process_monitor(first_processes)
        time.sleep(3)
        monitor.stop_process_monitor()

        # 清除第一轮的状态记录和回调状态
        monitor.clear_process_status()

        # 第二轮监控（使用新的进程列表）
        monitor.start_process_monitor(second_processes)
        # 第二轮监控完成时回调会再次触发

        # 3. 使用回调函数
        callback_count = 0

        def count_callback(monitor):
            nonlocal callback_count
            callback_count += 1
            print(f"回调被执行 {callback_count} 次")

        monitor = ProcessMonitor(completion_callback=count_callback)

        # 第一轮监控
        monitor.start_process_monitor(first_processes)
        time.sleep(5)  # 等待进程完成和回调执行
        monitor.stop_process_monitor()

        # 清除状态并重置回调状态
        monitor.clear_process_status()

        # 第二轮监控
        monitor.start_process_monitor(second_processes)
        time.sleep(5)  # 等待进程完成和回调执行
        monitor.stop_process_monitor()

        print(f"回调总共被执行了 {callback_count} 次")  # 应该输出2
        ```
        """
        # 清除进程状态记录
        with self.__process_monitor_lock:
            previous_count = len(self.__process_status_tracker)
            self.__process_status_tracker.clear()
            logger.debug(f"已清除所有进程状态记录 (共 {previous_count} 条)")

        # 重置回调完成状态
        with self.__callback_lock:
            previous_state = self.__all_processes_completed
            self.__all_processes_completed = False
            if previous_state:
                logger.info("已重置完成回调状态，下一轮监控将再次触发回调")
            else:
                logger.debug("完成回调状态未改变，保持未执行状态")

    def get_status_count(self, status: str) -> int:
        """
        获取指定状态的进程数量。

        使用示例:
        ```python
        # 1. 基本用法 - 获取各种状态的进程数量
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 等待一段时间收集数据
        time.sleep(3)

        # 获取各状态进程数
        running = monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_RUNNING)
        completed = monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_COMPLETED)
        failed = monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_FAILED)

        print(f"运行中: {running}, 已完成: {completed}, 失败: {failed}")

        # 2. 监控进程完成情况
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        total_processes = len(worker_processes)

        # 等待所有进程完成
        while monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_RUNNING) > 0:
            completed = monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_COMPLETED)
            print(f"进度: {completed}/{total_processes} 完成")
            time.sleep(1)

        # 检查是否有失败的进程
        failed = monitor.get_status_count(ProcessMonitor.PROCESS_STATUS_FAILED)
        if failed > 0:
            print(f"警告: {failed}个进程执行失败")

        # 3. 与ManagedMultiProcess结合使用
        mp = ManagedMultiProcess(tasks, process_task, 3)
        mp.run()

        # 定期检查进度和状态
        while mp.is_running():
            # 获取内部ProcessMonitor状态
            status = mp.get_process_status_summary()
            completed = status['summary'][ProcessMonitor.PROCESS_STATUS_COMPLETED]
            failed = status['summary'][ProcessMonitor.PROCESS_STATUS_FAILED]
            running = status['summary'][ProcessMonitor.PROCESS_STATUS_RUNNING]

            print(f"进度 - 运行中: {running}, 已完成: {completed}, 失败: {failed}")
            time.sleep(1)
        ```

        Args:
            status: 要查询的进程状态，应使用类定义的PROCESS_STATUS_*常量

        Returns:
            int: 当前处于指定状态的进程数量
        """
        with self.__process_monitor_lock:
            return sum(1 for info in self.__process_status_tracker.values()
                       if info.get("status") == status)

    @property
    def process_count(self) -> int:
        """
        获取当前追踪的进程总数。

        使用示例:
        ```python
        # 1. 基本用法 - 获取进程总数
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 等待一段时间收集数据
        time.sleep(1)

        # 获取监控的进程总数
        total = monitor.process_count
        print(f"当前监控的进程总数: {total}")

        # 2. 验证监控覆盖率
        monitor = ProcessMonitor()

        # 创建10个工作进程
        processes = [Process(target=worker_func, args=(i % 3,)) for i in range(10)]
        for p in processes:
            p.start()

        # 启动监控
        monitor.start_process_monitor(processes)

        # 检查是否所有进程都被监控
        time.sleep(0.5)  # 给监控线程一点时间初始化记录
        if monitor.process_count == len(processes):
            print("所有进程都已被监控")
        else:
            print(f"监控覆盖不完整: {monitor.process_count}/{len(processes)}")

        # 3. 与ManagedMultiProcess结合使用
        mp = ManagedMultiProcess(tasks, process_task, 3)
        mp.run()

        # 获取进程数量统计
        time.sleep(0.5)  # 给监控线程一点时间初始化记录
        status = mp.get_process_status_summary()
        process_count = len(status['processes'])

        # 验证进程数是否符合预期
        if process_count == 3:  # 期望的进程数
            print("所有工作进程都已被监控")
        else:
            print(f"监控进程数({process_count})与预期(3)不符")
        ```

        Returns:
            int: 监控器中记录的进程总数
        """
        with self.__process_monitor_lock:
            return len(self.__process_status_tracker)

    def __should_execute_callback(self) -> bool:
        """
        确定是否应该执行完成回调函数。

        此方法检查以下条件：
        1. 存在至少一个已追踪的进程
        2. 没有运行中的进程
        3. 回调函数已设置
        4. 回调尚未执行

        Returns:
            bool: 如果应该执行回调返回True，否则返回False
        """
        # 首先检查是否已执行过回调
        with self.__callback_lock:
            if self.__all_processes_completed:
                logger.debug("完成回调已经执行过，不需要再次执行")
                return False

            # 检查回调函数是否设置
            if self.__completion_callback is None:
                logger.debug("没有设置完成回调函数，不执行回调")
                return False

        # 检查所有进程是否已完成
        if not self.are_all_processes_completed():
            return False

        logger.info("所有条件满足，准备执行完成回调")
        return True

    def __execute_completion_callback(self):
        """
        执行完成回调函数，确保回调只被执行一次，且仅在所有进程完成时执行。

        此方法首先检查是否满足执行回调的条件，然后在确认所有条件满足后异步执行回调函数。
        执行过程包括详细的日志记录和错误处理。
        """
        # 检查是否应该执行回调
        if not self.__should_execute_callback():
            return

        # 使用锁确保线程安全的标记回调状态
        with self.__callback_lock:
            # 双重检查，确保在获取锁后条件仍然满足
            if self.__all_processes_completed:
                logger.debug("完成回调已经执行过，跳过")
                return

            # 标记为已完成
            logger.debug("标记回调状态为已完成")
            self.__all_processes_completed = True

        # 记录进程统计信息，用于调试
        self.__log_process_status_before_callback()

        # 异步执行回调函数
        logger.info("开始异步执行完成回调函数")
        self.__execute_callback_async()

    def __log_process_status_before_callback(self):
        """
        在执行回调前记录详细的进程状态信息，便于调试和问题排查。
        """
        with self.__process_monitor_lock:
            total = len(self.__process_status_tracker)
            running = self.get_status_count(self.PROCESS_STATUS_RUNNING)
            completed = self.get_status_count(self.PROCESS_STATUS_COMPLETED)
            failed = self.get_status_count(self.PROCESS_STATUS_FAILED)
            terminated = self.get_status_count(self.PROCESS_STATUS_TERMINATED)
            forced_terminated = self.get_status_count(self.PROCESS_STATUS_FORCED_TERMINATED)
            graceful_stopped = self.get_status_count(self.PROCESS_STATUS_GRACEFUL_STOPPED)

            logger.info(f"回调执行前进程状态统计 - 总计: {total}, 运行中: {running}, 已完成: {completed}, "
                        f"失败: {failed}, 被终止: {terminated}, 强制终止: {forced_terminated}, "
                        f"优雅停止: {graceful_stopped}")

            # 记录每个进程的详细状态
            for pid, info in self.__process_status_tracker.items():
                logger.debug(f"进程 {pid} ({info.get('process_name', 'unknown')}) - "
                             f"状态: {info.get('status', 'unknown')}, "
                             f"退出码: {info.get('exit_code', 'none')}, "
                             f"停止方式: {info.get('stop_method', 'unknown')}, "
                             f"运行时长: {info.get('duration', 0):.2f}秒")

    def __execute_callback_async(self):
        """
        在新线程中异步执行回调函数。

        这确保回调函数的执行不会阻塞监控线程或主线程，并且可以安全地处理可能出现的异常。
        增加了更详细的日志记录和错误处理机制。
        """
        def run_callback():
            try:
                start_time = time.time()
                logger.info("开始执行完成回调函数")

                # 再次检查回调函数是否存在
                callback_func = None
                with self.__callback_lock:
                    callback_func = self.__completion_callback

                if callback_func is not None:
                    callback_func(self)
                    duration = time.time() - start_time
                    logger.info(f"完成回调函数执行完毕，耗时: {duration:.4f}秒")
                else:
                    logger.warning("执行时发现回调函数已被清除，跳过执行")
            except Exception as e:
                logger.error(f"执行完成回调函数时出错: {e}")
                logger.error(traceback.format_exc())

        # 创建并启动新线程执行回调
        callback_thread = threading.Thread(
            target=run_callback,
            daemon=True,
            name="CompletionCallback"
        )
        callback_thread.start()
        logger.debug(f"完成回调线程已启动: {callback_thread.name}")

    def set_completion_callback(self, callback_func=None) -> None:
        """
        设置在所有进程完成时执行的回调函数。

        回调函数将在所有监控的进程完成（无论正常或异常）时被调用。
        回调函数接收当前ProcessMonitor实例作为参数，以便可以查询进程状态信息。
        如果指定为None，将清除当前的回调函数。

        使用示例:
        ```python
        # 1. 基本用法 - 设置回调函数
        monitor = ProcessMonitor()

        def on_all_completed(monitor):
            print("所有进程已完成")
            status = monitor.get_process_status_summary()
            print(f"最终状态: {status['summary']}")

        monitor.set_completion_callback(on_all_completed)
        monitor.start_process_monitor(processes)

        # 2. 清除回调函数
        monitor = ProcessMonitor()

        # 设置回调
        monitor.set_completion_callback(lambda m: print("完成"))

        # 检查回调是否已设置
        if monitor.get_completion_callback() is not None:
            print("回调函数已设置")

        # 清除回调
        monitor.set_completion_callback(None)

        # 验证回调已清除
        if monitor.get_completion_callback() is None:
            print("回调函数已清除")

        # 3. 更改回调函数
        monitor = ProcessMonitor()

        # 设置初始回调
        monitor.set_completion_callback(lambda m: print("原始回调"))

        # 更改为新回调
        def new_callback(monitor):
            print("这是新的回调函数")
            status = monitor.get_process_status_summary()
            print(f"进程状态: {status['summary']}")

        monitor.set_completion_callback(new_callback)

        # 开始监控(将使用新回调)
        monitor.start_process_monitor(processes)
        ```

        Args:
            callback_func: 回调函数，接收ProcessMonitor实例作为参数。如果为None，则清除当前回调。
        """
        with self.__callback_lock:
            previous_callback = self.__completion_callback
            self.__completion_callback = callback_func

            if callback_func is None and previous_callback is not None:
                logger.debug("已清除完成回调函数")
            elif callback_func is not None and previous_callback is None:
                logger.debug("已设置完成回调函数")
            elif callback_func is not None and previous_callback is not None:
                logger.debug("已更新完成回调函数")

    def get_completion_callback(self):
        """
        获取当前设置的完成回调函数。

        使用示例:
        ```python
        # 1. 基本用法 - 获取回调函数
        monitor = ProcessMonitor()

        # 检查是否已设置回调
        current_callback = monitor.get_completion_callback()
        if current_callback is None:
            print("未设置回调函数")
        else:
            print("已设置回调函数")

        # 2. 与set_completion_callback配合使用
        monitor = ProcessMonitor()

        # 设置回调
        def my_callback(monitor):
            print("所有进程已完成")

        monitor.set_completion_callback(my_callback)

        # 获取并检查回调
        callback = monitor.get_completion_callback()
        if callback == my_callback:
            print("成功获取到正确的回调函数引用")

        # 3. 保存和恢复回调
        monitor = ProcessMonitor()

        # 设置初始回调
        original_callback = lambda m: print("回调执行")
        monitor.set_completion_callback(original_callback)

        # 保存当前回调
        saved_callback = monitor.get_completion_callback()

        # 临时使用不同的回调
        monitor.set_completion_callback(lambda m: print("临时回调"))

        # 监控一些进程...
        # ...

        # 恢复原始回调
        monitor.set_completion_callback(saved_callback)
        ```

        Returns:
            Callable|None: 当前设置的回调函数，如果未设置则返回None
        """
        with self.__callback_lock:
            return self.__completion_callback

    def mark_process_stop_method(self, pid: int, stop_method: str, force_state: str = None) -> bool:
        """
        标记进程的终止方式，并可选择性地设置进程状态。

        此方法允许外部代码（如ManagedMultiProcess的__terminate_process方法）
        显式地标记进程的终止方式，特别是对于被手动终止的进程。

        使用示例:
        ```python
        # 1. 基本用法 - 标记进程被强制终止
        monitor = ProcessMonitor()
        monitor.start_process_monitor(processes)

        # 在另一个地方终止进程
        process = processes[0]
        process.terminate()

        # 标记该进程被强制终止
        monitor.mark_process_stop_method(process.pid, "force",
                                        ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED)

        # 2. 只标记终止方式，不修改状态
        monitor.mark_process_stop_method(process.pid, "signal")

        # 3. 与ManagedMultiProcess结合使用
        # 在ManagedMultiProcess.__terminate_process内部:
        #
        # def __terminate_process(self, process, force=False):
        #     # ... 终止进程的代码 ...
        #
        #     # 标记进程终止方式
        #     if hasattr(self, '_ManagedMultiProcess__process_monitor') and self.__process_monitor:
        #         stop_method = "force" if force else "signal"
        #         status = ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED if force else ProcessMonitor.PROCESS_STATUS_GRACEFUL_STOPPED
        #         self.__process_monitor.mark_process_stop_method(process.pid, stop_method, status)
        """
        with self.__process_monitor_lock:
            # 检查进程是否在状态追踪表中
            if pid not in self.__process_status_tracker:
                logger.warning(f"进程 {pid} 不在状态追踪列表中，无法标记终止方式")
                return False

            # 更新终止方式
            self.__process_status_tracker[pid]["stop_method"] = stop_method
            logger.debug(f"已标记进程 {pid} 的终止方式为 {stop_method}")

            # 如果提供了状态，也进行设置
            if force_state is not None:
                previous_status = self.__process_status_tracker[pid]["status"]
                self.__process_status_tracker[pid]["status"] = force_state
                logger.info(f"已强制将进程 {pid} 的状态从 {previous_status} 更改为 {force_state}")

            return True

    def are_all_processes_completed(self) -> bool:
        """
        检查所有被追踪的进程是否都已完成。

        此方法用于外部调用者判断所有进程是否已完成处理，无论它们是正常完成、
        失败、被终止或强制终止。此方法只检查进程状态，不会触发回调函数的执行。

        使用示例:
        ```python
        # 1. 基本用法 - 检查进程是否全部完成
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 周期性检查所有进程是否完成
        while not monitor.are_all_processes_completed():
            print("仍有进程在运行中...")
            time.sleep(1)

        print("所有进程已完成!")

        # 2. 结合状态摘要使用
        monitor = ProcessMonitor()
        monitor.start_process_monitor(worker_processes)

        # 等待所有进程完成
        while not monitor.are_all_processes_completed():
            time.sleep(1)

        # 获取最终状态
        final_status = monitor.get_process_status_summary()
        print(f"最终状态: {final_status['summary']}")

        # 3. 与wait_all结合使用
        def wait_all(timeout=None):
            # 基于are_all_processes_completed实现等待功能
            start_time = time.time()
            while not monitor.are_all_processes_completed():
                if timeout is not None and time.time() - start_time > timeout:
                    return False  # 超时
                time.sleep(0.1)
            return True  # 所有进程已完成

        # 使用自定义的wait_all函数
        if wait_all(timeout=10):
            print("所有进程在10秒内完成")
        else:
            print("等待超时，仍有进程在运行")
        ```

        Returns:
            bool: 如果所有进程都已完成（不在运行状态）返回True，否则返回False
        """
        with self.__process_monitor_lock:
            # 如果没有追踪任何进程，认为"全部完成"
            if not self.__process_status_tracker:
                return True

            # 检查是否有任何运行中的进程
            running_processes = sum(1 for info in self.__process_status_tracker.values()
                                    if info.get("status") == self.PROCESS_STATUS_RUNNING)

            all_completed = running_processes == 0

            if all_completed:
                logger.debug("确认所有进程已完成")
            else:
                logger.debug(f"仍有 {running_processes} 个进程在运行中")

            return all_completed


# 为了保持与现有代码的兼容性，提供一个全局实例和兼容函数
_default_monitor = ProcessMonitor(completion_callback=None)

# 兼容函数，使用默认监控器实例


def monitor_worker_processes(worker_processes: List[Process],
                             process_status_tracker: Dict[int, Dict[str, Any]],
                             process_monitor_lock: threading.Lock,
                             process_monitor_running: threading.Event) -> None:
    """兼容函数，请使用ProcessMonitor.monitor_worker_processes()替代"""
    logger.warning("使用了已弃用的函数monitor_worker_processes，请使用ProcessMonitor类代替")
    _default_monitor.monitor_worker_processes(worker_processes)


def get_process_status_summary(process_status_tracker: Dict[int, Dict[str, Any]],
                               process_monitor_lock: threading.Lock,
                               process_monitor_thread: Optional[threading.Thread]) -> Dict[str, Any]:
    """兼容函数，请使用ProcessMonitor.get_process_status_summary()替代"""
    logger.warning("使用了已弃用的函数get_process_status_summary，请使用ProcessMonitor类代替")
    result = _default_monitor.get_process_status_summary()
    # 确保兼容返回结果中包含新增的状态类型
    if "summary" in result and ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED not in result["summary"]:
        result["summary"][ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED] = 0
    if "summary" in result and ProcessMonitor.PROCESS_STATUS_GRACEFUL_STOPPED not in result["summary"]:
        result["summary"][ProcessMonitor.PROCESS_STATUS_GRACEFUL_STOPPED] = 0
    return result


def start_process_monitor(worker_processes: List[Process],
                          process_status_tracker: Dict[int, Dict[str, Any]],
                          process_monitor_lock: threading.Lock,
                          process_monitor_running: threading.Event) -> Optional[threading.Thread]:
    """
    兼容函数，请使用ProcessMonitor.start_process_monitor()替代

    注意：这个兼容函数使用默认的ProcessMonitor实例，如果需要使用回调功能，
    请直接创建ProcessMonitor实例，并使用其start_process_monitor方法。
    """
    logger.warning("使用了已弃用的函数start_process_monitor，请使用ProcessMonitor类代替")
    success = _default_monitor.start_process_monitor(worker_processes)
    return _default_monitor._ProcessMonitor__process_monitor_thread if success else None


def stop_process_monitor(process_monitor_thread: Optional[threading.Thread],
                         process_monitor_running: threading.Event,
                         timeout: float = 2.0) -> bool:
    """兼容函数，请使用ProcessMonitor.stop_process_monitor()替代"""
    logger.warning("使用了已弃用的函数stop_process_monitor，请使用ProcessMonitor类代替")
    return _default_monitor.stop_process_monitor(timeout=timeout)


# 导出进程状态常量以保持兼容性
PROCESS_STATUS_UNKNOWN = ProcessMonitor.PROCESS_STATUS_UNKNOWN
PROCESS_STATUS_RUNNING = ProcessMonitor.PROCESS_STATUS_RUNNING
PROCESS_STATUS_COMPLETED = ProcessMonitor.PROCESS_STATUS_COMPLETED
PROCESS_STATUS_STOPPED = ProcessMonitor.PROCESS_STATUS_STOPPED
PROCESS_STATUS_FAILED = ProcessMonitor.PROCESS_STATUS_FAILED
PROCESS_STATUS_TERMINATED = ProcessMonitor.PROCESS_STATUS_TERMINATED
PROCESS_STATUS_FORCED_TERMINATED = ProcessMonitor.PROCESS_STATUS_FORCED_TERMINATED
PROCESS_STATUS_GRACEFUL_STOPPED = ProcessMonitor.PROCESS_STATUS_GRACEFUL_STOPPED


def close_shared_data_resources(managed_process):
    """
    手动关闭ManagedMultiProcess实例的共享数据资源。

    此函数关闭与SharedDataManager相关的所有共享数据资源，包括DataManagerManager和共享数据管理器代理。
    调用此函数后，所有对共享数据的访问操作将不再可用。

    此函数设计为显式调用，不会被自动触发，确保用户可以完全控制共享数据资源的生命周期。

    Args:
        managed_process: ManagedMultiProcess实例对象

    Returns:
        bool: 操作是否成功

    示例:
        ```python
        from global_tools.utils.manager_process3.managed_helper import close_shared_data_resources

        # 创建并使用ManagedMultiProcess
        mp = ManagedMultiProcess(tasks, process_task, 4)
        mp.run()
        mp.wait_all()

        # 获取并使用处理结果
        results = mp.get_results()

        # 停止进程但保留共享数据资源
        mp.stop_all(immediate=True)

        # 在不需要访问共享数据时手动关闭资源
        close_shared_data_resources(mp)
        # 或使用mp对象的方法（内部也会调用此函数）
        # mp.close_shared_data_resources()
        ```
    """
    logger.info("开始手动关闭共享数据资源...")
    success = True

    try:
        # 先停止实时通知线程并等待其完全退出
        if hasattr(managed_process, '_ManagedMultiProcess__realtime_notify_running'):
            logger.debug("确保实时通知处理线程停止...")
            managed_process._ManagedMultiProcess__realtime_notify_running.clear()

            if hasattr(managed_process, '_ManagedMultiProcess__realtime_notify_thread') and managed_process._ManagedMultiProcess__realtime_notify_thread and managed_process._ManagedMultiProcess__realtime_notify_thread.is_alive():
                # 使用更长的超时时间，确保线程有足够时间完全退出
                wait_timeout = 3.0
                logger.debug(f"等待实时通知处理线程退出 (超时: {wait_timeout}秒)...")
                managed_process._ManagedMultiProcess__realtime_notify_thread.join(timeout=wait_timeout)

                if managed_process._ManagedMultiProcess__realtime_notify_thread.is_alive():
                    logger.warning(f"实时通知处理线程在 {wait_timeout} 秒内未能停止，但将继续关闭 Manager。可能会出现 BrokenPipeError，这是正常的。")
                else:
                    logger.debug("实时通知处理线程已成功停止")
            else:
                logger.info("实时通知处理线程未启动，无需停止。")

        # 确保回调处理线程停止
        if hasattr(managed_process, '_ManagedMultiProcess__callback_worker_running'):
            logger.debug("确保回调处理线程停止...")
            managed_process._ManagedMultiProcess__callback_worker_running.clear()

            if hasattr(managed_process, '_ManagedMultiProcess__callback_worker_thread') and managed_process._ManagedMultiProcess__callback_worker_thread and managed_process._ManagedMultiProcess__callback_worker_thread.is_alive():
                # 使用更长的超时时间
                wait_timeout = 3.0
                logger.debug(f"等待回调处理线程退出 (超时: {wait_timeout}秒)...")
                managed_process._ManagedMultiProcess__callback_worker_thread.join(timeout=wait_timeout)

                if managed_process._ManagedMultiProcess__callback_worker_thread.is_alive():
                    logger.warning(f"回调处理线程在 {wait_timeout} 秒内未能停止")
                else:
                    logger.debug("回调处理线程已成功停止")

        # 清空线程引用，即使线程可能仍在运行
        managed_process._ManagedMultiProcess__realtime_notify_thread = None
        managed_process._ManagedMultiProcess__callback_worker_thread = None

        # 关闭 DataManagerManager (管理 SharedDataManager 实例)
        if hasattr(managed_process, '_ManagedMultiProcess__data_manager_manager') and managed_process._ManagedMultiProcess__data_manager_manager:
            logger.info("开始关闭 DataManagerManager...")
            try:
                # 先尝试使用我们新增的方法来关闭所有SharedDataManager实例
                try:
                    if hasattr(managed_process._ManagedMultiProcess__data_manager_manager, 'shutdown_all_resources'):
                        logger.info("首先关闭所有共享资源...")
                        count = managed_process._ManagedMultiProcess__data_manager_manager.shutdown_all_resources()
                        logger.info(f"已成功关闭 {count} 个SharedDataManager实例的资源")
                    else:
                        logger.warning("DataManagerManager没有shutdown_all_resources方法，无法正确关闭SharedDataManager实例")
                except Exception as sdm_err:
                    logger.error(f"关闭SharedDataManager实例时出错: {sdm_err}", exc_info=True)

                # 确保在关闭前关闭所有代理对象的引用 (以防万一)
                managed_process._ManagedMultiProcess__shared_data_manager_proxy = None
                # 现在可以安全地关闭管理器
                managed_process._ManagedMultiProcess__data_manager_manager.shutdown()
                logger.info("DataManagerManager 已成功关闭。")
            except Exception as e:
                logger.error(f"关闭 DataManagerManager 时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                success = False
        else:
            logger.info("DataManagerManager 不存在或已关闭。")
        managed_process._ManagedMultiProcess__data_manager_manager = None  # 显式置 None
        managed_process._ManagedMultiProcess__shared_data_manager_proxy = None  # 代理也失效了

        # 关闭 status_manager
        if hasattr(managed_process, '_ManagedMultiProcess__status_manager') and managed_process._ManagedMultiProcess__status_manager:
            logger.info("开始关闭状态 Manager...")
            try:
                # 确保先解除对状态字典的引用 (以防万一)
                managed_process._ManagedMultiProcess__process_status_dict = None
                # 关闭管理器
                managed_process._ManagedMultiProcess__status_manager.shutdown()
                logger.info("状态 Manager 已成功关闭。")
            except Exception as e:
                logger.error(f"关闭状态 Manager 时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                success = False
        else:
            logger.info("状态 Manager 不存在或已关闭。")
        managed_process._ManagedMultiProcess__status_manager = None  # 显式置 None
        managed_process._ManagedMultiProcess__process_status_dict = None  # 代理也失效了

        logger.info("共享数据资源关闭" + ("成功" if success else "过程中出现错误"))
        return success
    except Exception as e:
        logger.error(f"关闭共享数据资源时出现意外错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def check_and_release_shared_lock(shared_data_manager_proxy, timeout=0.5, force=False):
    """
    检测 SharedDataManager 的共享锁是否被占用，并尝试释放被卡住的锁。

    此函数通过尝试以非阻塞方式获取锁来检测锁状态，并提供强制释放机制。
    当 force=True 时，会尝试在 Manager 进程中创建新的锁对象来替换被卡住的锁。

    警告: 强制释放锁是一个危险操作，可能导致共享数据不一致。只应在确认锁确实被
          卡住且无法通过正常方式释放时使用此方法。

    Args:
        shared_data_manager_proxy: SharedDataManager 的代理对象
        timeout: 尝试获取锁的超时时间（秒），默认 0.5 秒
        force: 是否强制释放锁，默认为 False

    Returns:
        dict: 包含锁状态信息的字典:
            - 'locked': 布尔值，表示锁是否被占用
            - 'released': 布尔值，表示是否成功释放了锁
            - 'method': 字符串，表示使用的方法 ('check_only', 'timeout_acquire', 'force_reset')
            - 'error': 如果有错误发生，包含错误信息
    """
    from multiprocessing.managers import AcquirerProxy


    result = {
        'locked': False,
        'released': False,
        'method': 'check_only',
        'error': None
    }

    if shared_data_manager_proxy is None:
        result['error'] = "共享数据管理器代理对象为空"
        logger.error(result['error'])
        return result

    try:
        # 获取锁对象
        lock_proxy = shared_data_manager_proxy.get_lock()
        if not isinstance(lock_proxy, AcquirerProxy):
            result['error'] = f"获取到的锁不是 AcquirerProxy 类型: {type(lock_proxy)}"
            logger.error(result['error'])
            return result

        # 方法1: 尝试非阻塞方式获取锁检测状态
        logger.debug("尝试非阻塞方式获取锁检测状态...")
        acquired = False
        try:
            acquired = lock_proxy.acquire(blocking=False)
            if acquired:
                logger.debug("成功获取锁，锁未被占用")
                result['locked'] = False
                lock_proxy.release()
                logger.debug("已释放获取的锁")
            else:
                logger.warning("锁当前被占用")
                result['locked'] = True
                result['method'] = 'check_only'
        except Exception as e:
            logger.error(f"检测锁状态时出错: {e}")
            result['error'] = f"检测锁状态时出错: {str(e)}"
            result['locked'] = True  # 假设出错表示锁被占用或状态异常

        # 如果锁被占用且需要释放，尝试方法2: 使用超时方式
        if result['locked'] and (force or timeout > 0):
            logger.debug(f"尝试使用超时方式 ({timeout}秒) 获取锁...")
            result['method'] = 'timeout_acquire'
            try:
                acquired = lock_proxy.acquire(blocking=True, timeout=timeout)
                if acquired:
                    logger.info("成功在超时时间内获取到锁，现在释放它")
                    lock_proxy.release()
                    result['released'] = True
                    result['locked'] = False
                else:
                    logger.warning(f"在 {timeout} 秒内无法获取锁，锁可能被卡住")
            except Exception as e:
                logger.error(f"尝试超时获取锁时出错: {e}")
                result['error'] = f"尝试超时获取锁时出错: {str(e)}"

        # 如果超时无效且强制模式打开，尝试方法3: 在Manager进程中重置锁
        if result['locked'] and force and not result['released']:
            logger.warning("尝试强制重置锁...")
            result['method'] = 'force_reset'
            try:
                # 这个方法需要在 DataManagerManager 中注册特殊方法实现
                # 这里假设已经实现了 reset_shared_lock 方法
                if hasattr(shared_data_manager_proxy, 'reset_shared_lock'):
                    was_reset = shared_data_manager_proxy.reset_shared_lock()
                    if was_reset:
                        logger.info("锁已成功重置")
                        result['released'] = True
                        result['locked'] = False
                    else:
                        logger.error("尝试重置锁失败")
                else:
                    logger.error("共享数据管理器没有 reset_shared_lock 方法，无法强制重置锁")
                    result['error'] = "共享数据管理器不支持锁重置"
            except Exception as e:
                logger.error(f"尝试重置锁时出错: {e}", exc_info=True)
                result['error'] = f"尝试重置锁时出错: {str(e)}"

    except Exception as e:
        # 处理整体过程中的异常
        error_msg = f"检测/释放锁过程中发生错误: {str(e)}"
        logger.error(error_msg, exc_info=True)
        result['error'] = error_msg

    return result
