@echo off
chcp 936 >nul
echo 正在启动 PostgreSQL 服务...

sc query PostgreSQL_17 | findstr "RUNNING" > nul
if %errorlevel% equ 0 (
    echo PostgreSQL 服务已经在运行中...
) else (
    sc start PostgreSQL_17
    echo 正在等待服务启动...
    ping 127.0.0.1 -n 5 > nul
    sc query PostgreSQL_17 | findstr "RUNNING" > nul
    if %errorlevel% equ 0 (
        echo PostgreSQL 服务已成功启动！
    ) else (
        echo PostgreSQL 服务启动失败，请检查错误日志。
    )
)

echo.
echo 当前PostgreSQL服务状态:
sc query PostgreSQL_17

echo.
echo 检查端口监听状态:
netstat -ano | findstr :9001

echo.
echo 按任意键退出...
pause > nul 