import traceback
import json
import os
import time
from typing import List, Optional, Union, Callable, Dict, Any, Tuple, Generic, TypeVar
import logging
from PyQt5.QtCore import (
    Qt,
    QEvent,
    pyqtSignal,
    QTimer,
    QObject,
    QRunnable,
    QThreadPool,
    QPoint,
    QRect,
)
from PyQt5.QtWidgets import (
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QVBoxLayout,
    QWidget,
    QApplication,
    QLabel,
    QAbstractItemView,
    QPushButton,
)
from PyQt5.QtGui import QColor, QPalette, QFont, QCursor, QFocusEvent
from global_tools.utils import ClassInstanceManager


class ButtonPressEffectEnhancer(QObject):
    """
    为容器中的所有 QPushButton 添加按下时的凹陷效果。

    递归遍历指定容器中的所有 QPushButton 控件，并通过事件过滤器为它们添加按下时的视觉反馈效果，
    使按钮在点击时呈现凹陷感，提升用户体验。此类不会改变按钮的其他样式属性，如大小、颜色等。

    使用示例:
        ```python
        # 1. 基本使用方法
        # 假设 'my_container' 是一个 QWidget、QFrame 或 QDialog 等容器控件
        enhancer = ButtonPressEffectEnhancer(my_container)

        # 2. 在类中使用
        class MyDialog(QDialog):
            def __init__(self):
                super().__init__()
                # 设置对话框内容...

                # 为对话框中的所有按钮添加按下效果增强
                self.button_enhancer = ButtonPressEffectEnhancer(self)
        ```
    """

    # __logger = logging.getLogger("ButtonPressEffectEnhancer")
    __logger = ClassInstanceManager.get_instance(key="ui_logger")

    def __init__(self, container, parent=None):
        """
        初始化按钮按下效果增强器。

        Args:
            container: 包含按钮的PyQt5容器控件(QWidget, QFrame, QDialog等)
            parent: 父QObject对象，默认为None
        """
        super().__init__(parent)

        self.__container = container
        self.__enhanced_buttons = {}  # 跟踪已增强的按钮及其原始位置

        try:
            self.__logger.debug(
                "开始初始化ButtonPressEffectEnhancer，容器: %s",
                container.__class__.__name__,
            )

            # 查找并增强所有按钮
            self.__find_and_enhance_buttons(container)

            self.__logger.debug(
                "ButtonPressEffectEnhancer初始化完成，共处理 %d 个按钮",
                len(self.__enhanced_buttons),
            )
        except Exception as e:
            self.__logger.error(
                "初始化ButtonPressEffectEnhancer时出错: %s\n%s",
                e,
                traceback.format_exc(),
            )

    def __find_and_enhance_buttons(self, container):
        """
        递归查找容器中的所有QPushButton并为其增加按下效果。

        Args:
            container: 要搜索的容器控件
        """
        if not container:
            self.__logger.warning("容器对象为空，跳过按钮查找")
            return

        try:
            # 遍历容器的所有子控件（不仅是直接子控件）
            for child in container.findChildren(QPushButton):
                # 是按钮，设置增强效果
                self.__setup_button_effect(child)

        except Exception as e:
            self.__logger.error("递归查找按钮时出错: %s\n%s", e, traceback.format_exc())

    def __setup_button_effect(self, button):
        """
        为按钮设置按下效果。

        Args:
            button: 要增强的QPushButton对象
        """
        try:
            # 检查按钮是否已安装事件过滤器
            if button in self.__enhanced_buttons:
                self.__logger.debug(
                    "按钮 %s 已增强，跳过", button.objectName() or "Unnamed"
                )
                return

            # 保存按钮原始样式和阴影效果
            original_style = button.styleSheet()

            # 存储按钮信息
            self.__enhanced_buttons[button] = {
                "original_style": original_style,
                "is_pressed": False,
            }

            # 直接连接按钮的按下和释放信号，替代事件过滤器
            button.pressed.connect(lambda btn=button: self.__on_button_pressed(btn))
            button.released.connect(lambda btn=button: self.__on_button_released(btn))
            button.clicked.connect(
                lambda checked=False, btn=button: self.__on_button_clicked(btn)
            )

            # 强制应用默认样式表，确保按钮有适当的视觉效果
            self.__apply_default_style(button)

            # 连接按钮销毁信号，以便清理
            button.destroyed.connect(
                lambda obj=None, btn=button: self.__cleanup_button(btn)
            )

            self.__logger.debug(
                "成功为按钮 %s 设置按下效果", button.objectName() or "Unnamed"
            )
        except Exception as e:
            self.__logger.error(
                "设置按钮 %s 的按下效果时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __apply_default_style(self, button):
        """
        应用默认样式到按钮，确保按钮有适当的视觉效果基础。

        Args:
            button: 要应用样式的QPushButton
        """
        try:
            # 获取原始样式
            original_style = self.__enhanced_buttons[button]["original_style"]

            # 如果按钮没有样式，添加一个基本样式
            if not original_style.strip():
                base_style = """
                    QPushButton {
                        border: 1px solid #8f8f91;
                        border-radius: 3px;
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                     stop: 0 #f6f7fa, stop: 1 #dadbde);
                        min-width: 40px;
                        padding: 3px;
                    }
                """
                button.setStyleSheet(base_style)
                self.__enhanced_buttons[button]["original_style"] = base_style
                self.__logger.debug(
                    "已应用默认样式到按钮 %s", button.objectName() or "Unnamed"
                )
        except Exception as e:
            self.__logger.error(
                "应用默认样式到按钮 %s 时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __on_button_pressed(self, button):
        """
        处理按钮按下事件。

        Args:
            button: 被按下的QPushButton
        """
        try:
            if button not in self.__enhanced_buttons:
                return

            # 标记按钮为已按下状态
            self.__enhanced_buttons[button]["is_pressed"] = True

            # 创建按下效果
            self.__create_press_effect(button)

            self.__logger.debug("按钮 %s 被按下", button.objectName() or "Unnamed")
        except Exception as e:
            self.__logger.error(
                "处理按钮 %s 按下事件时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __on_button_released(self, button):
        """
        处理按钮释放事件。

        Args:
            button: 被释放的QPushButton
        """
        try:
            if button not in self.__enhanced_buttons:
                return

            # 标记按钮为未按下状态
            self.__enhanced_buttons[button]["is_pressed"] = False

            # 移除按下效果
            self.__remove_press_effect(button)

            self.__logger.debug("按钮 %s 被释放", button.objectName() or "Unnamed")
        except Exception as e:
            self.__logger.error(
                "处理按钮 %s 释放事件时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __on_button_clicked(self, button):
        """
        处理按钮点击事件。

        确保无论点击事件如何触发，按钮都能正确恢复原始状态。

        Args:
            button: 被点击的QPushButton
        """
        try:
            # 延迟一小段时间后确保按钮恢复原始状态
            QTimer.singleShot(50, lambda: self.__ensure_button_reset(button))
        except Exception as e:
            self.__logger.error(
                "处理按钮 %s 点击事件时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __ensure_button_reset(self, button):
        """
        确保按钮重置回原始状态。

        Args:
            button: 要重置的QPushButton
        """
        try:
            if button not in self.__enhanced_buttons:
                return

            # 无论当前状态如何，都恢复按钮原始状态
            self.__remove_press_effect(button)
            self.__enhanced_buttons[button]["is_pressed"] = False
        except Exception as e:
            self.__logger.error(
                "重置按钮 %s 状态时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __create_press_effect(self, button):
        """
        创建按钮按下效果。

        Args:
            button: 要应用效果的QPushButton
        """
        try:
            # 使用更安全的方法实现按钮按下效果，避免改变按钮大小

            # 获取原始样式
            original_style = self.__enhanced_buttons[button]["original_style"]

            # 设置按钮属性以标记按下状态
            button.setProperty("customPressed", True)

            # 使用阴影和颜色变化实现视觉效果，而不改变大小和边框
            press_style = original_style.strip()

            # 存储按钮原始位置
            original_pos = button.pos()
            self.__enhanced_buttons[button]["original_pos"] = original_pos

            # 应用微小偏移而不改变按钮大小和边框
            button.move(original_pos.x() + 1, original_pos.y() + 1)

            # 应用视觉样式但不改变按钮尺寸
            if "QPushButton:pressed" not in press_style:
                # 仅添加背景色变化，不更改padding和border
                if press_style:
                    if not press_style.endswith("}"):
                        press_style += "\n}"

                    press_style += """
                    QPushButton[customPressed="true"] {
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                     stop: 0 #d7d7d7, stop: 1 #b0b0b0);
                    }
                    """
                else:
                    # 默认样式，只改变背景色
                    press_style = """
                    QPushButton {
                        border: 1px solid #8f8f91;
                        border-radius: 3px;
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                     stop: 0 #f6f7fa, stop: 1 #dadbde);
                        min-width: 40px;
                    }
                    QPushButton[customPressed="true"] {
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                     stop: 0 #d7d7d7, stop: 1 #b0b0b0);
                    }
                    """

                button.setStyleSheet(press_style)

            self.__logger.debug(
                "已创建按钮 %s 的按下效果", button.objectName() or "Unnamed"
            )
        except Exception as e:
            self.__logger.error(
                "创建按钮 %s 的按下效果时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __remove_press_effect(self, button):
        """
        移除按钮按下效果。

        Args:
            button: 要移除效果的QPushButton
        """
        try:
            # 移除按钮的自定义属性
            button.setProperty("customPressed", False)

            # 恢复原始样式
            original_style = self.__enhanced_buttons[button]["original_style"]
            button.setStyleSheet(original_style)

            # 恢复原始位置（如果存储了）
            if "original_pos" in self.__enhanced_buttons[button]:
                original_pos = self.__enhanced_buttons[button]["original_pos"]
                button.move(original_pos)

            self.__logger.debug(
                "已移除按钮 %s 的按下效果", button.objectName() or "Unnamed"
            )
        except Exception as e:
            self.__logger.error(
                "移除按钮 %s 的按下效果时出错: %s\n%s",
                button.objectName() or "Unnamed",
                e,
                traceback.format_exc(),
            )

    def __cleanup_button(self, button):
        """
        当按钮被销毁时清理相关资源。

        Args:
            button: 被销毁的QPushButton
        """
        try:
            if button in self.__enhanced_buttons:
                self.__enhanced_buttons.pop(button, None)
                self.__logger.debug(
                    "按钮 %s 已从增强列表中移除",
                    button.objectName() if hasattr(button, "objectName") else "Unknown",
                )
        except Exception as e:
            self.__logger.error("清理按钮资源时出错: %s\n%s", e, traceback.format_exc())

    def refresh(self):
        """
        刷新按钮增强效果，查找新添加的按钮并应用效果。

        如果容器中动态添加了新按钮，可以调用此方法重新扫描并应用效果。

        示例:
            ```python
            # 添加新按钮后刷新
            new_button = QPushButton("新按钮", container)
            enhancer.refresh()
            ```
        """
        try:
            self.__logger.debug("开始刷新按钮增强效果")
            self.__find_and_enhance_buttons(self.__container)
            self.__logger.debug(
                "按钮增强效果刷新完成，当前共 %d 个按钮", len(self.__enhanced_buttons)
            )
        except Exception as e:
            self.__logger.error(
                "刷新按钮增强效果时出错: %s\n%s", e, traceback.format_exc()
            )
