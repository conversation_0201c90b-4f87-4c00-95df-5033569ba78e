#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
这是一个临时文件，用于修复 ManagedMultiProcess 类的 stop_all 方法和添加 on_all_processes_completed 和 __check_processes_completed 方法
"""

import threading
import time
import logging
import traceback
from multiprocessing import Process
from typing import Callable, Any

# 正确格式的 __check_processes_completed 方法


def __check_processes_completed(self):
    """
    私有方法：检查所有进程是否正常执行完成。
    在stop_all中调用，用于检测所有进程是否正常执行完成。
    """
    logger.debug("开始检查进程是否正常完成...")

    # 只有没有进程或所有进程已退出且退出码为0时，才认为所有进程正常完成
    if not hasattr(self, 'worker_processes') or not isinstance(self.worker_processes, list) or not self.worker_processes:
        logger.info("没有活动的工作进程，视为正常完成。")
        # 触发进程正常执行完成事件
        self.__process_event_manager.trigger_event(ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP)
        return True

    # 检查每个进程的状态
    all_completed = True
    for proc in self.worker_processes:
        if isinstance(proc, Process) and proc.is_alive():
            all_completed = False
            break

    if all_completed:
        logger.info("所有工作进程已正常执行完成。")
        # 触发进程正常执行完成事件
        self.__process_event_manager.trigger_event(ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP)
        return True

    return False

# 正确格式的 on_all_processes_completed 方法


def on_all_processes_completed(self, callback: Callable[[Any], None]) -> None:
    """
    注册一个回调函数，当所有多进程正常执行完成后调用。

    该方法会在stop_all被调用时启动检测逻辑，当所有进程正常完成时执行回调函数。
    回调函数会异步执行，不会阻塞其他代码的执行。

    Args:
        callback: 回调函数，接收ManagedMultiProcess实例作为参数

    Returns:
        None

    Example:
        ```python
        # 创建多进程管理器实例
        mp = ManagedMultiProcess(data, process_func, num_processes=4)

        # 注册回调函数
        def on_completed(mp_instance):
            results = mp_instance.get_results()
            print(f"所有进程已完成，结果: {results}")

        mp.on_all_processes_completed(on_completed)

        # 启动处理
        mp.run()

        # 在适当时机停止
        mp.stop_all()  # 这会触发检测，当所有进程正常完成时会调用on_completed
        ```
    """
    # 使用进程事件管理器注册回调
    self.__process_event_manager.register_event_handler(
        ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP,
        callback
    )
    logger.debug(f"已注册进程正常完成回调函数: {callback.__name__}")

# stop_all 方法中的异步检测代码


def stop_all_async_detection_code():
    """
    这个部分是 stop_all 方法中需要添加的代码
    应该在设置停止标志后添加
    """
    # 设置停止标志
    self.__is_stopping = True
    self.__is_running = False
    self.__status = "正在停止"

    # 添加异步检测进程是否正常完成的逻辑
    def _async_check_processes():
        try:
            logger.debug("开始异步检测进程是否正常完成...")
            while self.__is_stopping:
                # 检查进程是否已经完成
                if self.__check_processes_completed():
                    logger.info("异步检测确认所有进程已正常完成")
                    break
                time.sleep(0.1)  # 短暂休眠以避免占用过多CPU
        except Exception as e:
            logger.error(f"异步检测进程完成状态时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    # 启动异步检测线程
    check_thread = threading.Thread(target=_async_check_processes, daemon=True)
    check_thread.start()
    logger.debug("已启动异步进程完成检测线程")
