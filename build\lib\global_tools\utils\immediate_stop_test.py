#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ManagedMultiProcess的立即停止模式与普通停止模式的对比
"""

from create_process import ManagedMultiProcess
import os
import sys
import time
import threading
import multiprocessing

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入ManagedMultiProcess类

# 定义一个长时间运行的测试函数，模拟耗时任务


def long_running_task(shared_data, item, lock, *args):
    """
    模拟一个长时间运行的任务，打印进度并休眠

    这个任务会运行10秒，每秒打印一次，不会主动检查停止标志
    """
    print(f"开始处理任务: {item}")
    for i in range(20):  # 每个任务会运行20秒
        print(f"任务{item} 进度: {i+1}/20")
        time.sleep(1)  # 睡眠1秒
    print(f"任务{item}完成")


def test_normal_stop():
    """测试普通停止模式（优雅停止）"""
    print("\n" + "=" * 50)
    print("测试普通停止模式（优雅停止）")
    print("=" * 50)

    start_time = time.time()

    # 创建ManagedMultiProcess实例，只处理2个项目以加快测试速度
    mp = ManagedMultiProcess([1, 2], long_running_task, num_processes=2)
    mp.run()

    # 2秒后尝试停止进程
    def stop_thread():
        time.sleep(2)
        print("\n开始尝试停止进程（普通模式）...")
        stop_start = time.time()
        mp.stop_all(immediate=False, force_timeout=2.0)
        stop_duration = time.time() - stop_start
        print(f"停止操作耗时: {stop_duration:.2f}秒")

    t = threading.Thread(target=stop_thread)
    t.start()

    # 等待所有进程完成或被强制终止
    mp.wait_all(timeout=30)
    t.join()

    total_duration = time.time() - start_time
    print(f"普通停止模式总耗时: {total_duration:.2f}秒")


def test_immediate_stop():
    """测试立即停止模式（强制终止）"""
    print("\n" + "=" * 50)
    print("测试立即停止模式（强制终止）")
    print("=" * 50)

    start_time = time.time()

    # 创建ManagedMultiProcess实例，只处理2个项目以加快测试速度
    mp = ManagedMultiProcess([1, 2], long_running_task, num_processes=2)
    mp.run()

    # 2秒后尝试立即停止进程
    def stop_thread():
        time.sleep(2)
        print("\n开始尝试停止进程（立即模式）...")
        stop_start = time.time()
        mp.stop_all(immediate=True)
        stop_duration = time.time() - stop_start
        print(f"停止操作耗时: {stop_duration:.2f}秒")

    t = threading.Thread(target=stop_thread)
    t.start()

    # 等待所有进程完成或被强制终止
    mp.wait_all(timeout=30)
    t.join()

    total_duration = time.time() - start_time
    print(f"立即停止模式总耗时: {total_duration:.2f}秒")


if __name__ == "__main__":
    # 防止递归启动进程（在Windows平台上特别重要）
    multiprocessing.freeze_support()

    # 先测试普通停止模式
    test_normal_stop()

    # 等待一段时间，确保所有资源释放
    time.sleep(2)

    # 再测试立即停止模式
    test_immediate_stop()

    print("\n" + "=" * 50)
    print("测试结论: 立即停止模式可以更快地终止进程，但可能不会正确清理资源")
    print("普通停止模式会等待当前任务完成或超时后才强制终止，更安全但响应较慢")
    print("=" * 50)
