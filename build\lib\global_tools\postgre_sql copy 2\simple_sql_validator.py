#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化但有效的SQL表达式验证器

基于原有修复逻辑的改进版本，专门解决IS NULL和IS NOT NULL的验证问题。
"""

import re
from typing import List, Set


def is_valid_sql_expression(sql_where: str) -> bool:
    """
    简化的SQL表达式验证器
    
    专门修复IS NULL和IS NOT NULL条件的验证问题，同时保持对其他SQL表达式的支持。
    
    Args:
        sql_where (str): SQL WHERE子句片段
        
    Returns:
        bool: True表示有效的SQL表达式，False表示仅为字段名或无效表达式
        
    Examples:
        >>> is_valid_sql_expression('("obb_data" IS NOT NULL)')
        True
        >>> is_valid_sql_expression('("segmentation_data" IS NOT NULL)')
        True
        >>> is_valid_sql_expression('"field_name"')
        False
        >>> is_valid_sql_expression('("id" = 1 AND "name" LIKE \'%test%\')')
        True
    """
    if not sql_where or not isinstance(sql_where, str):
        return False

    # 预处理：标准化空白字符（制表符、换行符等转换为空格）
    normalized_sql = re.sub(r'\s+', ' ', sql_where)

    # 1. 基础运算符检查 - 确保包含有效的SQL运算符
    operators = [
        '=', '>', '<', '>=', '<=', '!=', '<>',
        ' in ', ' like ', ' ilike ', ' between ', ' is ', ' not ', ' exists ',
        ' regexp ', ' similar ', ' any ', ' all ', ' some ',
        '->', '->>', '#>', '#>>', '@>', '<@', '?', '?|', '?&',
        '||', '&&', '~', '~*', '!~', '!~*'
    ]

    has_operator = any(op in normalized_sql.lower() for op in operators)
    
    if not has_operator:
        return False
    
    # 2. 改进的单字段检测 - 智能识别SQL关键字
    # 移除常见的SQL关键字和运算符，然后检查剩余部分
    sql_keywords_to_remove = [
        'is', 'not', 'null', 'true', 'false', 'and', 'or', 'in', 'like', 'ilike',
        'between', 'exists', 'any', 'all', 'some', 'regexp', 'similar', 'to',
        'case', 'when', 'then', 'else', 'end', 'distinct'
    ]
    
    # 转换为小写进行关键字匹配，使用标准化后的SQL
    cleaned = normalized_sql.lower()
    
    # 移除SQL关键字（使用单词边界确保精确匹配）
    for keyword in sql_keywords_to_remove:
        # 使用正则表达式确保只匹配完整的关键字，不影响字段名中包含关键字的情况
        pattern = r'\b' + re.escape(keyword) + r'\b'
        cleaned = re.sub(pattern, ' ', cleaned)
    
    # 移除运算符
    for op in operators:
        cleaned = cleaned.replace(op, ' ')
    
    # 移除标点符号、括号和多余空格（包括制表符、换行符等）
    cleaned = re.sub(r'[^\w\s]', ' ', cleaned)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    # 检查剩余部分是否只是单个标识符
    if not cleaned:
        # 如果清理后为空，说明只包含关键字和运算符，这是有效的SQL表达式
        return True
    
    # 分割剩余的标识符
    remaining_parts = cleaned.split()
    
    # 如果只剩下一个标识符，且该标识符是有效的字段名，则可能是单字段
    if len(remaining_parts) == 1 and remaining_parts[0].isidentifier():
        # 进一步检查：如果原始SQL包含复杂结构（如括号、多个运算符），则认为是有效表达式
        complex_indicators = ['(', ')', ' and ', ' or ', 'between', 'in (', 'exists']
        has_complex_structure = any(indicator in normalized_sql.lower() for indicator in complex_indicators)
        return has_complex_structure
    
    # 如果剩余多个标识符或包含复杂结构，则认为是有效的SQL表达式
    return True


def validate_sql_expression_detailed(sql_where: str) -> dict:
    """
    详细的SQL表达式验证
    
    Args:
        sql_where (str): SQL WHERE子句片段
        
    Returns:
        dict: 详细的验证结果
    """
    result = {
        'is_valid': False,
        'error_type': None,
        'error_message': '',
        'confidence_score': 0.0,
        'suggestions': []
    }
    
    if not sql_where or not isinstance(sql_where, str):
        result['error_type'] = 'empty_expression'
        result['error_message'] = 'SQL表达式为空或类型无效'
        result['confidence_score'] = 1.0
        return result
    
    # 使用简化验证器
    is_valid = is_valid_sql_expression(sql_where)
    
    if is_valid:
        result['is_valid'] = True
        result['confidence_score'] = 0.85
    else:
        # 检查是否是单字段
        cleaned = re.sub(r'[^\w]', '', sql_where)
        if cleaned.isidentifier():
            result['error_type'] = 'single_field'
            result['error_message'] = '表达式只包含单个字段名'
            result['suggestions'] = ['添加运算符和值来构成完整的条件表达式']
        else:
            result['error_type'] = 'operator_error'
            result['error_message'] = '未检测到有效的SQL运算符'
            result['suggestions'] = ['确认这是一个完整的SQL表达式']
        
        result['confidence_score'] = 0.9
    
    return result


# 测试函数
def test_simple_validator():
    """测试简化验证器"""
    test_cases = [
        # IS NULL 和 IS NOT NULL 测试
        ('("obb_data" IS NOT NULL)', True),
        ('("segmentation_data" IS NOT NULL)', True),
        ('("field" IS NULL)', True),
        ('("very_long_field_name_with_underscores" IS NOT NULL)', True),
        
        # 比较运算符测试
        ('("id" = 1)', True),
        ('("age" > 18)', True),
        ('("name" LIKE \'%test%\')', True),
        
        # 复杂表达式测试
        ('(("id" > 0) AND ("name" IS NOT NULL))', True),
        ('("age" BETWEEN 18 AND 65)', True),
        
        # 单字段测试（应该返回False）
        ('"field_name"', False),
        ('field_name', False),
        ('"segmentation_data"', False),
        
        # 边界条件测试
        ('', False),
        ('()', False),
        ('("field")', False),
    ]
    
    print("简化SQL验证器测试:")
    print("=" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for sql_expr, expected in test_cases:
        result = is_valid_sql_expression(sql_expr)
        status = "✓ 通过" if result == expected else "✗ 失败"
        
        print(f"{sql_expr:<45} -> {result:<5} (期望: {expected:<5}) {status}")
        
        if result == expected:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed == total


if __name__ == "__main__":
    test_simple_validator()
