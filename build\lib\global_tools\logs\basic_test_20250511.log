2025-05-11 18:50:32,714 - 7416 - MainThread - basic_test - INFO - basic_test.py:60 - 开始测试进程池管理器
2025-05-11 18:50:32,714 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:38 - 初始化进程池管理类
2025-05-11 18:50:32,714 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:42 - 进程池进程数量: 2
2025-05-11 18:50:32,714 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:49 - 任务数量: 5
2025-05-11 18:50:32,983 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:169 - 创建进程池，进程数量: 2
2025-05-11 18:50:33,048 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:172 - 进程池创建成功
2025-05-11 18:50:33,049 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:183 - 进程状态初始化完成
2025-05-11 18:50:33,049 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:79 - 进程池管理类初始化完成
2025-05-11 18:50:33,051 - 7416 - MainThread - basic_test - INFO - basic_test.py:77 - 开始执行任务
2025-05-11 18:50:33,051 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:410 - 执行所有任务，任务数量: 5
2025-05-11 18:50:33,051 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:442 - 已提交所有可序列化任务: 5/5
2025-05-11 18:50:33,052 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:446 - 等待所有任务完成
2025-05-11 18:50:33,052 - 7416 - MainThread - basic_test - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:50:33,052 - 7416 - MainThread - basic_test - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:50:33,053 - 7416 - MainThread - basic_test - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:50:33,053 - 7416 - MainThread - basic_test - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:50:33,053 - 7416 - MainThread - basic_test - ERROR - process_pool_manager.py:454 - 获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons
2025-05-11 18:50:33,053 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:461 - 所有任务已完成
2025-05-11 18:50:33,054 - 7416 - MainThread - basic_test - INFO - basic_test.py:84 - 任务结果: [{'status': 'error', 'error': '获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons'}, {'status': 'error', 'error': '获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons'}, {'status': 'error', 'error': '获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons'}, {'status': 'error', 'error': '获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons'}, {'status': 'error', 'error': '获取任务结果异常: Pickling an AuthenticationString object is disallowed for security reasons'}]
2025-05-11 18:50:33,054 - 7416 - MainThread - basic_test - INFO - basic_test.py:85 - 共享数据结果: []
2025-05-11 18:50:33,055 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:194 - 关闭进程池
2025-05-11 18:50:33,155 - 7416 - MainThread - basic_test - DEBUG - process_pool_manager.py:198 - 进程池已关闭
2025-05-11 18:50:33,155 - 7416 - MainThread - basic_test - INFO - basic_test.py:90 - 测试成功!
