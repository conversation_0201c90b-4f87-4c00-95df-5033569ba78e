import json
import traceback
import time
import datetime
import logging
import psycopg2

# from global_tools.utils import Colors #确保这一行被删除或注释
from .db_type_converter import DBTypeConverter

class Colors: # 添加本地Colors类
    GREEN = "\033[92m"
    RED = "\033[91m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    RESET = "\033[0m"
    # 对于无颜色输出:
    # GREEN = ""
    # RED = ""
    # YELLOW = ""
    # BLUE = ""
    # RESET = ""

class DataOperations1:
    """
    数据操作类，包含数据操作相关的方法(第1部分)
    """
    
    @staticmethod
    def filter_insert_data_by_schema(client, insert_data, table_schema):
        """
        检查插入数据的JSON字段是否存在于创建表的JSON字段中，如果不存在则删除。

        参数:
            client: PostgreSQLClient实例
            insert_data (dict 或 list): 要插入的数据，可以是单个字典或字典列表
            table_schema (dict): 表结构的JSON描述，支持完整格式和简化格式

        返回:
            dict: {
                "filtered_data": 过滤后的数据（保持原始数据结构：字典或列表）,
                "removed_fields": 被删除的字段列表,
                "success": 是否成功,
                "message": 处理信息
            }

        示例:
            # 完整格式示例
            table_schema = {
                "columns": {
                    "name": {"type": "VARCHAR(255)"},
                    "age": {"type": "INTEGER"}
                },
                ...
            }

            # 简化格式示例
            table_schema = {
                "name": {"type": "VARCHAR(255)"},
                "age": {"type": "INTEGER"}
            }

            # 输入数据示例
            insert_data = {"name": "张三", "age": 25, "invalid_field": "value"}
            # 或
            insert_data = [
                {"name": "张三", "age": 25, "invalid_field": "value"},
                {"name": "李四", "age": 30, "another_invalid": "value"}
            ]
        """
        try:
            # 获取有效的列名集合
            valid_columns = set()

            # 处理完整格式
            if isinstance(table_schema, dict) and "columns" in table_schema:
                valid_columns = set(table_schema["columns"].keys())
            # 处理简化格式
            else:
                valid_columns = set(table_schema.keys())

            removed_fields = set()  # 用于存储所有被删除的字段

            # 根据数据类型处理
            if isinstance(insert_data, list):
                # 处理数据列表
                filtered_data = []
                for item in insert_data:
                    if not isinstance(item, dict):
                        raise ValueError(f"数据项必须是字典类型，得到: {type(item)}")
                    # 收集当前项中需要删除的字段
                    current_removed = {
                        k for k in item.keys() if k not in valid_columns}
                    removed_fields.update(current_removed)
                    # 只保留有效的列
                    filtered_item = {k: v for k,
                                     v in item.items() if k in valid_columns}
                    filtered_data.append(filtered_item)
            elif isinstance(insert_data, dict):
                # 处理单个数据字典
                # 收集需要删除的字段
                removed_fields = {
                    k for k in insert_data.keys() if k not in valid_columns}
                # 只保留有效的列
                filtered_data = {
                    k: v for k, v in insert_data.items() if k in valid_columns}
            else:
                raise ValueError(f"插入数据必须是字典或字典列表，得到: {type(insert_data)}")

            # 准备返回结果
            result = {
                "filtered_data": filtered_data,
                "removed_fields": list(removed_fields),  # 转换为列表以便JSON序列化
                "success": True,
                "message": f"成功过滤数据。删除了 {len(removed_fields)} 个无效字段: {', '.join(removed_fields) if removed_fields else '无'}"
            }

            # client.logger.info(result["message"])
            return result

        except Exception as e:
            error_msg = f"过滤插入数据时发生错误: {str(e)}"
            client.logger.error(error_msg)
            traceback.print_exc()  # 打印详细异常堆栈
            return {
                "filtered_data": None,
                "removed_fields": [],
                "success": False,
                "message": error_msg
            }
            
    @staticmethod
    def insert_data(client, table_name, data_json):
        """
        向表中插入数据，数据以 JSON 格式描述。
        支持多种JSON格式：
        1. {"rows": [{"col1": "val1", ...}, {"col1": "val2", ...}]}
        2. [{"col1": "val1", ...}, {"col1": "val2", ...}] (直接是行列表)
        3. {"col1": "val1", "col2": "val2", ...} (单行数据)
        """
        transaction_status = "not_started"
        data_to_insert_list = [] # 用于存放处理后的行数据列表
        conn = None # 初始化 conn
        cursor = None # 在 try 块外初始化 cursor

        start_time = client._get_timestamp()

        try:
            # 1. 统一输入数据格式为行列表 (data_to_insert_list)
            if isinstance(data_json, str):
                try:
                    parsed_data = json.loads(data_json)
                except json.JSONDecodeError as e:
                    client.logger.error(f"JSON解析错误: {e}, 输入: {data_json}")
                    return {"success": False, "error": f"JSON解析错误: {e}", "table_name": table_name}
            else:
                parsed_data = data_json

            if isinstance(parsed_data, dict):
                if "rows" in parsed_data and isinstance(parsed_data["rows"], list):
                    data_to_insert_list = parsed_data["rows"]
                else: # 认为是单行数据
                    data_to_insert_list = [parsed_data]
            elif isinstance(parsed_data, list):
                data_to_insert_list = parsed_data
            else:
                client.logger.error(f"不支持的数据格式: {type(parsed_data)}。数据应为字典、列表或JSON字符串。")
                return {"success": False, "error": "不支持的数据格式", "table_name": table_name}

            if not data_to_insert_list:
                client.logger.warning(f"没有数据需要插入到表 '{table_name}'")
                return {"success": True, "message": "没有数据需要插入。", "inserted_count": 0, "expected_count": 0, "table_name": table_name}

            # 确保列表中的每一项都是字典
            if not all(isinstance(item, dict) for item in data_to_insert_list):
                client.logger.error(f"数据列表中的项不全是字典: {data_to_insert_list[:5]}") # 日志记录前5条有问题的
                return {"success": False, "error": "数据列表中的项不全是字典", "table_name": table_name}
                
            client.logger.info(f"开始向表 '{table_name}' 插入数据: {len(data_to_insert_list)} 条记录")

            # 2. 检查表是否存在
            if not client._table_exists(table_name):
                error_msg = f"表 '{table_name}' 不存在。"
                client.logger.error(error_msg)
                return {"success": False, "error": error_msg, "table_name": table_name}

            # 3. 获取列信息并准备SQL
            # get_all_columns 返回的是带引号的列名字符串，例如 "\"col1\"", "\"col2\""
            # 或者在没有列时返回 "*"
            table_columns_info_str = client.get_all_columns(table_name)
            if not table_columns_info_str or table_columns_info_str == "*":
                error_msg = f"无法获取表 '{table_name}' 的列信息，或表结构不完整。"
                client.logger.error(error_msg)
                return {"success": False, "error": error_msg, "table_name": table_name}
            
            # 分割并去除每个列名周围的引号，以进行匹配
            valid_column_names = {col.strip().strip('"') for col in table_columns_info_str.split(',')}
            
            # 使用第一条有效数据来确定列的顺序和数量
            first_item_keys = list(data_to_insert_list[0].keys())
            # filtered_keys 现在应该与不带引号的列名进行比较
            filtered_keys = [key for key in first_item_keys if key in valid_column_names]
            
            # 对列名进行安全处理，替换内部双引号为两个双引号，然后用双引号包裹
            processed_keys = []
            for key in filtered_keys:
                escaped_key = key.replace('"', '""')
                processed_keys.append(f'"{escaped_key}"')
            columns_sql_str = ", ".join(processed_keys)

            placeholders_str = ", ".join(["%s"] * len(filtered_keys))
            # 在表名和列名中处理可能存在的双引号
            safe_table_name = table_name.replace('"', '""')

            # 修复点：如果 filtered_keys 为空，自动执行 DEFAULT VALUES（提前 return，彻底避免后续逻辑干扰）
            if not filtered_keys:
                sql = f'INSERT INTO "{safe_table_name}" DEFAULT VALUES'
                conn = client._get_connection()
                cursor = conn.cursor()
                transaction_status = "started"
                for _ in data_to_insert_list:
                    cursor.execute(sql)
                inserted_count = len(data_to_insert_list)
                if not client.in_transaction:
                    conn.commit()
                    transaction_status = "committed"
                else:
                    transaction_status = "in_transaction"
                success_message = f"{Colors.GREEN}成功向表 '{table_name}' 插入 {inserted_count} 行数据 (共尝试 {len(data_to_insert_list)} 行){Colors.RESET}"
                client.logger.info(success_message)
                return {
                    "success": True,
                    "message": success_message,
                    "inserted_count": inserted_count,
                    "expected_count": len(data_to_insert_list),
                    "transaction_status": transaction_status,
                    "table_name": table_name,
                    "timestamp": client._get_timestamp(),
                    "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
                }

            # filtered_keys 非空时，正常批量插入
            sql = f'INSERT INTO "{safe_table_name}" ({columns_sql_str}) VALUES ({placeholders_str})'
            values_list = []
            for item in data_to_insert_list: # item 现在确保是字典
                values_list.append(tuple(client._adapt_value_for_db(item.get(key)) for key in filtered_keys))

            if not values_list: # 理论上如果 data_to_insert_list 非空，这里也不会为空
                client.logger.warning(f"没有有效的数据可以插入到表 '{table_name}' 的列: {filtered_keys}")
                return {"success": True, "message": "没有有效数据插入。", "inserted_count": 0, "table_name": table_name}

            # 4. 执行插入
            conn = client._get_connection()
            cursor = conn.cursor() # cursor 在这里赋值
            transaction_status = "started"
            cursor.executemany(sql, values_list)
            inserted_count = cursor.rowcount

            if inserted_count != len(data_to_insert_list):
                client.logger.warning(f"批量插入行数不匹配：预期 {len(data_to_insert_list)}, 实际 {inserted_count}。可能部分行因约束冲突未插入。")
                # 这里不直接回滚，允许部分成功，但需要更明确的错误或成功信息

            if not client.in_transaction:
                conn.commit()
                transaction_status = "committed"
            else:
                transaction_status = "in_transaction"

            success_message = f"{Colors.GREEN}成功向表 '{table_name}' 插入 {inserted_count} 行数据 (共尝试 {len(data_to_insert_list)} 行){Colors.RESET}"
            client.logger.info(success_message)

            return {
                "success": True,
                "message": success_message,
                "inserted_count": inserted_count,
                "expected_count": len(data_to_insert_list),
                "transaction_status": transaction_status,
                "table_name": table_name,
                "timestamp": client._get_timestamp(),
                "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
            }

        except psycopg2.Error as e: # 更具体地捕获psycopg2的错误
            if conn and not client.in_transaction:
                try: conn.rollback()
                except Exception as rb_e: client.logger.error(f"回滚事务失败: {rb_e}")
            transaction_status = "rolled_back_due_to_db_error"
            
            pg_error_code = getattr(e, 'pgcode', None)
            detail_message = str(e).strip()
            
            if isinstance(e, psycopg2.errors.UniqueViolation):
                error_type = "UniqueViolation"
                detail_message = f"UniqueViolation: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.ForeignKeyViolation):
                error_type = "ForeignKeyViolation"
                detail_message = f"ForeignKeyViolation: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.NotNullViolation):
                error_type = "NotNullViolation"
                detail_message = f"NotNullViolation: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.CheckViolation):
                error_type = "CheckViolation"
                detail_message = f"CheckViolation: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.StringDataRightTruncation):
                error_type = "StringDataRightTruncation"
                detail_message = f"StringDataRightTruncation: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.NumericValueOutOfRange):
                error_type = "NumericValueOutOfRange"
                detail_message = f"NumericValueOutOfRange: {detail_message}, 错误代码: {pg_error_code}"
            elif isinstance(e, psycopg2.errors.InvalidTextRepresentation): # 例如，向int列插入文本
                error_type = "InvalidTextRepresentation"
                detail_message = f"InvalidTextRepresentation: {detail_message}, 错误代码: {pg_error_code}"
            else:
                error_type = "DatabaseError" # 通用数据库错误
                if pg_error_code:
                    detail_message = f"数据库错误: {detail_message}, 错误代码: {pg_error_code}"

            error_log_message = f"{Colors.RED}插入数据到表 '{table_name}' 失败 ({error_type}): {detail_message}{Colors.RESET}"
            client.logger.error(error_log_message)
            traceback.print_exc()  # 打印详细异常堆栈
            from .exceptions import ExecutionError
            raise ExecutionError(
                message=detail_message,
                error_code=pg_error_code,
                sql=sql,
                params=values_list if 'values_list' in locals() else None
            )
            
        except Exception as e: # 其他通用错误
            if conn and not client.in_transaction: # 确保 conn 已被赋值
                try: conn.rollback()
                except Exception as rb_e: client.logger.error(f"回滚事务失败: {rb_e}")
            transaction_status = "rolled_back_due_to_general_error"
            
            error_message = f"{Colors.RED}插入数据到表 '{table_name}' 失败: {str(e)}{Colors.RESET}"
            client.logger.error(error_message)
            traceback.print_exc()  # 打印详细异常堆栈
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "inserted_count": 0,
                "expected_count": len(data_to_insert_list) if data_to_insert_list else 0,
                "transaction_status": transaction_status,
                "error_type": "GeneralError",
                "timestamp": client._get_timestamp(),
                "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
            }
            
        finally:
            if cursor: # 确保 cursor 已被赋值
                try: cursor.close()
                except Exception: pass
            if conn and not client.in_transaction: # 确保 conn 已被赋值
                client._release_connection() 