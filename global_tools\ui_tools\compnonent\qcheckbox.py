import os
import json
import logging
import traceback
from typing import List, Optional, Dict, Union, Callable, Any, Set, Tuple
from functools import partial
import time
import re
import threading
from threading import Lock
from pathlib import Path
from collections import deque

from PyQt5.QtWidgets import (QWidget, QLayout, QGridLayout, QCheckBox,
                             QVBoxLayout, QHBoxLayout, QGroupBox, QFrame, QTextEdit, QLineEdit)
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, Qt, QTimer, QThread, QMutex, QMutexLocker, QEvent
from PyQt5 import sip  # 导入sip模块，用于检查Qt对象是否已被销毁
from global_tools.utils import ClassInstanceManager, Logger, LogLevel, Colors
from global_tools.ui_tools import LogOutput


class CheckBoxManager(QObject):
    """管理复选框控件列表，符合 PyQt5 信号槽规范（单例模式）

    CheckBoxManager 提供了统一管理复选框控件列表的功能，包括操作和监控复选框状态。
    它符合 PyQt5 的信号槽机制，可以通过信号监听复选框状态变化。

    ===== 单例模式说明 =====
    该类实现了线程安全的单例模式：
    - 首次创建时需要传入复选框列表和logger
    - 后续调用可以不传参数，直接返回已创建的实例
    - 支持重置单例实例，重新创建新的管理器
    - 在多线程环境下保证线程安全

    使用示例:
    ---------

    1. 创建管理器（单例模式）:
    ```python
    # 准备复选框列表
    checkboxes = [checkbox1, checkbox2, checkbox3]  # 你的复选框控件列表
    logger = ClassInstanceManager.get_instance(key="ui_logger")

    # 方式1：直接创建管理器（首次创建）
    manager = CheckBoxManager(checkboxes, logger=logger)

    # 方式2：使用推荐的类方法创建（首次创建）
    manager = CheckBoxManager.get_instance(checkboxes, logger=logger)

    # 方式3：后续获取已创建的实例（无需参数）
    manager = CheckBoxManager.get_instance()
    # 或者
    manager = CheckBoxManager([])  # 传入空列表使用缓存参数

    # 也可以传入从不同来源收集的复选框
    all_checkboxes = []
    all_checkboxes.extend(widget1.findChildren(QCheckBox))
    all_checkboxes.extend(widget2.findChildren(QCheckBox))
    manager = CheckBoxManager.get_instance(all_checkboxes, logger=logger)
    ```

    2. 获取复选框:
    ```python
    # 获取所有复选框
    all_boxes = manager.get_all_checkboxes()

    # 获取所有选中的复选框
    checked_boxes = manager.get_checked_boxes()

    # 获取所有未选中的复选框
    unchecked_boxes = manager.get_unchecked_boxes()

    # 获取未选中且文本为"选项1"的复选框
    unchecked_option1 = manager.get_unchecked_boxes(filter_text="选项1", inverse=False)

    # 获取未选中且文本不在列表中的复选框
    filtered = manager.get_unchecked_boxes(filter_text=["选项1", "选项2"], inverse=True)
    ```

    3. 获取复选框文本:
    ```python
    # 获取所有选中复选框的文本
    checked_texts = manager.get_checked_texts()
    print(f"选中项: {', '.join(checked_texts)}")
    ```

    4. 通过文本或对象名查找复选框:
    ```python
    # 通过文本查找
    checkbox = manager.get_checkbox_by_text("选项1")
    if checkbox:
        checkbox.setChecked(True)

    # 通过对象名查找
    checkbox = manager.get_checkbox_by_object_name("option1_checkbox")
    if checkbox:
        checkbox.setChecked(True)
    ```

    5. 设置复选框状态:
    ```python
    # 通过文本设置状态
    success = manager.set_checked_by_text("选项1", checked=True)
    if not success:
        print("未找到复选框")

    # 设置所有复选框状态
    manager.set_all_checked(checked=True)  # 全选
    manager.set_all_checked(checked=False) # 全不选
    ```

    6. 使用回调处理复选框点击:
    ```python
    def on_checkbox_clicked(checkbox, checked):
        print(f"复选框 {checkbox.text()} 状态变为: {checked}")

    # 为所有复选框设置点击回调
    manager.set_click_callback(on_checkbox_clicked)
    ```

    7. 使用信号槽监听状态变化:
    ```python
    def on_state_changed(checkbox, checked):
        print(f"复选框 {checkbox.text()} 状态变为: {checked}")

    # 连接信号
    manager.checkbox_checked.connect(on_state_changed)
    ```

    8. 设置互斥选择模式(类似单选按钮):
    ```python
    # 所有复选框互斥
    manager.set_exclusive_selection()

    # 指定文本的复选框互斥
    manager.set_exclusive_selection(filter_text="选项\\d+")  # 正则匹配"选项"后跟数字

    # 指定多个文本模式互斥
    manager.set_exclusive_selection(filter_text=["选项[A-C]", "测试.*"])

    # 添加状态变化回调
    def on_exclusive_changed(checkbox, checked):
        if checked:
            print(f"选中了: {checkbox.text()}")

    manager.set_exclusive_selection(callback=on_exclusive_changed)

    # 使用信号监听互斥选择变化
    manager.exclusive_selection_changed.connect(on_exclusive_changed)
    ```

    9. 通过对象名获取复选框选中状态:
    ```python
    # 获取指定objectName的复选框选中状态
    state = manager.get_checked_state_by_object_name("option1_checkbox")
    if state is not None:
        print(f"复选框选中状态: {state}")
    else:
        print("未找到复选框")
    ```

    10. 单例模式特殊用法:
    ```python
    # 检查实例是否已创建
    if CheckBoxManager.is_instance_created():
        manager = CheckBoxManager.get_instance()
    else:
        # 首次创建
        manager = CheckBoxManager.get_instance(layout, logger=logger)

    # 获取缓存的参数
    args, kwargs = CheckBoxManager.get_cached_args()
    print(f"缓存的容器数量: {len(args)}")

    # 重置单例实例（在需要重新初始化时）
    CheckBoxManager.reset_instance()
    new_manager = CheckBoxManager.get_instance(new_layout, logger=new_logger)

    # 在不同模块中获取同一个实例
    # module_a.py
    manager_a = CheckBoxManager.get_instance()

    # module_b.py
    manager_b = CheckBoxManager.get_instance()
    # manager_a 和 manager_b 是同一个实例
    assert manager_a is manager_b
    ```
    """

    # =================================================================================
    # 单例模式相关类变量
    # =================================================================================
    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _initialized = False  # 初始化标志
    _cached_args = None  # 缓存的构造参数
    _cached_kwargs = None  # 缓存的关键字参数

    # 定义信号
    checkbox_checked = pyqtSignal(QCheckBox, bool)  # 复选框选中状态改变信号
    exclusive_selection_changed = pyqtSignal(QCheckBox, bool)  # 互斥选择状态改变信号

    # 内部信号，用于安全地更新复选框状态
    __safe_set_checked_signal = pyqtSignal(QCheckBox, bool)

    def __new__(cls, checkboxes: List[QCheckBox], logger=None, parent=None):
        """
        创建或返回 CheckBoxManager 的单例实例（线程安全）

        Args:
            checkboxes: 要管理的复选框控件列表，必须是QCheckBox实例的列表
            logger: 日志记录器实例
            parent: 父QObject对象，默认为None

        Returns:
            CheckBoxManager: 单例实例
        """
        # 双重检查锁定模式，确保线程安全
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建新实例
                    cls._instance = super(CheckBoxManager, cls).__new__(cls)
                    cls._initialized = False
                    # 缓存参数供后续使用
                    cls._cached_args = checkboxes
                    cls._cached_kwargs = {'logger': logger, 'parent': parent}
        else:
            # 检查现有实例是否仍然有效
            try:
                if sip.isdeleted(cls._instance):
                    # 实例已被删除，重新创建
                    with cls._lock:
                        if sip.isdeleted(cls._instance):
                            cls._instance = super(CheckBoxManager, cls).__new__(cls)
                            cls._initialized = False
                            # 如果没有传入新参数，使用缓存的参数
                            if not checkboxes and cls._cached_args:
                                cls._cached_args = cls._cached_args
                                cls._cached_kwargs = cls._cached_kwargs
                            else:
                                cls._cached_args = checkboxes
                                cls._cached_kwargs = {'logger': logger, 'parent': parent}
            except RuntimeError:
                # 处理可能的运行时错误
                with cls._lock:
                    cls._instance = super(CheckBoxManager, cls).__new__(cls)
                    cls._initialized = False
                    cls._cached_args = checkboxes if checkboxes else cls._cached_args
                    cls._cached_kwargs = {'logger': logger, 'parent': parent}

        return cls._instance

    def __init__(self, checkboxes: List[QCheckBox], logger=None, parent=None):
        """初始化复选框管理器（单例模式）

        Args:
            checkboxes: 要管理的复选框控件列表，必须是QCheckBox实例的列表
                       首次创建时必须提供，后续调用可以为空（使用缓存的参数）
            logger: 日志记录器实例，首次创建时必须提供
            parent: 父QObject对象，默认为None

        注意：
            - 如果实例已经初始化过，此方法会直接返回，不会重复初始化
            - 如果没有传入参数且有缓存参数，会使用缓存的参数进行初始化
            - 可以通过 reset_instance() 方法重置单例实例
            - 传入的复选框列表会被直接使用，不再从容器中查找
        """
        # 检查是否已经初始化过
        if self.__class__._initialized:
            return

        # 如果没有传入参数，尝试使用缓存的参数
        if not checkboxes and self.__class__._cached_args:
            checkboxes = self.__class__._cached_args
            if self.__class__._cached_kwargs:
                logger = self.__class__._cached_kwargs.get('logger', logger)
                parent = self.__class__._cached_kwargs.get('parent', parent)

        # 如果仍然没有logger，尝试从ClassInstanceManager获取
        if logger is None:
            try:
                from global_tools.utils import ClassInstanceManager
                logger = ClassInstanceManager.get_instance(key="ui_logger")
                if logger is None:
                    # 如果还是没有logger，记录警告但继续初始化
                    import logging
                    logging.warning("CheckBoxManager 初始化时未提供 logger，将创建空的管理器")
            except:
                pass  # 如果获取logger失败，忽略警告

        super().__init__(parent)
        self.__logger = logger

        if self.__logger:
            self.__logger.debug("初始化复选框管理器")

        # 验证并直接使用传入的复选框列表
        if checkboxes is None:
            checkboxes = []

        # 验证所有项都是 QCheckBox 实例
        if not isinstance(checkboxes, list):
            raise TypeError("checkboxes 参数必须是列表类型")

        for i, checkbox in enumerate(checkboxes):
            if not isinstance(checkbox, QCheckBox):
                raise TypeError(f"checkboxes[{i}] 必须是 QCheckBox 实例，实际类型: {type(checkbox)}")

        # 直接使用传入的复选框列表
        self.__checkboxes = list(checkboxes)  # 创建副本以避免外部修改

        # 连接内部信号槽，用于安全更新
        self.__safe_set_checked_signal.connect(self.__safe_set_checked_slot, Qt.QueuedConnection)

        if self.__logger:
            self.__logger.debug(f"管理 {len(self.__checkboxes)} 个复选框")

        # 标记为已初始化
        self.__class__._initialized = True

    @pyqtSlot(QCheckBox, bool)
    def __safe_set_checked_slot(self, checkbox: QCheckBox, checked: bool) -> None:
        """
        通过Qt事件队列安全地设置复选框状态的槽函数。
        """
        if not sip.isdeleted(checkbox):
            checkbox.setChecked(checked)



    def get_all_checkboxes(self) -> List[QCheckBox]:
        """获取所有复选框

        Returns:
            所有复选框列表
        """
        self.__logger.debug(f"获取所有复选框，共 {len(self.__checkboxes)} 个")
        return self.__checkboxes

    def get_checked_boxes(self) -> List[QCheckBox]:
        """获取所有选中的复选框

        Returns:
            选中的复选框列表
        """
        checked_boxes = [box for box in self.__checkboxes if box.isChecked()]
        self.__logger.debug(f"获取选中的复选框，共 {len(checked_boxes)} 个")
        return checked_boxes

    def get_unchecked_boxes(self, filter_text: Optional[Union[str, List[str]]] = None, inverse: bool = True) -> List[QCheckBox]:
        """获取所有未选中的复选框

        Args:
            filter_text: 用于过滤复选框的文本，可以是单个字符串或字符串列表。
                        如果提供，只返回文本匹配的复选框
            inverse: 是否反选过滤结果，默认为True。
                    当为True时，返回不匹配filter_text的复选框

        Returns:
            未选中的复选框列表
        """
        unchecked = [box for box in self.__checkboxes if not box.isChecked()]
        self.__logger.debug(f"获取未选中的复选框，初始数量: {len(unchecked)}")

        if filter_text is not None:
            if isinstance(filter_text, str):
                self.__logger.debug(f"使用文本过滤: {filter_text}, 反选: {inverse}")
                result = [box for box in unchecked if (box.text() == filter_text) != inverse]
            elif isinstance(filter_text, list):
                self.__logger.debug(f"使用文本列表过滤，列表长度: {len(filter_text)}, 反选: {inverse}")
                result = [box for box in unchecked if (box.text() in filter_text) != inverse]
            self.__logger.debug(f"过滤后的结果数量: {len(result)}")
            return result

        self.__logger.debug(f"不使用过滤，返回所有未选中复选框: {len(unchecked)}")
        return unchecked

    def get_checked_texts(self) -> List[str]:
        """获取所有选中复选框的文本

        Returns:
            选中复选框的文本列表
        """
        texts = [box.text() for box in self.__checkboxes if box.isChecked()]
        self.__logger.debug(f"获取选中复选框的文本，共 {len(texts)} 个")
        return texts

    def set_checked_by_text(self, text: str, checked: bool = True) -> bool:
        """通过文本设置复选框状态

        Args:
            text: 要设置的复选框文本
            checked: 是否选中,默认True

        Returns:
            bool: 是否成功设置（找到匹配的复选框）
        """
        self.__logger.debug(f"尝试设置文本为 '{text}' 的复选框为 {checked}")
        for box in self.__checkboxes:
            if box.text() == text:
                # box.setChecked(checked) # 直接更新改为信号槽
                self.__safe_set_checked_signal.emit(box, checked)
                self.__logger.debug("设置成功")
                # 发出信号的逻辑已移至状态变化事件处理中，避免重复
                return True

        self.__logger.debug(f"未找到文本为 '{text}' 的复选框")
        return False

    def set_all_checked(self, checked: bool = True) -> None:
        """设置所有复选框的状态

        Args:
            checked: 是否选中,默认True
        """
        self.__logger.debug(f"设置所有复选框的状态为 {checked}")
        for box in self.__checkboxes:
            # box.setChecked(checked) # 直接更新改为信号槽
            self.__safe_set_checked_signal.emit(box, checked)
            # 发出信号的逻辑已移至状态变化事件处理中，避免重复

    def get_checkbox_by_text(self, text: str) -> Optional[QCheckBox]:
        """通过文本查找复选框

        Args:
            text: 要查找的复选框文本

        Returns:
            QCheckBox | None: 找到的复选框对象，如果未找到则返回None
        """
        self.__logger.debug(f"通过文本 '{text}' 查找复选框")
        for box in self.__checkboxes:
            if box.text() == text:
                self.__logger.debug("找到复选框")
                return box

        self.__logger.debug("未找到复选框")
        return None

    def get_checkbox_by_object_name(self, object_name: str) -> Optional[QCheckBox]:
        """通过 ObjectName 查找复选框

        Args:
            object_name: 要查找的复选框 ObjectName

        Returns:
            QCheckBox | None: 找到的复选框对象，如果未找到则返回None
        """
        self.__logger.debug(f"通过对象名 '{object_name}' 查找复选框")
        for box in self.__checkboxes:
            if box.objectName() == object_name:
                self.__logger.debug("找到复选框")
                return box

        self.__logger.debug("未找到复选框")
        return None

    def set_click_callback(self, callback: Callable[[QCheckBox, bool], None]) -> None:
        """为所有复选框添加点击事件处理函数

        Args:
            callback: 回调函数，接收两个参数：
                     - checkbox: QCheckBox对象
                     - checked: bool类型，表示复选框是否被选中
                     函数签名应为: def callback(checkbox: QCheckBox, checked: bool) -> None

        Example:
            ```python
            def on_checkbox_clicked(checkbox: QCheckBox, checked: bool):
                print(f"复选框 {checkbox.text()} 被{'选中' if checked else '取消选中'}")

            manager = CheckBoxManager(container)
            manager.set_click_callback(on_checkbox_clicked)
            ```
        """
        self.__logger.debug("为所有复选框设置点击回调")

        # 首先断开之前可能存在的连接
        self.__disconnect_all_checkbox_signals()

        # 设置新的回调，使用内部方法包装回调
        for checkbox in self.__checkboxes:
            checkbox.clicked.connect(
                lambda state, cb=checkbox: self.__on_checkbox_clicked_wrapper(cb, state, callback)
            )

    def __on_checkbox_clicked_wrapper(self, checkbox: QCheckBox, state: bool, callback: Callable[[QCheckBox, bool], None]) -> None:
        """包装复选框点击事件，发出信号并调用回调

        Args:
            checkbox: 被点击的复选框
            state: 复选框状态
            callback: 用户提供的回调函数
        """
        self.__logger.debug(f"复选框 '{checkbox.text() or checkbox.objectName()}' 状态变为 {state}")

        # 发出信号
        self.checkbox_checked.emit(checkbox, state)

        # 调用用户回调
        callback(checkbox, state)

    def __disconnect_all_checkbox_signals(self) -> None:
        """断开所有复选框的信号连接"""
        self.__logger.debug("断开所有复选框的信号连接")
        for checkbox in self.__checkboxes:
            try:
                checkbox.clicked.disconnect()
                self.__logger.debug(f"断开复选框 '{checkbox.text() or checkbox.objectName()}' 的信号")
            except TypeError:
                # 忽略没有连接的情况
                self.__logger.debug(f"复选框 '{checkbox.text() or checkbox.objectName()}' 没有信号连接")
                pass

    @staticmethod
    def __get_filtered_checkboxes(
        checkboxes: List[QCheckBox],
        filter_text: Optional[Union[str, List[str]]],
        logger: Optional[Logger] = None
    ) -> List[QCheckBox]:
        """根据过滤文本获取目标复选框（静态方法）

        Args:
            checkboxes: 要过滤的复选框列表
            filter_text: 过滤文本，可以是字符串或字符串列表，支持正则表达式
            logger: 可选的日志记录器

        Returns:
            符合条件的复选框列表
        """
        target_boxes = checkboxes

        if filter_text is None:
            if logger:
                logger.debug("没有提供过滤条件，返回所有复选框")
            return target_boxes

        if isinstance(filter_text, str):
            if logger:
                logger.debug(f"使用字符串过滤: {filter_text}")
            try:
                pattern = re.compile(filter_text)
                target_boxes = [box for box in checkboxes if pattern.match(box.text())]
                if logger:
                    logger.debug(f"正则匹配成功，匹配到 {len(target_boxes)} 个复选框")
            except re.error:
                # 如果正则表达式无效，退回到精确匹配
                if logger:
                    logger.debug(f"正则表达式无效，使用精确匹配")
                target_boxes = [box for box in checkboxes if box.text() == filter_text]
                if logger:
                    logger.debug(f"精确匹配到 {len(target_boxes)} 个复选框")

        elif isinstance(filter_text, list):
            if logger:
                logger.debug(f"使用文本列表过滤，列表长度: {len(filter_text)}")
            target_boxes = []
            for pattern_text in filter_text:
                try:
                    pattern = re.compile(pattern_text)
                    matches = [box for box in checkboxes if pattern.match(box.text())]
                    if logger:
                        logger.debug(f"正则 '{pattern_text}' 匹配到 {len(matches)} 个复选框")
                    target_boxes.extend(matches)
                except re.error:
                    # 如果正则表达式无效，退回到精确匹配
                    matches = [box for box in checkboxes if box.text() == pattern_text]
                    if logger:
                        logger.debug(f"精确匹配 '{pattern_text}' 匹配到 {len(matches)} 个复选框")
                    target_boxes.extend(matches)

        return target_boxes

    @staticmethod
    def __on_exclusive_checkbox_clicked(
        current_checkbox: QCheckBox,
        checked: bool,
        filtered_boxes: List[QCheckBox],
        callback: Optional[Callable[[QCheckBox, bool], None]] = None,
        logger: Optional[Logger] = None,
        signal_emitter: Optional[Callable[[QCheckBox, bool], None]] = None
    ) -> None:
        """处理互斥选择模式下的复选框点击事件（静态方法）

        Args:
            current_checkbox: 当前被点击的复选框
            checked: 复选框状态
            filtered_boxes: 过滤后的复选框列表（互斥组）
            callback: 用户提供的回调函数
            logger: 可选的日志记录器
            signal_emitter: 可选的信号发射器，用于替代实例信号
        """
        if logger:
            logger.debug(f"互斥模式: 复选框 '{current_checkbox.text()}' 状态变为 {checked}")

        if checked:
            # 取消其他复选框的选中状态
            for box in filtered_boxes:
                if box != current_checkbox and box.isChecked():
                    if logger:
                        logger.debug(f"请求取消选中复选框: {box.text()}")
                    # 直接设置状态，不使用信号槽（因为是静态方法）
                    box.setChecked(False)

        # 发出信号（如果提供了信号发射器）
        if signal_emitter:
            signal_emitter(current_checkbox, checked)

        # 调用用户回调
        if callback:
            callback(current_checkbox, checked)

    def set_exclusive_selection(self, filter_text: Optional[Union[str, List[str]]] = None,
                                callback: Optional[Callable[[QCheckBox, bool], None]] = None) -> None:
        """设置复选框为单选模式（互斥选择）

        当一个复选框被选中时，其他复选框会自动取消选中。类似于 QRadioButton 的行为，
        但保留了 QCheckBox 的外观和可以完全取消选择的特性。

        Args:
            filter_text (str | list[str], optional):
                - 用于指定需要互斥选择的复选框范围
                - 可以是单个字符串或字符串列表
                - 如果为 None，则对所有复选框生效
                - 如果提供字符串，支持正则表达式匹配复选框文本
                - 如果提供字符串列表，列表中的每个字符串都支持正则表达式

            callback (Callable[[QCheckBox, bool], None], optional):
                - 选择状态改变时的回调函数
                - 函数签名：def callback(checkbox: QCheckBox, checked: bool) -> None
                - checkbox: 发生改变的复选框对象
                - checked: 是否被选中
                - 如果为 None，则不调用回调

        Example:
            ```python
            # 创建复选框管理器
            manager = CheckBoxManager(container)

            # 对所有复选框启用单选模式
            manager.set_exclusive_selection()

            # 使用正则表达式匹配复选框
            manager.set_exclusive_selection(filter_text=r"选项\\d+")  # 匹配"选项"后跟数字

            # 使用正则表达式列表
            manager.set_exclusive_selection(filter_text=[
                r"选项[A-C]",   # 匹配"选项A"到"选项C"
                r"测试.*"       # 匹配以"测试"开头的所有选项
            ])

            # 添加状态改变回调
            def on_selection_changed(checkbox: QCheckBox, checked: bool):
                print(f"选择改变：{checkbox.text()} -> {checked}")

            manager.set_exclusive_selection(callback=on_selection_changed)

            # 使用信号处理状态变化
            manager.exclusive_selection_changed.connect(on_selection_changed)
            ```
        """
        # 调用静态方法版本，传入实例的复选框列表和logger
        CheckBoxManager.set_exclusive_selection_static(
            self.__checkboxes,
            filter_text=filter_text,
            callback=callback,
            logger=self.__logger,
            signal_emitter=lambda cb, checked: self.exclusive_selection_changed.emit(cb, checked)
        )

    @staticmethod
    def set_exclusive_selection_static(
        checkboxes: List[QCheckBox],
        filter_text: Optional[Union[str, List[str]]] = None,
        callback: Optional[Callable[[QCheckBox, bool], None]] = None,
        logger: Optional[Logger] = None,
        signal_emitter: Optional[Callable[[QCheckBox, bool], None]] = None
    ) -> None:
        """设置复选框为单选模式（互斥选择）- 静态方法版本

        这是 set_exclusive_selection 的静态方法版本，可以直接通过类名调用，
        无需创建管理器实例。适用于已有复选框列表的场景。

        当一个复选框被选中时，其他复选框会自动取消选中。类似于 QRadioButton 的行为，
        但保留了 QCheckBox 的外观和可以完全取消选择的特性。

        Args:
            checkboxes (List[QCheckBox]): 要进行互斥选择设置的复选框控件列表

            filter_text (str | list[str], optional):
                - 用于指定需要互斥选择的复选框范围
                - 可以是单个字符串或字符串列表
                - 如果为 None，则对所有复选框生效
                - 如果提供字符串，支持正则表达式匹配复选框文本
                - 如果提供字符串列表，列表中的每个字符串都支持正则表达式

            callback (Callable[[QCheckBox, bool], None], optional):
                - 选择状态改变时的回调函数
                - 函数签名：def callback(checkbox: QCheckBox, checked: bool) -> None
                - checkbox: 发生改变的复选框对象
                - checked: 是否被选中
                - 如果为 None，则不调用回调

            logger (Logger, optional):
                - 可选的日志记录器，用于记录调试信息
                - 如果为 None，则不记录日志

            signal_emitter (Callable[[QCheckBox, bool], None], optional):
                - 可选的信号发射器，用于发射状态变化信号
                - 函数签名：def signal_emitter(checkbox: QCheckBox, checked: bool) -> None
                - 如果为 None，则不发射信号

        Example:
            ```python
            # 静态方法调用示例
            checkboxes = [checkbox1, checkbox2, checkbox3]
            logger = ClassInstanceManager.get_instance(key="ui_logger")

            # 基本用法
            CheckBoxManager.set_exclusive_selection_static(checkboxes)

            # 带过滤和回调的用法
            CheckBoxManager.set_exclusive_selection_static(
                checkboxes,
                filter_text=r"选项.*",
                callback=on_selection_changed,
                logger=logger
            )

            # 与管理器实例集成
            manager = CheckBoxManager.get_instance(container, logger=logger)
            all_checkboxes = manager.get_all_checkboxes()
            CheckBoxManager.set_exclusive_selection_static(
                all_checkboxes,
                filter_text="特定选项",
                callback=on_selection_changed,
                logger=logger
            )
            ```

        Note:
            推荐使用实例方法 set_exclusive_selection()，除非你需要在没有管理器实例的情况下使用。
        """
        if logger:
            logger.debug(f"设置互斥选择模式，过滤条件：{filter_text}")

        # 获取目标复选框列表
        target_boxes = CheckBoxManager.__get_filtered_checkboxes(checkboxes, filter_text, logger)
        if logger:
            logger.debug(f"目标复选框数量: {len(target_boxes)}")

        # 断开所有目标复选框的现有连接
        for checkbox in target_boxes:
            try:
                checkbox.clicked.disconnect()
                if logger:
                    logger.debug(f"断开复选框 '{checkbox.text()}' 的信号")
            except TypeError:
                # 忽略没有连接的情况
                pass

        # 添加新的点击事件处理
        for checkbox in target_boxes:
            checkbox.clicked.connect(
                lambda state, cb=checkbox: CheckBoxManager.__on_exclusive_checkbox_clicked(
                    cb, state, target_boxes, callback, logger, signal_emitter
                )
            )
            if logger:
                logger.debug(f"为复选框 '{checkbox.text()}' 添加互斥选择处理")

    def get_checked_state_by_object_name(self, object_name: str) -> Optional[bool]:
        """通过 ObjectName 获取复选框的选中状态

        Args:
            object_name: 要查找的复选框 ObjectName

        Returns:
            Optional[bool]: 复选框的选中状态，如果未找到则返回None

        Example:
            ```python
            # 获取指定objectName的复选框选中状态
            state = manager.get_checked_state_by_object_name("option1_checkbox")
            if state is not None:
                print(f"复选框选中状态: {state}")
            else:
                print("未找到复选框")
            ```
        """
        self.__logger.debug(f"通过对象名 '{object_name}' 获取复选框选中状态")

        checkbox = self.get_checkbox_by_object_name(object_name)
        if checkbox:
            checked_state = checkbox.isChecked()
            self.__logger.debug(f"找到复选框，选中状态: {checked_state}")
            return checked_state

        self.__logger.debug("未找到复选框")
        return None

    # =================================================================================
    # 单例模式相关类方法
    # =================================================================================

    @classmethod
    def get_instance(cls, checkboxes: List[QCheckBox] = None, logger=None, parent=None) -> 'CheckBoxManager':
        """
        获取CheckBoxManager的单例实例

        这是推荐的获取实例的方法，相比直接调用构造函数更加明确。

        Args:
            checkboxes: 要管理的复选框控件列表（首次创建时需要）
            logger: 日志记录器实例（首次创建时需要）
            parent: 父对象（可选）

        Returns:
            CheckBoxManager: 单例实例

        Raises:
            RuntimeError: 当创建实例失败时
            TypeError: 当参数类型不正确时

        使用示例:
            ```python
            # 首次创建，需要传入复选框列表和logger
            checkboxes = [checkbox1, checkbox2, checkbox3]
            logger = ClassInstanceManager.get_instance(key="ui_logger")
            manager = CheckBoxManager.get_instance(checkboxes, logger=logger)

            # 后续获取，可以不传参数
            manager = CheckBoxManager.get_instance()
            ```
        """
        try:
            if checkboxes is None:
                checkboxes = []
            return cls(checkboxes, logger=logger, parent=parent)
        except Exception as e:
            # 记录错误日志
            try:
                from global_tools.utils import ClassInstanceManager
                error_logger = ClassInstanceManager.get_instance(key="ui_logger")
                if error_logger:
                    error_logger.error(f"获取CheckBoxManager单例实例失败: {str(e)}")
            except:
                pass
            raise RuntimeError(f"获取CheckBoxManager单例实例失败: {str(e)}") from e

    @classmethod
    def reset_instance(cls) -> None:
        """
        重置单例实例

        清理当前实例并重置单例状态，下次调用时会创建新的实例。
        这在需要重新初始化管理器或在测试中很有用。

        Raises:
            RuntimeError: 当重置过程中发生严重错误时

        使用示例:
            ```python
            # 重置单例实例
            CheckBoxManager.reset_instance()

            # 下次调用会创建新的实例
            new_manager = CheckBoxManager.get_instance(new_layout, logger=new_logger)
            ```
        """
        with cls._lock:
            if cls._instance is not None:
                try:
                    # 尝试清理现有实例
                    if not sip.isdeleted(cls._instance):
                        # 清理复选框列表和信号连接
                        try:
                            cls._instance.__safe_set_checked_signal.disconnect()
                            # 清空复选框列表
                            if hasattr(cls._instance, '_CheckBoxManager__checkboxes'):
                                cls._instance._CheckBoxManager__checkboxes.clear()
                        except:
                            pass
                except Exception as e:
                    # 如果清理失败，记录错误但继续重置
                    try:
                        from global_tools.utils import ClassInstanceManager
                        logger = ClassInstanceManager.get_instance(key="ui_logger")
                        if logger:
                            logger.warning(f"重置CheckBoxManager实例时清理失败: {str(e)}")
                    except:
                        pass

            # 重置所有单例相关的类变量
            cls._instance = None
            cls._initialized = False
            cls._cached_args = None
            cls._cached_kwargs = None

    @classmethod
    def is_instance_created(cls) -> bool:
        """
        检查单例实例是否已创建且有效

        Returns:
            bool: 如果实例已创建且有效返回True，否则返回False

        Note:
            此方法不会抛出异常，在任何错误情况下都会返回False

        使用示例:
            ```python
            if CheckBoxManager.is_instance_created():
                manager = CheckBoxManager.get_instance()
            else:
                manager = CheckBoxManager.get_instance(layout, logger=logger)
            ```
        """
        try:
            if cls._instance is None:
                return False

            # 检查实例是否被删除和是否已初始化
            return not sip.isdeleted(cls._instance) and cls._initialized
        except (RuntimeError, AttributeError) as e:
            # 记录调试信息但不抛出异常
            try:
                from global_tools.utils import ClassInstanceManager
                debug_logger = ClassInstanceManager.get_instance(key="ui_logger")
                if debug_logger:
                    debug_logger.debug(f"检查CheckBoxManager实例状态时出现异常: {str(e)}")
            except:
                pass
            return False
        except Exception:
            # 对于任何其他异常，都返回False
            return False

    @classmethod
    def get_cached_args(cls) -> Tuple[Tuple, Dict]:
        """
        获取缓存的构造参数

        Returns:
            Tuple[Tuple, Dict]: 包含位置参数和关键字参数的元组
                               如果没有缓存参数，返回空元组和空字典

        Note:
            此方法总是返回有效的元组，即使在错误情况下也不会抛出异常

        使用示例:
            ```python
            args, kwargs = CheckBoxManager.get_cached_args()
            print(f"缓存的复选框数量: {len(args)}")
            print(f"缓存的logger: {kwargs.get('logger')}")
            ```
        """
        try:
            # 确保返回的是元组和字典类型
            cached_args = cls._cached_args if cls._cached_args is not None else ()
            cached_kwargs = cls._cached_kwargs if cls._cached_kwargs is not None else {}

            # 验证类型
            if not isinstance(cached_args, tuple):
                cached_args = tuple(cached_args) if cached_args else ()
            if not isinstance(cached_kwargs, dict):
                cached_kwargs = {}

            return (cached_args, cached_kwargs)
        except Exception as e:
            # 记录错误但返回默认值
            try:
                from global_tools.utils import ClassInstanceManager
                error_logger = ClassInstanceManager.get_instance(key="ui_logger")
                if error_logger:
                    error_logger.warning(f"获取CheckBoxManager缓存参数时出现异常: {str(e)}")
            except:
                pass
            return ((), {})


class _SaveWorker( QObject ):
    """
    负责在工作线程中执行复选框状态的保存操作。
    通过信号通知主线程保存的结果和日志信息。
    """
    # 信号定义
    log_message_signal = pyqtSignal( str, str )  # (message, color_name_or_hex)
    log_multi_style_message_signal = pyqtSignal( list )  # (message_parts_with_style_dicts)
    save_finished_signal = pyqtSignal( bool, int, int )  # (success, saved_count, total_count)

    def __init__( self, parent: Optional[ QObject ] = None, logger: Optional[ Logger ] = None ):
        """
        初始化SaveWorker。

        Args:
                parent: 父QObject对象，通常应为None，因为会被moveToThread
                logger: 日志记录器实例，如果为None则使用全局logger
        """
        super().__init__( parent )
        self.__logger: Logger = logger  # type: ignore
        self.__logger.debug( "SaveWorker已初始化。" )

    @pyqtSlot( dict, str, str )  # states_copy, config_file_path, config_dir_path
    def perform_save( self, states_to_save: Dict[ str, bool ], config_file_path: str, config_dir_path: str ) -> None:
        """
        在工作线程中执行实际的文件保存逻辑。

        Args:
                states_to_save: 要保存的状态字典的副本。
                config_file_path: 配置文件的完整路径。
                config_dir_path: 配置目录的路径。
        """
        self.__logger.info( f"SaveWorker: 开始保存状态到 {config_file_path}" )
        try:
            os.makedirs( config_dir_path, exist_ok=True )

            existing_states = { }
            if os.path.exists( config_file_path ):
                try:
                    with open( config_file_path, 'r', encoding='utf-8' ) as f:
                        content = f.read()
                        if not content.strip():
                            self.__logger.warning( f"配置文件 {config_file_path} 为空，将视为空字典。" )
                            existing_states = { }
                        else:
                            existing_states = json.loads( content )
                    if not isinstance( existing_states, dict ):
                        self.__logger.warning( f"配置文件 {config_file_path} 格式错误，重置为空字典。" )
                        self.log_message_signal.emit( f"配置文件格式错误，重置为空", "ORANGE" )
                        existing_states = { }
                except json.JSONDecodeError as jde:
                    self.__logger.error(
                        f"读取并解析配置文件失败 (JSONDecodeError): {jde}。文件内容可能已损坏。重置为空字典。"
                    )
                    self.log_message_signal.emit( f"配置文件解析失败: {jde}，重置为空", "RED" )
                    existing_states = { }
                except Exception as e:
                    self.__logger.error( f"读取配置文件时发生未知错误: {e}" )
                    self.log_message_signal.emit( f"读取配置文件失败: {e}", "RED" )
                    traceback.print_exc()
                    existing_states = { }

            updated_states = { **existing_states, **states_to_save }

            with open( config_file_path, 'w', encoding='utf-8' ) as f:
                json.dump( updated_states, f, indent=2 )

            states_count = len( states_to_save )
            total_count = len( updated_states )
            self.__logger.info(
                f"SaveWorker: 已保存 {states_count} 个相关复选框状态到 {config_file_path}，总状态数: {total_count}"
            )

            log_parts = [
                ("SaveWorker 保存完成: ", { "color": "GREEN", "bold": True }),
                (f"已更新/新增 {states_count} 个复选框状态", { "color": "BLACK" }),
                (f"，总状态数: {total_count}", { "color": "BLUE" })
            ]
            self.log_multi_style_message_signal.emit( log_parts )
            self.save_finished_signal.emit( True, states_count, total_count )

        except Exception as e:
            self.__logger.error( f"SaveWorker: 保存复选框状态失败: {e}" )
            self.log_message_signal.emit( f"SaveWorker 保存复选框状态失败: {e}", "RED" )
            traceback.print_exc()
            self.save_finished_signal.emit( False, 0, 0 )


class QCheckBoxSynchronizer(QObject):
    """
    QCheckBox 控件选中状态同步器（增强版）

    该类实现了多个 QCheckBox 控件之间的选中状态同步功能。当同步组中的任意一个 QCheckBox 控件
    选中状态发生变化时，该组内的其他所有 QCheckBox 控件都会自动同步更新为相同的选中状态。

    ===== 核心功能增强 =====
    1. **实时状态同步机制**：
       - 监听 stateChanged 信号，确保任何状态变化都能立即触发同步
       - 支持程序API调用（setChecked()）和用户手动点击的同步
       - 智能防循环机制，避免无限递归调用
       - 只同步状态不一致的控件，提高同步效率

    2. **失焦同步保障机制**：
       - 通过事件过滤器监听 focusOutEvent 事件
       - 在控件失去焦点时执行完整的同步检查
       - 作为实时同步的补充保障，处理边缘情况和异步更新场景
       - 确保数据一致性，防止同步遗漏

    3. **增强的异步处理**：
       - 使用信号槽机制确保UI更新在主线程中执行
       - 临时阻塞信号防止循环触发
       - 批量处理同步操作，提高性能
       - 详细的状态检查和错误恢复

    ===== 原有功能保持 =====
    - 支持多个同步组，每个组内的 QCheckBox 控件选中状态保持同步
    - 使用异步机制避免阻塞主线程，确保 UI 响应性
    - 集成日志记录功能，记录同步操作的详细信息
    - 提供完善的错误处理和异常恢复机制
    - 支持动态添加和移除同步组

    ===== 设计模式 =====
    - 观察者模式：监听 QCheckBox 的状态变化和失焦事件
    - 组合模式：管理多个同步组
    - 信号槽模式：使用 Qt 信号槽实现异步通信
    - 事件过滤器模式：监听控件的失焦事件

    ===== 使用场景 =====
    - 表单中需要多个复选框显示相同选中状态的场景
    - 实时数据同步显示
    - 用户输入的即时反馈和同步
    - 需要高可靠性数据一致性的应用

    ===== 注意事项 =====
    - 所有 QCheckBox 控件必须设置唯一的 objectName
    - 同步操作是异步的，不会阻塞 UI 线程
    - 支持动态添加和移除同步组
    - 自动处理控件删除和资源清理
    - 线程安全，支持多线程环境使用
    """

    # 定义信号
    sync_started = pyqtSignal(int, str)  # 同步开始信号：组索引，触发控件名称
    sync_completed = pyqtSignal(int, int)  # 同步完成信号：组索引，更新的控件数量
    sync_error = pyqtSignal(int, str, str)  # 同步错误信号：组索引，错误类型，错误消息

    # 内部异步更新信号
    __async_update_signal = pyqtSignal(int, str, bool)  # 组索引，目标控件名称，新选中状态

    def __init__(self, sync_groups: List[List[QCheckBox]], log_output=None, logger=None, parent: Optional[QObject] = None):
        """
        初始化 QCheckBox 选中状态同步器

        Args:
            sync_groups (List[List[QCheckBox]]): 二维列表，每个子列表包含需要同步的 QCheckBox 控件组
                例如：[[checkbox1, checkbox2, checkbox3], [checkbox4, checkbox5]] 表示两个同步组
            log_output: LogOutput 实例，用于在 UI 界面中输出突出重点日志
            logger: Logger 实例，用于在控制台中输出日志
            parent (Optional[QObject]): Qt 父对象，用于内存管理

        Raises:
            ValueError: 当传入的参数不符合要求时抛出
            TypeError: 当参数类型不正确时抛出

        使用示例:
            ```python
            # 创建 QCheckBox 控件
            checkbox1 = QCheckBox("选项1")
            checkbox1.setObjectName("checkbox1")
            checkbox2 = QCheckBox("选项2")
            checkbox2.setObjectName("checkbox2")
            checkbox3 = QCheckBox("选项3")
            checkbox3.setObjectName("checkbox3")

            # 创建同步器
            sync_groups = [[checkbox1, checkbox2], [checkbox3]]
            synchronizer = QCheckBoxSynchronizer(sync_groups, log_output, logger)

            # 现在 checkbox1 和 checkbox2 的选中状态会保持同步
            checkbox1.setChecked(True)  # checkbox2 也会自动选中
            ```
        """
        super().__init__(parent)

        # 参数验证
        if not isinstance(sync_groups, list):
            raise TypeError("sync_groups 必须是一个列表")

        if not sync_groups:
            raise ValueError("sync_groups 不能为空")

        # 初始化实例变量
        self.__sync_groups: List[List[QCheckBox]] = []
        self.__group_mapping: Dict[QCheckBox, int] = {}  # 控件到组索引的映射
        self.__updating_flags: Dict[int, bool] = {}  # 防止循环更新的标志
        self.__log_output = log_output
        self.__logger: Logger = logger if logger else ClassInstanceManager.get_instance(key="ui_logger")

        # 连接内部信号
        self.__async_update_signal.connect(self.__on_async_update)

        # 处理传入的同步组
        self.__process_sync_groups(sync_groups)

        # 记录初始化日志
        if self.__logger:
            self.__logger.info(f"QCheckBoxSynchronizer 初始化完成，共创建 {len(self.__sync_groups)} 个同步组")

        if self.__log_output:
            self.__log_output.append_multi_style([
                ("QCheckBox同步器", {'bold': True, 'color': '#2E8B57'}),
                (" 初始化完成，共 ", {'color': '#666666'}),
                (f"{len(self.__sync_groups)}", {'bold': True, 'color': '#FF6347'}),
                (" 个同步组", {'color': '#666666'})
            ])

    def __process_sync_groups(self, sync_groups: List[List[QCheckBox]]) -> None:
        """
        处理传入的同步组，验证并设置信号连接

        Args:
            sync_groups: 同步组列表

        Raises:
            ValueError: 当控件验证失败时抛出
        """
        for group_index, group in enumerate(sync_groups):
            if not isinstance(group, list):
                raise TypeError(f"同步组 {group_index} 必须是一个列表")

            if not group:
                if self.__logger:
                    self.__logger.warning(f"跳过空的同步组 {group_index}")
                continue

            validated_group = []
            for control in group:
                if not isinstance(control, QCheckBox):
                    raise TypeError(f"同步组 {group_index} 中包含非 QCheckBox 类型的控件: {type(control)}")

                # 检查是否已被删除
                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning(f"跳过已删除的 QCheckBox 控件")
                    continue

                # 检查 objectName
                object_name = control.objectName()
                if not object_name:
                    if self.__logger:
                        self.__logger.warning(f"QCheckBox 控件未设置 objectName，将跳过")
                    continue

                # 检查是否已在其他组中
                if control in self.__group_mapping:
                    old_group = self.__group_mapping[control]
                    if self.__logger:
                        self.__logger.warning(f"控件 '{object_name}' 已在组 {old_group} 中，将移动到组 {group_index}")

                validated_group.append(control)
                self.__group_mapping[control] = group_index

            if validated_group:
                self.__sync_groups.append(validated_group)
                self.__updating_flags[group_index] = False

                # 为组内每个控件设置信号连接和事件过滤器
                for control in validated_group:
                    # 连接状态变化信号（实时同步）
                    control.stateChanged.connect(
                        lambda state, ctrl=control, idx=group_index: self.__on_state_changed(idx, ctrl, state)
                    )

                    # 安装事件过滤器以监听失焦事件（失焦同步保障）
                    control.installEventFilter(self)

                if self.__logger:
                    control_names = [ctrl.objectName() for ctrl in validated_group]
                    self.__logger.debug(f"创建同步组 {group_index}，包含控件: {control_names}")
            else:
                if self.__logger:
                    self.__logger.warning(f"同步组 {group_index} 没有有效的控件，已跳过")

    def eventFilter(self, obj: QObject, event: QEvent) -> bool:
        """
        事件过滤器，用于监听QCheckBox控件的失焦事件

        当QCheckBox控件失去焦点时，执行一次完整的同步检查，
        作为实时同步的补充保障机制，确保数据一致性。

        Args:
            obj: 事件源对象
            event: 事件对象

        Returns:
            bool: 是否处理了该事件（False表示继续传递事件）
        """
        try:
            # 只处理QCheckBox控件的失焦事件
            if isinstance(obj, QCheckBox) and event.type() == QEvent.FocusOut:
                # 检查控件是否在同步管理中
                if obj in self.__group_mapping:
                    group_index = self.__group_mapping[obj]
                    current_checked = obj.isChecked()

                    if self.__logger:
                        self.__logger.debug(f"检测到失焦事件：组 {group_index}，控件 '{obj.objectName()}'，执行同步检查")

                    # 执行失焦同步检查
                    self.__on_focus_out_sync_check(group_index, obj, current_checked)

            # 继续传递事件，不拦截
            return False

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"事件过滤器处理失焦事件时发生错误: {str(e)}")
                self.__logger.debug(traceback.format_exc())
            return False

    def __on_focus_out_sync_check(self, group_index: int, source_control: QCheckBox, current_checked: bool) -> None:
        """
        失焦同步检查处理函数

        当控件失去焦点时，检查同步组内所有控件的选中状态是否一致，
        如果发现不一致，则以失焦控件的状态为准进行同步。

        Args:
            group_index: 同步组索引
            source_control: 失焦的控件
            current_checked: 当前选中状态
        """
        try:
            # 防止在更新过程中触发
            if self.__updating_flags.get(group_index, False):
                return

            # 检查控件是否仍然有效
            if sip.isdeleted(source_control):
                if self.__logger:
                    self.__logger.warning("失焦的控件已被删除")
                return

            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]
            source_name = source_control.objectName()

            # 检查组内其他控件的状态是否与失焦控件一致
            inconsistent_controls = []
            for control in group:
                if control == source_control:
                    continue

                if sip.isdeleted(control):
                    continue

                if control.isChecked() != current_checked:
                    inconsistent_controls.append(control)

            # 如果发现不一致的控件，进行同步
            if inconsistent_controls:
                if self.__logger:
                    control_names = [ctrl.objectName() for ctrl in inconsistent_controls]
                    self.__logger.info(f"失焦同步检查：发现 {len(inconsistent_controls)} 个不一致的控件 {control_names}，以 '{source_name}' 为准进行同步")

                # 发射同步开始信号
                self.sync_started.emit(group_index, source_name)

                # 执行同步
                self.__sync_group_async(group_index, source_control, current_checked)

                if self.__log_output:
                    self.__log_output.append_multi_style([
                        ("失焦同步", {'bold': True, 'color': '#FF8C00'}),
                        (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                        (f"从 '{source_name}' ", {'color': '#666666'}),
                        ("同步到", {'color': '#666666'}),
                        (f" {len(inconsistent_controls)} ", {'color': '#FF6347', 'bold': True}),
                        ("个控件", {'color': '#666666'})
                    ])
            else:
                if self.__logger:
                    self.__logger.debug(f"失焦同步检查：组 {group_index} 状态已一致，无需同步")

        except Exception as e:
            error_msg = f"失焦同步检查时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "focus_out_sync_error", error_msg)

    def __on_state_changed(self, group_index: int, source_control: QCheckBox, new_state: int) -> None:
        """
        处理状态变化事件（实时同步机制）

        当同步组中任意一个QCheckBox控件选中状态发生变化时（无论是通过程序API调用setChecked()
        还是用户手动点击），立即触发同步逻辑，确保同步组内所有QCheckBox控件的选中状态保持完全一致。

        Args:
            group_index: 同步组索引
            source_control: 触发变化的控件
            new_state: 新的状态值 (Qt.Checked=2, Qt.Unchecked=0, Qt.PartiallyChecked=1)
        """
        try:
            # 防止循环更新
            if self.__updating_flags.get(group_index, False):
                if self.__logger:
                    self.__logger.debug(f"组 {group_index} 正在更新中，跳过状态变化处理")
                return

            # 检查控件是否仍然有效
            if sip.isdeleted(source_control):
                if self.__logger:
                    self.__logger.warning("触发状态变化的控件已被删除")
                return

            # 转换为布尔值
            new_checked = new_state == Qt.Checked
            source_name = source_control.objectName()

            # 发射同步开始信号
            self.sync_started.emit(group_index, source_name)

            # 记录详细日志
            if self.__logger:
                self.__logger.debug(f"实时同步触发：组 {group_index}，控件 '{source_name}'，新状态: {new_checked}")

            # 异步同步组内其他控件
            self.__sync_group_async(group_index, source_control, new_checked)

        except Exception as e:
            error_msg = f"处理状态变化时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "state_change_error", error_msg)

    def __sync_group_async(self, group_index: int, source_control: QCheckBox, new_checked: bool) -> None:
        """
        异步同步组内其他控件的选中状态

        Args:
            group_index: 同步组索引
            source_control: 触发同步的源控件
            new_checked: 新的选中状态
        """
        try:
            # 检查组索引有效性
            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]
            source_name = source_control.objectName()

            # 找出需要同步的控件（状态不一致的控件）
            controls_to_sync = []
            for control in group:
                if control == source_control:
                    continue

                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning(f"跳过已删除的控件")
                    continue

                if control.isChecked() != new_checked:
                    controls_to_sync.append(control)

            if not controls_to_sync:
                if self.__logger:
                    self.__logger.debug(f"组 {group_index} 内所有控件状态已一致，无需同步")
                self.sync_completed.emit(group_index, 0)
                return

            # 记录同步信息
            if self.__logger:
                control_names = [ctrl.objectName() for ctrl in controls_to_sync]
                self.__logger.info(f"同步组 {group_index}：从 '{source_name}' 同步到 {len(controls_to_sync)} 个控件 {control_names}")

            # 异步更新每个需要同步的控件
            for control in controls_to_sync:
                control_name = control.objectName()
                self.__async_update_signal.emit(group_index, control_name, new_checked)

            # 记录UI日志
            if self.__log_output:
                status_text = "选中" if new_checked else "取消选中"
                self.__log_output.append_multi_style([
                    ("实时同步", {'bold': True, 'color': '#4169E1'}),
                    (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                    (f"从 '{source_name}' ", {'color': '#666666'}),
                    (f"{status_text}", {'color': '#32CD32' if new_checked else '#FF8C00', 'bold': True}),
                    (" 同步到 ", {'color': '#666666'}),
                    (f"{len(controls_to_sync)}", {'color': '#FF6347', 'bold': True}),
                    (" 个控件", {'color': '#666666'})
                ])

        except Exception as e:
            error_msg = f"异步同步组内控件时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "async_sync_error", error_msg)

    def __on_async_update(self, group_index: int, target_control_name: str, new_checked: bool) -> None:
        """
        异步更新信号的槽函数，在主线程中安全地更新控件状态

        Args:
            group_index: 同步组索引
            target_control_name: 目标控件名称
            new_checked: 新的选中状态
        """
        try:
            # 检查组索引有效性
            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"异步更新时发现无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]

            # 查找目标控件
            target_control = None
            for control in group:
                if control.objectName() == target_control_name:
                    target_control = control
                    break

            if target_control is None:
                if self.__logger:
                    self.__logger.warning(f"未找到名为 '{target_control_name}' 的控件")
                return

            # 检查控件是否已被删除
            if sip.isdeleted(target_control):
                if self.__logger:
                    self.__logger.warning(f"目标控件 '{target_control_name}' 已被删除")
                return

            # 检查状态是否需要更新
            if target_control.isChecked() == new_checked:
                if self.__logger:
                    self.__logger.debug(f"控件 '{target_control_name}' 状态已是目标状态，跳过更新")
                return

            # 设置更新标志，防止循环触发
            self.__updating_flags[group_index] = True

            try:
                # 临时阻塞信号，防止触发连锁反应
                target_control.blockSignals(True)
                target_control.setChecked(new_checked)
                target_control.blockSignals(False)

                if self.__logger:
                    self.__logger.debug(f"成功更新控件 '{target_control_name}' 状态为: {new_checked}")

            finally:
                # 确保信号阻塞被解除
                target_control.blockSignals(False)
                # 重置更新标志
                self.__updating_flags[group_index] = False

            # 发射同步完成信号
            self.sync_completed.emit(group_index, 1)

        except Exception as e:
            error_msg = f"异步更新控件状态时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            # 确保更新标志被重置
            self.__updating_flags[group_index] = False
            self.sync_error.emit(group_index, "async_update_error", error_msg)

    def add_sync_group(self, checkboxes: List[QCheckBox]) -> int:
        """
        动态添加新的同步组

        Args:
            checkboxes: 要添加到新同步组的 QCheckBox 控件列表

        Returns:
            int: 新创建的同步组索引，如果创建失败返回 -1

        Raises:
            TypeError: 当参数类型不正确时抛出
            ValueError: 当控件验证失败时抛出

        使用示例:
            ```python
            # 创建新的复选框
            new_checkbox1 = QCheckBox("新选项1")
            new_checkbox1.setObjectName("new_checkbox1")
            new_checkbox2 = QCheckBox("新选项2")
            new_checkbox2.setObjectName("new_checkbox2")

            # 添加新的同步组
            group_index = synchronizer.add_sync_group([new_checkbox1, new_checkbox2])
            if group_index >= 0:
                print(f"成功创建同步组 {group_index}")
            ```
        """
        try:
            if not isinstance(checkboxes, list):
                raise TypeError("checkboxes 必须是一个列表")

            if not checkboxes:
                raise ValueError("checkboxes 不能为空")

            # 验证所有控件
            validated_group = []
            for control in checkboxes:
                if not isinstance(control, QCheckBox):
                    raise TypeError(f"列表中包含非 QCheckBox 类型的控件: {type(control)}")

                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning(f"跳过已删除的 QCheckBox 控件")
                    continue

                object_name = control.objectName()
                if not object_name:
                    if self.__logger:
                        self.__logger.warning(f"QCheckBox 控件未设置 objectName，将跳过")
                    continue

                # 检查是否已在其他组中
                if control in self.__group_mapping:
                    old_group = self.__group_mapping[control]
                    if self.__logger:
                        self.__logger.warning(f"控件 '{object_name}' 已在组 {old_group} 中，将移动到新组")

                validated_group.append(control)

            if not validated_group:
                if self.__logger:
                    self.__logger.warning("没有有效的控件可添加到同步组")
                return -1

            # 创建新的同步组
            group_index = len(self.__sync_groups)
            self.__sync_groups.append(validated_group)
            self.__updating_flags[group_index] = False

            # 更新控件映射
            for control in validated_group:
                self.__group_mapping[control] = group_index

                # 连接信号
                control.stateChanged.connect(
                    lambda state, ctrl=control, idx=group_index: self.__on_state_changed(idx, ctrl, state)
                )

                # 安装事件过滤器
                control.installEventFilter(self)

            if self.__logger:
                control_names = [ctrl.objectName() for ctrl in validated_group]
                self.__logger.info(f"成功添加同步组 {group_index}，包含控件: {control_names}")

            if self.__log_output:
                self.__log_output.append_multi_style([
                    ("添加同步组", {'bold': True, 'color': '#32CD32'}),
                    (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                    (f"包含 {len(validated_group)} 个控件", {'color': '#666666'})
                ])

            return group_index

        except Exception as e:
            error_msg = f"添加同步组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return -1

    def remove_sync_group(self, group_index: int) -> bool:
        """
        移除指定的同步组

        Args:
            group_index: 要移除的同步组索引

        Returns:
            bool: 移除是否成功

        使用示例:
            ```python
            # 移除同步组
            success = synchronizer.remove_sync_group(0)
            if success:
                print("同步组移除成功")
            ```
        """
        try:
            if group_index < 0 or group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.warning(f"无效的组索引: {group_index}")
                return False

            group = self.__sync_groups[group_index]

            # 断开信号连接并移除事件过滤器
            for control in group:
                if not sip.isdeleted(control):
                    try:
                        control.stateChanged.disconnect()
                        control.removeEventFilter(self)
                    except:
                        pass

                # 从映射中移除
                if control in self.__group_mapping:
                    del self.__group_mapping[control]

            # 移除同步组
            del self.__sync_groups[group_index]
            del self.__updating_flags[group_index]

            # 更新后续组的索引映射
            for control, mapped_index in list(self.__group_mapping.items()):
                if mapped_index > group_index:
                    self.__group_mapping[control] = mapped_index - 1

            # 更新更新标志字典的键
            new_updating_flags = {}
            for idx, flag in self.__updating_flags.items():
                if idx > group_index:
                    new_updating_flags[idx - 1] = flag
                else:
                    new_updating_flags[idx] = flag
            self.__updating_flags = new_updating_flags

            if self.__logger:
                self.__logger.info(f"成功移除同步组 {group_index}")

            if self.__log_output:
                self.__log_output.append_multi_style([
                    ("移除同步组", {'bold': True, 'color': '#FF8C00'}),
                    (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                    ("成功", {'color': '#32CD32'})
                ])

            return True

        except Exception as e:
            error_msg = f"移除同步组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return False

    def get_sync_groups_count(self) -> int:
        """
        获取同步组的数量

        Returns:
            int: 同步组数量

        使用示例:
            ```python
            count = synchronizer.get_sync_groups_count()
            print(f"当前有 {count} 个同步组")
            ```
        """
        return len(self.__sync_groups)

    def get_sync_group_controls(self, group_index: int) -> List[QCheckBox]:
        """
        获取指定同步组中的所有控件

        Args:
            group_index: 同步组索引

        Returns:
            List[QCheckBox]: 同步组中的控件列表，如果组索引无效返回空列表

        使用示例:
            ```python
            controls = synchronizer.get_sync_group_controls(0)
            for control in controls:
                print(f"控件: {control.objectName()}, 状态: {control.isChecked()}")
            ```
        """
        if group_index < 0 or group_index >= len(self.__sync_groups):
            if self.__logger:
                self.__logger.warning(f"无效的组索引: {group_index}")
            return []

        # 过滤掉已删除的控件
        valid_controls = []
        for control in self.__sync_groups[group_index]:
            if not sip.isdeleted(control):
                valid_controls.append(control)

        return valid_controls

    def get_control_group_index(self, control: QCheckBox) -> int:
        """
        获取指定控件所属的同步组索引

        Args:
            control: QCheckBox 控件

        Returns:
            int: 同步组索引，如果控件不在任何同步组中返回 -1

        使用示例:
            ```python
            group_index = synchronizer.get_control_group_index(checkbox1)
            if group_index >= 0:
                print(f"控件属于同步组 {group_index}")
            else:
                print("控件不在任何同步组中")
            ```
        """
        return self.__group_mapping.get(control, -1)

    def is_group_synchronized(self, group_index: int) -> bool:
        """
        检查指定同步组内所有控件的状态是否一致

        Args:
            group_index: 同步组索引

        Returns:
            bool: 如果组内所有控件状态一致返回 True，否则返回 False

        使用示例:
            ```python
            if synchronizer.is_group_synchronized(0):
                print("同步组 0 状态一致")
            else:
                print("同步组 0 状态不一致")
            ```
        """
        try:
            if group_index < 0 or group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.warning(f"无效的组索引: {group_index}")
                return False

            group = self.__sync_groups[group_index]
            valid_controls = [ctrl for ctrl in group if not sip.isdeleted(ctrl)]

            if not valid_controls:
                return True  # 空组认为是同步的

            # 以第一个控件的状态为基准
            reference_state = valid_controls[0].isChecked()

            # 检查其他控件是否与基准状态一致
            for control in valid_controls[1:]:
                if control.isChecked() != reference_state:
                    return False

            return True

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"检查组同步状态时发生错误: {str(e)}")
            return False

    def force_sync_group(self, group_index: int, target_state: bool) -> bool:
        """
        强制同步指定组内所有控件到目标状态

        Args:
            group_index: 同步组索引
            target_state: 目标选中状态

        Returns:
            bool: 同步是否成功

        使用示例:
            ```python
            # 强制将同步组 0 中所有控件设置为选中状态
            success = synchronizer.force_sync_group(0, True)
            if success:
                print("强制同步成功")
            ```
        """
        try:
            if group_index < 0 or group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.warning(f"无效的组索引: {group_index}")
                return False

            group = self.__sync_groups[group_index]
            valid_controls = [ctrl for ctrl in group if not sip.isdeleted(ctrl)]

            if not valid_controls:
                if self.__logger:
                    self.__logger.warning(f"同步组 {group_index} 中没有有效的控件")
                return False

            # 设置更新标志
            self.__updating_flags[group_index] = True

            try:
                updated_count = 0
                for control in valid_controls:
                    if control.isChecked() != target_state:
                        control.blockSignals(True)
                        control.setChecked(target_state)
                        control.blockSignals(False)
                        updated_count += 1

                if self.__logger:
                    self.__logger.info(f"强制同步组 {group_index}：更新了 {updated_count} 个控件到状态 {target_state}")

                if self.__log_output:
                    status_text = "选中" if target_state else "取消选中"
                    self.__log_output.append_multi_style([
                        ("强制同步", {'bold': True, 'color': '#DC143C'}),
                        (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                        (f"到 {status_text} ", {'color': '#32CD32' if target_state else '#FF8C00', 'bold': True}),
                        (f"更新了 {updated_count} 个控件", {'color': '#666666'})
                    ])

                return True

            finally:
                # 确保信号阻塞被解除
                for control in valid_controls:
                    control.blockSignals(False)
                # 重置更新标志
                self.__updating_flags[group_index] = False

        except Exception as e:
            error_msg = f"强制同步组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return False

    def cleanup(self) -> None:
        """
        清理资源，断开所有信号连接和事件过滤器

        使用示例:
            ```python
            # 在不再需要同步器时清理资源
            synchronizer.cleanup()
            ```
        """
        try:
            if self.__logger:
                self.__logger.info("开始清理 QCheckBoxSynchronizer 资源...")

            # 断开所有信号连接和移除事件过滤器
            for group in self.__sync_groups:
                for control in group:
                    if not sip.isdeleted(control):
                        try:
                            control.stateChanged.disconnect()
                            control.removeEventFilter(self)
                        except:
                            pass

            # 清空所有数据结构
            self.__sync_groups.clear()
            self.__group_mapping.clear()
            self.__updating_flags.clear()

            if self.__logger:
                self.__logger.info("QCheckBoxSynchronizer 资源清理完成")

            if self.__log_output:
                self.__log_output.append("QCheckBox同步器资源清理完成", color='#32CD32')

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"清理资源时发生错误: {str(e)}")
                self.__logger.debug(traceback.format_exc())


class CheckBoxStateManager( QObject ):
    """
    管理PyQt5容器中QCheckBox控件的状态，支持状态持久化和恢复。

    此类提供以下功能:
    1. 递归查找容器及其子容器中的所有QCheckBox控件（支持单个容器或容器列表）
    2. 监听QCheckBox状态变化并实时记录
    3. 使用防抖机制避免频繁写入配置文件
    4. 将状态保存到配置文件进行持久化（采用增量更新，不会覆盖其他控件状态）
    5. 应用程序启动时从配置文件恢复QCheckBox状态
    6. 支持定时自动保存功能，可设置保存间隔
    7. 使用父容器.子容器.控件名的路径格式作为唯一标识，避免命名冲突
    8. 提供多种方式查找复选框（通过文本、对象名或层级路径）
    9. 支持状态变化信号，可连接自定义处理函数
    10. 安全处理Qt对象生命周期，防止访问已销毁对象
    11. 线程安全的自动保存机制，避免与手动保存冲突
    12. 支持按容器管理复选框，当一个复选框状态变化时，能检查并更新同一容器中所有复选框的状态
    13. 对容器中复选框状态提供单独的加载和保存功能
    14. 支持UI界面日志输出，显示运行时状态变化
    """

    # 定义状态变化信号，参数：复选框、新状态
    checkbox_state_changed = pyqtSignal( QCheckBox, bool )

    # 自定义信号，用于从主线程请求工作线程执行保存操作
    _request_save_on_worker_signal = pyqtSignal( dict, str, str )

    def __init__(
        self,
        container: Union[ QWidget, QLayout, List[ Union[ QWidget, QLayout ] ] ],
        config_dir: str,
        config_filename: str = "checkbox_states.json",
        debounce_delay: int = 300,
        parent: Optional[ QObject ] = None,
        log_output: Optional[ LogOutput ] = None,
        logger: Optional[ Logger ] = None
    ):
        """
        初始化CheckBoxStateManager。

        Args:
                container: 包含QCheckBox控件的容器，可以是QWidget、QLayout或它们的列表
                config_dir: 保存配置文件夹路径
                config_filename: 状态配置文件名称，默认为checkbox_states.json
                debounce_delay: 防抖延迟时间（毫秒），默认为300ms
                parent: 父QObject对象，有助于Qt对象树管理
                log_output: LogOutput实例，用于在UI界面输出日志信息
                logger: 日志记录器实例，如果为None则使用全局logger
        """
        super().__init__( parent )

        # 设置日志记录器
        self.__logger: Logger = logger  # type: ignore

        # 存储日志输出实例
        self.__log_output = log_output

        # 存储所有找到的复选框
        self.__checkboxes: List[ QCheckBox ] = [ ]

        # 存储复选框状态，格式：{objectName: isChecked}
        self.__states: Dict[ str, bool ] = { }

        # 存储复选框的层级路径，格式：{checkbox: 路径字符串}
        self.__checkbox_paths: Dict[ QCheckBox, str ] = { }

        # 存储信号连接，用于清理
        self.__connections: Dict[ QCheckBox, List[ Callable ] ] = { }

        # 存储每个复选框所属的父容器，格式：{checkbox: parent_container}
        self.__checkbox_containers: Dict[ QCheckBox, Union[ QWidget, QLayout ] ] = { }

        # 存储每个容器包含的复选框，格式：{container: set(checkbox1, checkbox2, ...)}
        self.__container_checkboxes: Dict[ Union[ QWidget, QLayout ], Set[ QCheckBox ] ] = { }

        # 防抖QTimer (替代threading.Timer)
        self.__save_timer_qt: Optional[ QTimer ] = None
        self.__debounce_delay = debounce_delay

        # 自动保存QTimer (替代threading.Timer)
        self.__auto_save_qtimer: Optional[QTimer] = QTimer( self )
        if self.__auto_save_qtimer:
            self.__auto_save_qtimer.timeout.connect( self.__trigger_auto_save_on_worker )
        self.__auto_save_interval: Optional[ float ] = None
        self.__manual_save_time: Optional[ float ] = None  # 记录上次手动保存时间

        # 工作线程和保存工作对象
        self.__save_qthread: QThread = QThread( self )  # 线程以self为父对象
        self.__save_worker: _SaveWorker = _SaveWorker( logger=self.__logger )  # Worker无父对象
        self.__save_worker.moveToThread( self.__save_qthread )

        # 连接保存请求信号到worker的执行方法
        self._request_save_on_worker_signal.connect( self.__save_worker.perform_save )

        # 连接worker的日志信号到主线程的处理方法
        self.__save_worker.log_message_signal.connect( self.__handle_worker_log )
        self.__save_worker.log_multi_style_message_signal.connect( self.__handle_worker_log_multi_style )

        self.__save_worker.save_finished_signal.connect( self.__handle_save_finished )

        # 启动工作线程
        self.__save_qthread.start()
        self.__logger.debug( "保存工作线程已启动" )

        # 保存原始容器引用
        self.__original_containers: List[ Union[ QWidget, QLayout ] ] = [ ]
        if isinstance( container, list ):
            self.__original_containers.extend( container )
        else:
            self.__original_containers.append( container )

        # 配置文件路径
        self.__config_dir = config_dir or os.path.join(
            os.path.dirname( os.path.dirname( os.path.abspath( __file__ ) ) ), "config"
        )
        self.__config_file = os.path.join( self.__config_dir, config_filename )

        # 确保配置目录存在
        os.makedirs( self.__config_dir, exist_ok=True )

        # 记录日志
        if self.__log_output:
            self.__log_output.append( f"初始化CheckBoxStateManager，配置文件: {config_filename}", color=Colors.BLUE )

        # 查找所有复选框
        # 处理容器是列表的情况
        if isinstance( container, list ):
            for cont in container:
                self.__find_checkboxes( cont )
        else:
            self.__find_checkboxes( container )

        self.__logger.debug( f"找到 {len( self.__checkboxes )} 个复选框控件" )

        # 记录日志
        if self.__log_output and self.__checkboxes:
            self.__log_output.append( f"找到 {len( self.__checkboxes )} 个复选框控件", color=Colors.GREEN )

        # 连接信号
        self.__connect_signals()

    def __find_checkboxes( self, container: Union[ QWidget, QLayout ], parent_path: str = "" ) -> None:
        """
        递归查找容器中所有QCheckBox控件。

        Args:
                container: QWidget或QLayout容器
                parent_path: 父容器路径，用于构建层级路径
        """
        self.__logger.debug( f"在容器 {container} 中查找复选框" )

        try:
            # 获取当前容器的名称
            container_name = ""
            if isinstance( container, QWidget ):
                container_name = container.objectName() or f"widget_{id( container )}"
            elif isinstance( container, QLayout ):
                container_name = container.objectName() or f"layout_{id( container )}"

            # 构建当前路径
            current_path = parent_path
            if current_path and container_name:
                current_path = f"{current_path}.{container_name}"
            elif container_name:
                current_path = container_name

            # 处理布局容器
            if isinstance( container, QLayout ):
                for i in range( container.count() ):
                    item = container.itemAt( i )
                    if item is None:
                        continue

                    # 递归处理子布局
                    if item.layout():
                        self.__find_checkboxes( item.layout(), current_path )

                    # 处理子部件
                    if item.widget():
                        self.__find_checkboxes( item.widget(), current_path )

            # 处理部件容器
            elif isinstance( container, QWidget ):
                # 检查容器本身是否为QCheckBox
                if isinstance( container, QCheckBox ):
                    self.__add_checkbox( container, current_path, container.parentWidget() )

                # 递归处理子部件
                for child in container.findChildren( QObject ):  # 使用QObject以便检查layout()
                    if isinstance( child, QCheckBox ):
                        self.__add_checkbox( child, current_path, container )
                    elif isinstance(child, QWidget) and (child.layout() or isinstance( child, (QGroupBox, QFrame) )):
                        # 递归处理有布局或可能包含子部件的容器
                        self.__find_checkboxes( child, current_path )
        except Exception as e:
            self.__logger.error( f"查找复选框时发生错误: {e}" )
            if self.__log_output:
                self.__log_output.append( f"查找复选框时发生错误: {e}", color=Colors.RED )
            traceback.print_exc()

    def __add_checkbox(
        self, checkbox: QCheckBox, parent_path: str = "", parent_container: Optional[ QWidget ] = None
    ) -> None:
        """
        添加QCheckBox到管理列表并初始化状态。

        Args:
                checkbox: 要添加的QCheckBox控件
                parent_path: 父容器路径，用于构建完整路径
                parent_container: 复选框所属的父容器
        """
        # 确保复选框不重复添加
        if checkbox not in self.__checkboxes and self.__is_valid_pyqt_object( checkbox ):
            # 获取复选框名称
            checkbox_name = checkbox.objectName() or f"checkbox_{id( checkbox )}"
            text = checkbox.text()

            # 构建完整路径作为唯一标识
            full_path = checkbox_name
            if parent_path:
                full_path = f"{parent_path}.{checkbox_name}"

            self.__logger.debug( f"添加复选框: {full_path} - '{text}'" )
            self.__checkboxes.append( checkbox )

            # 记录复选框路径
            self.__checkbox_paths[ checkbox ] = full_path

            # 记录初始状态
            self.__states[ full_path ] = checkbox.isChecked()

            # 找到所属原始容器
            container = None
            if parent_container is not None:
                # 检查传入的父容器是否是原始容器之一
                for orig_container in self.__original_containers:
                    if parent_container == orig_container:
                        container = parent_container
                        break

                # 如果不是原始容器，查找原始容器祖先
                if container is None and isinstance( parent_container, QWidget ):
                    for orig_container in self.__original_containers:
                        if isinstance( orig_container, QWidget ):
                            # 检查是否是祖先关系
                            parent = parent_container
                            while parent is not None:
                                if parent == orig_container:
                                    container = orig_container
                                    break
                                parent = parent.parentWidget()
                            if container is not None:
                                break

            # 如果找不到明确的原始容器，使用第一个原始容器作为默认值
            if container is None and self.__original_containers:
                container = self.__original_containers[ 0 ]

            # 记录复选框所属的父容器
            if container is not None:
                self.__checkbox_containers[ checkbox ] = container

                # 记录容器包含的复选框
                if container not in self.__container_checkboxes:
                    self.__container_checkboxes[ container ] = set()
                self.__container_checkboxes[ container ].add( checkbox )

    def __connect_signals( self ) -> None:
        """为所有复选框连接状态变化信号。"""
        for checkbox in self.__checkboxes:
            # 获取复选框路径
            path = self.__get_checkbox_path( checkbox )

            # 创建信号处理函数
            handler = partial( self.__on_checkbox_state_changed, checkbox, path )

            # 连接信号
            checkbox.stateChanged.connect( handler )

            # 记录连接，以便清理
            if checkbox not in self.__connections:
                self.__connections[ checkbox ] = [ ]
            self.__connections[ checkbox ].append( handler )

            self.__logger.debug( f"已连接复选框信号: {path}" )

    @pyqtSlot( int )
    def __on_checkbox_state_changed( self, checkbox: QCheckBox, path: str, state: int ) -> None:
        """
        处理复选框状态变化。
        当复选框状态变化时，检查同一容器中所有复选框的状态，将变化的状态与配置文件比较并更新。

        Args:
                checkbox: 发生变化的复选框
                path: 复选框的层级路径
                state: 新状态值 (Qt.Checked=2, Qt.Unchecked=0, Qt.PartiallyChecked=1)
        """
        try:
            if not self.__is_valid_pyqt_object( checkbox ):
                self.__logger.warning( f"复选框 {path} 已被销毁或无效" )
                return

            # 获取布尔状态
            is_checked = state == Qt.Checked

            # 更新状态记录
            self.__states[ path ] = is_checked

            # 发出信号
            self.checkbox_state_changed.emit( checkbox, is_checked )

            # 记录日志
            if self.__log_output:
                checkbox_name = checkbox.text() or path
                check_status = "选中" if is_checked else "取消选中"
                self.__log_output.append_multi_style(
                    [
                        ("复选框状态变化: ", { "color": Colors.BLUE }),
                        (f"{checkbox_name}", { "color": Colors.PURPLE, "bold": True }),
                        (f" - {check_status}", { "color": Colors.GREEN if is_checked else Colors.ORANGE })
                    ]
                )

            # 查找当前复选框所属的容器
            container = self.__checkbox_containers.get( checkbox )
            if container is None:
                self.__logger.warning( f"无法确定复选框 {path} 所属的容器" )
                # 只更新当前复选框状态
                self.__debounce_save()
                return

            # 获取同一容器中的所有复选框
            container_checkboxes = self.get_checkboxes_by_container( container )
            if not container_checkboxes:
                self.__logger.warning( f"容器中未找到有效的复选框" )
                # 只更新当前复选框状态
                self.__debounce_save()
                return

            # 更新同一容器中所有复选框的状态
            self.__logger.debug( f"更新容器 {container} 中的 {len( container_checkboxes )} 个复选框状态" )

            # 获取所有复选框的最新状态
            container_paths = [ ]
            for cb in container_checkboxes:
                cb_path = self.__get_checkbox_path( cb )
                current_state = cb.isChecked()
                self.__states[ cb_path ] = current_state
                container_paths.append( cb_path )
                self.__logger.debug( f"复选框 {cb_path} 当前状态: {current_state}" )

            # 比较内存中的状态与配置文件中的状态
            changes = self.__compare_with_config( container_paths )

            # 如果有变化，更新配置
            if changes:
                self.__logger.info( f"检测到 {len( changes )} 个复选框状态变化，更新配置文件" )
                if self.__log_output:
                    self.__log_output.append( f"检测到 {len( changes )} 个复选框状态变化，准备保存", color=Colors.BLUE )
                self.__debounce_save()
            else:
                self.__logger.debug( "未检测到状态变化，跳过保存" )

            self.__logger.debug( f"复选框 {path} 状态变为: {is_checked}" )

        except Exception as e:
            self.__logger.error( f"处理复选框状态变化时发生错误: {e}" )
            if self.__log_output:
                self.__log_output.append( f"处理复选框状态变化时发生错误: {e}", color=Colors.RED )
            traceback.print_exc()
            # 发生错误时仍然尝试保存当前状态
            self.__debounce_save()

    def __debounce_save( self ) -> None:
        """使用防抖机制延迟保存状态 (QTimer实现)。"""
        self.__logger.debug( "尝试启动防抖保存定时器..." )

        # 检查 QTimer 是否存在
        if self.__save_timer_qt is not None and self.__save_timer_qt.isActive():
            self.__save_timer_qt.stop()
            self.__logger.debug( "已停止现有的防抖保存QTimer。" )

        # 如果QTimer不存在，创建一个新的
        if self.__save_timer_qt is None:
            self.__save_timer_qt = QTimer( self )  # self作为父对象，便于生命周期管理
            self.__save_timer_qt.setSingleShot( True )  # 单次触发
            self.__save_timer_qt.timeout.connect( self.__execute_save_via_worker )
            self.__logger.debug( "已创建新的防抖保存QTimer并连接信号。" )

        # 启动定时器
        self.__save_timer_qt.start( self.__debounce_delay )
        self.__logger.debug( f"防抖保存QTimer已启动，延迟: {self.__debounce_delay}ms。" )

    @pyqtSlot()
    def __execute_save_via_worker( self ) -> None:
        """
        由防抖QTimer触发，通过工作线程执行保存操作。
        """
        self.__logger.debug( "防抖QTimer触发，准备执行保存操作。" )
        self.save_states()  # 调用save_states方法，该方法已修改为使用工作线程

    def save_states( self ) -> bool:
        """
        将所有复选框状态通过工作线程保存到配置文件。
        采用增量更新方式。

        Returns:
                bool: True表示保存请求已成功发出，False表示线程未运行或无状态保存。
                          实际保存结果是异步的。

        使用示例:
        ---------
        ```python
        # manager 是 CheckBoxStateManager 实例
        success = manager.save_states()  # 异步保存，此调用立即返回
        if success:
                print("保存请求已发送。")
        ```
        """
        self.__logger.info( "请求通过工作线程保存状态..." )

        # 检查工作线程是否运行
        if not self.__save_qthread.isRunning():
            self.__logger.warning( "保存工作线程未运行，无法保存状态。" )
            if self.__log_output:
                self.__log_output.append( "保存工作线程未运行，保存失败。", color=Colors.RED )
            return False

        # 更新 self.__states 以获取最新状态 (这部分在主线程)
        # 确保 get_all_checkboxes() 返回的都是有效对象
        current_checkbox_states = { }
        for checkbox in self.get_all_checkboxes():
            path = self.__get_checkbox_path( checkbox )
            if path:
                current_checkbox_states[ path ] = checkbox.isChecked()

        # 更新内部状态变量
        self.__states.update( current_checkbox_states )

        if not self.__states:
            self.__logger.info( "没有状态需要保存。" )
            if self.__log_output:
                self.__log_output.append( "没有状态需要保存。", color=Colors.BLUE )
            return True

            # 创建状态的副本，传递给工作线程
        states_copy_for_worker = self.__states.copy()

        # 发送信号给工作线程，请求执行保存操作
        self._request_save_on_worker_signal.emit( states_copy_for_worker, self.__config_file, self.__config_dir )

        # 记录手动保存时间，用于优化自动保存
        self.__manual_save_time = time.time()

        self.__logger.debug( f"已向工作线程发出保存 {len( states_copy_for_worker )} 个状态的请求。" )
        if self.__log_output:
            self.__log_output.append_multi_style(
                [
                    ("保存请求已发送: ", { "color": Colors.BLUE, "bold": True }),
                    (f"准备保存 {len( states_copy_for_worker )} 个复选框状态", { "color": Colors.BLACK })
                ]
            )
        return True

    def load_states( self ) -> bool:
        """
        从配置文件加载复选框状态并应用。

        Returns:
                bool: 加载是否成功
        """
        if not os.path.exists( self.__config_file ):
            self.__logger.info( f"配置文件不存在: {self.__config_file}, 跳过加载状态" )
            if self.__log_output:
                self.__log_output.append( f"配置文件不存在，跳过加载状态", color=Colors.ORANGE )
            return False

        try:
            if self.__log_output:
                self.__log_output.append( f"正在加载复选框状态...", color=Colors.BLUE )

            with open( self.__config_file, 'r', encoding='utf-8' ) as f:
                saved_states = json.load( f )

            if not isinstance( saved_states, dict ):
                self.__logger.error( f"配置文件格式错误: {self.__config_file}" )
                if self.__log_output:
                    self.__log_output.append( f"配置文件格式错误", color=Colors.RED )
                return False

            # 应用保存的状态
            applied_count = 0
            for checkbox in self.__checkboxes:
                # 获取复选框路径
                path = self.__get_checkbox_path( checkbox )

                if path in saved_states:
                    # 临时断开信号，避免触发保存
                    self.__disconnect_checkbox_signals( checkbox )

                    # 设置状态
                    checkbox.setChecked( saved_states[ path ] )

                    # 重新连接信号
                    self.__reconnect_checkbox_signals( checkbox )

                    # 更新内存中的状态
                    self.__states[ path ] = saved_states[ path ]
                    applied_count += 1

            self.__logger.info(
                f"已从 {self.__config_file} 加载并应用 {applied_count}/{len( self.__checkboxes )} 个复选框状态"
            )

            if self.__log_output:
                self.__log_output.append_multi_style(
                    [
                        ("加载完成: ", { "color": Colors.GREEN, "bold": True }),
                        (f"已应用 {applied_count}/{len( self.__checkboxes )} 个复选框状态", { "color": Colors.BLACK })
                    ]
                )

            return True

        except Exception as e:
            self.__logger.error( f"加载复选框状态失败: {e}" )
            if self.__log_output:
                self.__log_output.append( f"加载复选框状态失败: {e}", color=Colors.RED )
            traceback.print_exc()
            return False

    def __disconnect_checkbox_signals( self, checkbox: QCheckBox ) -> None:
        """
        临时断开复选框信号连接。

        Args:
                checkbox: 要断开信号的复选框
        """
        if checkbox in self.__connections:
            for handler in self.__connections[ checkbox ]:
                try:
                    checkbox.stateChanged.disconnect( handler )
                except Exception:
                    pass  # 忽略断开失败的情况

    def __reconnect_checkbox_signals( self, checkbox: QCheckBox ) -> None:
        """
        重新连接复选框信号。

        Args:
                checkbox: 要重新连接信号的复选框
        """
        if checkbox in self.__connections:
            for handler in self.__connections[ checkbox ]:
                try:
                    checkbox.stateChanged.connect( handler )
                except Exception:
                    pass  # 忽略连接失败的情况

    def get_all_checkboxes( self ) -> List[ QCheckBox ]:
        """
        获取所有管理的复选框列表。

        Returns:
                List[QCheckBox]: 复选框列表
        """
        return [ cb for cb in self.__checkboxes if self.__is_valid_pyqt_object( cb ) ]

    def get_checkbox_by_text( self, text: str ) -> Optional[ QCheckBox ]:
        """
        根据显示文本查找复选框。

        Args:
                text: 复选框显示的文本

        Returns:
                Optional[QCheckBox]: 找到的复选框，如果未找到则返回None
        """
        for checkbox in self.get_all_checkboxes():
            if checkbox.text() == text:
                return checkbox
        return None

    def get_checkbox_by_object_name( self, object_name: str ) -> Optional[ QCheckBox ]:
        """
        根据对象名称查找复选框。

        Args:
                object_name: 复选框的objectName

        Returns:
                Optional[QCheckBox]: 找到的复选框，如果未找到则返回None
        """
        for checkbox in self.get_all_checkboxes():
            if checkbox.objectName() == object_name:
                return checkbox
        return None

    def get_checked_checkboxes( self ) -> List[ QCheckBox ]:
        """
        获取所有选中状态的复选框。

        Returns:
                List[QCheckBox]: 选中的复选框列表
        """
        return [ cb for cb in self.get_all_checkboxes() if cb.isChecked() ]

    def get_unchecked_checkboxes( self ) -> List[ QCheckBox ]:
        """
        获取所有未选中状态的复选框。

        Returns:
                List[QCheckBox]: 未选中的复选框列表
        """
        return [ cb for cb in self.get_all_checkboxes() if not cb.isChecked() ]

    def get_checked_names( self ) -> List[ str ]:
        """
        获取所有选中状态的复选框名称。

        Returns:
                List[str]: 选中的复选框层级路径列表
        """
        return [ self.__get_checkbox_path( cb ) for cb in self.get_checked_checkboxes() ]

    def get_current_states( self ) -> Dict[ str, bool ]:
        """
        获取当前所有复选框的状态。

        Returns:
                Dict[str, bool]: 格式为{路径: isChecked}的状态字典
        """
        # 更新状态记录，确保最新
        for checkbox in self.get_all_checkboxes():
            path = self.__get_checkbox_path( checkbox )
            self.__states[ path ] = checkbox.isChecked()

        return self.__states.copy()

    def set_checkbox_state( self, path: str, checked: bool ) -> bool:
        """
        根据层级路径设置复选框状态。

        Args:
                path: 复选框的层级路径或objectName
                checked: 是否选中

        Returns:
                bool: 设置是否成功
        """
        # 首先根据路径查找复选框
        for checkbox in self.get_all_checkboxes():
            checkbox_path = self.__checkbox_paths.get( checkbox )

            # 匹配路径或对象名
            if checkbox_path == path or checkbox.objectName() == path:
                checkbox.setChecked( checked )
                return True

        self.__logger.warning( f"未找到路径为 {path} 的复选框" )
        return False

    def set_all_checked( self, checked: bool = True ) -> None:
        """
        设置所有复选框的选中状态。

        Args:
                checked: 是否选中，默认为True（全选）
        """
        for checkbox in self.get_all_checkboxes():
            checkbox.setChecked( checked )

    def __is_valid_pyqt_object( self, obj: Optional[ QObject ] ) -> bool:
        """
        安全地检查PyQt对象是否有效（未被销毁）。

        Args:
                obj: 要检查的PyQt对象

        Returns:
                bool: 如果对象存在且未被销毁则返回True，否则返回False
        """
        try:
            return obj is not None and not sip.isdeleted( obj )
        except Exception:
            return False

    def cleanup( self ) -> None:
        """
        清理资源并断开信号连接（应用程序退出前调用）。

        该方法会执行以下清理操作：
        1. 停止自动保存功能
        2. 取消防抖定时器
        3. 立即保存当前状态
        4. 断开所有信号连接
        5. 清空所有引用集合

        示例:
                ```python
                # 在窗口关闭事件中调用
                def closeEvent(self, event):
                        self.checkbox_manager.cleanup()
                        super().closeEvent(event)
                ```
        """
        if self.__log_output:
            self.__log_output.append( "正在清理复选框状态管理器...", color=Colors.BLUE )

        self.__logger.debug( "CheckBoxStateManager cleanup: 开始清理..." )

        # 1. 停止自动保存 QTimer
        if hasattr( self, '_CheckBoxStateManager__auto_save_qtimer' ) and self.__auto_save_qtimer is not None:
            if self.__auto_save_qtimer.isActive():
                self.__auto_save_qtimer.stop()
                self.__logger.debug( "Cleanup: 自动保存QTimer已停止。" )
        else:
            self.__logger.debug( "Cleanup: 自动保存QTimer不存在或已为None。" )

        # 2. 停止防抖保存 QTimer
        if hasattr( self, '_CheckBoxStateManager__save_timer_qt' ) and self.__save_timer_qt is not None:
            if self.__save_timer_qt.isActive():
                self.__save_timer_qt.stop()
                self.__logger.debug( "Cleanup: 防抖保存QTimer已停止。" )
        else:
            self.__logger.debug( "Cleanup: 防抖保存QTimer不存在或已为None。" )

        # 3. 请求最后一次保存 (异步)
        if self.__states:  # 只有在有状态需要保存时才保存
            self.__logger.info( "Cleanup: 请求进行最后一次状态保存 (异步)..." )
            self.save_states()  # 这是异步的

        # 4. 关闭并等待工作线程
        if hasattr( self, '_CheckBoxStateManager__save_qthread' ) and self.__save_qthread is not None:
            if self.__save_qthread.isRunning():
                self.__logger.debug( "Cleanup: 请求保存工作线程退出..." )
                self.__save_qthread.quit()
                self.__logger.debug( "Cleanup: 等待保存工作线程完成..." )
                if not self.__save_qthread.wait( 7000 ):  # 等待7秒
                    self.__logger.warning( "Cleanup: 保存工作线程在7秒内未能正常退出，将尝试强制终止。" )
                    self.__save_qthread.terminate()
                    self.__save_qthread.wait()  # 等待终止完成
                    self.__logger.debug( "Cleanup: 保存工作线程已强制终止。" )
                else:
                    self.__logger.debug( "Cleanup: 保存工作线程已正常退出。" )
            else:
                self.__logger.debug( "Cleanup: 保存工作线程未在运行。" )
        else:
            self.__logger.debug( "Cleanup: 保存工作QThread不存在或已为None。" )

        # 5. 安全删除 QTimer 对象
        if hasattr( self, '_CheckBoxStateManager__auto_save_qtimer' ) and self.__auto_save_qtimer is not None:
            self.__auto_save_qtimer.deleteLater()
        self.__auto_save_qtimer = None
        self.__logger.debug( "Cleanup: 自动保存QTimer已标记为deleteLater。" )

        if hasattr( self, '_CheckBoxStateManager__save_timer_qt' ) and self.__save_timer_qt is not None:
            self.__save_timer_qt.deleteLater()
        self.__save_timer_qt = None
        self.__logger.debug( "Cleanup: 防抖保存QTimer已标记为deleteLater。" )

        # 断开所有信号连接
        disconnected_count = 0
        for checkbox, handlers in list( self.__connections.items() ):
            for handler in handlers:
                try:
                    if self.__is_valid_pyqt_object( checkbox ):
                        checkbox.stateChanged.disconnect( handler )
                        disconnected_count += 1
                except Exception:
                    pass  # 忽略断开失败的情况

        # 清空连接记录
        self.__connections.clear()

        # 清空容器引用
        self.__checkbox_containers.clear()
        self.__container_checkboxes.clear()
        self.__original_containers.clear()

        # 清空其他资源
        self.__checkboxes.clear()
        self.__states.clear()
        self.__checkbox_paths.clear()

        self.__logger.debug( "CheckBoxStateManager资源已清理" )

        if self.__log_output:
            self.__log_output.append_multi_style(
                [
                    ("清理完成: ", { "color": Colors.GREEN, "bold": True }),
                    (f"已断开 {disconnected_count} 个信号连接", { "color": Colors.BLACK })
                ]
            )

    def __del__( self ):
        """析构函数，确保资源被清理。"""
        try:
            self.cleanup()
        except Exception:
            pass  # 忽略析构函数中的异常

    def set_debounce_delay( self, delay_ms: int ) -> None:
        """
        设置状态保存的防抖延迟时间。

        Args:
                delay_ms: 延迟时间（毫秒）
        """
        if delay_ms <= 0:
            self.__logger.warning( f"无效的防抖延迟时间: {delay_ms}，使用默认值300ms" )
            delay_ms = 300

        self.__debounce_delay = delay_ms
        self.__logger.debug( f"防抖延迟时间已更新为: {delay_ms}ms" )

        # 如果有正在运行的定时器，取消并重新创建
        self.__debounce_save()

    def start_auto_save( self, interval_seconds: float = 60.0 ) -> None:
        """
        启动自动保存功能，定期通过工作线程保存复选框状态。

        Args:
                interval_seconds: 自动保存间隔（秒），默认60秒

        使用示例:
        ---------
        ```python
        # manager = CheckBoxStateManager(...)
        manager.start_auto_save(interval_seconds=120)  # 每2分钟自动保存一次
        ```
        """
        if interval_seconds <= 0:
            self.__logger.warning( f"无效的自动保存间隔: {interval_seconds}秒，使用默认值60秒" )
            interval_seconds = 60.0

        # 停止现有的自动保存
        self.stop_auto_save()

        # 设置新的间隔并启动定时器
        self.__auto_save_interval = interval_seconds
        if self.__auto_save_qtimer is not None:
            self.__auto_save_qtimer.start( int( self.__auto_save_interval * 1000 ) )  # QTimer以毫秒为单位

        self.__logger.info( f"已启动自动保存功能，间隔: {self.__auto_save_interval}秒。" )
        if self.__log_output:
            self.__log_output.append( f"已启动自动保存功能，间隔: {self.__auto_save_interval}秒", color=Colors.GREEN )

    def stop_auto_save( self ) -> None:
        """
        停止自动保存功能。

        使用示例:
        ---------
        ```python
        manager.stop_auto_save()
        ```
        """
        # 检查是否当前启用了自动保存
        was_enabled = self.is_auto_save_enabled()

        # 停止定时器
        if self.__auto_save_qtimer is not None and self.__auto_save_qtimer.isActive():
            self.__auto_save_qtimer.stop()

        # 标记为未启用
        self.__auto_save_interval = None

        self.__logger.debug( "已停止自动保存功能。" )
        if was_enabled and self.__log_output:
            self.__log_output.append( "已停止自动保存功能", color=Colors.ORANGE )

    @pyqtSlot()
    def __trigger_auto_save_on_worker( self ) -> None:
        """
        由自动保存QTimer的timeout信号触发。
        检查是否需要跳过本次保存，如果不需要，则通过工作线程执行保存。
        """
        self.__logger.debug( "自动保存QTimer触发。" )

        # 如果自动保存已停用，则不执行保存
        if self.__auto_save_interval is None:
            self.__logger.debug( "自动保存已停止（interval is None），回调中止。" )
            # 确保定时器已停止
            if self.__auto_save_qtimer is not None and self.__auto_save_qtimer.isActive():
                self.__auto_save_qtimer.stop()
            return

        # 检查是否应该跳过本次自动保存（如果最近有手动保存）
        skip_save_due_to_manual = False
        current_time = time.time()

        if self.__manual_save_time is not None and self.__auto_save_interval is not None:
            time_since_manual_save = current_time - self.__manual_save_time
            if time_since_manual_save < self.__auto_save_interval:
                skip_save_due_to_manual = True
                self.__logger.debug(
                    f"最近有手动保存 ({time_since_manual_save:.1f}秒前，自动保存间隔 {self.__auto_save_interval}秒)，跳过此次自动保存。"
                )

        # 根据检查结果决定是否保存
        if skip_save_due_to_manual:
            if self.__log_output:
                self.__log_output.append( "跳过此次自动保存（因近期有手动保存）", color=Colors.ORANGE )
        else:
            self.__logger.info( "自动保存：准备通过工作线程保存状态..." )

            if self.__log_output:
                self.__log_output.append( f"自动保存触发，当前管理状态数: {len( self.__states )}", color=Colors.BLUE )

            # 调用保存方法
            save_triggered = self.save_states()

            if save_triggered:
                self.__logger.debug( f"自动保存请求已发送给工作线程。" )
            # QTimer是重复触发的，不需要在这里重新调度

    def is_auto_save_enabled( self ) -> bool:
        """
        检查自动保存功能是否已启用。

        Returns:
                bool: 如果自动保存已启用则返回True，否则返回False

        使用示例:
        ---------
        ```python
        if manager.is_auto_save_enabled():
                print("自动保存已启用")
        ```
        """
        # 通过自动保存间隔是否为None来判断
        return self.__auto_save_interval is not None

    def get_auto_save_interval( self ) -> Optional[ float ]:
        """
        获取当前的自动保存间隔。

        Returns:
                Optional[float]: 自动保存间隔（秒），如果未启用自动保存则返回None
        """
        return self.__auto_save_interval

    def __get_checkbox_path( self, checkbox: QCheckBox ) -> str:
        """
        获取复选框的层级路径。

        Args:
                checkbox: 要获取路径的复选框

        Returns:
                str: 复选框的层级路径
        """
        # 首先从路径字典中获取
        path = self.__checkbox_paths.get( checkbox )
        if not path:
            # 如果没有路径信息，使用对象名作为备选
            path = checkbox.objectName() or f"checkbox_{id( checkbox )}"
        return path

    def get_checkbox_by_path( self, path: str ) -> Optional[ QCheckBox ]:
        """
        根据层级路径查找复选框。

        Args:
                path: 复选框的层级路径

        Returns:
                Optional[QCheckBox]: 找到的复选框，如果未找到则返回None

        示例:
                ```python
                # 获取特定路径的复选框
                checkbox = manager.get_checkbox_by_path("main_widget.option1")
                if checkbox:
                        print(f"找到复选框: {checkbox.text()}")
                ```
        """
        # 反向查找路径对应的复选框
        for checkbox, cb_path in self.__checkbox_paths.items():
            if cb_path == path and self.__is_valid_pyqt_object( checkbox ):
                return checkbox

        # 如果在路径字典中没找到，尝试通过对象名匹配
        for checkbox in self.get_all_checkboxes():
            if checkbox.objectName() == path:
                return checkbox

        return None

    def get_checkboxes_by_container( self, container: Union[ QWidget, QLayout ] ) -> List[ QCheckBox ]:
        """
        获取指定容器中的所有复选框。

        Args:
                container: 要查找复选框的容器

        Returns:
                List[QCheckBox]: 容器中的复选框列表

        示例:
                ```python
                # 获取特定容器中的所有复选框
                container_checkboxes = manager.get_checkboxes_by_container(my_widget)
                print(f"容器中有 {len(container_checkboxes)} 个复选框")
                for cb in container_checkboxes:
                        print(f"- {cb.text()}: {cb.isChecked()}")
                ```
        """
        if container not in self.__container_checkboxes:
            return [ ]

        # 过滤出有效的复选框
        return [ cb for cb in self.__container_checkboxes[ container ]
                 if self.__is_valid_pyqt_object( cb ) ]

    def __compare_with_config( self, paths_to_check: List[ str ] ) -> Dict[ str, bool ]:
        """
        比较内存中的状态与配置文件中的状态，找出差异。

        Args:
                paths_to_check: 要检查的复选框路径列表

        Returns:
                Dict[str, bool]: 发生变化的复选框路径和新状态的字典
        """
        try:
            # 如果配置文件不存在，所有状态都是新的
            if not os.path.exists( self.__config_file ):
                return { path: self.__states[ path ] for path in paths_to_check if path in self.__states }

            # 读取配置文件中的状态
            with open( self.__config_file, 'r', encoding='utf-8' ) as f:
                config_states = json.load( f )

            if not isinstance( config_states, dict ):
                self.__logger.error( f"配置文件格式错误: {self.__config_file}" )
                return { }

            # 查找状态不同的复选框
            changes = { }
            for path in paths_to_check:
                if path in self.__states:
                    current_state = self.__states[ path ]
                    # 如果路径不在配置中或状态不同，则记录变化
                    if path not in config_states or config_states[ path ] != current_state:
                        changes[ path ] = current_state

            return changes

        except Exception as e:
            self.__logger.error( f"比较状态时发生错误: {e}" )
            traceback.print_exc()
            return { }

    def update_container_states( self, container: Union[ QWidget, QLayout ] ) -> bool:
        """
        更新并保存指定容器中所有复选框的状态。

        此方法会读取容器中所有复选框的当前状态，与配置文件比较，
        并将变化的状态保存到配置文件。

        Args:
                container: 要更新复选框状态的容器

        Returns:
                bool: 更新是否成功

        示例:
                ```python
                # 更新特定容器中所有复选框的状态
                success = manager.update_container_states(my_widget)
                if success:
                        print("容器中复选框状态已更新")
                ```
        """
        try:
            container_name = ""
            if hasattr( container, "objectName" ):
                container_name = container.objectName() or f"container_{id( container )}"
            else:
                container_name = f"container_{id( container )}"

            if self.__log_output:
                self.__log_output.append( f"正在更新容器 {container_name} 中的复选框状态...", color=Colors.BLUE )

            # 获取容器中的所有复选框
            container_checkboxes = self.get_checkboxes_by_container( container )
            if not container_checkboxes:
                self.__logger.info( f"容器中未找到有效的复选框" )
                if self.__log_output:
                    self.__log_output.append( f"容器中未找到有效的复选框", color=Colors.ORANGE )
                return False

            # 更新所有复选框的状态
            container_paths = [ ]
            for cb in container_checkboxes:
                cb_path = self.__get_checkbox_path( cb )
                current_state = cb.isChecked()
                self.__states[ cb_path ] = current_state
                container_paths.append( cb_path )

            # 比较内存中的状态与配置文件中的状态
            changes = self.__compare_with_config( container_paths )

            # 如果有变化，更新配置
            if changes:
                self.__logger.info( f"检测到 {len( changes )} 个复选框状态变化，更新配置文件" )
                if self.__log_output:
                    self.__log_output.append(
                        f"检测到 {len( changes )} 个复选框状态变化，正在保存...", color=Colors.BLUE
                    )
                success = self.save_states()
                return success
            else:
                self.__logger.debug( "未检测到状态变化，跳过保存" )
                if self.__log_output:
                    self.__log_output.append( "未检测到状态变化，跳过保存", color=Colors.GREEN )
                return True

        except Exception as e:
            self.__logger.error( f"更新容器状态时发生错误: {e}" )
            if self.__log_output:
                self.__log_output.append( f"更新容器状态时发生错误: {e}", color=Colors.RED )
            traceback.print_exc()
            return False

    def load_container_states( self, container: Union[ QWidget, QLayout ] ) -> bool:
        """
        从配置文件加载并应用指定容器中复选框的状态。

        Args:
                container: 要加载复选框状态的容器

        Returns:
                bool: 加载是否成功

        示例:
                ```python
                # 加载特定容器中复选框的状态
                success = manager.load_container_states(my_widget)
                if success:
                        print("容器中复选框状态已从配置加载")
                ```
        """
        container_name = ""
        if hasattr( container, "objectName" ):
            container_name = container.objectName() or f"container_{id( container )}"
        else:
            container_name = f"container_{id( container )}"

        if not os.path.exists( self.__config_file ):
            self.__logger.info( f"配置文件不存在: {self.__config_file}, 跳过加载状态" )
            if self.__log_output:
                self.__log_output.append( f"配置文件不存在，跳过加载状态", color=Colors.ORANGE )
            return False

        try:
            if self.__log_output:
                self.__log_output.append( f"正在加载容器 {container_name} 中的复选框状态...", color=Colors.BLUE )

            # 加载配置文件
            with open( self.__config_file, 'r', encoding='utf-8' ) as f:
                saved_states = json.load( f )

            if not isinstance( saved_states, dict ):
                self.__logger.error( f"配置文件格式错误: {self.__config_file}" )
                if self.__log_output:
                    self.__log_output.append( f"配置文件格式错误", color=Colors.RED )
                return False

            # 获取容器中的所有复选框
            container_checkboxes = self.get_checkboxes_by_container( container )
            if not container_checkboxes:
                self.__logger.info( f"容器中未找到有效的复选框" )
                if self.__log_output:
                    self.__log_output.append( f"容器中未找到有效的复选框", color=Colors.ORANGE )
                return False

            # 应用保存的状态
            applied_count = 0
            for checkbox in container_checkboxes:
                # 获取复选框路径
                path = self.__get_checkbox_path( checkbox )

                if path in saved_states:
                    # 临时断开信号，避免触发保存
                    self.__disconnect_checkbox_signals( checkbox )

                    # 设置状态
                    checkbox.setChecked( saved_states[ path ] )

                    # 重新连接信号
                    self.__reconnect_checkbox_signals( checkbox )

                    # 更新内存中的状态
                    self.__states[ path ] = saved_states[ path ]
                    applied_count += 1

            self.__logger.info(
                f"已从 {self.__config_file} 加载并应用 {applied_count}/{len( container_checkboxes )} 个容器中的复选框状态"
            )

            if self.__log_output:
                self.__log_output.append_multi_style(
                    [
                        ("加载完成: ", { "color": Colors.GREEN, "bold": True }),
                        (f"{container_name} 容器中已应用 {applied_count}/{len( container_checkboxes )} 个复选框状态",
                         { "color": Colors.BLACK })
                    ]
                )

            return applied_count > 0

        except Exception as e:
            self.__logger.error( f"加载容器状态失败: {e}" )
            if self.__log_output:
                self.__log_output.append( f"加载容器状态失败: {e}", color=Colors.RED )
            traceback.print_exc()
            return False

    def get_checkbox_container( self, checkbox: QCheckBox ) -> Optional[ Union[ QWidget, QLayout ] ]:
        """
        获取复选框所属的父容器。

        Args:
                checkbox: 要查询的复选框

        Returns:
                Optional[Union[QWidget, QLayout]]: 复选框所属的父容器，如果未找到则返回None

        示例:
                ```python
                # 获取复选框所属的容器
                checkbox = manager.get_checkbox_by_text("选项1")
                if checkbox:
                        container = manager.get_checkbox_container(checkbox)
                        if container:
                                print(f"复选框 '选项1' 属于容器: {container.objectName()}")
                ```
        """
        if not self.__is_valid_pyqt_object( checkbox ):
            self.__logger.warning( f"无效的复选框对象" )
            return None

        return self.__checkbox_containers.get( checkbox )

    # 更新历史
    # ------------------------------
    # 改进说明:
    # 1. 添加了按容器管理复选框的功能，当一个复选框状态变化时，会检查并更新同一容器中所有复选框的状态
    # 2. 实现了对容器内复选框状态的单独加载和保存功能
    # 3. 每个复选框关联到其所属的原始容器，提供了检索和操作这些关系的方法
    # 4. 对比内存中的状态与配置文件中的状态，只有在状态发生变化时才更新配置文件
    # 5. 所有新增功能都具有强健的错误处理机制，确保即使在异常情况下也能正常工作
    # ------------------------------

    @pyqtSlot( str, str )
    def __handle_worker_log( self, message: str, color_name: str ) -> None:
        """
        处理来自工作线程的日志消息。

        Args:
                message: 日志消息
                color_name: 颜色名称或十六进制值
        """
        if self.__log_output:
            # 映射颜色名称到Colors常量
            color_map = {
                "BLUE": Colors.BLUE,
                "GREEN": Colors.GREEN,
                "RED": Colors.RED,
                "ORANGE": Colors.ORANGE,
                "PURPLE": Colors.PURPLE,
                "BLACK": Colors.BLACK
            }
            actual_color = color_map.get( color_name.upper(), Colors.BLACK )  # 默认为黑色
            self.__log_output.append( message, color=actual_color )

    @pyqtSlot( list )
    def __handle_worker_log_multi_style( self, parts: list ) -> None:
        """
        处理来自工作线程的多样式日志消息。

        Args:
                parts: 包含文本和样式的元组列表
        """
        if self.__log_output:
            processed_parts = [ ]
            color_map = {
                "BLUE": Colors.BLUE,
                "GREEN": Colors.GREEN,
                "RED": Colors.RED,
                "ORANGE": Colors.ORANGE,
                "PURPLE": Colors.PURPLE,
                "BLACK": Colors.BLACK
            }
            for item_tuple in parts:
                if not (isinstance( item_tuple, tuple ) and len( item_tuple ) == 2):
                    self.__logger.warning( f"工作线程传递的多样式日志格式无效: {item_tuple}" )
                    continue

                text, style_dict = item_tuple
                if not isinstance( style_dict, dict ):
                    self.__logger.warning( f"工作线程传递的样式字典无效: {style_dict}" )
                    processed_parts.append( (text, { }) )  # 使用默认样式添加
                    continue

                if "color" in style_dict and isinstance( style_dict[ "color" ], str ):
                    style_dict[ "color" ] = color_map.get( style_dict[ "color" ].upper(), Colors.BLACK )
                processed_parts.append( (text, style_dict) )

            self.__log_output.append_multi_style( processed_parts )

    @pyqtSlot( bool, int, int )
    def __handle_save_finished( self, success: bool, saved_count: int, total_count: int ) -> None:
        """
        处理工作线程保存完成的信号。

        Args:
                success: 保存是否成功
                saved_count: 保存的状态数量
                total_count: 配置文件中的总状态数量
        """
        if success:
            self.__logger.info( f"工作线程报告：保存成功，保存了 {saved_count} 个状态，总共 {total_count} 个状态" )
        else:
            self.__logger.error( "工作线程报告：保存失败" )
            if self.__log_output:
                self.__log_output.append( "保存操作失败，请检查日志获取详细信息", color=Colors.RED )


class SelectAllCheckBoxManager( QObject ):
    """
    管理一个或多个容器内 "全选/取消全选" 复选框与其余 "子项" 复选框之间的联动逻辑。

    该类旨在简化UI中常见的全选功能实现。它会自动发现指定容器内的所有复选框，
    并将一个指定的 "全选" 复选框与其它所有 "子项" 复选框进行逻辑绑定。

    核心功能:
    1.  **独立的容器逻辑**: 当处理一个容器列表时，每个容器内的 "全选" 逻辑是完全独立的，
            一个容器内的操作不会影响其他容器。
    2.  **递归查找**: 在每个指定的容器内自动递归查找所有QCheckBox控件。
    3.  **角色分配**: 在每个容器内，根据提供的文本标签（如 "全选/取消全选"），
            将一个复选框识别为 "全选框"，其余的则为 "子项框"。
    4.  **双向逻辑绑定**:
            - 当 "全选框" 被勾选/取消勾选时，其对应的 "子项框" 会被同步勾选/取消勾选。
            - 当任何一个 "子项框" 的状态改变时，会重新评估其同组所有 "子项框" 的状态，
              并相应地更新其对应的 "全选框" 的状态。
    5.  **信号循环预防**: 内置了一个简单的锁机制 (`__is_updating`)，以防止在程序化地更改复选框状态时，
            触发的信号导致无限递归循环。
    6.  **资源清理**: 提供了 `cleanup` 方法以安全地断开所有信号连接，防止在对象销毁后出现问题。

    内部实现调用链:
    1. `__init__`: 接收容器（或容器列表），然后调用 `__setup_all_containers`。
    2. `__setup_all_containers`: 遍历所有容器，并对每个容器调用 `__setup_single_container`。
    3. `__setup_single_container`:
            - 在单个容器内查找所有复选框。
            - 将它们分类为 "全选" 和 "子项"。
            - 如果分类成功，将这组关系存储在 `__container_map` 中。
            - 调用 `__connect_signals_for_group` 为这特定的一组控件设置信号。
    4. `__connect_signals_for_group`:
            - 使用 `functools.partial` 来创建带有上下文的槽函数。
            - 将 "全选" 和 "子项" 复选框的 `stateChanged` 信号连接到这些包装后的槽函数。
    5. 槽函数 (`__on_select_all_state_changed`, `__on_child_state_changed`):
            - 从 `partial` 包装的参数中获取它们需要操作的确切复选框列表，而不是从实例变量中获取。
            - 执行状态更新逻辑。
    """

    def __init__(
        self,
        container: Union[QWidget, QLayout, List[Union[QWidget, QLayout]]],
        select_all_text: str,
        parent: Optional[ QObject ] = None,
        logger: Optional[ Logger ] = None
    ):
        """
        初始化 SelectAllCheckBoxManager。

        Args:
                container (Union[QWidget, QLayout, List[Union[QWidget, QLayout]]]):
                        包含QCheckBox控件的顶层容器。可以是单个QWidget、QLayout，或它们的列表。
                        每个容器的逻辑是独立的。
                select_all_text (str): "全选/取消全选" 复选框的显示文本，用于唯一标识它。
                parent (Optional[QObject]): 父QObject对象，有助于Qt对象树管理。
                logger (Optional[Logger]): 日志记录器实例。如果为None，则使用全局logger。
        """
        super().__init__( parent )

        # 统一处理单个容器和容器列表的情况
        containers = container if isinstance( container, list ) else [ container ]

        for c in containers:
            if not isinstance( c, (QWidget, QLayout) ):
                raise TypeError( "容器参数 'container' 列表中的所有项都必须是 QWidget 或 QLayout 实例。" )

        if not select_all_text:
            raise ValueError( "参数 'select_all_text' 不能为空字符串。" )

        self.__logger: Logger = logger  # type: ignore

        # 数据结构，用于存储每个容器的独立逻辑单元
        self.__container_map: Dict[ Union[ QWidget, QLayout ], Dict[ str, Any ] ] = { }
        self.__is_updating = False  # 全局锁，防止信号循环
        self.__connections: Dict[ QCheckBox, Callable ] = { }

        self.__setup_all_containers( containers, select_all_text )

    def __is_valid_pyqt_object( self, obj: Optional[ QObject ] ) -> bool:
        """
        安全地检查PyQt对象是否有效（未被销毁）。

        Args:
                obj (Optional[QObject]): 要检查的PyQt对象。

        Returns:
                bool: 如果对象存在且未被销毁则返回True，否则返回False。
        """
        try:
            return obj is not None and not sip.isdeleted( obj )
        except Exception:
            return False

    def __setup_all_containers( self, containers: List[ Union[ QWidget, QLayout ] ], select_all_text: str ) -> None:
        """
        遍历所有提供的容器，并为每个容器独立设置 "全选" 逻辑。

        Args:
                containers (List[Union[QWidget, QLayout]]): 要设置的容器列表。
                select_all_text (str): "全选" 复选框的标识文本。
        """
        for container in containers:
            self.__setup_single_container( container, select_all_text )

    def __setup_single_container( self, container: Union[ QWidget, QLayout ], select_all_text: str ) -> None:
        """
        在指定的单个容器中查找、分类并连接复选框。
        """
        self.__logger.debug( f"开始在容器 {container} 中查找复选框..." )

        # 1. 查找当前容器内的所有复选框
        all_checkboxes_in_container = [ ]
        if isinstance( container, QWidget ):
            all_checkboxes_in_container = container.findChildren( QCheckBox )
        elif isinstance( container, QLayout ):
            all_checkboxes_in_container = self.__get_checkboxes_from_layout( container )

        # 2. 将复选框分类为 "全选" 和 "子项"
        select_all_cb: Optional[QCheckBox] = None
        child_cbs: List[QCheckBox] = [ ]
        for checkbox in all_checkboxes_in_container:
            if not self.__is_valid_pyqt_object( checkbox ):
                continue

            if checkbox.text() == select_all_text:
                if select_all_cb is None:
                    select_all_cb = checkbox
                else:
                    self.__logger.warning( f"在容器 {container} 中发现多个文本为 '{select_all_text}' 的复选框。只使用第一个。" )
            else:
                if checkbox not in child_cbs:
                    child_cbs.append( checkbox )

        # 3. 如果找到了有效的组合，则存储并连接信号
        if self.__is_valid_pyqt_object( select_all_cb ) and child_cbs:
            # mypy type narrowing
            assert select_all_cb is not None
            self.__logger.info(
                f"在容器 {container} 中设置成功: 找到 '全选' 复选框 '{select_all_cb.text()}' "
                f"和 {len( child_cbs )} 个 '子项' 复选框。"
            )
            self.__container_map[ container ] = { "select_all": select_all_cb, "children": child_cbs }
            self.__connect_signals_for_group( select_all_cb, child_cbs )
            # 初始化 "全选" 复选框的初始状态
            self.__on_child_state_changed( select_all_cb, child_cbs )
        else:
            # 记录未能成功设置的原因
            if not self.__is_valid_pyqt_object( select_all_cb ):
                self.__logger.warning( f"未能在容器 {container} 中找到文本为 '{select_all_text}' 的 '全选' 复选框。" )
            if not child_cbs:
                self.__logger.warning( f"在容器 {container} 中找到了 '全选' 复选框，但没有找到任何 '子项' 复选框。" )

    def __get_checkboxes_from_layout( self, layout: QLayout ) -> List[ QCheckBox ]:
        """
        从一个QLayout中递归地收集所有的QCheckBox控件。

        Args:
                layout (QLayout): 要搜索的布局。

        Returns:
                List[QCheckBox]: 找到的QCheckBox列表。
        """
        checkboxes = [ ]
        for i in range( layout.count() ):
            item = layout.itemAt( i )
            if not item:
                continue

            widget = item.widget()
            if widget:
                # 如果控件本身是复选框，则添加
                if isinstance( widget, QCheckBox ):
                    if widget not in checkboxes:
                        checkboxes.append( widget )
                # 同样在控件内部搜索其他复选框（例如，QGroupBox内的复选框）
                # findChildren是递归的，会找到所有后代。
                found_in_widget = widget.findChildren( QCheckBox )
                for cb in found_in_widget:
                    if cb not in checkboxes:
                        checkboxes.append( cb )

            sub_layout = item.layout()
            if sub_layout:
                found_in_layout = self.__get_checkboxes_from_layout( sub_layout )
                for cb in found_in_layout:
                    if cb not in checkboxes:
                        checkboxes.append( cb )
        return checkboxes

    def __connect_signals_for_group( self, select_all_cb: QCheckBox, child_cbs: List[ QCheckBox ] ) -> None:
        """
        为特定的一组 "全选" 和 "子项" 复选框连接信号。
        使用 functools.partial 将上下文（即它们关联的复选框列表）传递给槽函数。

        Args:
                select_all_cb (QCheckBox): "全选" 复选框。
                child_cbs (List[QCheckBox]): 与之关联的 "子项" 复选框列表。
        """
        # 连接 "全选" 复选框
        handler_all = partial( self.__on_select_all_state_changed, child_cbs )
        select_all_cb.stateChanged.connect( handler_all )
        self.__connections[ select_all_cb ] = handler_all
        self.__logger.debug( f"已连接 '全选' 复选框 '{select_all_cb.text()}' 的信号。" )

        # 连接 "子项" 复选框
        for checkbox in child_cbs:
            handler_child = partial( self.__on_child_state_changed, select_all_cb, child_cbs )
            checkbox.stateChanged.connect( handler_child )
            self.__connections[ checkbox ] = handler_child
        self.__logger.debug( f"已为一组 {len( child_cbs )} 个 '子项' 复选框连接信号。" )

    def __on_select_all_state_changed( self, child_checkboxes: List[ QCheckBox ], state: int ) -> None:
        """
        当 "全选" 复选框状态改变时调用的槽函数。

        此函数会同步其关联的 "子项" 复选框的状态。
        它是一个通用处理器，通过 partial 从信号接收其需要操作的特定子项列表。

        Args:
                child_checkboxes (List[QCheckBox]): 需要更新状态的 "子项" 复选框列表。
                state (int): 复选框的新状态 (Qt.Checked, Qt.Unchecked)。
        """
        if self.__is_updating:
            return

        self.__is_updating = True
        try:
            is_checked = (state == Qt.Checked)
            self.__logger.debug(
                f"一个 '全选' 复选框状态改变。正在更新 {len( child_checkboxes )} 个子项..."
            )
            for checkbox in child_checkboxes:
                if self.__is_valid_pyqt_object( checkbox ):
                    checkbox.setChecked( is_checked )
        finally:
            self.__is_updating = False

    def __on_child_state_changed(
        self, select_all_checkbox: QCheckBox, sibling_checkboxes: List[ QCheckBox ], state: int = -1
    ) -> None:
        """
        当任何一个 "子项" 复选框状态改变时调用的槽函数。

        此函数会检查其关联的所有 "兄弟" 复选框的当前状态，并更新其对应的 "全选" 复选框。
        它是一个通用处理器，通过 partial 从信号接收其上下文。

        Args:
                select_all_checkbox (QCheckBox): 需要更新的 "全选" 复选框。
                sibling_checkboxes (List[QCheckBox]): 用于判断状态的所有 "兄弟" 复选框列表。
                state (int): 触发信号的复选框的新状态。此处未使用，但槽函数签名需要。
                                         默认值为-1，以便在初始化时可以直接调用。
        """
        if self.__is_updating:
            return

        if not self.__is_valid_pyqt_object( select_all_checkbox ):
            return

        self.__is_updating = True
        try:
            # 检查所有兄弟项是否都有效且被选中
            all_checked = all(
                self.__is_valid_pyqt_object( cb ) and cb.isChecked() for cb in sibling_checkboxes
            )
            self.__logger.debug( f"一个子项状态改变。其组内所有子项是否都已选中: {all_checked}。" )
            select_all_checkbox.setChecked( all_checked )
        finally:
            self.__is_updating = False

    def cleanup( self ) -> None:
        """
        清理资源，断开所有已连接的信号。
        在父窗口或容器关闭时调用此方法，以防止悬挂连接和潜在的错误。
        """
        self.__logger.debug( "正在清理 SelectAllCheckBoxManager 资源..." )
        disconnected_count = 0
        for checkbox, handler in self.__connections.items():
            try:
                if self.__is_valid_pyqt_object( checkbox ):
                    checkbox.stateChanged.disconnect( handler )
                    disconnected_count += 1
            except TypeError:
                # 当连接可能已经不存在时，disconnect 会引发 TypeError
                pass
            except Exception as e:
                self.__logger.warning( f"断开信号时出现未知错误: {e}" )

        self.__connections.clear()
        self.__container_map.clear()
        self.__logger.info( f"清理完成。已断开 {disconnected_count} 个信号连接。" )
