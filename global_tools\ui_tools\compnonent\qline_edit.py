import traceback
import threading
from typing import Callable, Dict, List, Optional, Union, Tuple, Any, TYPE_CHECKING
from PyQt5.QtWidgets import (
    QLineEdit,
)
from PyQt5.QtCore import (
    QRegExp,
    QThread,
    pyqtSignal,
    QObject,
    Qt,
    QTimer,
    QEvent,
    QTime,
)
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5 import sip
from PyQt5.QtWidgets import *
import re
import logging
from functools import partial
from global_tools.utils import ClassInstanceManager,Logger

# 为了类型提示导入LineEditControl类型
if TYPE_CHECKING:
    from global_tools.ui_tools.helper import LineEditControl


class LineEditManager(QObject):
    """
    Qt文本输入框(QLineEdit)的批量管理类（单例模式）

    该类扩展了LineEditControl的功能，可以管理多个QLineEdit控件：
    1. 接收一组QLineEdit控件，使用objectName作为key进行管理
    2. 提供统一的接口操作所有QLineEdit，包括：
       - 输入验证（整数、浮点数、长度、正则表达式等）
       - 样式管理（正常样式、错误样式、禁用样式）
       - 事件处理（焦点获取/失去、文本变化、回车按下）
       - 回调函数支持（文本变化回调、失去焦点回调）
       - 延迟过滤功能（防止频繁触发回调）
    3. 每个方法都接受一个额外的name参数，用于指定要操作的特定QLineEdit
    4. 信号发射时会附带QLineEdit的名称，方便识别信号来源

    ===== 单例模式说明 =====
    该类实现了线程安全的单例模式：
    - 首次创建时需要传入QLineEdit控件参数
    - 后续调用可以不传参数，直接返回已创建的实例
    - 支持重置单例实例，重新创建新的管理器
    - 在多线程环境下保证线程安全

    ===== 信号定义 =====
    以下所有信号都增加了name参数，表示产生信号的QLineEdit控件名称：
    - text_changed(name, text): 当文本变化时触发
    - validation_changed(name, valid): 当验证状态变化时触发
    - focus_in(name): 当获取焦点时触发
    - focus_out(name, text): 当失去焦点时触发
    - return_pressed(name, text): 当按下回车键时触发
    - enabled_changed(name, enabled): 当启用状态变化时触发
    - style_changed(name, style): 当样式变化时触发
    - read_only_changed(name, read_only): 当只读状态变化时触发
    - echo_mode_changed(name, mode): 当回显模式变化时触发

    ===== 使用场景示例 =====

    1. 创建和初始化（单例模式）:
       ```python
       # 创建多个QLineEdit
       username_edit = QLineEdit()
       username_edit.setObjectName("username")

       password_edit = QLineEdit()
       password_edit.setObjectName("password")

       email_edit = QLineEdit()
       email_edit.setObjectName("email")

       # 方式1：直接创建管理器（首次创建）
       manager = LineEditManager(username_edit, password_edit, email_edit)

       # 方式2：使用推荐的类方法创建（首次创建）
       manager = LineEditManager.get_instance(username_edit, password_edit, email_edit)

       # 方式3：后续获取已创建的实例（无需参数）
       manager = LineEditManager.get_instance()
       # 或者
       manager = LineEditManager()

       # 也可以通过列表添加
       # edits = [username_edit, password_edit, email_edit]
       # manager = LineEditManager.get_instance(*edits)
       ```

    2. 设置验证器:
       ```python
       # 为用户名设置长度验证（4-20个字符）
       manager.set_input_validator("username", 'length', min_length=4, max_length=20)

       # 为密码设置正则表达式验证（至少包含一个字母和一个数字）
       manager.set_input_validator("password", 'regexp', pattern='^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{6,}$')

       # 为邮箱设置邮箱格式验证
       manager.set_input_validator("email", 'email')
       ```

    3. 设置样式:
       ```python
       # 为所有输入框设置统一的自定义样式
       for name in manager.get_all_names():
           manager.set_custom_style(name,
               normal="border: 1px solid blue; padding: 5px;",
               focus="border: 2px solid darkblue; padding: 5px;",
               error="border: 2px solid red; background: #ffe0e0; padding: 5px;"
           )

       # 或者为单个输入框设置样式
       manager.set_custom_style("password",
           normal="border: 1px solid gray; border-radius: 4px; padding: 5px;",
           focus="border: 2px solid blue; border-radius: 4px; padding: 5px;"
       )
       ```

    4. 设置回调函数:
       ```python
       # 设置文本变化回调
       def on_username_changed(text):
           print(f"用户名已更改为: {text}")

       manager.set_callback("username", on_username_changed)

       # 设置密码框的焦点失去回调
       def on_password_complete(text):
           print(f"密码输入完成，长度为: {len(text)}")

       manager.set_focus_out_callback("password", on_password_complete)
       ```

    5. 使用信号槽:
       ```python
       # 连接信号
       def on_text_changed(name, text):
           print(f"输入框 '{name}' 的文本变为: {text}")

       manager.text_changed.connect(on_text_changed)

       def on_validation_changed(name, valid):
           status = "通过" if valid else "不通过"
           print(f"输入框 '{name}' 的验证状态: {status}")

       manager.validation_changed.connect(on_validation_changed)

       # 连接回车按下信号
       def on_return_pressed(name, text):
           if name == "password":
               print("用户按下了回车键提交密码")

       manager.return_pressed.connect(on_return_pressed)
       ```

    6. 获取和设置文本:
       ```python
       # 获取用户名
       username = manager.get_text("username")

       # 设置邮箱默认值
       manager.set_text("email", "<EMAIL>")

       # 清空密码
       manager.clear("password")

       # 设置占位文本
       manager.set_placeholder("username", "请输入用户名")
       manager.set_placeholder("password", "请输入密码")
       manager.set_placeholder("email", "请输入电子邮箱")
       ```

    7. 批量操作:
       ```python
       # 禁用所有输入框
       for name in manager.get_all_names():
           manager.set_enabled(name, False)

       # 启用特定输入框
       manager.set_enabled("username", True)

       # 设置密码框为密码模式
       manager.set_echo_mode("password", QLineEdit.Password)
       ```

    8. 检查特定控件是否在管理器中:
       ```python
       if manager.has_line_edit("username"):
           print("用户名输入框在管理器中")

       if not manager.has_line_edit("address"):
           print("地址输入框不在管理器中")
       ```

    9. 获取原始QLineEdit对象进行高级操作:
       ```python
       # 获取原始QLineEdit对象
       email_edit = manager.get_line_edit("email")
       if email_edit:
           # 直接使用QLineEdit的原生方法
           email_edit.setCursorPosition(0)
       ```

    10. 单例模式特殊用法:
        ```python
        # 检查实例是否已创建
        if LineEditManager.is_instance_created():
            manager = LineEditManager.get_instance()
        else:
            # 首次创建
            manager = LineEditManager.get_instance(edit1, edit2)

        # 获取缓存的参数
        args, kwargs = LineEditManager.get_cached_args()
        print(f"缓存的参数: {len(args)} 个控件")

        # 重置单例实例（在需要重新初始化时）
        LineEditManager.reset_instance()
        new_manager = LineEditManager.get_instance(new_edit1, new_edit2)

        # 在不同模块中获取同一个实例
        # module_a.py
        manager_a = LineEditManager.get_instance()

        # module_b.py
        manager_b = LineEditManager.get_instance()
        # manager_a 和 manager_b 是同一个实例
        assert manager_a is manager_b
        ```
    """

    # =================================================================================
    # 单例模式相关类变量
    # =================================================================================
    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _initialized = False  # 初始化标志
    _cached_args = None  # 缓存的构造参数
    _cached_kwargs = None  # 缓存的关键字参数

    # 定义信号，比LineEditControl多一个name参数
    text_changed = pyqtSignal(str, str)  # name, text
    validation_changed = pyqtSignal(str, bool)  # name, valid
    focus_in = pyqtSignal(str)  # name
    focus_out = pyqtSignal(str, str)  # name, text
    return_pressed = pyqtSignal(str, str)  # name, text
    enabled_changed = pyqtSignal(str, bool)  # name, enabled
    style_changed = pyqtSignal(str, str)  # name, style
    read_only_changed = pyqtSignal(str, bool)  # name, read_only
    echo_mode_changed = pyqtSignal(str, int)  # name, mode

    # =================================================================================
    # Internal Signals for Thread-Safe UI Updates
    # ---------------------------------------------------------------------------------
    # 以下信号用于在内部实现线程安全的UI更新。
    # 公共方法（如 set_text）会发射这些信号，而不是直接修改QLineEdit控件。
    # 信号会被连接到相应的私有槽函数（如 __on_set_text），
    # Qt的事件循环机制会确保这些槽函数总是在主GUI线程中执行，从而避免跨线程操作UI导致的崩溃。
    # =================================================================================
    __set_input_validator_signal = pyqtSignal(str, str, dict)
    __set_input_mask_signal = pyqtSignal(str, str)
    __set_max_length_signal = pyqtSignal(str, int)
    __set_text_signal = pyqtSignal(str, str)
    __clear_signal = pyqtSignal(str)
    __set_placeholder_signal = pyqtSignal(str, str)
    __set_enabled_signal = pyqtSignal(str, bool)
    __set_auto_clear_signal = pyqtSignal(str, bool)
    __set_custom_style_signal = pyqtSignal(str, object)
    __set_delay_time_signal = pyqtSignal(str, int)
    __set_read_only_signal = pyqtSignal(str, bool)
    __set_echo_mode_signal = pyqtSignal(str, QLineEdit.EchoMode)

    def __new__(cls, *line_edits: Union[QLineEdit, List[QLineEdit]], parent: Optional[QObject] = None):
        """
        单例模式的 __new__ 方法

        实现线程安全的单例模式：
        - 如果实例不存在，创建新实例
        - 如果实例已存在且有效，直接返回现有实例
        - 如果实例已存在但无效（被删除），重新创建实例

        Args:
            *line_edits: QLineEdit控件参数（首次创建时需要）
            parent: 父对象（可选）

        Returns:
            LineEditManager: 单例实例
        """
        # 双重检查锁定模式，确保线程安全
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建新实例
                    cls._instance = super(LineEditManager, cls).__new__(cls)
                    cls._initialized = False
                    # 缓存参数供后续使用
                    cls._cached_args = line_edits
                    cls._cached_kwargs = {'parent': parent}
        else:
            # 检查现有实例是否仍然有效
            try:
                if sip.isdeleted(cls._instance):
                    # 实例已被删除，重新创建
                    with cls._lock:
                        if sip.isdeleted(cls._instance):
                            cls._instance = super(LineEditManager, cls).__new__(cls)
                            cls._initialized = False
                            # 如果没有传入新参数，使用缓存的参数
                            if not line_edits and cls._cached_args:
                                cls._cached_args = cls._cached_args
                                cls._cached_kwargs = cls._cached_kwargs
                            else:
                                cls._cached_args = line_edits
                                cls._cached_kwargs = {'parent': parent}
            except RuntimeError:
                # 处理可能的运行时错误
                with cls._lock:
                    cls._instance = super(LineEditManager, cls).__new__(cls)
                    cls._initialized = False
                    cls._cached_args = line_edits if line_edits else cls._cached_args
                    cls._cached_kwargs = {'parent': parent}

        return cls._instance

    def __init__(self, *line_edits: Union[QLineEdit, List[QLineEdit]], parent: Optional[QObject] = None):
        """
        初始化LineEditManager（单例模式）

        Args:
            *line_edits: 一个或多个QLineEdit实例，或包含QLineEdit实例的列表
                        首次创建时必须提供，后续调用可以为空（使用缓存的参数）
            parent: 父QObject对象，默认为None

        注意：
            - 如果实例已经初始化过，此方法会直接返回，不会重复初始化
            - 如果没有传入参数且有缓存参数，会使用缓存的参数进行初始化
            - 可以通过 reset_instance() 方法重置单例实例
        """
        # 检查是否已经初始化过
        if self.__class__._initialized:
            return

        # 如果没有传入参数，尝试使用缓存的参数
        if not line_edits and self.__class__._cached_args:
            line_edits = self.__class__._cached_args
            if self.__class__._cached_kwargs and 'parent' in self.__class__._cached_kwargs:
                parent = self.__class__._cached_kwargs['parent']

        # 如果仍然没有参数，记录警告但继续初始化
        if not line_edits:
            # 延迟获取logger，避免循环依赖
            try:
                logger = ClassInstanceManager.get_instance(key="ui_logger")
                if logger:
                    logger.warning("LineEditManager 初始化时未提供 QLineEdit 控件，将创建空的管理器")
            except:
                pass  # 如果获取logger失败，忽略警告

        super().__init__(parent)

        # -------------------------------------------------
        # 连接内部信号到槽函数，以实现线程安全的UI更新
        # -------------------------------------------------
        self.__set_input_validator_signal.connect(self.__on_set_input_validator)
        self.__set_input_mask_signal.connect(self.__on_set_input_mask)
        self.__set_max_length_signal.connect(self.__on_set_max_length)
        self.__set_text_signal.connect(self.__on_set_text)
        self.__clear_signal.connect(self.__on_clear)
        self.__set_placeholder_signal.connect(self.__on_set_placeholder)
        self.__set_enabled_signal.connect(self.__on_set_enabled)
        self.__set_auto_clear_signal.connect(self.__on_set_auto_clear)
        self.__set_custom_style_signal.connect(self.__on_set_custom_style)
        self.__set_delay_time_signal.connect(self.__on_set_delay_time)
        self.__set_read_only_signal.connect(self.__on_set_read_only)
        self.__set_echo_mode_signal.connect(self.__on_set_echo_mode)

        # 内部存储和记录
        self.__line_edit_controls: Dict[str, Any] = {}  # 存储每个QLineEdit对应的控制器
        # self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__logger:Logger = ClassInstanceManager.get_instance(key="ui_logger") # type: ignore
        self.__logger.debug("初始化 LineEditManager...")

        # 处理传入的QLineEdit控件
        for item in line_edits:
            if isinstance(item, QLineEdit):
                self.__add_line_edit(item)
            elif isinstance(item, list):
                for line_edit in item:
                    if isinstance(line_edit, QLineEdit):
                        self.__add_line_edit(line_edit)
                    else:
                        self.__logger.warning(f"列表中的项目不是QLineEdit类型: {type(line_edit)}")
            else:
                self.__logger.warning(f"传入的参数不是QLineEdit或QLineEdit列表: {type(item)}")

        self.__logger.debug(f"LineEditManager初始化完成，共管理{len(self.__line_edit_controls)}个输入框")

        # 标记为已初始化
        self.__class__._initialized = True

    def __add_line_edit(self, line_edit: QLineEdit) -> None:
        """
        添加一个QLineEdit到管理器中

        Args:
            line_edit: 要添加的QLineEdit实例
        """
        # 检查QLineEdit是否已被删除
        if sip.isdeleted(line_edit):
            self.__logger.warning(f"尝试添加一个已被删除的QLineEdit，已跳过")
            return

        # 获取QLineEdit的objectName
        object_name = line_edit.objectName()

        # 确保QLineEdit有objectName
        if not object_name:
            self.__logger.error(f"尝试添加一个未设置objectName的QLineEdit，已跳过。请为所有QLineEdit设置唯一的objectName")
            return

        # 检查是否已有同名的QLineEdit
        if object_name in self.__line_edit_controls:
            self.__logger.warning(f"已存在名为'{object_name}'的QLineEdit，将被覆盖")

        try:
            # 创建LineEditControl实例并保存
            from global_tools.ui_tools.helper import LineEditControl
            control = LineEditControl(line_edit)

            # 连接信号
            self.__connect_control_signals(object_name, control)

            # 保存到字典中
            self.__line_edit_controls[object_name] = control
            self.__logger.debug(f"成功添加QLineEdit '{object_name}'到管理器")
        except Exception as e:
            self.__logger.error(f"添加QLineEdit '{object_name}'时发生错误: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __connect_control_signals(self, name: str, control: Any) -> None:
        """
        连接LineEditControl的信号到LineEditManager的信号

        Args:
            name: QLineEdit的名称
            control: 对应的LineEditControl实例
        """
        # 连接所有信号，添加name参数
        control.text_changed.connect(lambda text: self.text_changed.emit(name, text))
        control.validation_changed.connect(lambda valid: self.validation_changed.emit(name, valid))
        control.focus_in.connect(lambda: self.focus_in.emit(name))
        control.focus_out.connect(lambda text: self.focus_out.emit(name, text))
        control.return_pressed.connect(lambda text: self.return_pressed.emit(name, text))
        control.enabled_changed.connect(lambda enabled: self.enabled_changed.emit(name, enabled))
        control.style_changed.connect(lambda style: self.style_changed.emit(name, style))
        control.read_only_changed.connect(lambda read_only: self.read_only_changed.emit(name, read_only))
        control.echo_mode_changed.connect(lambda mode: self.echo_mode_changed.emit(name, mode))

    def get_line_edit(self, name: str) -> Optional[QLineEdit]:
        """
        获取指定名称的QLineEdit实例

        Args:
            name: QLineEdit的名称（objectName）

        Returns:
            QLineEdit实例，如果未找到则返回None
        """
        control = self.__get_control(name)
        if control:
            return control.line_edit
        return None

    def __get_control(self, name: str) -> Optional[Any]:
        """
        获取指定名称的LineEditControl实例

        Args:
            name: QLineEdit的名称（objectName）

        Returns:
            LineEditControl实例，如果未找到则返回None
        """
        if name not in self.__line_edit_controls:
            self.__logger.warning(f"未找到名为'{name}'的QLineEdit")
            return None

        control = self.__line_edit_controls[name]

        # 检查控件是否已被删除
        if sip.isdeleted(control.line_edit):
            self.__logger.warning(f"名为'{name}'的QLineEdit已被删除，将从管理器中移除")
            del self.__line_edit_controls[name]
            return None

        return control

    def has_line_edit(self, name: str) -> bool:
        """
        检查是否存在指定名称的QLineEdit

        Args:
            name: QLineEdit的名称（objectName）

        Returns:
            如果管理器中存在该QLineEdit且未被删除，则返回True；否则返回False
        """
        return self.__get_control(name) is not None

    def get_all_names(self) -> List[str]:
        """
        获取所有被管理的QLineEdit的名称列表

        Returns:
            所有QLineEdit的名称列表
        """
        # 检查并清理已删除的控件
        names_to_remove = []
        for name, control in self.__line_edit_controls.items():
            if sip.isdeleted(control.line_edit):
                names_to_remove.append(name)

        for name in names_to_remove:
            self.__logger.debug(f"移除已删除的QLineEdit: {name}")
            del self.__line_edit_controls[name]

        return list(self.__line_edit_controls.keys())

    # ======= 以下方法转发到对应的LineEditControl实例 =======

    def set_input_validator(self, name: str, validator_type: str, **kwargs) -> bool:
        """
        通过信号槽机制设置输入验证器，确保线程安全。

        Args:
            name: QLineEdit的名称
            validator_type: 验证器类型，支持'int', 'float', 'length', 'regexp', 'email', 'ipaddress'等
            **kwargs: 验证器的其他参数，取决于validator_type

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_input_validator_signal.emit(name, validator_type, kwargs)
        return True

    def set_input_mask(self, name: str, mask: str) -> bool:
        """
        通过信号槽机制设置输入掩码，确保线程安全。

        Args:
            name: QLineEdit的名称
            mask: 输入掩码字符串

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_input_mask_signal.emit(name, mask)
        return True

    def set_max_length(self, name: str, length: int) -> bool:
        """
        通过信号槽机制设置最大输入长度，确保线程安全。

        Args:
            name: QLineEdit的名称
            length: 最大长度值

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_max_length_signal.emit(name, length)
        return True

    def set_callback(self, name: str, callback: Callable[[str], None]) -> bool:
        """
        设置文本变化回调函数

        Args:
            name: QLineEdit的名称
            callback: 回调函数，接收文本作为参数

        Returns:
            设置是否成功
        """
        control = self.__get_control(name)
        if not control:
            return False

        try:
            control.set_callback(callback)
            return True
        except Exception as e:
            self.__logger.error(f"为'{name}'设置回调函数时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())
            return False

    def set_focus_out_callback(self, name: str, callback: Callable[[str], None]) -> bool:
        """
        设置失去焦点回调函数

        Args:
            name: QLineEdit的名称
            callback: 回调函数，接收文本作为参数

        Returns:
            设置是否成功
        """
        control = self.__get_control(name)
        if not control:
            return False

        try:
            control.set_focus_out_callback(callback)
            return True
        except Exception as e:
            self.__logger.error(f"为'{name}'设置失去焦点回调时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())
            return False

    def get_text(self, name: str) -> Optional[str]:
        """
        获取输入框的文本

        Args:
            name: QLineEdit的名称

        Returns:
            输入框的文本，如果未找到输入框则返回None
        """
        control = self.__get_control(name)
        if not control:
            return None

        try:
            return control.get_text()
        except Exception as e:
            self.__logger.error(f"获取'{name}'的文本时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())
            return None

    def set_text(self, name: str, text: str) -> bool:
        """
        通过信号槽机制设置输入框的文本，确保线程安全。

        Args:
            name: QLineEdit的名称
            text: 要设置的文本

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_text_signal.emit(name, text)
        return True

    def clear(self, name: str) -> bool:
        """
        通过信号槽机制清空输入框，确保线程安全。

        Args:
            name: QLineEdit的名称

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__clear_signal.emit(name)
        return True

    def clear_all(self) -> None:
        """
        清空所有输入框
        """
        for name in self.get_all_names():
            self.clear(name)

    def set_placeholder(self, name: str, text: str) -> bool:
        """
        通过信号槽机制设置占位文本，确保线程安全。

        Args:
            name: QLineEdit的名称
            text: 占位文本

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_placeholder_signal.emit(name, text)
        return True

    def set_enabled(self, name: str, enabled: bool) -> bool:
        """
        通过信号槽机制设置输入框是否启用，确保线程安全。

        Args:
            name: QLineEdit的名称
            enabled: True表示启用，False表示禁用

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_enabled_signal.emit(name, enabled)
        return True

    def enable(self, name: str) -> bool:
        """
        启用输入框

        Args:
            name: QLineEdit的名称

        Returns:
            操作是否成功
        """
        return self.set_enabled(name, True)

    def disable(self, name: str) -> bool:
        """
        禁用输入框

        Args:
            name: QLineEdit的名称

        Returns:
            操作是否成功
        """
        return self.set_enabled(name, False)

    def set_auto_clear(self, name: str, enabled: bool) -> bool:
        """
        设置是否在获取焦点时自动清空。此操作是线程安全的。

        Args:
            name: QLineEdit的名称
            enabled: True表示启用自动清空，False表示禁用

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_auto_clear_signal.emit(name, enabled)
        return True

    def set_all_auto_clear(self, enabled: bool) -> Dict[str, bool]:
        """
        批量设置所有QLineEdit控件是否在获取焦点时自动清空内容

        此方法会遍历所有被管理的QLineEdit控件，并为每个控件设置相同的自动清空功能状态。
        可用于快速禁用所有输入框的自动清空功能，或者统一启用此功能。

        Args:
            enabled: True表示启用自动清空，False表示禁用

        Returns:
            包含每个控件名称和设置结果的字典，True表示设置成功，False表示设置失败

        示例:
        ```python
        # 禁用所有输入框的自动清空功能
        results = manager.set_all_auto_clear(False)
        
        # 查看设置结果
        for name, success in results.items():
            if not success:
                print(f"设置控件 '{name}' 失败")
        
        # 检查是否有任何控件设置失败
        if not all(results.values()):
            print("部分控件设置失败")
        ```
        """
        self.__logger.debug(f"开始为所有QLineEdit设置自动清空功能: enabled={enabled}")
        results = {}
        
        # 获取所有控件名称
        names = self.get_all_names()
        self.__logger.debug(f"找到 {len(names)} 个QLineEdit控件")
        
        # 遍历设置每个控件
        for name in names:
            try:
                success = self.set_auto_clear(name, enabled)
                results[name] = success
                if success:
                    self.__logger.debug(f"成功设置'{name}'的自动清空功能: {enabled}")
                else:
                    self.__logger.warning(f"设置'{name}'的自动清空功能失败")
            except Exception as e:
                self.__logger.error(f"设置'{name}'的自动清空功能时发生异常: {str(e)}")
                self.__logger.debug(traceback.format_exc())
                results[name] = False
        
        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        self.__logger.debug(f"自动清空功能设置完成: 成功 {success_count}/{len(names)}")
        
        return results

    def set_custom_style(self, name: str, normal=None, focus=None, error=None, disabled=None) -> bool:
        """
        通过信号槽机制设置自定义样式，确保线程安全。

        Args:
            name: QLineEdit的名称
            normal: 正常状态的样式
            focus: 获取焦点时的样式
            error: 输入错误时的样式
            disabled: 禁用状态的样式

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 将样式打包并通过信号发送，以确保线程安全
        styles = {'normal': normal, 'focus': focus, 'error': error, 'disabled': disabled}
        self.__set_custom_style_signal.emit(name, styles)
        return True

    def set_delay_time(self, name: str, milliseconds: int) -> bool:
        """
        设置延迟触发时间。此操作是线程安全的。

        Args:
            name: QLineEdit的名称
            milliseconds: 延迟毫秒数

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_delay_time_signal.emit(name, milliseconds)
        return True

    def set_read_only(self, name: str, read_only: bool) -> bool:
        """
        通过信号槽机制设置输入框是否只读，确保线程安全。

        Args:
            name: QLineEdit的名称
            read_only: True表示只读，False表示可编辑

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_read_only_signal.emit(name, read_only)
        return True

    def set_echo_mode(self, name: str, mode: QLineEdit.EchoMode) -> bool:
        """
        通过信号槽机制设置输入框的回显模式，确保线程安全。

        Args:
            name: QLineEdit的名称
            mode: 回显模式，如QLineEdit.Normal, QLineEdit.Password等

        Returns:
            请求发送成功返回 True，控件不存在则返回 False
        """
        if not self.has_line_edit(name):
            return False
        # 通过发射信号，将UI更新操作委托给GUI线程执行
        self.__set_echo_mode_signal.emit(name, mode)
        return True

    def cleanup(self) -> None:
        """
        清理资源，断开信号连接
        """
        self.__logger.debug("开始清理LineEditManager资源...")
        # 保存一份名称列表，防止在迭代过程中字典发生变化
        names = list(self.__line_edit_controls.keys())

        for name in names:
            try:
                # 空实现，因为LineEditControl没有提供cleanup方法
                # 如果后续添加，这里可以调用
                pass
            except Exception as e:
                self.__logger.error(f"清理'{name}'时出错: {str(e)}")

        # 清空字典
        self.__line_edit_controls.clear()
        self.__logger.debug("LineEditManager资源清理完成")

    # =================================================================================
    # 单例模式相关类方法
    # =================================================================================

    @classmethod
    def get_instance(cls, *line_edits: Union[QLineEdit, List[QLineEdit]], parent: Optional[QObject] = None) -> 'LineEditManager':
        """
        获取LineEditManager的单例实例

        这是推荐的获取实例的方法，相比直接调用构造函数更加明确。

        Args:
            *line_edits: QLineEdit控件（首次创建时需要）
            parent: 父对象（可选）

        Returns:
            LineEditManager: 单例实例

        使用示例:
            ```python
            # 首次创建，需要传入控件
            edit1 = QLineEdit()
            edit1.setObjectName("edit1")
            manager = LineEditManager.get_instance(edit1)

            # 后续获取，可以不传参数
            manager = LineEditManager.get_instance()
            ```
        """
        return cls(*line_edits, parent=parent)

    @classmethod
    def reset_instance(cls) -> None:
        """
        重置单例实例

        清理当前实例并重置单例状态，下次调用时会创建新的实例。
        这在需要重新初始化管理器或在测试中很有用。

        使用示例:
            ```python
            # 重置单例实例
            LineEditManager.reset_instance()

            # 下次调用会创建新的实例
            new_manager = LineEditManager.get_instance(new_edit1, new_edit2)
            ```
        """
        with cls._lock:
            if cls._instance is not None:
                try:
                    # 尝试清理现有实例
                    if not sip.isdeleted(cls._instance):
                        cls._instance.cleanup()
                except Exception as e:
                    # 如果清理失败，记录错误但继续重置
                    try:
                        logger = ClassInstanceManager.get_instance(key="ui_logger")
                        if logger:
                            logger.warning(f"重置LineEditManager实例时清理失败: {str(e)}")
                    except:
                        pass

            # 重置所有单例相关的类变量
            cls._instance = None
            cls._initialized = False
            cls._cached_args = None
            cls._cached_kwargs = None

    @classmethod
    def is_instance_created(cls) -> bool:
        """
        检查单例实例是否已创建且有效

        Returns:
            bool: 如果实例已创建且有效返回True，否则返回False

        使用示例:
            ```python
            if LineEditManager.is_instance_created():
                manager = LineEditManager.get_instance()
            else:
                manager = LineEditManager.get_instance(edit1, edit2)
            ```
        """
        if cls._instance is None:
            return False

        try:
            return not sip.isdeleted(cls._instance) and cls._initialized
        except RuntimeError:
            return False

    @classmethod
    def get_cached_args(cls) -> Tuple[Tuple, Dict]:
        """
        获取缓存的构造参数

        Returns:
            Tuple[Tuple, Dict]: 缓存的位置参数和关键字参数

        使用示例:
            ```python
            args, kwargs = LineEditManager.get_cached_args()
            print(f"缓存的参数: {args}, {kwargs}")
            ```
        """
        return (cls._cached_args or (), cls._cached_kwargs or {})

    # =================================================================================
    # Private Slots for Thread-Safe UI Updates
    # =================================================================================

    def __on_set_input_validator(self, name: str, validator_type: str, kwargs: dict) -> None:
        """
        (私有槽) 在GUI线程中安全地设置输入验证器。
        此方法由 __set_input_validator_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_input_validator(validator_type, **kwargs)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置验证器时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_input_mask(self, name: str, mask: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置输入掩码。
        此方法由 __set_input_mask_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_input_mask(mask)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置输入掩码时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_max_length(self, name: str, length: int) -> None:
        """
        (私有槽) 在GUI线程中安全地设置最大长度。
        此方法由 __set_max_length_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_max_length(length)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置最大长度时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_text(self, name: str, text: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置文本。
        此方法由 __set_text_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_text(text)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置文本时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_clear(self, name: str) -> None:
        """
        (私有槽) 在GUI线程中安全地清空文本。
        此方法由 __clear_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.clear()
        except Exception as e:
            self.__logger.error(f"在槽函数中清空'{name}'时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_placeholder(self, name: str, text: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置占位文本。
        此方法由 __set_placeholder_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_placeholder(text)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置占位文本时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_enabled(self, name: str, enabled: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置启用状态。
        此方法由 __set_enabled_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_enabled(enabled)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置'{name}'的启用状态时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_auto_clear(self, name: str, enabled: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置自动清空功能。
        此方法由 __set_auto_clear_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_auto_clear(enabled)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置'{name}'的自动清空功能时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_custom_style(self, name: str, styles: dict) -> None:
        """
        (私有槽) 在GUI线程中安全地设置自定义样式。
        此方法由 __set_custom_style_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_custom_style(**styles)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置自定义样式时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_delay_time(self, name: str, milliseconds: int) -> None:
        """
        (私有槽) 在GUI线程中安全地设置延迟时间。
        此方法由 __set_delay_time_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_delay_time(milliseconds)
        except Exception as e:
            self.__logger.error(f"在槽函数中为'{name}'设置延迟时间时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_read_only(self, name: str, read_only: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置只读状态。
        此方法由 __set_read_only_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_read_only(read_only)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置'{name}'的只读状态时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_echo_mode(self, name: str, mode: QLineEdit.EchoMode) -> None:
        """
        (私有槽) 在GUI线程中安全地设置回显模式。
        此方法由 __set_echo_mode_signal 信号触发。
        """
        control = self.__get_control(name)
        if not control:
            return
        try:
            # 执行实际的UI更新
            control.set_echo_mode(mode)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置'{name}'的回显模式时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())


"""
LineEditManager 类实现总结（单例模式版本）：

1. 设计思路：
   - 采用组合模式，内部使用 LineEditControl 实例管理每个 QLineEdit 控件
   - 使用字典存储，key 是 QLineEdit 的 objectName，value 是对应的 LineEditControl 实例
   - 所有公共方法接受 name 参数，用于指定操作的特定控件
   - 信号转发设计：为每个 LineEditControl 的信号添加转发，额外传递控件名称
   - 实现线程安全的单例模式，确保全局唯一实例

2. 核心功能：
   - 批量管理多个 QLineEdit 控件，支持 LineEditControl 的所有原有功能
   - 提供统一的接口操作所有输入框，包括验证、样式管理、事件处理等
   - 健壮性处理：检测控件是否已删除，自动清理无效控件引用
   - 异常处理：每个方法都有完整的异常捕获与日志记录机制
   - 单例管理：全局唯一实例，支持跨模块访问

3. 单例模式特性：
   - 线程安全：使用双重检查锁定模式确保多线程环境下的安全性
   - 参数缓存：首次创建时缓存参数，后续调用可以无参数获取实例
   - 实例验证：自动检测实例有效性，支持实例重建
   - 灵活重置：提供 reset_instance() 方法支持重新初始化
   - 状态查询：提供 is_instance_created() 方法检查实例状态

4. 主要改进：
   - 相比原始 LineEditControl 类，增加了批量管理多个控件的能力
   - 所有信号增加了 name 参数，方便识别信号来源
   - 增加了更多便捷方法，如 clear_all()、has_line_edit() 等
   - 完善的错误处理和日志记录，提高了代码的健壮性
   - 新增单例模式，支持全局统一管理和跨模块访问

5. 使用场景：
   - 表单管理：同时管理多个表单输入字段
   - 批量操作：通过循环轻松设置多个输入框的共同属性
   - 统一事件处理：通过一个信号处理函数处理多个输入框的事件
   - 全局管理：在复杂应用中提供全局统一的输入框管理器
   - 跨模块访问：不同模块可以获取同一个管理器实例

6. 单例模式使用建议：
   - 推荐使用 get_instance() 类方法获取实例，语义更清晰
   - 首次创建时必须提供 QLineEdit 控件参数
   - 后续获取可以无参数调用，会返回已创建的实例
   - 在需要重新初始化时使用 reset_instance() 方法
   - 在多线程环境中可以安全使用，无需额外同步
"""


class QLineEditSynchronizer(QObject):
    """
    QLineEdit 控件内容同步器（增强版）

    该类实现了多个 QLineEdit 控件之间的内容同步功能。当同步组中的任意一个 QLineEdit 控件
    内容发生变化时，该组内的其他所有 QLineEdit 控件都会自动同步更新为相同的内容。

    ===== 核心功能增强 =====
    1. **实时内容同步机制**：
       - 监听 textChanged 信号，确保任何文本变化都能立即触发同步
       - 支持程序API调用（setText()）和用户手动输入的同步
       - 智能防循环机制，避免无限递归调用
       - 只同步内容不一致的控件，提高同步效率

    2. **失焦同步保障机制**：
       - 通过事件过滤器监听 focusOutEvent 事件
       - 在控件失去焦点时执行完整的同步检查
       - 作为实时同步的补充保障，处理边缘情况和异步更新场景
       - 确保数据一致性，防止同步遗漏

    3. **增强的异步处理**：
       - 使用信号槽机制确保UI更新在主线程中执行
       - 临时阻塞信号防止循环触发
       - 批量处理同步操作，提高性能
       - 详细的状态检查和错误恢复

    ===== 原有功能保持 =====
    - 支持多个同步组，每个组内的 QLineEdit 控件内容保持同步
    - 使用异步机制避免阻塞主线程，确保 UI 响应性
    - 集成日志记录功能，记录同步操作的详细信息
    - 提供完善的错误处理和异常恢复机制
    - 支持动态添加和移除同步组

    ===== 设计模式 =====
    - 观察者模式：监听 QLineEdit 的文本变化和失焦事件
    - 组合模式：管理多个同步组
    - 信号槽模式：使用 Qt 信号槽实现异步通信
    - 事件过滤器模式：监听控件的失焦事件

    ===== 使用场景 =====
    - 表单中需要多个输入框显示相同内容的场景
    - 实时数据同步显示
    - 用户输入的即时反馈和同步
    - 需要高可靠性数据一致性的应用

    ===== 注意事项 =====
    - 所有 QLineEdit 控件必须设置唯一的 objectName
    - 同步操作是异步的，不会阻塞 UI 线程
    - 支持动态添加和移除同步组
    - 自动处理控件删除和资源清理
    - 线程安全，支持多线程环境使用
    """

    # 定义信号
    sync_started = pyqtSignal(int, str)  # 同步开始信号：组索引，触发控件名称
    sync_completed = pyqtSignal(int, int)  # 同步完成信号：组索引，更新的控件数量
    sync_error = pyqtSignal(int, str, str)  # 同步错误信号：组索引，错误类型，错误消息

    # 内部异步更新信号
    __async_update_signal = pyqtSignal(int, str, str)  # 组索引，目标控件名称，新文本内容

    def __init__(self, sync_groups: List[List[QLineEdit]], log_output=None, logger=None, parent: Optional[QObject] = None):
        """
        初始化 QLineEdit 内容同步器

        Args:
            sync_groups (List[List[QLineEdit]]): 二维列表，每个子列表包含需要同步的 QLineEdit 控件组
                例如：[[edit1, edit2, edit3], [edit4, edit5]] 表示两个同步组
            log_output: LogOutput 实例，用于在 UI 界面中输出突出重点日志
            logger: Logger 实例，用于在控制台中输出日志
            parent (Optional[QObject]): Qt 父对象，用于内存管理

        Raises:
            ValueError: 当传入的参数不符合要求时抛出
            TypeError: 当参数类型不正确时抛出

        使用示例:
            ```python
            # 创建 QLineEdit 控件
            edit1 = QLineEdit()
            edit1.setObjectName("edit1")
            edit2 = QLineEdit()
            edit2.setObjectName("edit2")
            edit3 = QLineEdit()
            edit3.setObjectName("edit3")

            # 创建同步器
            sync_groups = [[edit1, edit2], [edit3]]
            synchronizer = QLineEditSynchronizer(sync_groups, log_output, logger)

            # 现在 edit1 和 edit2 的内容会保持同步
            edit1.setText("测试内容")  # edit2 也会自动显示"测试内容"
            ```
        """
        super().__init__(parent)

        # 参数验证
        if not isinstance(sync_groups, list):
            raise TypeError("sync_groups 必须是一个列表")

        if not sync_groups:
            raise ValueError("sync_groups 不能为空")

        # 初始化实例变量
        self.__sync_groups: List[List[QLineEdit]] = []
        self.__group_mapping: Dict[QLineEdit, int] = {}  # 控件到组索引的映射
        self.__updating_flags: Dict[int, bool] = {}  # 防止循环更新的标志
        self.__log_output = log_output
        self.__logger: Logger = logger if logger else ClassInstanceManager.get_instance(key="ui_logger")

        # 连接内部信号
        self.__async_update_signal.connect(self.__on_async_update)

        # 处理传入的同步组
        self.__process_sync_groups(sync_groups)

        # 记录初始化日志
        if self.__logger:
            self.__logger.info(f"QLineEditSynchronizer 初始化完成，共创建 {len(self.__sync_groups)} 个同步组")

        if self.__log_output:
            self.__log_output.append_multi_style([
                ("QLineEdit同步器", {'bold': True, 'color': '#2E8B57'}),
                (" 初始化完成，共 ", {'color': '#666666'}),
                (f"{len(self.__sync_groups)}", {'bold': True, 'color': '#FF6347'}),
                (" 个同步组", {'color': '#666666'})
            ])

    def __process_sync_groups(self, sync_groups: List[List[QLineEdit]]) -> None:
        """
        处理传入的同步组，验证并设置信号连接

        Args:
            sync_groups: 同步组列表

        Raises:
            ValueError: 当控件验证失败时抛出
        """
        for group_index, group in enumerate(sync_groups):
            if not isinstance(group, list):
                raise TypeError(f"同步组 {group_index} 必须是一个列表")

            if not group:
                if self.__logger:
                    self.__logger.warning(f"跳过空的同步组 {group_index}")
                continue

            validated_group = []
            for control in group:
                if not isinstance(control, QLineEdit):
                    raise TypeError(f"同步组 {group_index} 中包含非 QLineEdit 类型的控件: {type(control)}")

                # 检查是否已被删除
                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning(f"跳过已删除的 QLineEdit 控件")
                    continue

                # 检查 objectName
                object_name = control.objectName()
                if not object_name:
                    if self.__logger:
                        self.__logger.warning(f"QLineEdit 控件未设置 objectName，将跳过")
                    continue

                # 检查是否已在其他组中
                if control in self.__group_mapping:
                    old_group = self.__group_mapping[control]
                    if self.__logger:
                        self.__logger.warning(f"控件 '{object_name}' 已在组 {old_group} 中，将移动到组 {group_index}")

                validated_group.append(control)
                self.__group_mapping[control] = group_index

            if validated_group:
                self.__sync_groups.append(validated_group)
                self.__updating_flags[group_index] = False

                # 为组内每个控件设置信号连接和事件过滤器
                for control in validated_group:
                    # 连接文本变化信号（实时同步）
                    control.textChanged.connect(
                        lambda text, ctrl=control, idx=group_index: self.__on_text_changed(idx, ctrl, text)
                    )

                    # 安装事件过滤器以监听失焦事件（失焦同步保障）
                    control.installEventFilter(self)

                if self.__logger:
                    control_names = [ctrl.objectName() for ctrl in validated_group]
                    self.__logger.debug(f"创建同步组 {group_index}，包含控件: {control_names}")
            else:
                if self.__logger:
                    self.__logger.warning(f"同步组 {group_index} 没有有效的控件，已跳过")

    def eventFilter(self, obj: QObject, event: QEvent) -> bool:
        """
        事件过滤器，用于监听QLineEdit控件的失焦事件

        当QLineEdit控件失去焦点时，执行一次完整的同步检查，
        作为实时同步的补充保障机制，确保数据一致性。

        Args:
            obj: 事件源对象
            event: 事件对象

        Returns:
            bool: 是否处理了该事件（False表示继续传递事件）
        """
        try:
            # 只处理QLineEdit控件的失焦事件
            if isinstance(obj, QLineEdit) and event.type() == QEvent.FocusOut:
                # 检查控件是否在同步管理中
                if obj in self.__group_mapping:
                    group_index = self.__group_mapping[obj]
                    current_text = obj.text()

                    if self.__logger:
                        self.__logger.debug(f"检测到失焦事件：组 {group_index}，控件 '{obj.objectName()}'，执行同步检查")

                    # 执行失焦同步检查
                    self.__on_focus_out_sync_check(group_index, obj, current_text)

            # 继续传递事件，不拦截
            return False

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"事件过滤器处理失焦事件时发生错误: {str(e)}")
                self.__logger.debug(traceback.format_exc())
            return False

    def __on_focus_out_sync_check(self, group_index: int, source_control: QLineEdit, current_text: str) -> None:
        """
        失焦同步检查处理函数

        当控件失去焦点时，检查同步组内所有控件的内容是否一致，
        如果发现不一致，则以失焦控件的内容为准进行同步。

        Args:
            group_index: 同步组索引
            source_control: 失焦的控件
            current_text: 当前文本内容
        """
        try:
            # 防止在更新过程中触发
            if self.__updating_flags.get(group_index, False):
                return

            # 检查控件是否仍然有效
            if sip.isdeleted(source_control):
                if self.__logger:
                    self.__logger.warning("失焦的控件已被删除")
                return

            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]
            source_name = source_control.objectName()

            # 检查组内其他控件的内容是否与失焦控件一致
            inconsistent_controls = []
            for control in group:
                if control == source_control:
                    continue

                if sip.isdeleted(control):
                    continue

                if control.text() != current_text:
                    inconsistent_controls.append(control)

            # 如果发现不一致的控件，进行同步
            if inconsistent_controls:
                if self.__logger:
                    control_names = [ctrl.objectName() for ctrl in inconsistent_controls]
                    self.__logger.info(f"失焦同步检查：发现 {len(inconsistent_controls)} 个不一致的控件 {control_names}，以 '{source_name}' 为准进行同步")

                # 发射同步开始信号
                self.sync_started.emit(group_index, source_name)

                # 执行同步
                self.__sync_group_async(group_index, source_control, current_text)

                if self.__log_output:
                    self.__log_output.append_multi_style([
                        ("失焦同步", {'bold': True, 'color': '#FF8C00'}),
                        (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                        (f"从 '{source_name}' ", {'color': '#666666'}),
                        ("同步到", {'color': '#666666'}),
                        (f" {len(inconsistent_controls)} ", {'color': '#FF6347', 'bold': True}),
                        ("个控件", {'color': '#666666'})
                    ])
            else:
                if self.__logger:
                    self.__logger.debug(f"失焦同步检查：组 {group_index} 内容已一致，无需同步")

        except Exception as e:
            error_msg = f"失焦同步检查时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "focus_out_sync_error", error_msg)

    def __on_text_changed(self, group_index: int, source_control: QLineEdit, new_text: str) -> None:
        """
        处理文本变化事件（实时同步机制）

        当同步组中任意一个QLineEdit控件内容发生变化时（无论是通过程序API调用setText()
        还是用户手动输入），立即触发同步逻辑，确保同步组内所有QLineEdit控件的文本内容保持完全一致。

        Args:
            group_index: 同步组索引
            source_control: 触发变化的控件
            new_text: 新的文本内容
        """
        try:
            # 防止循环更新
            if self.__updating_flags.get(group_index, False):
                if self.__logger:
                    self.__logger.debug(f"组 {group_index} 正在更新中，跳过文本变化处理")
                return

            # 检查控件是否仍然有效
            if sip.isdeleted(source_control):
                if self.__logger:
                    self.__logger.warning("触发文本变化的控件已被删除")
                return

            source_name = source_control.objectName()

            # 发射同步开始信号
            self.sync_started.emit(group_index, source_name)

            # 记录详细日志
            if self.__logger:
                self.__logger.debug(f"实时同步触发：组 {group_index}，控件 '{source_name}'，新内容: '{new_text}'")

            # 异步同步组内其他控件
            self.__sync_group_async(group_index, source_control, new_text)

        except Exception as e:
            error_msg = f"处理文本变化时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "text_change_error", error_msg)

    def __sync_group_async(self, group_index: int, source_control: QLineEdit, new_text: str) -> None:
        """
        异步同步组内其他控件的内容（增强版）

        改进的同步逻辑，确保在各种场景下的稳定性和一致性：
        1. 只同步内容不一致的控件，提高效率
        2. 增强的防循环机制
        3. 更详细的日志记录和错误处理

        Args:
            group_index: 同步组索引
            source_control: 触发变化的源控件
            new_text: 新的文本内容
        """
        try:
            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]
            updated_count = 0
            skipped_count = 0

            # 设置更新标志，防止循环更新
            self.__updating_flags[group_index] = True

            # 收集需要更新的控件
            controls_to_update = []
            for control in group:
                if control == source_control:
                    continue  # 跳过源控件

                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning(f"跳过已删除的控件")
                    skipped_count += 1
                    continue

                # 只同步内容不一致的控件，提高效率
                if control.text() != new_text:
                    controls_to_update.append(control)
                else:
                    if self.__logger:
                        self.__logger.debug(f"控件 '{control.objectName()}' 内容已一致，跳过同步")
                    skipped_count += 1

            # 批量异步更新控件内容
            for control in controls_to_update:
                control_name = control.objectName()
                # 通过信号异步更新控件内容
                self.__async_update_signal.emit(group_index, control_name, new_text)
                updated_count += 1

            # 记录详细的同步日志
            if self.__logger:
                source_name = source_control.objectName()
                self.__logger.debug(f"异步同步组 {group_index} 完成：源控件 '{source_name}'，更新 {updated_count} 个控件，跳过 {skipped_count} 个控件")

            # 在UI中显示同步信息（仅当有实际更新时）
            if self.__log_output and updated_count > 0:
                source_name = source_control.objectName()
                self.__log_output.append_multi_style([
                    ("实时同步", {'bold': True, 'color': '#4682B4'}),
                    (f" 组{group_index} ", {'color': '#FF6347', 'bold': True}),
                    (f"从 '{source_name}' ", {'color': '#666666'}),
                    ("同步到", {'color': '#666666'}),
                    (f" {updated_count} ", {'color': '#FF6347', 'bold': True}),
                    ("个控件", {'color': '#666666'})
                ])

            # 发射同步完成信号
            self.sync_completed.emit(group_index, updated_count)

        except Exception as e:
            error_msg = f"异步同步时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "async_sync_error", error_msg)
        finally:
            # 重置更新标志
            self.__updating_flags[group_index] = False

    def __on_async_update(self, group_index: int, control_name: str, new_text: str) -> None:
        """
        异步更新信号的槽函数，在主线程中执行UI更新（增强版）

        改进的异步更新逻辑：
        1. 增强的控件查找和验证
        2. 更安全的文本更新机制
        3. 详细的状态检查和日志记录
        4. 更好的错误处理和恢复

        Args:
            group_index: 同步组索引
            control_name: 目标控件名称
            new_text: 新的文本内容
        """
        try:
            # 验证组索引
            if group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.warning(f"异步更新时发现无效的组索引: {group_index}")
                return

            group = self.__sync_groups[group_index]

            # 查找目标控件
            target_control = None
            for control in group:
                if sip.isdeleted(control):
                    continue

                if control.objectName() == control_name:
                    target_control = control
                    break

            if target_control is None:
                if self.__logger:
                    self.__logger.warning(f"异步更新时未找到名为 '{control_name}' 的控件")
                return

            # 检查是否需要更新（避免不必要的操作）
            current_text = target_control.text()
            if current_text == new_text:
                if self.__logger:
                    self.__logger.debug(f"控件 '{control_name}' 内容已是目标值，跳过更新")
                return

            # 临时断开textChanged信号，防止循环触发
            try:
                target_control.blockSignals(True)

                # 更新控件文本（这会在主线程中执行）
                target_control.setText(new_text)

                if self.__logger:
                    self.__logger.debug(f"已更新控件 '{control_name}' 的内容：'{current_text}' -> '{new_text}'")

            finally:
                # 恢复信号连接
                target_control.blockSignals(False)

        except Exception as e:
            error_msg = f"异步更新控件 '{control_name}' 时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

            self.sync_error.emit(group_index, "async_update_error", error_msg)

    def add_sync_group(self, group: List[QLineEdit]) -> int:
        """
        添加新的同步组

        Args:
            group: 包含 QLineEdit 控件的列表

        Returns:
            int: 新创建的同步组索引，如果创建失败返回 -1

        使用示例:
            ```python
            # 创建新的控件
            edit4 = QLineEdit()
            edit4.setObjectName("edit4")
            edit5 = QLineEdit()
            edit5.setObjectName("edit5")

            # 添加新的同步组
            group_index = synchronizer.add_sync_group([edit4, edit5])
            if group_index >= 0:
                print(f"成功创建同步组 {group_index}")
            ```
        """
        try:
            if not isinstance(group, list) or not group:
                if self.__logger:
                    self.__logger.error("添加同步组失败：传入的组为空或不是列表")
                return -1

            # 验证组内控件
            validated_group = []
            for control in group:
                if not isinstance(control, QLineEdit):
                    if self.__logger:
                        self.__logger.error(f"添加同步组失败：包含非 QLineEdit 类型的控件: {type(control)}")
                    return -1

                if sip.isdeleted(control):
                    if self.__logger:
                        self.__logger.warning("跳过已删除的控件")
                    continue

                object_name = control.objectName()
                if not object_name:
                    if self.__logger:
                        self.__logger.warning("跳过未设置 objectName 的控件")
                    continue

                validated_group.append(control)

            if not validated_group:
                if self.__logger:
                    self.__logger.error("添加同步组失败：没有有效的控件")
                return -1

            # 创建新组
            group_index = len(self.__sync_groups)
            self.__sync_groups.append(validated_group)
            self.__updating_flags[group_index] = False

            # 更新映射并设置信号连接
            for control in validated_group:
                # 如果控件已在其他组中，从旧组移除
                if control in self.__group_mapping:
                    old_group_index = self.__group_mapping[control]
                    if old_group_index < len(self.__sync_groups):
                        old_group = self.__sync_groups[old_group_index]
                        if control in old_group:
                            old_group.remove(control)
                            if self.__logger:
                                self.__logger.info(f"控件 '{control.objectName()}' 从组 {old_group_index} 移动到组 {group_index}")

                self.__group_mapping[control] = group_index

                # 设置信号连接和事件过滤器
                control.textChanged.connect(
                    lambda text, ctrl=control, idx=group_index: self.__on_text_changed(idx, ctrl, text)
                )

                # 安装事件过滤器以监听失焦事件
                control.installEventFilter(self)

            # 记录日志
            control_names = [ctrl.objectName() for ctrl in validated_group]
            if self.__logger:
                self.__logger.info(f"成功添加同步组 {group_index}，包含控件: {control_names}")

            if self.__log_output:
                self.__log_output.append_multi_style([
                    ("新增同步组", {'bold': True, 'color': '#28A745'}),
                    (f" {group_index} ", {'color': '#FF6347', 'bold': True}),
                    ("包含 ", {'color': '#666666'}),
                    (f"{len(validated_group)}", {'color': '#FF6347', 'bold': True}),
                    (" 个控件", {'color': '#666666'})
                ])

            return group_index

        except Exception as e:
            error_msg = f"添加同步组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return -1

    def remove_sync_group(self, group_index: int) -> bool:
        """
        移除指定的同步组

        Args:
            group_index: 要移除的同步组索引

        Returns:
            bool: 移除成功返回 True，失败返回 False

        使用示例:
            ```python
            # 移除索引为 1 的同步组
            success = synchronizer.remove_sync_group(1)
            if success:
                print("同步组移除成功")
            ```
        """
        try:
            if group_index < 0 or group_index >= len(self.__sync_groups):
                if self.__logger:
                    self.__logger.error(f"移除同步组失败：无效的组索引 {group_index}")
                return False

            group = self.__sync_groups[group_index]
            control_names = [ctrl.objectName() for ctrl in group if not sip.isdeleted(ctrl)]

            # 断开信号连接、移除事件过滤器并清理映射
            for control in group:
                if not sip.isdeleted(control):
                    # 断开信号连接
                    try:
                        control.textChanged.disconnect()
                    except TypeError:
                        pass  # 如果没有连接的信号，会抛出 TypeError

                    # 移除事件过滤器
                    try:
                        control.removeEventFilter(self)
                    except Exception as e:
                        if self.__logger:
                            self.__logger.debug(f"移除事件过滤器时出错: {str(e)}")

                    # 从映射中移除
                    if control in self.__group_mapping:
                        del self.__group_mapping[control]

            # 移除组
            del self.__sync_groups[group_index]
            if group_index in self.__updating_flags:
                del self.__updating_flags[group_index]

            # 更新后续组的索引映射
            for control, mapped_index in list(self.__group_mapping.items()):
                if mapped_index > group_index:
                    self.__group_mapping[control] = mapped_index - 1

            # 更新更新标志字典的键
            new_updating_flags = {}
            for idx, flag in self.__updating_flags.items():
                if idx > group_index:
                    new_updating_flags[idx - 1] = flag
                else:
                    new_updating_flags[idx] = flag
            self.__updating_flags = new_updating_flags

            # 记录日志
            if self.__logger:
                self.__logger.info(f"成功移除同步组 {group_index}，包含控件: {control_names}")

            if self.__log_output:
                self.__log_output.append_multi_style([
                    ("移除同步组", {'bold': True, 'color': '#DC3545'}),
                    (f" {group_index} ", {'color': '#FF6347', 'bold': True}),
                    ("包含 ", {'color': '#666666'}),
                    (f"{len(control_names)}", {'color': '#FF6347', 'bold': True}),
                    (" 个控件", {'color': '#666666'})
                ])

            return True

        except Exception as e:
            error_msg = f"移除同步组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return False

    def get_sync_groups_info(self) -> List[Dict[str, Any]]:
        """
        获取所有同步组的信息

        Returns:
            List[Dict[str, any]]: 包含每个同步组信息的列表

        使用示例:
            ```python
            groups_info = synchronizer.get_sync_groups_info()
            for info in groups_info:
                print(f"组 {info['index']}：{info['control_names']}")
            ```
        """
        try:
            groups_info = []

            for index, group in enumerate(self.__sync_groups):
                valid_controls = []
                control_names = []

                for control in group:
                    if not sip.isdeleted(control):
                        valid_controls.append(control)
                        control_names.append(control.objectName())

                group_info = {
                    'index': index,
                    'control_count': len(valid_controls),
                    'control_names': control_names,
                    'is_updating': self.__updating_flags.get(index, False)
                }

                groups_info.append(group_info)

            return groups_info

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"获取同步组信息时发生错误: {str(e)}")
            return []

    def clear_all_groups(self) -> None:
        """
        清空所有同步组中控件的内容

        使用示例:
            ```python
            # 清空所有同步组的内容
            synchronizer.clear_all_groups()
            ```
        """
        try:
            cleared_groups = 0
            cleared_controls = 0

            for group_index, group in enumerate(self.__sync_groups):
                # 设置更新标志，防止循环更新
                self.__updating_flags[group_index] = True

                group_cleared = 0
                for control in group:
                    if not sip.isdeleted(control) and control.text():
                        control.clear()
                        group_cleared += 1
                        cleared_controls += 1

                if group_cleared > 0:
                    cleared_groups += 1

                # 重置更新标志
                self.__updating_flags[group_index] = False

            # 记录日志
            if self.__logger:
                self.__logger.info(f"清空操作完成：{cleared_groups} 个组，{cleared_controls} 个控件")

            if self.__log_output and cleared_controls > 0:
                self.__log_output.append_multi_style([
                    ("清空完成", {'bold': True, 'color': '#6C757D'}),
                    (f" {cleared_groups} ", {'color': '#FF6347', 'bold': True}),
                    ("个组 ", {'color': '#666666'}),
                    (f"{cleared_controls}", {'color': '#FF6347', 'bold': True}),
                    (" 个控件", {'color': '#666666'})
                ])

        except Exception as e:
            error_msg = f"清空所有组时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

    def set_all_groups_text(self, text: str) -> None:
        """
        设置所有同步组中控件的文本内容

        Args:
            text: 要设置的文本内容

        使用示例:
            ```python
            # 设置所有同步组的内容为"统一内容"
            synchronizer.set_all_groups_text("统一内容")
            ```
        """
        try:
            updated_groups = 0
            updated_controls = 0

            for group_index, group in enumerate(self.__sync_groups):
                # 设置更新标志，防止循环更新
                self.__updating_flags[group_index] = True

                group_updated = 0
                for control in group:
                    if not sip.isdeleted(control):
                        control.setText(text)
                        group_updated += 1
                        updated_controls += 1

                if group_updated > 0:
                    updated_groups += 1

                # 重置更新标志
                self.__updating_flags[group_index] = False

            # 记录日志
            if self.__logger:
                self.__logger.info(f"批量设置文本完成：{updated_groups} 个组，{updated_controls} 个控件，内容: '{text}'")

            if self.__log_output and updated_controls > 0:
                self.__log_output.append_multi_style([
                    ("批量设置", {'bold': True, 'color': '#17A2B8'}),
                    (f" {updated_groups} ", {'color': '#FF6347', 'bold': True}),
                    ("个组 ", {'color': '#666666'}),
                    (f"{updated_controls}", {'color': '#FF6347', 'bold': True}),
                    (" 个控件", {'color': '#666666'})
                ])

        except Exception as e:
            error_msg = f"批量设置文本时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())

    def cleanup_deleted_controls(self) -> int:
        """
        清理已删除的控件引用

        Returns:
            int: 清理的控件数量

        使用示例:
            ```python
            # 清理已删除的控件
            cleaned_count = synchronizer.cleanup_deleted_controls()
            print(f"清理了 {cleaned_count} 个已删除的控件")
            ```
        """
        try:
            cleaned_count = 0

            # 清理组内的已删除控件
            for group_index, group in enumerate(self.__sync_groups):
                valid_controls = []
                for control in group:
                    if sip.isdeleted(control):
                        # 从映射中移除
                        if control in self.__group_mapping:
                            del self.__group_mapping[control]
                        cleaned_count += 1
                        if self.__logger:
                            self.__logger.debug(f"清理已删除的控件引用")
                    else:
                        valid_controls.append(control)

                # 更新组
                self.__sync_groups[group_index] = valid_controls

            # 移除空组
            empty_groups = []
            for group_index, group in enumerate(self.__sync_groups):
                if not group:
                    empty_groups.append(group_index)

            # 从后往前删除空组，避免索引变化
            for group_index in reversed(empty_groups):
                del self.__sync_groups[group_index]
                if group_index in self.__updating_flags:
                    del self.__updating_flags[group_index]
                cleaned_count += 1
                if self.__logger:
                    self.__logger.debug(f"移除空的同步组 {group_index}")

            # 重新构建映射
            self.__group_mapping.clear()
            for group_index, group in enumerate(self.__sync_groups):
                for control in group:
                    self.__group_mapping[control] = group_index

            # 记录日志
            if cleaned_count > 0:
                if self.__logger:
                    self.__logger.info(f"清理完成：移除了 {cleaned_count} 个无效引用")

                if self.__log_output:
                    self.__log_output.append_multi_style([
                        ("清理完成", {'bold': True, 'color': '#FFC107'}),
                        (" 移除 ", {'color': '#666666'}),
                        (f"{cleaned_count}", {'color': '#FF6347', 'bold': True}),
                        (" 个无效引用", {'color': '#666666'})
                    ])

            return cleaned_count

        except Exception as e:
            error_msg = f"清理已删除控件时发生错误: {str(e)}"
            if self.__logger:
                self.__logger.error(error_msg)
                self.__logger.debug(traceback.format_exc())
            return 0

    def get_group_by_control(self, control: QLineEdit) -> int:
        """
        根据控件获取其所在的同步组索引

        Args:
            control: QLineEdit 控件

        Returns:
            int: 同步组索引，如果控件不在任何组中返回 -1

        使用示例:
            ```python
            # 获取控件所在的组索引
            group_index = synchronizer.get_group_by_control(edit1)
            if group_index >= 0:
                print(f"控件在组 {group_index} 中")
            ```
        """
        try:
            if not isinstance(control, QLineEdit):
                return -1

            if sip.isdeleted(control):
                return -1

            return self.__group_mapping.get(control, -1)

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"获取控件组索引时发生错误: {str(e)}")
            return -1

    def is_control_in_sync(self, control: QLineEdit) -> bool:
        """
        检查控件是否在同步管理中

        Args:
            control: QLineEdit 控件

        Returns:
            bool: 如果控件在同步管理中返回 True，否则返回 False

        使用示例:
            ```python
            # 检查控件是否在同步管理中
            if synchronizer.is_control_in_sync(edit1):
                print("控件在同步管理中")
            ```
        """
        return self.get_group_by_control(control) >= 0

    def get_sync_group_count(self) -> int:
        """
        获取同步组的数量

        Returns:
            int: 同步组数量

        使用示例:
            ```python
            count = synchronizer.get_sync_group_count()
            print(f"当前有 {count} 个同步组")
            ```
        """
        return len(self.__sync_groups)

    def __del__(self):
        """
        析构函数，清理资源（增强版）

        确保完全清理所有资源：
        1. 断开所有信号连接
        2. 移除所有事件过滤器
        3. 清理数据结构
        """
        try:
            # 断开所有信号连接并移除事件过滤器
            for group in self.__sync_groups:
                for control in group:
                    if not sip.isdeleted(control):
                        try:
                            # 断开信号连接
                            control.textChanged.disconnect()
                        except TypeError:
                            pass  # 如果没有连接的信号，会抛出 TypeError

                        try:
                            # 移除事件过滤器
                            control.removeEventFilter(self)
                        except Exception:
                            pass  # 忽略移除事件过滤器时的错误

            # 清理数据结构
            self.__sync_groups.clear()
            self.__group_mapping.clear()
            self.__updating_flags.clear()

            if self.__logger:
                self.__logger.debug("QLineEditSynchronizer 资源清理完成")

        except Exception as e:
            if self.__logger:
                self.__logger.error(f"清理 QLineEditSynchronizer 资源时发生错误: {str(e)}")


"""
QLineEditSynchronizer 类实现总结（增强版）：

===== 核心功能增强 =====
1. **实时内容同步机制**：
   - 监听 textChanged 信号，确保任何文本变化都能立即触发同步
   - 支持程序API调用（setText()）和用户手动输入的同步
   - 智能防循环机制，使用 blockSignals() 避免无限递归
   - 只同步内容不一致的控件，显著提高同步效率

2. **失焦同步保障机制**：
   - 实现 eventFilter() 方法监听 QEvent.FocusOut 事件
   - 在控件失去焦点时执行完整的同步检查
   - 检测并修复同步组内的数据不一致问题
   - 作为实时同步的补充保障，确保数据完整性

3. **增强的异步处理**：
   - 改进的 __sync_group_async() 方法，支持批量处理
   - 优化的 __on_async_update() 方法，增强错误处理
   - 临时信号阻塞机制，防止循环触发
   - 详细的状态检查和性能统计

===== 原有功能保持 =====
4. **多组管理**：
   - 支持多个独立的同步组，每个组内控件内容保持一致
   - 动态添加和移除同步组功能
   - 智能的控件映射和组索引管理
   - 自动清理已删除的控件引用

5. **异步机制**：
   - 使用 Qt 信号槽实现异步通信，避免阻塞主线程
   - 确保 UI 更新在主线程中执行，保证线程安全
   - 高效的事件处理和响应机制

6. **日志和错误处理**：
   - 集成完善的日志记录功能，支持多级别日志输出
   - 详细的错误处理和异常恢复机制
   - UI 日志输出支持，便于调试和监控

===== 设计模式应用 =====
7. **设计特点**：
   - 观察者模式：监听文本变化和失焦事件
   - 事件过滤器模式：处理控件失焦事件
   - 信号槽模式：实现异步通信和UI更新
   - 组合模式：管理多个同步组

===== 性能优化 =====
8. **性能特性**：
   - 智能同步：只更新内容不一致的控件
   - 批量处理：减少信号发射次数
   - 资源管理：自动清理和内存优化
   - 状态缓存：高效的更新标志管理

===== 主要方法 =====
9. **核心方法**：
   - eventFilter(): 新增的事件过滤器，监听失焦事件
   - __on_focus_out_sync_check(): 新增的失焦同步检查方法
   - __sync_group_async(): 增强的异步同步方法
   - __on_async_update(): 改进的异步更新方法
   - add_sync_group(): 添加新的同步组（增强事件过滤器支持）
   - remove_sync_group(): 移除指定同步组（清理事件过滤器）
   - 其他原有方法保持功能完整性

===== 使用场景扩展 =====
10. **适用场景**：
    - 高可靠性要求的表单数据同步
    - 实时协作编辑应用
    - 复杂UI界面的状态管理
    - 需要数据一致性保障的业务系统
    - 多视图数据展示同步
"""
