"""
基础事件测试

测试 ManagedMultiProcess 的7个核心事件的基本触发机制：
1. PROCESS_CREATED - 所有子进程创建完成后
2. PROCESS_COMPLETED - 所有子进程执行完成后立即触发
3. PROCESS_COMPLETED_WITH_DATA - 所有子进程执行完成且数据更新队列为空后
4. PROCESS_STOPPED - 所有子进程停止后立即触发
5. PROCESS_STOPPED_WITH_DATA - 所有子进程停止且数据更新队列为空后
6. PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP - 调用stop_all后，所有子进程正常执行完成且数据更新队列为空时触发
7. PROCESS_ALL_COMPLETED_BEFORE_STOP - 调用stop_all后，所有子进程正常执行完成(不等待数据更新队列为空)，在__perform_stop_all前触发
"""

import sys
import os
import unittest
import time
import threading
from typing import List, Dict, Any

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager

# 添加测试模块路径
test_module_path = os.path.join(project_root, 'test', 'test_manager_process3')
if test_module_path not in sys.path:
    sys.path.insert(0, test_module_path)

from base_test import BaseTestCase, EventTestMixin
from test_utils import TestUtils, PerformanceMonitor
from mock_data import MockDataFactory, WorkerType


class TestBasicEvents(BaseTestCase, EventTestMixin):
    """
    基础事件测试类
    
    测试每个事件的基本触发机制和数据验证。
    """
    
    def setUp(self):
        """测试前设置"""
        super().setUp()
        self.setup_event_tracking()
        self.mock_factory = MockDataFactory()
        self.performance_monitor = PerformanceMonitor()
    
    def test_process_created_event(self):
        """
        测试 PROCESS_CREATED 事件
        
        验证：
        1. 事件在所有子进程创建完成后触发
        2. 事件数据包含正确的 mp_instance
        3. 事件只触发一次
        """
        self.logger.info("开始测试 PROCESS_CREATED 事件")
        
        # 创建测试场景
        scenario = self.mock_factory.create_test_scenario("basic_event_test")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册事件处理器
        created_handler = self.create_event_handler(ProcessEventManager.PROCESS_CREATED)
        mp.listen_event(ProcessEventManager.PROCESS_CREATED, created_handler)
        
        # 启动性能监控
        self.performance_monitor.start_monitoring()
        
        # 启动处理
        mp.run()
        
        # 验证 PROCESS_CREATED 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_CREATED, timeout=5.0)
        
        # 验证事件只触发一次
        time.sleep(1.0)  # 等待确保没有重复触发
        self.assertEqual(self.get_event_count(ProcessEventManager.PROCESS_CREATED), 1,
                        "PROCESS_CREATED 事件应该只触发一次")
        
        # 验证事件数据
        with self.event_lock:
            event_data = self.event_data.get(ProcessEventManager.PROCESS_CREATED)
            self.assertIsNotNone(event_data, "事件数据不应为空")
            self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")
        
        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 5.0)
        
        # 停止性能监控
        self.performance_monitor.stop_monitoring()
        self.performance_monitor.record_metric("data_count", len(data))
        self.performance_monitor.record_metric("num_processes", scenario.num_processes)
        
        self.logger.info(f"PROCESS_CREATED 事件测试完成\n{self.performance_monitor.get_summary()}")
    
    def test_process_completed_event(self):
        """
        测试 PROCESS_COMPLETED 事件
        
        验证：
        1. 事件在所有工作进程退出后立即触发
        2. 事件在 PROCESS_COMPLETED_WITH_DATA 之前触发
        3. 事件数据正确
        """
        self.logger.info("开始测试 PROCESS_COMPLETED 事件")
        
        # 创建测试场景
        scenario = self.mock_factory.create_test_scenario("quick_completion")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册相关事件处理器
        self.register_all_events(mp)
        
        # 启动处理
        mp.run()
        
        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 5.0)
        
        # 验证 PROCESS_COMPLETED 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_COMPLETED, timeout=2.0)
        
        # 验证事件触发顺序（PROCESS_COMPLETED 应在 PROCESS_COMPLETED_WITH_DATA 之前）
        expected_order = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA
        ]
        self.assert_event_order(expected_order, timeout=2.0)
        
        # 验证事件数据
        with self.event_lock:
            event_data = self.event_data.get(ProcessEventManager.PROCESS_COMPLETED)
            self.assertIsNotNone(event_data, "PROCESS_COMPLETED 事件数据不应为空")
            self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")
        
        self.logger.info("PROCESS_COMPLETED 事件测试完成")
    
    def test_process_completed_with_data_event(self):
        """
        测试 PROCESS_COMPLETED_WITH_DATA 事件
        
        验证：
        1. 事件在所有工作进程退出且数据通知队列为空后触发
        2. 事件在 PROCESS_COMPLETED 之后触发
        3. 共享数据已正确处理
        """
        self.logger.info("开始测试 PROCESS_COMPLETED_WITH_DATA 事件")
        
        # 创建数据共享测试场景
        scenario = self.mock_factory.create_test_scenario("data_sharing")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册相关事件处理器
        self.register_all_events(mp)
        
        # 启动处理
        mp.run()
        
        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 5.0)
        
        # 验证 PROCESS_COMPLETED_WITH_DATA 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_COMPLETED_WITH_DATA, timeout=5.0)
        
        # 验证事件触发顺序
        expected_order = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA
        ]
        self.assert_event_order(expected_order, timeout=2.0)
        
        # 验证共享数据已正确处理
        results = mp.get_results()
        self.assertIn("counter", results, "应该包含共享计数器")
        self.assertEqual(results["counter"], len(data), "共享计数器应等于数据项数量")
        
        self.assertIn("processed_items", results, "应该包含处理项列表")
        processed_items = results["processed_items"]
        self.assertEqual(len(processed_items), len(data), "处理项数量应等于输入数据数量")
        
        self.logger.info("PROCESS_COMPLETED_WITH_DATA 事件测试完成")
    
    def test_process_stopped_event(self):
        """
        测试 PROCESS_STOPPED 事件
        
        验证：
        1. 事件在调用 stop_all 后所有进程停止时触发
        2. 事件在 PROCESS_STOPPED_WITH_DATA 之前触发
        3. 事件数据正确
        """
        self.logger.info("开始测试 PROCESS_STOPPED 事件")
        
        # 创建长时间运行的测试场景
        scenario = self.mock_factory.create_test_scenario("long_running")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册相关事件处理器
        self.register_all_events(mp)
        
        # 启动处理
        mp.run()
        
        # 等待一段时间后停止
        time.sleep(0.5)
        mp.stop_all(immediate=False)  # 优雅停止
        
        # 验证 PROCESS_STOPPED 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_STOPPED, timeout=10.0)
        
        # 验证事件数据
        with self.event_lock:
            event_data = self.event_data.get(ProcessEventManager.PROCESS_STOPPED)
            self.assertIsNotNone(event_data, "PROCESS_STOPPED 事件数据不应为空")
            self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")
        
        self.logger.info("PROCESS_STOPPED 事件测试完成")
    
    def test_process_stopped_with_data_event(self):
        """
        测试 PROCESS_STOPPED_WITH_DATA 事件
        
        验证：
        1. 事件在所有进程停止且数据更新队列为空后触发
        2. 事件在 PROCESS_STOPPED 之后触发
        3. 数据已正确缓存
        """
        self.logger.info("开始测试 PROCESS_STOPPED_WITH_DATA 事件")
        
        # 创建数据共享测试场景
        scenario = self.mock_factory.create_test_scenario("data_sharing")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册相关事件处理器
        self.register_all_events(mp)
        
        # 启动处理
        mp.run()
        
        # 等待一段时间后停止
        time.sleep(1.0)
        mp.stop_all(immediate=False)
        
        # 验证 PROCESS_STOPPED_WITH_DATA 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, timeout=10.0)
        
        # 验证事件触发顺序
        with self.event_lock:
            stopped_index = -1
            stopped_with_data_index = -1
            
            for i, event in enumerate(self.triggered_events):
                if event == ProcessEventManager.PROCESS_STOPPED:
                    stopped_index = i
                elif event == ProcessEventManager.PROCESS_STOPPED_WITH_DATA:
                    stopped_with_data_index = i
            
            self.assertGreater(stopped_index, -1, "PROCESS_STOPPED 事件应该被触发")
            self.assertGreater(stopped_with_data_index, -1, "PROCESS_STOPPED_WITH_DATA 事件应该被触发")
            self.assertLess(stopped_index, stopped_with_data_index, 
                           "PROCESS_STOPPED 应该在 PROCESS_STOPPED_WITH_DATA 之前触发")
        
        # 验证数据已缓存
        results = mp.get_results()
        self.assertIsInstance(results, dict, "结果应该是字典类型")
        
        self.logger.info("PROCESS_STOPPED_WITH_DATA 事件测试完成")

    def test_process_all_completed_before_stop_event(self):
        """
        测试 PROCESS_ALL_COMPLETED_BEFORE_STOP 事件

        验证：
        1. 事件在调用 stop_all 后，所有子进程正常执行完成时触发
        2. 事件在 __perform_stop_all 前触发
        3. 事件不等待数据更新队列为空
        """
        self.logger.info("开始测试 PROCESS_ALL_COMPLETED_BEFORE_STOP 事件")

        # 创建快速完成的测试场景
        scenario = self.mock_factory.create_test_scenario("quick_completion")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)

        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)

        # 注册相关事件处理器
        self.register_all_events(mp)

        # 启动处理
        mp.run()

        # 等待进程开始运行
        time.sleep(0.2)

        # 调用 stop_all（此时进程应该已经完成或即将完成）
        mp.stop_all(immediate=False)

        # 验证 PROCESS_ALL_COMPLETED_BEFORE_STOP 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP, timeout=5.0)

        # 验证事件数据
        with self.event_lock:
            event_data = self.event_data.get(ProcessEventManager.PROCESS_ALL_COMPLETED_BEFORE_STOP)
            self.assertIsNotNone(event_data, "PROCESS_ALL_COMPLETED_BEFORE_STOP 事件数据不应为空")
            self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")

        self.logger.info("PROCESS_ALL_COMPLETED_BEFORE_STOP 事件测试完成")

    def test_process_all_completed_with_data_on_stop_event(self):
        """
        测试 PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP 事件

        验证：
        1. 事件在调用 stop_all 后，所有子进程正常执行完成且数据更新队列为空时触发
        2. 事件在数据处理完成后触发
        3. 共享数据已正确处理
        """
        self.logger.info("开始测试 PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP 事件")

        # 创建数据共享测试场景
        scenario = self.mock_factory.create_test_scenario("data_sharing")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)

        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)

        # 注册相关事件处理器
        self.register_all_events(mp)

        # 启动处理
        mp.run()

        # 等待进程开始运行
        time.sleep(0.5)

        # 调用 stop_all
        mp.stop_all(immediate=False)

        # 验证 PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP 事件被触发
        self.assert_event_triggered(ProcessEventManager.PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP, timeout=10.0)

        # 验证事件数据
        with self.event_lock:
            event_data = self.event_data.get(ProcessEventManager.PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP)
            self.assertIsNotNone(event_data, "PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP 事件数据不应为空")
            self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")

        # 验证共享数据已正确处理
        results = mp.get_results()
        self.assertIn("counter", results, "应该包含共享计数器")
        self.assertIn("processed_items", results, "应该包含处理项列表")

        self.logger.info("PROCESS_ALL_COMPLETED_WITH_DATA_ON_STOP 事件测试完成")

    def test_event_data_consistency(self):
        """
        测试事件数据一致性

        验证：
        1. 所有事件的 mp_instance 参数一致
        2. 事件触发时机正确
        3. 事件数据完整性
        """
        self.logger.info("开始测试事件数据一致性")

        # 创建测试场景
        scenario = self.mock_factory.create_test_scenario("basic_event_test")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)

        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)

        # 注册所有事件处理器
        self.register_all_events(mp)

        # 启动处理
        mp.run()

        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 5.0)

        # 验证基本事件被触发
        basic_events = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA
        ]

        for event in basic_events:
            self.assert_event_triggered(event, timeout=2.0)

        # 验证所有事件的 mp_instance 一致性
        with self.event_lock:
            for event_name, event_data in self.event_data.items():
                self.assertIs(event_data['mp_instance'], mp,
                            f"事件 {event_name} 的 mp_instance 应该一致")
                self.assertIsInstance(event_data['timestamp'], float,
                                    f"事件 {event_name} 应该有时间戳")

        self.logger.info("事件数据一致性测试完成")

    def test_single_process_events(self):
        """
        测试单进程场景下的事件触发

        验证：
        1. 单进程模式下事件正常触发
        2. 事件顺序正确
        3. 数据处理正确
        """
        self.logger.info("开始测试单进程事件")

        # 创建单进程测试场景
        scenario = self.mock_factory.create_test_scenario("single_process")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)

        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)

        # 注册相关事件处理器
        self.register_all_events(mp)

        # 启动处理
        mp.run()

        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 5.0)

        # 验证基本事件被触发
        basic_events = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA
        ]

        for event in basic_events:
            self.assert_event_triggered(event, timeout=2.0)

        # 验证数据处理结果
        results = mp.get_results()
        self.assertIn("processed_count", results, "应该包含处理计数")
        self.assertEqual(results["processed_count"], len(data), "处理计数应等于数据项数量")

        self.logger.info("单进程事件测试完成")

    def test_empty_data_events(self):
        """
        测试空数据场景下的事件触发

        验证：
        1. 空数据时事件正常触发
        2. 事件顺序正确
        3. 没有处理错误
        """
        self.logger.info("开始测试空数据事件")

        # 创建空数据测试场景
        scenario = self.mock_factory.create_test_scenario("empty_data")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)

        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)

        # 注册相关事件处理器
        self.register_all_events(mp)

        # 启动处理
        mp.run()

        # 等待处理完成
        mp.wait_all(timeout=scenario.expected_duration + 2.0)

        # 验证基本事件被触发
        basic_events = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA
        ]

        for event in basic_events:
            self.assert_event_triggered(event, timeout=2.0)

        # 验证没有错误
        errors = mp.get_all_errors()
        self.assertEqual(len(errors), 0, "空数据处理不应产生错误")

        self.logger.info("空数据事件测试完成")


if __name__ == '__main__':
    # 配置日志
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBasicEvents)

    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=None,
        descriptions=True,
        failfast=False
    )

    print("=" * 80)
    print("ManagedMultiProcess 基础事件测试")
    print("=" * 80)

    result = runner.run(suite)

    # 输出测试结果摘要
    print("\n" + "=" * 80)
    print("测试结果摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")

    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")

    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")

    print("=" * 80)

    # 返回退出码
    exit_code = 0 if result.wasSuccessful() else 1
    exit(exit_code)
