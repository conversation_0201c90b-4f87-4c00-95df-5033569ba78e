# Windows 11 驱动级键鼠模拟权威开发指南

## 文档概述

本指南旨在提供一个全面、详尽的、从零开始的步骤说明，用于在 Windows 11 操作系统上开发一个驱动级别的键盘与鼠标模拟方案。驱动级模拟意味着输入事件是在操作系统内核层面注入的，这使其能够绕过绝大多数应用程序级的钩子和限制，是实现最高权限、最稳定模拟的终极方案。

本方案的核心技术栈如下：
- **编程语言**: C++
- **开发框架**: Windows Driver Frameworks (WDF)，具体为 Kernel-Mode Driver Framework (KMDF)。
- **核心技术**: 虚拟HID设备框架 (Virtual HID Framework, VHF)
- **通信机制**: I/O Control Codes (IOCTL)
- **开发环境**: Visual Studio 2022 + Windows Driver Kit (WDK)

---

## 第一步：搭建开发环境

这是所有工作的基础。一个正确配置的环境可以避免后续大量的编译和兼容性问题。

1.  **安装 Visual Studio 2022**:
    *   从微软官网下载并运行 Visual Studio Installer。
    *   在"工作负载"选项卡中，必须勾选 **"使用 C++ 的桌面开发"**。这是编译C++代码的基础。
    *   完成安装。

2.  **安装 Windows SDK (Software Development Kit)**:
    *   通常，最新版本的 Windows SDK 会随 Visual Studio 的 C++ 工作负载一同安装。
    *   您可以在 Visual Studio Installer 的"单个组件"中搜索并确认已安装适用于 Windows 11 的最新版 SDK。

3.  **安装 WDK (Windows Driver Kit)**:
    *   这是驱动开发的核心。请访问微软官方文档网站，搜索 "Download the WDK" 并下载与您 Visual Studio 2022 版本匹配的 WDK 安装程序。
    *   WDK 会作为一个扩展插件安装到 Visual Studio 中。安装完成后，您会在 Visual Studio 的新建项目模板中看到驱动相关的选项。

4.  **环境验证**:
    *   重新启动 Visual Studio。
    *   点击"创建新项目"。
    *   在项目模板搜索框中输入 "driver"。如果您能看到如 "Kernel Mode Driver (KMDF)" 之类的模板，则证明您的开发环境已成功搭建。

---

## 第二步：创建内核驱动项目 (KMDF)

我们将创建一个内核驱动程序，它将作为模拟键鼠事件的执行者。

1.  **启动项目**:
    *   在 Visual Studio 中，选择"创建新项目"。
    *   选择 **"Kernel Mode Driver (KMDF)"** 模板。
    *   为您的项目命名（例如, `MyVirtualInputDriver`）并选择一个位置。

2.  **理解项目结构**:
    *   Visual Studio 会自动生成一个基础的驱动项目结构，通常包含以下关键文件：
        *   `Driver.h` / `Driver.c` (或 `.cpp`): 包含驱动程序的入口点 (`DriverEntry`) 和卸载逻辑。
        *   `Device.h` / `Device.c` (或 `.cpp`): 包含设备创建 (`EvtDeviceAdd`) 和 I/O 处理逻辑。
        *   `*.inf`: 这是一个信息文件，它告诉 Windows 如何安装您的驱动程序。

---

## 第三步：核心技术 - 创建虚拟HID设备 (VHF)

这是整个方案中最关键的一步。我们将通过VHF技术，在内核中凭空创造一个键盘或鼠标，让操作系统认为这是一个真实的物理设备。

1.  **规划HID报告描述符 (HID Report Descriptor)**:
    *   这是虚拟设备向操作系统提交的"功能说明书"。它是一个字节数组，精确定义了设备的能力。
    *   例如，一个鼠标的报告描述符会声明："我是一个指针设备，我有X轴和Y轴用于相对移动，我还有左、中、右三个按键。"
    *   您需要为您的虚拟鼠标和虚拟键盘分别精心设计报告描述符。网上有许多标准模板和工具可以帮助生成。

2.  **实现虚拟设备创建逻辑**:
    *   这段逻辑通常写在 `Device.c` 文件中的 `EvtDeviceAdd` 回调函数里。当系统发现您的"硬件"（即您的驱动）并为其创建设备对象时，此函数会被调用。
    *   **步骤**:
        a.  填充一个 `VHF_CONFIG` 结构体。
        b.  在此结构体中，指定您在上一步中设计的 HID 报告描述符。
        c.  调用 `VhfCreate` 函数，并将配置好的 `VHF_CONFIG` 传入。
        d.  如果调用成功，`VhfCreate` 会返回一个 **VHF 句柄**。您必须将此句柄安全地保存在您的设备上下文（Device Context）中，因为后续所有的模拟操作都需要使用它。
        e.  此步骤完成后，您应该能在 Windows 的设备管理器中看到一个新的"HID-compliant mouse"或"HID Keyboard Device"出现。

---

## 第四步：建立通信桥梁 (IOCTL)

现在内核中已经有了一个可以工作的虚拟设备，我们需要一种方法从外部（例如一个Python脚本）向它发送命令。

1.  **定义IOCTL控制码**:
    *   在一个共享的头文件（`.h`）中，使用 `CTL_CODE` 宏来定义一组唯一的IOCTL码。这个头文件需要同时被内核驱动项目和用户模式的通信DLL项目包含。
    *   示例定义:
        *   `IOCTL_SEND_MOUSE_INPUT`: 用于发送鼠标移动或点击指令。
        *   `IOCTL_SEND_KEYBOARD_INPUT`: 用于发送键盘按键指令。

2.  **创建设备接口**:
    *   在驱动的 `EvtDeviceAdd` 函数中，调用 `WdfDeviceCreateDeviceInterface` 函数。
    *   这将为您的驱动设备创建一个用户模式可见的"别名"或"链接"，应用程序可以通过这个链接找到并打开您的设备。

3.  **实现IOCTL处理器**:
    *   在 `Device.c` 中，实现 `EvtIoDeviceControl` 回调函数。这个函数是驱动接收所有IOCTL请求的入口点。
    *   函数内部通常是一个 `switch` 语句，根据传入的IOCTL控制码，分发到不同的处理逻辑。

---

## 第五步：执行输入模拟

这是命令的最终执行环节，将IOCTL请求转化为对虚拟HID设备的操作。

1.  **在IOCTL处理器中编写逻辑**:
    *   在 `EvtIoDeviceControl` 的 `switch` 语句的每个 `case` 中：
        a.  从传入的I/O请求包(IRP)中，安全地获取用户模式程序发送过来的数据（例如鼠标的 `dx`, `dy` 坐标，或键盘的扫描码）。
        b.  根据您在第三步中定义的HID报告描述符，构建一个匹配的 **HID输入报告 (HID Input Report)** 结构体。
        c.  将从用户模式获取的数据填充到这个HID输入报告中。
        d.  调用 `VhfReadReportSubmit` 函数，并将之前保存的VHF句柄和填充好的HID输入报告作为参数传入。
    *   一旦 `VhfReadReportSubmit` 被调用，VHF框架和操作系统内核就会接管，将这个报告解释为真实的硬件输入，从而实现光标移动或按键事件。

---

## 第六步：编译、签名与部署

1.  **编译驱动**:
    *   在 Visual Studio 中，将解决方案平台设置为 `x64`。
    *   选择 `Debug` 或 `Release` 配置进行构建。
    *   构建成功后，会在输出目录中生成关键文件：`*.sys` (驱动文件), `*.inf` (安装文件), `*.pdb` (调试符号), `*.cat` (安全目录)。

2.  **为测试进行驱动签名**:
    *   64位Windows强制要求驱动签名。在开发阶段，我们启用"测试模式"。
    *   以管理员身份打开命令提示符，执行 `bcdedit /set testsigning on`，然后重启电脑。
    *   在Visual Studio的项目属性中，找到 `驱动程序签名` -> `常规` -> `签名模式`，选择 `测试签名`。这样每次编译时，VS会自动为您的 `.sys` 文件进行测试签名。

3.  **安装和部署**:
    *   将 `.sys` 和 `.inf` 文件复制到测试机上。
    *   右键点击 `.inf` 文件，选择"安装"。Windows会根据 `.inf` 文件的指示，将 `.sys` 文件复制到系统目录并加载驱动。
    *   您可以在设备管理器中检查设备是否出现，或使用 `sc query yourdriverservicename` 命令查看服务状态。

---

## 第七步：创建用户模式通信DLL

为了让Python能够方便地调用驱动，我们创建一个C++ DLL作为中间层。

1.  **创建DLL项目**: 在Visual Studio中创建一个新的 "Dynamic-Link Library (DLL)" 项目。
2.  **实现导出函数**:
    *   在这个DLL中，编写简单的C风格导出函数，例如 `void MoveMouse(int dx, int dy)`。
    *   在函数内部:
        a.  使用 `CreateFile` API，传入在第四步中创建的设备接口链接，来打开与驱动的连接，获得一个设备句柄。
        b.  使用 `DeviceIoControl` API，传入设备句柄、对应的IOCTL码和要发送的数据。
        c.  关闭句柄。
    *   这个DLL将所有复杂的Windows API调用都封装了起来。

---

## 第八步：编写Python调用脚本

这是最顶层的控制逻辑，用Python实现。

1.  **使用 `ctypes`**: Python的 `ctypes` 标准库可以加载和调用C编译的DLL。
2.  **编写脚本**:
    *   使用 `ctypes.CDLL("path/to/your/bridge.dll")` 加载您在第七步中创建的DLL。
    *   为加载的DLL中的函数定义参数类型和返回类型，这是一个好习惯，可以避免很多隐蔽的错误。
    *   现在，您可以像调用普通Python函数一样，调用DLL中的函数（如 `bridge.MoveMouse(10, 10)`），从而驱动整个模拟链路。

---

## 第九步：调试与发布

1.  **调试**:
    *   **用户模式**: 可以使用Visual Studio的标准调试器调试DLL。
    *   **内核模式**: 必须使用内核调试器 **WinDbg**。您可以使用 `DbgPrint` 或 `KdPrint` 在驱动代码中输出调试信息，然后在WinDbg中查看。对于复杂的调试，需要配置双机调试环境。

2.  **发布**:
    *   要让您的驱动在任何未开启测试模式的普通电脑上运行，必须获得微软的官方签名。
    *   您需要购买一个 **EV代码签名证书**。
    *   将您的驱动包提交到微软的 **"Windows硬件开发者中心仪表板"**，通过一系列自动化测试后，微软会为您的驱动交叉签名。这是一个专业且有成本的流程。

## 结论

通过遵循以上步骤，您将能够构建一个功能强大、稳定可靠的驱动级键鼠模拟方案。这个过程技术性很强，尤其是在内核驱动开发和调试部分，需要极大的耐心和细致。但一旦完成，其所能达到的效果是任何用户模式方案都无法比拟的。 


- 测试模型
    - bcdedit /set testsigning on 开启测试签名
    - bcdedit /set testsigning off 关闭测试签名


