## 目录

- [`CheckBoxManager` - 集中管理复选框](#checkboxmanager)
  - [功能与适用场景 (概述)](#概述)
  - [自动发现与注册 (初始化)](#初始化)
  - [响应状态变化 (信号)](#信号)
  - [核心API详解 (方法)](#方法)
  - [实现原理探究 (深度解析与设计哲学)](#深度解析与设计哲学)
- [`CheckBoxStateManager`](#checkboxstatemanager)
  - [核心概念与工作原理](#核心概念与工作原理)
  - [快速上手](#快速上手)
  - [API 详解](#api-详解)
  - [注意事项](#注意事项)
- [`QPushButtonManager` - 高级按钮控制器](#pushbuttonmanager)
  - [概述](#概述-1)
  - [初始化](#初始化-1)
  - [信号](#信号-1)
  - [方法](#方法-1)
  - [深度解析与设计哲学](#深度解析与设计哲学-1)
- [`QLabelManager` - 动态标签管理器](#qlabelmanager)
  - [概述](#概述-2)
  - [初始化](#初始化-2)
  - [信号](#信号-2)
  - [方法](#方法-2)
  - [深度解析与设计哲学](#深度解析与设计哲学-2)
- [`LineEditManager` - 批量输入框控制器](#lineeditmanager)
  - [概述](#概述-3)
  - [初始化](#初始化-3)
  - [信号](#信号-3)
  - [方法](#方法-3)
  - [深度解析与设计哲学](#深度解析与设计哲学-3)
- [`LineEditMemory` - 输入框持久化记忆](#lineeditmemory)
  - [概述](#概述-4)
  - [初始化](#初始化-4)
  - [方法](#方法-4)
  - [深度解析与设计哲学](#深度解析与设计哲学-4)
- [`TextEditMemory` - 多行文本框持久化记忆](#texteditmemory)
  - [概述](#概述-5)
  - [初始化](#初始化-5)
  - [方法](#方法-5)
  - [深度解析与设计哲学](#深度解析与设计哲学-5)
- [`TextEditManager` - 多行文本框批量管理器](#texteditmanager)
  - [概述](#概述-6)
  - [初始化](#初始化-6)
  - [信号](#信号-6)
  - [方法](#方法-6)
  - [深度解析与设计哲学](#深度解析与设计哲学-6)
- [`InputCompleter` & `InputCompleterCache` - 强大的输入自动补全与历史记录](#inputcompleter--inputcompletercache)
  - [组合使用示例](#组合使用示例)
  - [InputCompleter 核心方法](#inputcompleter-核心方法)
  - [InputCompleter 深度解析](#inputcompleter-深度解析)
  - [InputCompleterCache 深度解析](#inputcompletercache-深度解析)
- [`ButtonPressEffectEnhancer` - 按钮点击凹陷效果增强器](#buttonpresseffectenhancer)
  - [概述](#概述-7)
  - [初始化](#初始化-7)
  - [方法](#方法-7)
  - [深度解析与设计哲学](#深度解析与设计哲学-7)
- [`LogOutput` - 线程安全的富文本日志窗口](#logoutput)
  - [概述](#概述-8)
  - [初始化](#初始化-8)
  - [方法](#方法-8)
  - [深度解析与设计哲学](#深度解析与设计哲学-8)
- [`SignalSlotManager` - 全局信号槽事件总线](#signalslotmanager)
  - [概述](#概述-9)
  - [初始化](#初始化-9)
  - [方法](#方法-9)
  - [深度解析与设计哲学](#深度解析与设计哲学-9)
- [`NestedScrollAreaFixer` - 嵌套滚动区域无缝滚动修复器](#nestedscrollareafixer)
  - [概述](#概述-10)
  - [初始化](#初始化-10)
  - [方法](#方法-10)
  - [深度解析与设计哲学](#深度解析与设计哲学-10)
- [`SelectAllCheckBoxManager` - 全选 逻辑控制器](#SelectAllCheckBoxManager)
- [`QProgressBarHelper` - 动画进度条控制器](#qprogressbarhelper)

---

# UI Tools 组件库文档

本文档详细介绍了 `ui_tools` 模块中包含的各个UI组件的功能、API和使用方法。每个组件都旨在简化和增强PyQt5应用的开发体验。

--- 

## `CheckBoxManager`

### 概述

`CheckBoxManager` 类提供了一个集中管理 `QCheckBox` 控件的强大机制。它能够递归地从一个或多个Qt容器（如 `QWidget`、`QLayout`）中查找所有的复选框，并提供统一的接口来操作它们、监控它们的状态变化。这个类特别适合用于需要对大量复选框进行分组、批量操作或实现复杂逻辑（如互斥选择）的场景。

其核心功能包括：
- **自动发现**: 在指定的容器内递归查找所有 `QCheckBox` 实例。
- **状态查询**: 方便地获取所有、选中或未选中的复选框列表及其文本。
- **批量操作**: 一键全选、全不选或根据文本设置特定复选框的状态。
- **事件处理**: 通过回调函数或PyQt信号/槽机制统一处理复选框的点击事件。
- **互斥选择**: 可以将一组复选框配置为类似 `QRadioButton` 的互斥模式，但仍保留复选框的外观和可取消选择的特性。
- **灵活过滤**: 多数方法支持通过文本、正则表达式或列表进行过滤，以精确定位目标复选框。

### 初始化

**`__init__(self, *containers, logger, parent=None)`**

构造函数负责初始化管理器，并立即在传入的容器中查找所有复选框。

- **实现细节**: 构造函数会遍历所有传入的 `containers`。对每一个 `container`，它会调用私有的 `__find_checkboxes` 方法。此方法通过递归方式，深度遍历容器的子控件和子布局。如果遇到 `QCheckBox`，则将其引用存入内部的 `__checkboxes` 列表中。这种设计使得开发者无需手动逐个注册复选框，只需指定顶层容器即可。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `*containers` | `QWidget`, `QLayout` | 一个或多个Qt容器对象。可以是任何 `QWidget` 的子类（如 `QGroupBox`, `QFrame`）或 `QLayout` 的子类。 |
| `logger` | `logging.Logger` | 日志记录器实例，用于输出内部操作信息。 |
| `parent` | `QObject`, optional | Qt的父对象，用于内存管理。默认为 `None`。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QGroupBox, QCheckBox
import logging

# 配置一个简单的logger
logging.basicConfig(level=logging.DEBUG)
ui_logger = logging.getLogger("ui_tools")

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CheckBoxManager Demo")
        
        main_layout = QVBoxLayout(self)
        
        # 容器1: 一个Group Box
        group1 = QGroupBox("选项组 1")
        layout1 = QVBoxLayout()
        layout1.addWidget(QCheckBox("苹果"))
        layout1.addWidget(QCheckBox("香蕉"))
        group1.setLayout(layout1)
        
        # 容器2: 一个普通Widget和其布局
        widget2 = QWidget()
        layout2 = QHBoxLayout()
        check3 = QCheckBox("启用所有功能")
        check3.setObjectName("enable_all")
        layout2.addWidget(check3)
        widget2.setLayout(layout2)

        main_layout.addWidget(group1)
        main_layout.addWidget(widget2)

        # 初始化CheckBoxManager，管理两个容器中的所有复选框
        self.manager = CheckBoxManager(group1, widget2, logger=ui_logger, parent=self)
        
        print(f"管理器找到了 {len(self.manager.get_all_checkboxes())} 个复选框。")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())
```

### 信号

- **`checkbox_checked(QCheckBox, bool)`**
  - **描述:** 当任何一个被管理的复选框的选中状态发生改变时，此信号会被发出。这通常是通过用户点击或通过 `set_checked_by_text`, `set_all_checked` 等方法改变状态时触发。
  - **参数:**
    - `checkbox` (`QCheckBox`): 状态发生改变的复选框对象。
    - `checked` (`bool`): 复选框新的选中状态 (`True` 为选中, `False` 为未选中)。
  - **使用示例:**
    ```python
    def on_any_checkbox_changed(checkbox, is_checked):
        print(f"信号捕获: 复选框 '{checkbox.text()}' 的状态变为 {is_checked}")

    manager.checkbox_checked.connect(on_any_checkbox_changed)

    # 通过代码改变状态也会触发信号
    manager.set_checked_by_text("苹果", True)
    ```

- **`exclusive_selection_changed(QCheckBox, bool)`**
  - **描述:** 当设置了互斥选择模式 (`set_exclusive_selection`) 后，任何一个属于互斥组的复选框状态改变时，此信号会被发出。
  - **参数:**
    - `checkbox` (`QCheckBox`): 状态发生改变的复选框对象。
    - `checked` (`bool`): 复选框新的选中状态。
  - **使用示例:**
    ```python
    def on_exclusive_choice_made(checkbox, is_checked):
        if is_checked:
            print(f"互斥选择信号: 用户选择了 '{checkbox.text()}'")

    # 设置所有复选框为互斥模式
    manager.set_exclusive_selection()
    manager.exclusive_selection_changed.connect(on_exclusive_choice_made)
    ```

### 方法

#### `get_all_checkboxes()`
- **描述:** 返回管理器中所有已发现的 `QCheckBox` 对象的列表。
- **返回:** `List[QCheckBox]` - 包含所有 `QCheckBox` 对象的列表。
- **使用示例:**
  ```python
  all_boxes = manager.get_all_checkboxes()
  for box in all_boxes:
      print(f"找到复选框: {box.text()} (ObjectName: {box.objectName()})")
  ```

#### `get_checked_boxes()`
- **描述:** 返回当前所有处于"选中"状态的复选框。
- **返回:** `List[QCheckBox]` - 仅包含已选中的 `QCheckBox` 对象的列表。
- **使用示例:**
  ```python
  manager.set_checked_by_text("苹果", True)
  checked_list = manager.get_checked_boxes()
  print(f"当前选中的水果: {[box.text() for box in checked_list]}")
  ```

#### `get_unchecked_boxes(filter_text=None, inverse=True)`
- **描述:** 获取所有未选中的复选框，并可选择根据文本进行过滤。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `filter_text` | `str` \| `List[str]`, optional | 用于过滤的文本或文本列表。默认为 `None`，不过滤。 |
  | `inverse` | `bool` | 是否反转过滤逻辑。`True` (默认) 返回**不匹配** `filter_text` 的项；`False` 返回**匹配** `filter_text` 的项。 |
- **返回:** `List[QCheckBox]` - 符合条件的未选中复选框列表。
- **使用示例:**
  ```python
  # 假设 "苹果" 和 "启用所有功能" 是选中的
  manager.set_all_checked(False)
  manager.set_checked_by_text("苹果", True)
  manager.get_checkbox_by_object_name("enable_all").setChecked(True)
  
  # 获取所有未选中的复选框
  unchecked_all = manager.get_unchecked_boxes()
  print(f"所有未选中的: {[box.text() for box in unchecked_all]}") # -> ['香蕉']

  # 获取未选中且文本为 "香蕉" 的复选框
  unchecked_banana = manager.get_unchecked_boxes(filter_text="香蕉", inverse=False)
  print(f"未选中的香蕉: {[box.text() for box in unchecked_banana]}") # -> ['香蕉']
  
  # 获取未选中且文本不是 "香蕉" 的复选框
  unchecked_not_banana = manager.get_unchecked_boxes(filter_text="香蕉", inverse=True)
  print(f"未选中且不是香蕉的: {[box.text() for box in unchecked_not_banana]}") # -> []
  ```

#### `get_checked_texts()`
- **描述:** 获取所有选中复选框的显示文本。
- **返回:** `List[str]` - 包含所有选中复选框文本的字符串列表。
- **使用示例:**
  ```python
  manager.set_all_checked(False)
  manager.set_checked_by_text("苹果", True)
  manager.set_checked_by_text("香蕉", True)
  selected_texts = manager.get_checked_texts()
  print(f"选中的项目有: {', '.join(selected_texts)}") # -> "选中的项目有: 苹果, 香蕉"
  ```

#### `get_checkbox_by_text(text)`
- **描述:** 通过其显示的文本精确查找并返回第一个匹配的复选框。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `text` | `str` | 要查找的复选框的显示文本。 |
- **返回:** `QCheckBox` 或 `None` - 如果找到，返回对应的 `QCheckBox` 对象；否则返回 `None`。
- **使用示例:**
  ```python
  apple_checkbox = manager.get_checkbox_by_text("苹果")
  if apple_checkbox:
      apple_checkbox.setChecked(True)
  else:
      print("未找到'苹果'复选框。")
  ```

#### `get_checkbox_by_object_name(object_name)`
- **描述:** 通过其 `objectName` 属性查找并返回对应的复选框。这是一种比文本更可靠的定位方式，因为 `objectName` 通常是稳定且唯一的。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `object_name` | `str` | 要查找的复选框的 `objectName`。 |
- **返回:** `QCheckBox` 或 `None` - 如果找到，返回对应的 `QCheckBox` 对象；否则返回 `None`。
- **使用示例:**
  ```python
  # 假设一个复选框在创建时设置了 objectName
  # check_box.setObjectName("my_special_checkbox")
  
  special_box = manager.get_checkbox_by_object_name("enable_all")
  if special_box:
      special_box.setChecked(True)
  ```

#### `get_checked_state_by_object_name(object_name)`
- **描述:** 通过 `objectName` 查找复选框并返回其当前的选中状态。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `object_name` | `str` | 要查询的复选框的 `objectName`。 |
- **返回:** `bool` 或 `None` - 如果找到复选框，返回其选中状态 (`True`/`False`)；如果未找到，返回 `None`。
- **使用示例:**
  ```python
  state = manager.get_checked_state_by_object_name("enable_all")
  if state is True:
      print("所有功能已启用。")
  elif state is False:
      print("所有功能未启用。")
  else:
      print("未找到名为 'enable_all' 的复选框。")
  ```

#### `set_checked_by_text(text, checked=True)`
- **描述:** 根据文本精确匹配来设置复选框的选中状态。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `text` | `str` | 目标复选框的显示文本。 |
  | `checked` | `bool` | 要设置的选中状态，默认为 `True`。 |
- **返回:** `bool` - 如果成功找到并设置了状态，返回 `True`；否则返回 `False`。
- **使用示例:**
  ```python
  was_set = manager.set_checked_by_text("香蕉", True)
  if not was_set:
      print("设置失败，可能没有'香蕉'这个选项。")
  ```

#### `set_all_checked(checked=True)`
- **描述:** 批量设置管理器中所有复选框的选中状态。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `checked` | `bool` | 要设置的选中状态，默认为 `True` (全选)。 |
- **使用示例:**
  ```python
  # 全选所有复选框
  manager.set_all_checked(True)
  
  # 全不选所有复选框
  manager.set_all_checked(False)
  ```

#### `set_click_callback(callback)`
- **描述:** 为所有管理的复选框设置一个统一的点击事件回调函数。每次任何一个复选框被点击，都会调用这个函数。
- **实现细节**: 此方法会首先断开所有复选框上之前可能通过此方法连接的 `clicked` 信号，以避免重复调用。然后，它会遍历所有复选框，将它们的 `clicked` 信号连接到一个内部的包装函数 `__on_checkbox_clicked_wrapper`。这个包装函数在调用用户提供的 `callback` 之前，会先发射 `checkbox_checked` 信号。这确保了无论是使用回调还是信号槽，都能捕获到状态变化。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `callback` | `Callable[[QCheckBox, bool], None]` | 回调函数。它接收两个参数：被点击的 `QCheckBox` 对象和它新的 `bool` 状态。 |
- **使用示例:**
  ```python
  def my_click_handler(checkbox, is_checked):
      print(f"回调触发: {checkbox.text()} 现在是 {'选中' if is_checked else '未选中'}")
      if checkbox.objectName() == "enable_all" and is_checked:
          # 假设选中"启用所有"会自动勾选其他选项
          manager.set_checked_by_text("苹果", True)
          manager.set_checked_by_text("香蕉", True)
  
  manager.set_click_callback(my_click_handler)
  ```

#### `set_exclusive_selection(filter_text=None, callback=None)`
- **描述:** 将一组复选框设置为互斥选择模式。在该模式下，当一个复选框被选中时，组内其他所有复选框都会自动被取消选中。这对于实现"单选多"的场景非常有用。
- **实现细节**:
    1.  `__get_filtered_checkboxes` 方法会根据 `filter_text` (支持正则) 筛选出需要互斥的复选框组。
    2.  `set_exclusive_selection` 会断开这些目标复选框上所有已连接的 `clicked` 信号，以防止冲突。
    3.  然后，它将每个目标复选框的 `clicked` 信号连接到私有的 `__on_exclusive_checkbox_clicked` 槽函数。
    4.  当用户点击一个复选框并使其变为选中状态时，`__on_exclusive_checkbox_clicked` 会被触发。它会遍历互斥组中的所有其他复选框，如果发现有其他已选中的，就调用 `setChecked(False)` 将其取消。
    5.  无论是取消其他复选框还是改变当前复选框状态，都会发出 `exclusive_selection_changed` 信号，并调用用户提供的 `callback`。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `filter_text` | `str` \| `List[str]`, optional | 用于筛选互斥组的文本或正则表达式。`None` 表示所有复选框都互斥。 |
  | `callback` | `Callable[[QCheckBox, bool], None]`, optional | 状态变化时的回调函数。 |
- **使用示例:**
  ```python
  # 假设有三个选项： "选项A", "选项B", "选项C"
  manager.set_exclusive_selection(filter_text=r"选项[A-C]") # 使用正则表达式匹配
  
  # 当你点击 "选项A" (设为True), 然后再点击 "选项B" (设为True), 
  # "选项A" 会自动变为 False。
  ```

### 深度解析与设计哲学

- **健壮性设计 — 递归查找 `__find_checkboxes`**:
  - `CheckBoxManager` 的核心能力始于其在初始化时对所有复选框的自动发现。`__find_checkboxes` 方法被设计为可以处理复杂的UI布局。
  - **递归遍历**: 它采用深度优先搜索（DFS）的策略。当遇到一个`QLayout`时，它会遍历该布局下的所有子项；如果子项是另一个布局，它会递归进入；如果子项是一个`QWidget`，它同样会递归进入以查找更深层次的子控件。
  - **类型安全**: 通过 `isinstance(container, QLayout)` 和 `isinstance(container, QWidget)` 进行检查，该方法可以智能地区分并处理不同类型的容器，确保了即使在混合了多种控件和布局的复杂UI树中，也能够准确无误地找到所有`QCheckBox`，而不会因为遇到非预期的控件类型而崩溃。

- **信号与回调的协同 — `set_click_callback` 与 `checkbox_checked` 信号**:
  - 管理器同时提供了回调函数和信号两种机制来响应事件，这是为了适应不同的编程范式和需求。
  - **回调 (`set_click_callback`)**: 适用于简单、直接的逻辑。当一个操作只需要一个固定的响应函数时，使用回调非常方便快捷。
  - **信号 (`checkbox_checked`)**: 遵循了Qt的核心设计哲学，旨在实现组件间的低耦合通信。在更大型的架构（如MVC、MVVM）中，视图层（包含复选框）不应直接了解或调用控制器/视图模型的方法。视图只需发射一个信号，任何对该信号感兴趣的组件都可以连接到它，从而实现清晰的关注点分离。
  - **事件顺序**: 内部的 `__on_checkbox_clicked_wrapper` 包装函数确保了 `checkbox_checked` 信号总是在用户提供的回调函数之前被发射。这提供了一个可预测的、一致的事件处理顺序。

- **互斥选择的实现精要 — `set_exclusive_selection`**:
  - 此功能是该类的亮点，其背后有几个关键的Python和Qt技术点。
  - **`lambda`闭包陷阱与解决方案**: 在循环中为信号连接`lambda`函数是一个常见的陷阱。如果代码是 `lambda: self.__on_exclusive_checkbox_clicked(checkbox, ...)`，那么所有的lambda函数在实际被调用时，引用的 `checkbox` 变量都将是循环结束时的最后一个`checkbox`对象。通过使用 `lambda state, cb=checkbox: ...` 的形式，利用了Python函数默认参数在定义时就被计算的特性，`cb=checkbox` 将**当前循环迭代**的`checkbox`对象"冻结"并绑定为该`lambda`函数的默认参数`cb`。这确保了每个信号都连接到了一个能正确引用对应复选框的槽函数。
  - **信号清理的重要性**: 在连接新的`clicked`信号处理器之前，代码会先尝试调用 `checkbox.clicked.disconnect()`。这是至关重要的健壮性措施。如果没有这一步，多次调用`set_exclusive_selection`或与`set_click_callback`混用，会导致一个按钮的`clicked`信号上附加了多个处理函数。当用户点击按钮时，这些函数会以不可预知的顺序被全部调用，引发逻辑混乱和难以调试的bug。先断开连接确保了任何时候一个`clicked`信号只有一个管理者，保证了行为的确定性。

---

## `CheckBoxStateManager`

`CheckBoxStateManager` 是一个功能强大的管理器，旨在自动处理 `PyQt5` 应用程序中 `QCheckBox` 控件的状态持久化和恢复。它通过后台线程、防抖机制和清晰的生命周期管理，确保了UI的流畅响应和数据的安全。

**核心功能:**
- **自动发现:** 递归查找指定容器（或容器列表）中的所有 `QCheckBox` 控件。
- **状态持久化:** 将所有复选框的选中状态以JSON格式保存到本地文件。
- **状态恢复:** 在程序启动时从配置文件中加载并应用复选框状态。
- **线程安全保存:** 所有文件写入操作都在一个独立的后台线程中执行，避免阻塞UI主线程。
- **防抖机制 (Debouncing):** 当复选框状态频繁变化时，延迟并合并保存操作，避免不必要的磁盘I/O。
- **自动保存:** 支持按设定的时间间隔定期自动保存状态，作为数据安全的保障。
- **层级路径标识:** 为每个复选框生成一个基于其父容器层级的唯一路径（如 `parent_widget.group_box.my_checkbox`），以确保状态的精确映射。
- **按容器管理:** 支持对特定容器内的复选框进行独立的加载和保存操作。
- **生命周期管理:** 提供明确的 `cleanup` 方法，用于在程序退出时安全地停止后台任务和释放资源。
- **UI日志集成:** 可以与 `LogOutput` 控件集成，将操作过程中的详细日志实时显示在UI上。

### 核心概念与工作原理

#### 1. 线程安全的保存机制

为了防止文件读写操作阻塞UI线程导致界面卡顿，`CheckBoxStateManager` 将所有保存任务都委托给一个后台工作线程 (`_SaveWorker`)。主线程与工作线程之间通过Qt的信号和槽机制进行通信。

```mermaid
sequenceDiagram
    participant User
    participant MainThread as 主线程 (UI)
    participant WorkerThread as 工作线程 (I/O)

    User->>MainThread: 点击CheckBox
    MainThread->>MainThread: on_checkbox_state_changed() 触发
    MainThread->>MainThread: __debounce_save() (启动/重置QTimer)
    Note over MainThread: 等待防抖延迟...
    MainThread->>MainThread: __execute_save_via_worker() (QTimer超时)
    MainThread->>WorkerThread: emit _request_save_on_worker_signal(states)
    WorkerThread->>WorkerThread: perform_save(states)
    Note over WorkerThread: 读文件, 合并数据, 写文件
    WorkerThread-->>MainThread: emit save_finished_signal(result)
    MainThread->>MainThread: __handle_save_finished(result) (更新UI日志)
```

#### 2. 防抖 (Debouncing) 机制

当用户快速连续点击多个复选框时，每个点击都会触发一次状态变化事件。如果不加处理，可能会导致密集的、不必要的文件写入。防抖机制通过一个 `QTimer` 来解决这个问题：每次状态变化时，它会重置这个计时器。只有当用户停止操作一小段时间（由 `debounce_delay` 参数决定）后，计时器才会真正触发，执行一次保存操作。这极大地提高了性能和效率。

#### 3. 生命周期管理

由于存在后台线程，因此安全地关闭和清理资源至关重要。`cleanup()` 方法是 `CheckBoxStateManager` 生命周期中的最后一个环节。它会负责：
1.  停止所有定时器（防抖和自动保存）。
2.  请求最后一次保存，确保所有更改都已写入磁盘。
3.  **安全地停止后台线程**，并等待其完全退出。这是防止程序在文件未写完时就关闭的关键。
4.  断开所有信号连接，释放内存。

> **警告:** 必须在应用程序主窗口的 `closeEvent` 或退出逻辑中调用 `cleanup()` 方法，否则可能导致数据丢失或程序无法正常退出。

### 快速上手

下面是一个在PyQt5窗口中使用 `CheckBoxStateManager` 的完整示例。

```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QGroupBox, QCheckBox

# 假设以下类已定义在 global_tools.ui_tools.compnonent.qcheckbox
from global_tools.ui_tools.compnonent.qcheckbox import CheckBoxStateManager

# 假设有一个LogOutput和Logger的简单实现
class SimpleLogOutput:
    def append(self, message, color=None):
        print(f"LOG: {message}")
    def append_multi_style(self, parts):
        log_str = "LOG: " + "".join([p[0] for p in parts])
        print(log_str)
        
class SimpleLogger:
    def debug(self, msg): print(f"DEBUG: {msg}")
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

class MyWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CheckBoxStateManager Demo")
        self.setGeometry(100, 100, 300, 200)
        
        # --- UI ---
        main_layout = QVBoxLayout(self)
        
        # 创建一个容器
        self.settings_group = QGroupBox("Settings")
        self.settings_group.setObjectName("settings_group")
        group_layout = QVBoxLayout(self.settings_group)
        
        # 添加一些复选框
        self.cb1 = QCheckBox("Enable Feature A")
        self.cb1.setObjectName("feature_a_checkbox")
        
        self.cb2 = QCheckBox("Enable Feature B")
        self.cb2.setObjectName("feature_b_checkbox")
        
        group_layout.addWidget(self.cb1)
        group_layout.addWidget(self.cb2)
        
        main_layout.addWidget(self.settings_group)
        
        # --- State Manager ---
        # 准备日志和配置路径
        log_output = SimpleLogOutput()
        logger = SimpleLogger()
        config_dir = "./config"
        
        # 初始化管理器
        # 管理 self.settings_group 内的所有复选框
        self.state_manager = CheckBoxStateManager(
            container=self.settings_group,
            config_dir=config_dir,
            log_output=log_output,
            logger=logger,
            parent=self # 将管理器作为窗口的子对象，便于生命周期管理
        )
        
        # 从配置文件加载之前的状态
        self.state_manager.load_states()
        
        # 启动自动保存，每60秒一次
        self.state_manager.start_auto_save(60)

    # 必须实现 closeEvent 来调用 cleanup
    def closeEvent(self, event):
        print("Window is closing, cleaning up state manager...")
        # 这是确保数据安全的关键步骤
        self.state_manager.cleanup()
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MyWindow()
    window.show()
    sys.exit(app.exec_())

```

### API 详解

#### `__init__(container, config_dir, config_filename="checkbox_states.json", debounce_delay=300, parent=None, log_output=None, logger=None)`

构造函数，用于初始化状态管理器。

- **`container`** (`Union[QWidget, QLayout, List[...]]`): 需要管理的单个或多个UI容器。管理器会自动查找这些容器及其子孙控件中的所有 `QCheckBox`。
- **`config_dir`** (`str`): 用于存放配置文件的目录路径。
- **`config_filename`** (`str`, optional): 状态配置文件的名称。默认为 `checkbox_states.json`。
- **`debounce_delay`** (`int`, optional): 状态变化后的防抖延迟时间（毫秒）。默认为 `300`。
- **`parent`** (`QObject`, optional): Qt的父对象，用于自动的生命周期管理。推荐设置为窗口实例。
- **`log_output`** (`LogOutput`, optional): 一个实现了 `append` 和 `append_multi_style` 方法的UI日志输出控件实例。
- **`logger`** (`Logger`, optional): 一个标准的日志记录器实例。

---

#### 公共方法 (Public Methods)

##### `load_states()`

从配置文件加载所有复选框的状态并应用到UI上。通常在程序启动和UI初始化后调用。
- **返回值:** `bool` - 如果成功加载了配置文件则返回 `True`，否则（如文件不存在）返回 `False`。
- **使用示例:**
  ```python
  # 在窗口初始化时加载状态
  self.state_manager.load_states()
  ```

##### `save_states()`

**异步地**将当前所有复选框的状态保存到配置文件。此方法会立即返回，实际的保存操作在后台线程中进行。
- **返回值:** `bool` - 如果保存请求成功发送到后台线程则返回 `True`。
- **使用示例:**
  ```python
  # 手动触发一次保存
  was_request_sent = self.state_manager.save_states()
  if was_request_sent:
      print("保存请求已发送到后台。")
  ```

##### `start_auto_save(interval_seconds=60.0)`

启动定时自动保存功能。
- **`interval_seconds`** (`float`): 自动保存的时间间隔（秒）。
- **使用示例:**
  ```python
  # 启动每5分钟一次的自动保存
  self.state_manager.start_auto_save(300)
  ```

##### `stop_auto_save()`

停止自动保存功能。
- **使用示例:**
  ```python
  self.state_manager.stop_auto_save()
  ```
  
##### `cleanup()`

清理资源，**必须**在应用程序退出前调用。此方法会停止所有定时器，执行最后一次保存，并安全地关闭后台线程。
- **使用示例:**
  ```python
  # 在主窗口的 closeEvent 中调用
  def closeEvent(self, event):
      self.state_manager.cleanup()
      super().closeEvent(event)
  ```
  
##### `load_container_states(container)`

仅加载并应用指定容器内复选框的状态。
- **`container`** (`Union[QWidget, QLayout]`): 要加载状态的目标容器。
- **返回值:** `bool` - 如果成功加载并应用了至少一个状态，则返回 `True`。
- **使用示例:**
  ```python
  # 只加载 settings_group 容器内的复选框状态
  self.state_manager.load_container_states(self.settings_group)
  ```
  
##### `update_container_states(container)`

检查指定容器内所有复选框的当前状态，并将有变化的状态保存到配置文件。
- **`container`** (`Union[QWidget, QLayout]`): 要更新状态的目标容器。
- **返回值:** `bool` - 如果状态有变化并成功发起了保存请求，则返回 `True`。
- **使用示例:**
  ```python
  # 检查并保存 settings_group 内复选框的状态
  self.state_manager.update_container_states(self.settings_group)
  ```

##### `get_all_checkboxes()`
- **功能描述:** 获取管理器当前发现并维护的所有有效的 `QCheckBox` 控件实例。
- **返回:** `List[QCheckBox]` - 一个包含所有有效 `QCheckBox` 对象的列表。
- **实现细节:** 此方法并非简单地返回内部存储的列表。它会进行一次过滤，调用私有的 `__is_valid_pyqt_object` 方法检查每个 `QCheckBox` 对象是否仍然存在于内存中且未被Qt销毁。这确保了返回的列表是绝对安全的，避免了在UI控件被动态移除后出现悬空引用的风险。
- **使用示例:**
  ```python
  all_boxes = self.state_manager.get_all_checkboxes()
  print(f"当前管理着 {len(all_boxes)} 个有效的复选框。")
  ```

##### `get_checkbox_by_text(text)`
- **功能描述:** 根据其显示的文本精确查找并返回第一个匹配的复选框。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `text` | `str` | 要查找的复选框的显示文本。 |
- **返回:** `Optional[QCheckBox]` - 如果找到，返回对应的 `QCheckBox` 对象；否则返回 `None`。
- **实现细节:** 该方法会遍历 `get_all_checkboxes()` 返回的安全列表，并对每个复选框的 `text()` 属性进行字符串比较。它返回找到的第一个匹配项。
- **使用示例:**
  ```python
  feature_a = self.state_manager.get_checkbox_by_text("Enable Feature A")
  if feature_a:
      feature_a.setChecked(True)
  ```

##### `get_checkbox_by_object_name(object_name)`
- **功能描述:** 根据其 `objectName` 属性查找并返回对应的复选框。这通常是比文本更可靠的定位方式。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `object_name` | `str` | 要查找的复选框的 `objectName`。 |
- **返回:** `Optional[QCheckBox]` - 如果找到，返回对应的 `QCheckBox` 对象；否则返回 `None`。
- **使用示例:**
  ```python
  feature_b = self.state_manager.get_checkbox_by_object_name("feature_b_checkbox")
  if feature_b:
      print(f"复选框 'feature_b_checkbox' 当前状态: {feature_b.isChecked()}")
  ```

##### `get_checkbox_by_path(path)`
- **功能描述:** 根据在初始化时生成的层级路径查找复选框。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `path` | `str` | 复选框的层级路径，例如 `"settings_group.feature_a_checkbox"`。 |
- **返回:** `Optional[QCheckBox]` - 如果找到，返回对应的 `QCheckBox` 对象；否则返回 `None`。
- **实现细节:** 此方法首先会高效地在内部的 `__checkbox_paths` 字典中反向查找路径。如果找不到，它会进行一次降级兼容搜索，尝试将给定的 `path` 作为 `objectName` 来查找。这增加了方法的灵活性。
- **使用示例:**
  ```python
  # 假设路径是 "settings_group.feature_a_checkbox"
  path_str = "settings_group.feature_a_checkbox"
  checkbox = self.state_manager.get_checkbox_by_path(path_str)
  if checkbox:
      checkbox.setChecked(False)
  ```

##### `get_checked_checkboxes()`
- **功能描述:** 获取所有当前处于"选中"状态的复选框。
- **返回:** `List[QCheckBox]` - 一个只包含已选中复选框的列表。
- **实现细节:** 该方法在 `get_all_checkboxes()` 返回的安全列表基础上，通过列表推导式进行过滤，只保留 `cb.isChecked()` 为 `True` 的项。
- **使用示例:**
  ```python
  checked_list = self.state_manager.get_checked_checkboxes()
  print(f"当前共有 {len(checked_list)} 个项目被选中。")
  ```

##### `get_current_states()`
- **功能描述:** 获取管理器中所有复选框的当前状态，并以字典形式返回。
- **返回:** `Dict[str, bool]` - 一个字典，键是复选框的层级路径，值是其选中状态 (`True`/`False`)。
- **实现细节:** 为确保返回的状态是最新的，此方法会先遍历所有复选框，用它们当前的 `isChecked()` 状态更新内部的 `__states` 字典。然后，它会返回该字典的一个**副本** (`copy()`)，这是一种保护性编程实践，可以防止外部代码意外地修改管理器的内部状态。
- **使用示例:**
  ```python
  all_states = self.state_manager.get_current_states()
  # all_states -> {'settings_group.feature_a_checkbox': True, 'settings_group.feature_b_checkbox': False}
  ```

##### `set_checkbox_state(path, checked)`
- **功能描述:** 根据层级路径或 `objectName` 设置单个复选框的选中状态。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `path` | `str` | 复选框的层级路径或 `objectName`。 |
  | `checked` | `bool` | 要设置的选中状态 (`True` 或 `False`)。 |
- **返回:** `bool` - 如果成功找到并设置了状态，返回 `True`；否则返回 `False`。
- **实现细节:** 此方法在查找目标复选框时，会同时匹配其层级路径和 `objectName`，只要有一个匹配成功，就会执行设置操作。这使得调用者可以灵活地使用任一标识符。
- **使用示例:**
  ```python
  # 使用路径设置
  self.state_manager.set_checkbox_state("settings_group.feature_a_checkbox", False)
  # 使用 objectName 设置
  self.state_manager.set_checkbox_state("feature_b_checkbox", True)
  ```

##### `set_all_checked(checked=True)`
- **功能描述:** 批量设置管理器中所有复选框的选中状态。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `checked` | `bool` | 要设置的选中状态，默认为 `True` (全选)。 |
- **实现细节:** 这是一个直接的操作，它会遍历 `get_all_checkboxes()` 返回的所有有效复选框，并逐个调用它们的 `setChecked()` 方法。每个 `setChecked()` 调用都会触发各自的 `stateChanged` 信号，进而触发管理器的状态更新和防抖保存逻辑。
- **使用示例:**
  ```python
  # 全选所有
  self.state_manager.set_all_checked(True)
  # 全不选
  self.state_manager.set_all_checked(False)
  ```

##### `set_click_callback(callback)`
- **描述:** 为所有管理的复选框设置一个统一的点击事件回调函数。每次任何一个复选框被点击，都会调用这个函数。
- **实现细节**: 此方法会首先断开所有复选框上之前可能通过此方法连接的 `clicked` 信号，以避免重复调用。然后，它会遍历所有复选框，将它们的 `clicked` 信号连接到一个内部的包装函数 `__on_checkbox_clicked_wrapper`。这个包装函数在调用用户提供的 `callback` 之前，会先发射 `checkbox_checked` 信号。这确保了无论是使用回调还是信号槽，都能捕获到状态变化。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `callback` | `Callable[[QCheckBox, bool], None]` | 回调函数。它接收两个参数：被点击的 `QCheckBox` 对象和它新的 `bool` 状态。 |
- **使用示例:**
  ```python
  def my_click_handler(checkbox, is_checked):
      print(f"回调触发: {checkbox.text()} 现在是 {'选中' if is_checked else '未选中'}")
      if checkbox.objectName() == "enable_all" and is_checked:
          # 假设选中"启用所有"会自动勾选其他选项
          manager.set_checked_by_text("苹果", True)
          manager.set_checked_by_text("香蕉", True)
  
  manager.set_click_callback(my_click_handler)
  ```

##### `set_exclusive_selection(filter_text=None, callback=None)`
- **描述:** 将一组复选框设置为互斥选择模式。在该模式下，当一个复选框被选中时，组内其他所有复选框都会自动被取消选中。这对于实现"单选多"的场景非常有用。
- **实现细节**:
    1.  `__get_filtered_checkboxes` 方法会根据 `filter_text` (支持正则) 筛选出需要互斥的复选框组。
    2.  `set_exclusive_selection` 会断开这些目标复选框上所有已连接的 `clicked` 信号，以防止冲突。
    3.  然后，它将每个目标复选框的 `clicked` 信号连接到私有的 `__on_exclusive_checkbox_clicked` 槽函数。
    4.  当用户点击一个复选框并使其变为选中状态时，`__on_exclusive_checkbox_clicked` 会被触发。它会遍历互斥组中的所有其他复选框，如果发现有其他已选中的，就调用 `setChecked(False)` 将其取消。
    5.  无论是取消其他复选框还是改变当前复选框状态，都会发出 `exclusive_selection_changed` 信号，并调用用户提供的 `callback`。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `filter_text` | `str` \| `List[str]`, optional | 用于筛选互斥组的文本或正则表达式。`None` 表示所有复选框都互斥。 |
  | `callback` | `Callable[[QCheckBox, bool], None]`, optional | 状态变化时的回调函数。 |
- **使用示例:**
  ```python
  # 假设有三个选项： "选项A", "选项B", "选项C"
  manager.set_exclusive_selection(filter_text=r"选项[A-C]") # 使用正则表达式匹配
  
  # 当你点击 "选项A" (设为True), 然后再点击 "选项B" (设为True), 
  # "选项A" 会自动变为 False。
  ```

##### `set_click_callback(callback)`
- **描述:** 为所有管理的复选框设置一个统一的点击事件回调函数。每次任何一个复选框被点击，都会调用这个函数。
- **实现细节**: 此方法会首先断开所有复选框上之前可能通过此方法连接的 `clicked` 信号，以避免重复调用。然后，它会遍历所有复选框，将它们的 `clicked` 信号连接到一个内部的包装函数 `__on_checkbox_clicked_wrapper`。这个包装函数在调用用户提供的 `callback` 之前，会先发射 `checkbox_checked` 信号。这确保了无论是使用回调还是信号槽，都能捕获到状态变化。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `callback` | `Callable[[QCheckBox, bool], None]` | 回调函数。它接收两个参数：被点击的 `QCheckBox` 对象和它新的 `bool` 状态。 |
- **使用示例:**
  ```python
  def my_click_handler(checkbox, is_checked):
      print(f"回调触发: {checkbox.text()} 现在是 {'选中' if is_checked else '未选中'}")
      if checkbox.objectName() == "enable_all" and is_checked:
          # 假设选中"启用所有"会自动勾选其他选项
          manager.set_checked_by_text("苹果", True)
          manager.set_checked_by_text("香蕉", True)
  
  manager.set_click_callback(my_click_handler)
  ```

##### `set_exclusive_selection(filter_text=None, callback=None)`
- **描述:** 将一组复选框设置为互斥选择模式。在该模式下，当一个复选框被选中时，组内其他所有复选框都会自动被取消选中。这对于实现"单选多"的场景非常有用。
- **实现细节**:
    1.  `__get_filtered_checkboxes` 方法会根据 `filter_text` (支持正则) 筛选出需要互斥的复选框组。
    2.  `set_exclusive_selection` 会断开这些目标复选框上所有已连接的 `clicked` 信号，以防止冲突。
    3.  然后，它将每个目标复选框的 `clicked` 信号连接到私有的 `__on_exclusive_checkbox_clicked` 槽函数。
    4.  当用户点击一个复选框并使其变为选中状态时，`__on_exclusive_checkbox_clicked` 会被触发。它会遍历互斥组中的所有其他复选框，如果发现有其他已选中的，就调用 `setChecked(False)` 将其取消。
    5.  无论是取消其他复选框还是改变当前复选框状态，都会发出 `exclusive_selection_changed` 信号，并调用用户提供的 `callback`。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `filter_text` | `str` \| `List[str]`, optional | 用于筛选互斥组的文本或正则表达式。`None` 表示所有复选框都互斥。 |
  | `callback` | `Callable[[QCheckBox, bool], None]`, optional | 状态变化时的回调函数。 |
- **使用示例:**
  ```python
  # 假设有三个选项： "选项A", "选项B", "选项C"
  manager.set_exclusive_selection(filter_text=r"选项[A-C]") # 使用正则表达式匹配
  
  # 当你点击 "选项A" (设为True), 然后再点击 "选项B" (设为True), 
  # "选项A" 会自动变为 False。
  ```

---

#### 信号 (Signals)

##### `checkbox_state_changed(QCheckBox, bool)`

当任何一个被管理的复选框状态发生变化时，会发出此信号。
- **参数:**
    1. `QCheckBox`: 状态发生变化的复选框实例。
    2. `bool`: 复选框的新状态 (`True` 为选中, `False` 为未选中)。
- **使用示例:**
  ```python
  def on_state_changed(checkbox, is_checked):
      print(f"信号捕捉到: {checkbox.text()} 变为 {is_checked}")
  
  self.state_manager.checkbox_state_changed.connect(on_state_changed)
  ```

### 注意事项

- **`cleanup()` 是强制性的:** 再次强调，不调用 `cleanup()` 会导致数据丢失和潜在的程序挂起。
- **对象所有权:** 建议将 `CheckBoxStateManager` 实例的 `parent` 设置为其管理的窗口或一个长期存在的对象，以确保其生命周期与UI保持一致。
- **配置文件:** 状态保存在一个纯文本的JSON文件中，易于检查和调试。

---

## `QPushButtonManager` - 高级按钮控制器

### 概述

`QPushButtonManager` 是一个用于集中管理一个或多个 `QPushButton` 控件的高级工具。它通过按钮的 `objectName` (或文本作为备选) 来唯一标识和操作按钮，极大地简化了对按钮组的逻辑控制、样式管理和事件处理。

核心功能包括：
- **批量管理**: 一次性初始化并管理多个按钮。
- **线程安全的状态控制**: 提供完全线程安全的 `set_enabled`, `set_text` 等方法。可以从任何工作线程安全地更新UI上的按钮状态，而无需手动处理信号槽。
- **自动化样式管理**: 能够为按钮定义禁用状态下的样式，并根据按钮的 `enabled` 状态自动应用或移除，同时保留其原始样式。
- **聚合信号**: 将多个按钮的信号（如 `clicked`, `enabled_changed`）聚合到管理器层面，方便在单一位置统一处理事件。
- **高级UI效果**: 内置了点击防抖（debounce）和鼠标悬停透明度动画等常用UI效果。
- **精细的焦点控制**: 提供了多种策略来处理按钮在禁用/启用时窗口的焦点变化，解决了在复杂表单中焦点意外跳转的问题，显著提升用户体验。
- **生命周期安全**: 通过事件过滤器和弱引用感知按钮的生命周期，能正确处理按钮被销毁时的资源清理。

### 初始化

**`__init__(self, buttons=None, parent: Optional[QObject] = None, auto_style: bool = False)`**

构造函数初始化管理器，注册传入的按钮，并可以立即为它们启用自动样式功能。

- **实现细节**:
  1.  构造函数接收一个按钮列表（或单个按钮）。它会遍历这个列表，并为每个按钮调用 `add_button` 方法。
  2.  `add_button` 方法是核心的注册逻辑。它首先确定按钮的唯一名称（优先使用 `objectName`，其次是 `text`）。如果名称为空，则会记录一个错误并跳过。
  3.  它将按钮对象和其相关数据（如原始样式表、禁用样式表、是否启用自动样式等）存储在一系列内部字典中，以按钮名称为键。
  4.  通过 `__setup_internal_connections` 为按钮安装事件过滤器和连接内部槽函数，用于捕获点击、状态变化等原生事件。
  5.  如果 `auto_style` 参数为 `True`，它会立即为该按钮启用自动样式管理。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `buttons` | `QPushButton` \| `List[QPushButton]`, optional | 一个或多个要管理的 `QPushButton` 实例。 |
| `parent` | `QObject`, optional | Qt的父对象，用于对象树管理。 |
| `auto_style` | `bool` | 是否为所有传入的按钮默认启用自动样式切换功能。默认为 `False`。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton

# 假设 QPushButtonManager 已导入
# from global_tools.ui_tools.compnonent.qpush_button import QPushButtonManager

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QPushButtonManager Demo")
        
        # 创建按钮并设置objectName
        self.submit_button = QPushButton("提交")
        self.submit_button.setObjectName("submitBtn")
        self.submit_button.setStyleSheet("background-color: lightblue;")
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setObjectName("cancelBtn")
        
        self.reset_button = QPushButton("重置")
        self.reset_button.setObjectName("resetBtn")
        self.reset_button.setEnabled(False) # 初始为禁用

        layout = QVBoxLayout(self)
        layout.addWidget(self.submit_button)
        layout.addWidget(self.cancel_button)
        layout.addWidget(self.reset_button)

        # 初始化管理器，并为所有按钮启用自动样式
        self.manager = QPushButtonManager(
            [self.submit_button, self.cancel_button, self.reset_button], 
            parent=self, 
            auto_style=True
        )
        
        # 为所有按钮设置一个统一的禁用样式
        disabled_style = "QPushButton:disabled { background-color: #d3d3d3; color: #888; border: 1px solid #aaa; }"
        for name in self.manager.get_all_button_names():
            self.manager.set_disabled_style(name, disabled_style)

        # 连接聚合的点击信号
        self.manager.button_clicked.connect(self.on_any_button_clicked)

    def on_any_button_clicked(self, button_name, is_checked):
        print(f"信号捕获: 按钮 '{button_name}' 被点击了。")
        if button_name == "submitBtn":
            # 提交后禁用自己和取消按钮，并启用重置按钮
            # 由于开启了 auto_style，按钮会自动变灰
            self.manager.set_enabled("submitBtn", False)
            self.manager.set_enabled("cancelBtn", False)
            self.manager.set_enabled("resetBtn", True)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())
```

### 信号

- **`button_clicked(str, bool)`**: 当任何一个被管理的按钮被点击时发出。
  - **参数**: `button_name` (按钮的名称), `checked` (按钮的选中状态)。
- **`enabled_changed(str, bool)`**: 当按钮的启用/禁用状态改变时发出。
  - **参数**: `button_name`, `enabled` (新的启用状态)。
- **`visibility_changed(str, bool)`**: 当按钮的可见性改变时发出。
  - **参数**: `button_name`, `visible` (新的可见状态)。
- **`text_changed(str, str)`**: 当按钮的文本通过`set_text`改变时发出。
  - **参数**: `button_name`, `text` (新的文本)。
- **`tooltip_changed(str, str)`**: 当按钮的工具提示通过`set_tooltip`改变时发出。
  - **参数**: `button_name`, `tooltip` (新的提示)。
- **`icon_changed(str)`**: 当按钮的图标通过`set_icon`改变时发出。
  - **参数**: `button_name`。
- **`checkable_changed(str, bool)`**: 当按钮的可选中状态 (`checkable`) 改变时发出。
  - **参数**: `button_name`, `checkable`。
- **`checked_changed(str, bool)`**: 当可选中按钮的选中状态改变时发出。
  - **参数**: `button_name`, `checked`。
- **`all_enabled_changed(bool, int)`**: 当使用 `set_all_enabled` 批量设置状态后发出。
  - **参数**: `enabled` (设置的状态), `count` (成功操作的按钮数量)。

### 方法

#### 按钮管理
- **`add_button(button: QPushButton, auto_style: bool = False) -> bool`**: 向管理器中添加一个新的按钮。
  - **参数**:
    - `button` (`QPushButton`): 要添加的 `QPushButton` 实例。
    - `auto_style` (`bool`): 是否为该按钮启用自动样式，默认为 `False`。
  - **返回**: `bool` - 添加成功返回 `True`；如果传入的不是 `QPushButton` 或按钮没有有效名称，则返回 `False`。
- **`remove_button(button_name: str) -> bool`**: 从管理器中移除一个按钮，并清理其相关的资源。
  - **参数**:
    - `button_name` (`str`): 要移除的按钮的名称 (`objectName` 或文本)。
  - **返回**: `bool` - 移除成功返回 `True`；如果按钮不存在，则返回 `False`。
- **`has_button(button_name: str) -> bool`**: 检查是否存在指定名称的按钮。
  - **参数**:
    - `button_name` (`str`): 要检查的按钮的名称。
  - **返回**: `bool` - 如果按钮存在，返回 `True`，否则返回 `False`。
- **`get_button(button_name: str) -> Optional[QPushButton]`**: 获取指定名称的原始 `QPushButton` 对象。
  - **参数**:
    - `button_name` (`str`): 要获取的按钮的名称。
  - **返回**: `Optional[QPushButton]` - 如果按钮存在，返回其 `QPushButton` 实例；否则返回 `None`。
- **`get_all_button_names() -> List[str]`**: 获取所有被管理的按钮的名称列表。
  - **返回**: `List[str]` - 包含所有按钮名称的字符串列表。
- **`get_all_buttons() -> Dict[str, QPushButton]`**: 获取所有按钮的名称到对象的字典副本。
  - **返回**: `Dict[str, QPushButton]` - 键为按钮名称、值为 `QPushButton` 实例的字典。

#### 属性设置 (线程安全)
- **`set_text(button_name: str, text: str) -> bool`**: (线程安全) 设置指定按钮的文本。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `text` (`str`): 要设置的新文本。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_tooltip(button_name: str, text: str) -> bool`**: (线程安全) 设置指定按钮的工具提示。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `text` (`str`): 要设置的工具提示文本。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_icon(button_name: str, icon: QIcon, size: Optional[QSize] = None) -> bool`**: (线程安全) 设置指定按钮的图标。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `icon` (`QIcon`): 要设置的 `QIcon` 对象。
    - `size` (`Optional[QSize]`): 可选参数，用于设置图标的尺寸。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_enabled(button_name: str, enabled: bool) -> bool`**: (线程安全) 设置单个按钮的启用/禁用状态。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `enabled` (`bool`): `True` 为启用，`False` 为禁用。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_all_enabled(enabled: bool) -> Dict[str, bool]`**: (线程安全) 批量设置所有受管按钮的启用/禁用状态。
  - **参数**:
    - `enabled` (`bool`): `True` 为启用，`False` 为禁用。
  - **返回**: `Dict[str, bool]` - 返回一个结果字典，键为按钮名称，值为该按钮操作是否成功。
- **`set_visible(button_name: str, visible: bool) -> bool`**: (线程安全) 设置按钮的可见性。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `visible` (`bool`): `True` 为可见，`False` 为隐藏。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_all_visible(visible: bool) -> Dict[str, bool]`**: (线程安全) 批量设置所有按钮的可见性。
  - **参数**:
    - `visible` (`bool`): `True` 为可见，`False` 为隐藏。
  - **返回**: `Dict[str, bool]` - 返回一个结果字典，键为按钮名称，值为该按钮操作是否成功。
- **`set_checkable(button_name: str, checkable: bool) -> bool`**: (线程安全) 设置按钮是否可作为切换按钮。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `checkable` (`bool`): `True` 使按钮可选中，`False` 则不可。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`set_checked(button_name: str, checked: bool) -> bool`**: (线程安全) 设置切换按钮的选中状态。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `checked` (`bool`): `True` 为选中状态，`False` 为未选中。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。

#### 状态查询
- **`is_enabled(button_name: str) -> Optional[bool]`**: 检查特定按钮当前是否为启用状态。
  - **参数**:
    - `button_name` (`str`): 要查询的按钮名称。
  - **返回**: `Optional[bool]` - 按钮启用返回 `True`，禁用返回 `False`，不存在返回 `None`。
- **`is_visible(button_name: str) -> Optional[bool]`**: 检查特定按钮是否可见。
  - **参数**:
    - `button_name` (`str`): 要查询的按钮名称。
  - **返回**: `Optional[bool]` - 按钮可见返回 `True`，不可见返回 `False`，不存在返回 `None`。
- **`is_checkable(button_name: str) -> Optional[bool]`**: 检查特定按钮是否可选中。
  - **参数**:
    - `button_name` (`str`): 要查询的按钮名称。
  - **返回**: `Optional[bool]` - 按钮可选中返回 `True`，不可选中返回 `False`，不存在返回 `None`。
- **`is_checked(button_name: str) -> Optional[bool]`**: 检查特定按钮是否被选中。
  - **参数**:
    - `button_name` (`str`): 要查询的按钮名称。
  - **返回**: `Optional[bool]` - 按钮被选中返回 `True`，未选中返回 `False`，不存在返回 `None`。

#### 样式与效果 (线程安全)
- **`enable_auto_style(button_name: str, enable: bool = True) -> bool`**: (线程安全) 启用/禁用单个按钮的自动样式功能。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `enable` (`bool`): `True` 为启用，`False` 为禁用。默认为 `True`。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`is_auto_style_enabled(button_name: str) -> Optional[bool]`**: 检查是否为按钮启用了自动样式。
  - **参数**:
    - `button_name` (`str`): 要查询的按钮名称。
  - **返回**: `Optional[bool]` - 已启用返回 `True`，未启用返回 `False`，按钮不存在返回 `None`。
- **`enable_all_auto_style(enable: bool = True) -> Dict[str, bool]`**: (线程安全) 为所有按钮启用或禁用自动样式。
  - **参数**:
    - `enable` (`bool`): `True` 为启用，`False` 为禁用。默认为 `True`。
  - **返回**: `Dict[str, bool]` - 返回一个结果字典，键为按钮名称，值为该按钮操作是否成功。
- **`set_disabled_style(button_name: str, style_sheet: str) -> bool`**: (线程安全) 为按钮设置一个自定义的禁用样式。此样式会在按钮被禁用且自动样式开启时应用。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `style_sheet` (`str`): 符合Qt CSS语法的样式表字符串。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`reset_to_original_style(button_name: str) -> bool`**: (线程安全) 强制将按钮的样式恢复到它被添加到管理器之前的原始样式。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`add_hover_opacity_effect(button_name: str, start_opacity: float = 1.0, end_opacity: float = 0.7, duration: int = 150) -> bool`**: (线程安全) 为按钮添加悬停时改变透明度的动画效果。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `start_opacity` (`float`): 正常状态下的不透明度 (0.0-1.0)。
    - `end_opacity` (`float`): 鼠标悬停时的不透明度 (0.0-1.0)。
    - `duration` (`int`): 动画持续时间，单位为毫秒。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。
- **`enable_debounce(button_name: str, interval_ms: int = 200) -> bool`**: (线程安全) 为按钮的点击事件启用防抖。在指定的时间间隔内，连续点击只会被识别为一次。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `interval_ms` (`int`): 防抖间隔，单位为毫秒。设置为0可禁用。
  - **返回**: `bool` - 操作成功（信号已发射）返回 `True`，按钮不存在则返回 `False`。

#### 焦点控制 (线程安全)
- **`set_preserve_focus(preserve: bool) -> None`**: (线程安全) 设置是否在启用/禁用按钮时保留当前窗口的焦点。这可以防止在禁用一个按钮后，焦点意外地跳到另一个控件上。
  - **参数**:
    - `preserve` (`bool`): `True` 启用焦点保持，`False` 禁用。
- **`is_preserve_focus_enabled() -> bool`**: 检查是否启用了焦点保持功能。
  - **返回**: `bool` - 如果已启用，返回 `True`，否则返回 `False`。
- **`set_focus_to_parent_on_disable(enable: bool) -> None`**: (线程安全) 设置在禁用按钮时，是否自动将焦点转移到其直接的父容器。
  - **参数**:
    - `enable` (`bool`): `True` 启用焦点转移，`False` 禁用。
- **`is_focus_to_parent_enabled() -> bool`**: 检查是否启用了禁用时焦点转移到父容器的功能。
  - **返回**: `bool` - 如果已启用，返回 `True`，否则返回 `False`。
- **`set_parent_focus_policy(policy: Qt.FocusPolicy) -> None`**: (线程安全) 设置当焦点需要转移到父容器时，父容器临时采用的焦点策略。
  - **参数**:
    - `policy` (`Qt.FocusPolicy`): 要设置的Qt焦点策略，如 `Qt.StrongFocus`。

#### 信号连接
- **`connect_clicked_signal(button_name: str, slot: Callable[[], Any], connection_type: Qt.ConnectionType = Qt.AutoConnection) -> bool`**: 连接指定按钮的 `clicked` 信号到槽函数。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `slot` (`Callable`): 要连接的槽函数。
    - `connection_type` (`Qt.ConnectionType`): Qt连接类型，如 `Qt.DirectConnection` 或 `Qt.QueuedConnection`。
  - **返回**: `bool` - 连接成功返回 `True`，失败（如按钮不存在或连接类型错误）返回 `False`。
- **`disconnect_clicked_signal(button_name: str, slot: Optional[Callable[[], Any]] = None) -> bool`**: 断开指定按钮的 `clicked` 信号连接。如果`slot`为`None`，则断开所有连接。
  - **参数**:
    - `button_name` (`str`): 目标按钮的名称。
    - `slot` (`Optional[Callable]`): 要断开的特定槽函数。如果为 `None`，则断开该按钮 `clicked` 信号的所有连接。
  - **返回**: `bool` - 断开成功返回 `True`，失败（如连接不存在）返回 `False`。

### 深度解析与设计哲学

- **非侵入式增强 (事件过滤器)**:
  - `QPushButtonManager` 的核心设计之一是它**不要求**开发者使用自定义的按钮子类。它可以作用于任何标准的 `QPushButton` 对象。这是通过Qt的**事件过滤器 (`installEventFilter`)**机制实现的。
  - 管理器为它所管理的每一个按钮都安装了自己作为事件过滤器。这使得管理器可以"拦截"并响应这些按钮的各种事件（如 `EnabledChange`, `Enter`, `Leave`），而无需修改按钮自身的代码。这是一种典型的**装饰器模式**应用，允许在不改变对象自身的情况下，动态地给对象添加额外的职责，具有极高的灵活性和极低的侵入性。

- **线程安全的UI更新模型**:
  - `QPushButtonManager` 的所有公共修改方法（如 `set_text`, `set_enabled` 等）都被设计为线程安全的。这意味着你可以从任何后台线程直接调用这些方法来更新UI，而无需担心线程冲突。
  - **实现原理**: 其内部使用了一套私有的信号/槽机制。当一个公共方法被调用时，它并不直接操作`QPushButton`控件，而是发射一个携带了操作信息的内部信号（例如，`__set_enabled_signal`）。这些内部信号被连接到在主GUI线程中执行的私有槽函数上（例如，`__on_set_enabled`）。由于Qt的跨线程信号默认使用`QueuedConnection`，这保证了所有对UI控件的实际修改都发生在了正确的线程中，从而避免了程序崩溃。这个模型是构建响应式、无卡顿、多线程GUI应用的基石。

- **精细化的焦点管理**:
  - 在复杂的UI（尤其是表单和向导界面）中，控件的焦点行为对用户体验至关重要。一个控件被禁用后，焦点会自动跳转到窗口中的下一个可用控件，这种默认行为有时会显得突兀或不合逻辑。
  - `QPushButtonManager` 提供 `set_preserve_focus` 和 `set_focus_to_parent_on_disable` 方法，给予开发者对焦点行为的精确控制。
    - **`set_preserve_focus`**: 通过在操作前后保存和恢复焦点，防止了因按钮状态改变而丢失当前输入焦点（例如，用户正在一个 `QLineEdit` 中输入时）。
    - **`set_focus_to_parent_on_disable`**: 在禁用按钮后，主动将焦点移至其父容器，这通常意味着激活了该容器内的下一个可聚焦控件，在逻辑上更连贯。它甚至能临时更改父容器的 `focusPolicy`，以确保焦点可以被设置。
  - 提供这些看似微小但实则重要的API，体现了组件设计的成熟度——它不仅考虑了核心功能，还深入到了提升用户体验的细节层面。

- **可叠加的样式管理**:
  - 当自动样式功能 (`enable_auto_style`) 开启时，管理器在禁用一个按钮时，并不会粗暴地替换掉整个样式表。相反，它会将开发者设置的`disabled_style`与按钮的`original_style`**拼接**起来。
  - 这种策略的优势在于，它允许禁用样式只覆盖必要的部分（如背景色和文字颜色），而按钮原有的其他样式（如字体、边框圆角、padding等）仍然可以保留。这使得样式管理更加灵活和可预测。

---

## `QLabelManager` - 高级标签与动画控制器

### 概述

`QLabelManager` 是一个专门用于集中管理一个或多个 `QLabel` 控件的强大工具。它通过标签的 `objectName` 作为唯一标识符，不仅提供了对标签属性（如文本、样式、可见性）的线程安全访问，还额外实现了标准`QLabel`所不具备的功能，如点击事件响应和内置的淡入淡出动画效果。

核心功能包括：
- **批量管理**: 通过构造函数或 `add_label` 方法，轻松管理一组 `QLabel`。
- **线程安全的UI更新**: 所有修改 `QLabel` 状态的公共方法（如 `set_text`, `set_visible`）都是线程安全的，允许从工作线程直接、安全地更新UI。
- **点击事件支持**: 通过内部的事件过滤器，使得 `QLabel` 可以像按钮一样响应鼠标点击，并发出 `label_clicked` 信号。
- **内置动画效果**: 提供了一个开箱即用的 `start_fade_animation` 方法，可以为标签应用平滑的淡出再淡入效果，用于状态更新时的视觉反馈。
- **自动生命周期管理**: 能够检测到已被删除的 `QLabel` 控件，并自动将其从管理器中清理，防止内存泄漏和悬空引用。

### 初始化

**`__init__(self, *labels: QLabel, parent: Optional[QObject] = None)`**

构造函数初始化管理器，并注册所有传入的 `QLabel` 控件。

- **实现细节**:
  1.  构造函数接收任意数量的 `QLabel` 对象作为参数 (`*labels`)。
  2.  它会遍历这些 `QLabel`，并调用内部的 `__add_label` 方法进行注册。
  3.  注册过程强制要求每个 `QLabel` 都必须有一个**唯一**的 `objectName`，否则该标签将被跳过。这是管理器识别和操作特定标签的唯一依据。
  4.  管理器会为每个成功注册的标签安装一个共享的 `LabelEventFilter`，用于捕获鼠标点击事件。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `*labels` | `QLabel` | 一个或多个要管理的 `QLabel` 实例。 |
| `parent` | `QObject`, optional | Qt的父对象，用于对象树管理。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

# 假设 QLabelManager 已导入
# from global_tools.ui_tools.compnonent.qlabel import QLabelManager

class StatusPanel(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QLabelManager Demo")
        
        # 创建标签并设置objectName
        self.status_label = QLabel("一切正常")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setStyleSheet("color: green;")
        
        self.warning_label = QLabel("点击查看警告")
        self.warning_label.setObjectName("warningLabel")
        self.warning_label.setAlignment(Qt.AlignCenter)

        layout = QVBoxLayout(self)
        layout.addWidget(self.status_label)
        layout.addWidget(self.warning_label)

        # 初始化管理器
        self.manager = QLabelManager(self.status_label, self.warning_label, parent=self)
        
        # 连接信号
        self.manager.label_clicked.connect(self.on_label_clicked)
        self.manager.text_changed.connect(self.on_text_changed)

    def on_label_clicked(self, label_name):
        if label_name == "warningLabel":
            # 模拟一个操作，并更新状态标签
            self.manager.set_text("statusLabel", "处理警告中...")
            self.manager.set_stylesheet("statusLabel", "color: orange;")
            # 为状态标签应用动画，提供视觉反馈
            self.manager.start_fade_animation("statusLabel", duration_ms=100)

    def on_text_changed(self, label_name, new_text):
        print(f"标签 '{label_name}' 的文本已更新为: '{new_text}'")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    panel = StatusPanel()
    panel.show()
    sys.exit(app.exec_())
```

### 信号

- **`text_changed(label_name: str, new_text: str)`**: 当标签文本通过 `set_text` 成功改变时发出。
- **`visibility_changed(label_name: str, is_visible: bool)`**: 当标签可见性通过 `set_visible` 成功改变时发出。
- **`stylesheet_changed(label_name: str, new_stylesheet: str)`**: 当标签样式表通过 `set_stylesheet` 成功改变时发出。
- **`alignment_changed(label_name: str, new_alignment: Qt.Alignment)`**: 当标签对齐方式通过 `set_alignment` 成功改变时发出。
- **`label_clicked(label_name: str)`**: 当任何一个被管理的、可点击的标签被点击时发出。

### 方法

#### 标签管理

- **`add_label(self, label: QLabel) -> bool`**: 添加一个新的 `QLabel` 到管理器中。
  - **参数**:
    - `label` (`QLabel`): 要添加的 `QLabel` 实例，必须有唯一的 `objectName`。
  - **返回**: `bool` - 添加成功返回 `True`，否则（如`objectName`缺失或重复）返回 `False`。

- **`remove_label(self, label_name: str) -> bool`**: 从管理器中显式移除一个 `QLabel`。
  - **参数**:
    - `label_name` (`str`): 要移除的 `QLabel` 的 `objectName`。
  - **返回**: `bool` - 移除成功返回 `True`，如果标签不存在则返回 `False`。

- **`get_label(self, label_name: str) -> Optional[QLabel]`**: 通过标签名称获取 `QLabel` 实例。
  - **参数**:
    - `label_name` (`str`): `QLabel` 的 `objectName`。
  - **返回**: `Optional[QLabel]` - 找到的 `QLabel` 实例，如果未找到或已被删除则返回 `None`。

- **`has_label(self, label_name: str) -> bool`**: 判断是否存在指定名称的标签。
  - **参数**:
    - `label_name` (`str`): `QLabel` 的 `objectName`。
  - **返回**: `bool` - 如果标签存在且有效返回 `True`，否则返回 `False`。
  
- **`get_all_label_names(self) -> List[str]`**: 获取所有标签的名称列表。
  - **返回**: `List[str]` - 一个包含所有当前有效标签名称的列表。

#### 属性设置 (线程安全)

- **`set_text(self, label_name: str, text: str) -> bool`**: 设置指定标签的文本。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
    - `text` (`str`): 要设置的新文本。
  - **返回**: `bool` - 如果请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`set_visible(self, label_name: str, visible: bool) -> bool`**: 设置指定标签的可见性。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
    - `visible` (`bool`): `True` 表示可见，`False` 表示隐藏。
  - **返回**: `bool` - 如果请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`set_stylesheet(self, label_name: str, stylesheet: str) -> bool`**: 设置指定标签的样式表。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
    - `stylesheet` (`str`): 要设置的样式表字符串。
  - **返回**: `bool` - 如果请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`set_alignment(self, label_name: str, alignment: Qt.Alignment) -> bool`**: 设置指定标签的对齐方式。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
    - `alignment` (`Qt.Alignment`): 要设置的对齐方式，如 `Qt.AlignCenter`。
  - **返回**: `bool` - 如果请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`clear_text(self, label_name: str) -> bool`**: 清空指定标签的文本。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
  - **返回**: `bool` - 如果请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`clear_all_texts(self) -> None`**: 清空所有被管理的标签的文本。

#### 状态查询

- **`get_text(self, label_name: str) -> Optional[str]`**: 获取指定标签的文本内容。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
  - **返回**: `Optional[str]` - 标签的文本，如果标签不存在则返回 `None`。

- **`is_visible(self, label_name: str) -> Optional[bool]`**: 获取指定标签的可见性状态。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
  - **返回**: `Optional[bool]` - 如果标签可见返回 `True`，不可见返回 `False`，不存在返回 `None`。

- **`get_stylesheet(self, label_name: str) -> Optional[str]`**: 获取指定标签的样式表。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
  - **返回**: `Optional[str]` - 标签的样式表，如果标签不存在则返回 `None`。

- **`get_alignment(self, label_name: str) -> Optional[Qt.Alignment]`**: 获取指定标签的对齐方式。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
  - **返回**: `Optional[Qt.Alignment]` - 标签的对齐方式，如果标签不存在则返回 `None`。

#### 动画与清理

- **`start_fade_animation(self, label_name: str, duration_ms: Optional[int] = None) -> bool`**: 为指定标签启动一个淡出再淡入的动画。
  - **参数**:
    - `label_name` (`str`): 目标标签的名称。
    - `duration_ms` (`Optional[int]`): 动画单程（淡出或淡入）的持续时间（毫秒）。如果为 `None`，则使用默认值。
  - **返回**: `bool` - 如果动画请求已发送，返回 `True`；如果标签不存在，返回 `False`。

- **`cleanup(self) -> None`**: 清理管理器，停止所有动画并释放资源。

### 深度解析与设计哲学

- **可点击的QLabel (事件过滤器)**:
  - 标准的 `QLabel` 控件没有 `clicked` 信号。`QLabelManager` 通过为其管理的每个 `QLabel` 安装一个事件过滤器 (`LabelEventFilter`) 来解决这个问题。
  - 这个过滤器专门监听 `mousePressEvent` 事件。当事件发生时，它会检查事件源（即被点击的 `QLabel`），并发出一个携带该标签 `objectName` 的自定义信号 (`label_clicked`)。管理器自身再连接这个信号，从而将点击事件暴露给最终用户。这是一种非常轻量且非侵入式的方式，用于为现有控件添加新行为。

- **线程安全的UI更新模型**:
  - 与 `QPushButtonManager` 类似，`QLabelManager` 的所有公共修改方法（如 `set_text`, `set_visible` 等）都被设计为线程安全的。
  - **实现原理**: 当一个公共方法被调用时，它并不直接操作`QLabel`控件，而是发射一个内部信号（例如，`__update_text_signal`）。这些信号被连接到在主GUI线程中执行的私有槽函数上（例如，`__on_update_text`）。Qt的事件循环确保了所有对UI控件的实际修改都发生在了正确的线程中，从而避免了线程冲突和程序崩溃。

- **动态动画资源管理**:
  - `QLabelManager` 的动画功能并非在初始化时就为所有标签创建好动画对象，而是在 `start_fade_animation` 首次被调用时**按需创建**。
  - `__get_or_create_animation_group` 方法负责为指定的 `QLabel` 创建并关联一个 `QGraphicsOpacityEffect`（用于控制透明度）和一个 `QSequentialAnimationGroup`（用于组织淡出和淡入动画）。这些对象被存储在 `__animation_data` 字典中以供复用。
  - 当一个标签被移除 (`remove_label`) 或管理器被清理 (`cleanup`) 时，相关的动画对象和效果会通过 `__cleanup_animation_data` 方法被显式地停止和销毁，确保了资源的正确释放。这种动态管理策略既高效又节省内存。

---

## `LineEditManager`

### 概述

`LineEditManager` 是一个用于批量管理多个 `QLineEdit` 控件的强大工具类。在复杂的UI界面（如表单、设置页面）中，通常需要对多个输入框进行统一的验证、样式控制和事件处理。`LineEditManager` 正是为了解决这一问题而设计的，它通过 `objectName` 索引每一个 `QLineEdit`，并提供了一套丰富、集中且线程安全的API。

核心功能：
- **批量管理**: 可以通过构造函数一次性注册任意数量的 `QLineEdit` 控件。
- **线程安全**: 所有修改UI状态的公共方法（如 `set_text`, `set_enabled` 等）都是线程安全的，允许从后台线程更新UI。
- **统一的输入验证**: 为输入框轻松设置多种内置验证器，如整数、浮点数、长度限制、正则表达式、Email格式等。
- **动态样式控制**: 根据输入框的状态（正常、焦点、错误、禁用）自动应用不同的样式表，提供即时的视觉反馈。
- **聚合信号**: 将所有受管 `QLineEdit` 的原生信号（如 `textChanged`, `returnPressed`）聚合到管理器层面，并在信号中注入来源控件的 `objectName`，极大地方便了事件的统一处理。
- **丰富的API**: 提供了对文本、占位符、启用/禁用状态、只读状态、回显模式（密码）等的全面控制。
- **生命周期安全**: 能自动检测并处理已被销毁的 `QLineEdit` 控件，防止程序因悬空引用而崩溃。

### 初始化

**`__init__(self, *line_edits: QLineEdit, parent: Optional[QObject] = None)`**

构造函数接收任意数量的 `QLineEdit` 实例作为参数，并立即将它们注册到管理器中。

- **实现细节**:
  1.  遍历所有传入的 `line_edits`。每个 `QLineEdit` **必须**设置一个唯一的 `objectName`，否则将被忽略。
  2.  对于每个有效的 `QLineEdit`，管理器会为其创建一个内部的 `LineEditControl` 实例（此类封装了对单个输入框的控制逻辑）。
  3.  将 `LineEditControl` 实例存储在一个以 `objectName` 为键的字典中。
  4.  将内部 `LineEditControl` 的信号连接到管理器的聚合信号上，并在连接过程中注入 `objectName`。
  5.  建立一套内部的私有信号/槽连接，用于实现线程安全的UI更新。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `*line_edits` | `QLineEdit` | 一个或多个要管理的 `QLineEdit` 实例。 |
| `parent` | `QObject`, optional | Qt的父对象，用于内存管理。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QFormLayout, QLineEdit
from global_tools.ui_tools.compnonent.qline_edit import LineEditManager # 假设路径

class FormDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LineEditManager Demo")

        # 1. 创建输入框并设置 objectName
        self.username_edit = QLineEdit()
        self.username_edit.setObjectName("username")
        
        self.email_edit = QLineEdit()
        self.email_edit.setObjectName("email")
        
        self.age_edit = QLineEdit()
        self.age_edit.setObjectName("age")

        # 2. 初始化 LineEditManager
        self.manager = LineEditManager(
            self.username_edit, self.email_edit, self.age_edit, 
            parent=self
        )

        # 3. 配置验证器和样式
        self.manager.set_input_validator("username", 'length', min_length=4, max_length=15)
        self.manager.set_input_validator("email", 'email')
        self.manager.set_input_validator("age", 'int', min_value=18, max_value=99)
        
        error_style = "background-color: #fff0f0; border: 1px solid red;"
        for name in self.manager.get_all_names():
            self.manager.set_custom_style(name, error=error_style)
            self.manager.set_placeholder(name, f"请输入 {name}...")

        # 4. 连接信号
        self.manager.validation_changed.connect(self.on_validation_changed)
        self.manager.text_changed.connect(self.on_any_text_changed)

        # --- 布局 ---
        layout = QFormLayout(self)
        layout.addRow("Username:", self.username_edit)
        layout.addRow("Email:", self.email_edit)
        layout.addRow("Age:", self.age_edit)

    def on_validation_changed(self, name, is_valid):
        status = "有效" if is_valid else "无效"
        print(f"信号捕获: 输入框 '{name}' 的输入内容现在是 {status} 的。")
        
    def on_any_text_changed(self, name, text):
        print(f"信号捕获: '{name}' 的文本变为 '{text}'")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = FormDemo()
    window.show()
    sys.exit(app.exec_())

```

### 信号

所有由 `LineEditManager` 发射的信号，其第一个参数总是 `name` (str)，即触发该事件的 `QLineEdit` 控件的 `objectName`。

- **`text_changed(name, text)`**: 当文本内容发生任何变化时发出。
- **`validation_changed(name, valid)`**: 当输入内容的有效性状态根据验证器发生变化时发出。`valid` 是一个布尔值。
- **`focus_in(name)`**: 当输入框获得焦点时发出。
- **`focus_out(name, text)`**: 当输入框失去焦点时发出。
- **`return_pressed(name, text)`**: 当在输入框中按下回车键时发出。
- **`enabled_changed(name, enabled)`**: 当启用/禁用状态改变时发出。
- **`style_changed(name, style)`**: 当样式表改变时发出。
- **`read_only_changed(name, read_only)`**: 当只读状态改变时发出。
- **`echo_mode_changed(name, mode)`**: 当回显模式（如密码模式）改变时发出。

### 方法

#### 控件管理

- **`has_line_edit(name)`**: 检查是否存在指定名称的 `QLineEdit`。返回 `bool`。
- **`get_line_edit(name)`**: 获取原始的 `QLineEdit` 实例，以便进行高级原生操作。返回 `Optional[QLineEdit]`。
- **`get_all_names()`**: 获取所有受管 `QLineEdit` 的 `objectName` 列表。返回 `List[str]`。

#### 数据操作 (线程安全)

- **`get_text(name)`**: 获取指定输入框的当前文本。返回 `Optional[str]`。
- **`set_text(name, text)`**: (线程安全) 设置指定输入框的文本。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `text` | `str` | 要设置的文本内容。 |
- **`clear(name)`**: (线程安全) 清空指定输入框的文本。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
- **`clear_all()`**: (线程安全) 清空所有输入框的文本。
- **`set_placeholder(name, text)`**: (线程安全) 设置指定输入框的占位符文本。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `text` | `str` | 要设置的占位符文本。 |

#### 验证与配置 (线程安全)

- **`set_input_validator(name, validator_type, **kwargs)`**: (线程安全) 为输入框设置验证器。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `validator_type` | `str` | 验证器的类型。支持: `'int'`, `'float'`, `'length'`, `'regexp'`, `'email'`。 |
    | `**kwargs` | - | 对应验证器类型的参数。例如: `min_value`, `max_value` (用于 `'int'`, `'float'`), `min_length`, `max_length` (用于 `'length'`), `pattern` (用于 `'regexp'`)。 |
- **`set_max_length(name, length)`**: (线程安全) 设置最大输入长度。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `length` | `int` | 允许输入的最大字符数。 |
- **`set_echo_mode(name, mode)`**: (线程安全) 设置回显模式。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `mode` | `QLineEdit.EchoMode` | 回显模式，如 `QLineEdit.Normal`, `QLineEdit.Password`, `QLineEdit.NoEcho`。 |
- **`set_read_only(name, read_only)`**: (线程安全) 设置是否只读。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `read_only` | `bool` | `True` 表示设为只读，`False` 表示可编辑。 |

#### 样式与状态 (线程安全)

- **`set_custom_style(name, normal=None, focus=None, error=None, disabled=None)`**: (线程安全) 为不同状态设置自定义样式表。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `normal` | `str`, optional | 正常状态下的样式表。 |
    | `focus` | `str`, optional | 获得焦点时的样式表。 |
    | `error` | `str`, optional | 输入验证失败时的样式表。 |
    | `disabled` | `str`, optional | 禁用状态下的样式表。 |
- **`set_enabled(name, enabled)`**: (线程安全) 设置输入框的启用/禁用状态。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `enabled` | `bool` | `True` 表示启用，`False` 表示禁用。 |

#### 回调函数

- **`set_callback(name, callback)`**: 为文本变化事件设置一个回调函数。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `callback` | `Callable[[str], None]` | 文本变化时调用的函数，接收新的文本作为参数。 |
- **`set_focus_out_callback(name, callback)`**: 为失去焦点事件设置一个回调函数。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `callback` | `Callable[[str], None]` | 失去焦点时调用的函数，接收当前文本作为参数。 |
- **`set_delay_time(name, milliseconds)`**: (线程安全) 设置 `text_changed` 事件的触发延迟（防抖），以避免在快速输入时过于频繁地触发回调。
  - **参数:**
    | 名称 | 类型 | 描述 |
    | --- | --- | --- |
    | `name` | `str` | 目标 `QLineEdit` 的 `objectName`。 |
    | `milliseconds` | `int` | 防抖的延迟时间，单位为毫秒。 |

### 深度解析与设计哲学

- **外观模式 (Facade Pattern)**: `LineEditManager` 是一个典型的外观模式应用。它为一组复杂的子系统（每个 `QLineEdit` 及其对应的 `LineEditControl`）提供了一个简化的、统一的高层接口。开发者无需关心如何单独管理每一个输入框的验证、样式和事件，只需与 `LineEditManager` 这一个中心点交互即可。这降低了代码的复杂度，提高了可维护性。

- **线程安全模型**: `LineEditManager` 通过一套内部的信号/槽机制来保证UI操作的线程安全，其设计思想与 `LogOutput` 和 `QLabelManager` 类似。
  - **请求分发**: 当一个公共的写操作方法（如 `set_text`）被调用时，它并不直接修改 `QLineEdit`，而是发射一个对应的内部信号（如 `__set_text_signal`）。
  - **队列化执行**: 这个内部信号被连接到在主GUI线程中执行的私有槽函数（如 `__on_set_text`）。Qt的事件系统确保了来自其他线程的信号调用会被放入主线程的事件队列中。
  - **安全更新**: 主线程的事件循环会从队列中取出这些请求并执行相应的槽函数，从而保证了所有对UI控件的实际修改都发生在正确的线程中。这个模型是构建响应式、无卡顿、多线程GUI应用的基石。

- **信号聚合与上下文注入**: 这是 `LineEditManager` 提供的核心便利之一。
  - **问题**: 如果手动管理多个输入框，你需要为每个输入框的 `textChanged` 信号分别连接一个槽函数，或者使用 `lambda` 来传递额外信息，这很繁琐。
  - **解决方案**: `LineEditManager` 在内部为你完成了这项工作。它在添加每个控件时，就将其原生信号连接到了一个包装函数上。这个包装函数在转发信号时，会将该控件的 `objectName` 一并作为参数发射出去。
  - **优势**: 开发者最终只需连接管理器层面提供的、带有 `name` 参数的聚合信号，就可以在一个槽函数里处理所有来源的事件，并通过 `name` 参数轻松地进行分支逻辑判断，代码因此变得极为清晰和高效。

- **健壮的生命周期管理**: GUI程序中，控件的销毁是常见的导致崩溃的原因。`LineEditManager` 通过在 `__get_control` 核心方法中始终使用 `sip.isdeleted()` 进行检查，确保了它不会试图访问一个已经被C++后端销毁的 `QLineEdit` 对象。如果检测到对象已删除，它会主动清理内部引用，从而防止了悬空指针和段错误的发生。

---

## `LineEditMemory`

### 概述

`LineEditMemory` 是一个非常实用的工具类，它能自动为 `QLineEdit` 控件添加持久化的输入记忆功能。当用户在一个受其管理的输入框中输入内容后，只要该输入框失去焦点，其内容就会被自动、异步地保存到本地的JSON配置文件中。当应用程序下次启动时，该类会自动加载这些内容并恢复到对应的输入框中，从而极大地提升了用户体验，尤其是在需要频繁填写固定信息的表单场景中。

核心功能：
- **自动保存与加载**: 无需手动调用，自动在失去焦点时保存，在初始化时加载。
- **支持多个控件**: 可以同时管理单个或多个 `QLineEdit` 控件。
- **异步写入**: 保存操作在后台工作线程中执行，完全不会阻塞UI主线程。
- **防抖机制 (Debounce)**: 内置计时器，避免在焦点快速切换时（例如，用户快速用Tab键切换输入框）进行不必要的、频繁的文件写入操作。
- **可配置性**: 配置文件名和路径均可自定义。
- **清晰的日志**: 提供面向用户的操作日志（通过 `LogOutput`）和面向开发者的调试日志。
- **健壮的键生成**: 自动为每个 `QLineEdit` 生成一个唯一的、可读性强的标识键（基于其 `objectName` 和其父控件的 `objectName`），用于在配置文件中存储其值。

### 初始化

**`__init__(self, line_edits, log_output, config_filename="lineEdit_memory.json", ...)`**

构造函数负责初始化管理器，设置配置，并立即加载已保存的配置来填充输入框。

- **实现细节**:
  1.  初始化时，它会为自身创建一个工作线程 `QThread` 和一个 `_LineEditSaveWorker` 对象，并将后者移动到工作线程中。这是实现异步保存的关键。
  2.  它会连接一系列内部信号和槽，用于在主线程和工作线程之间安全地传递数据和指令。
  3.  它会遍历传入的 `line_edits`，为每个控件安装事件过滤器，以捕获 `FocusIn` 和 `FocusOut` 事件。
  4.  调用 `_load_config_and_populate` 方法，该方法会读取JSON配置文件，并根据其中存储的键值对，填充对应 `QLineEdit` 的文本。
  5.  使用 `QTimer` 实现防抖功能。当 `FocusOut` 事件发生时，内容并不会立即保存，而是被添加到一个待处理队列 `__save_queue` 中，并启动防抖计时器。只有当计时器倒计时结束（例如500ms内没有新的保存请求），才会真正触发后台保存。

**参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
| `line_edits` | `QLineEdit` \| `List[QLineEdit]` | 一个或多个要管理的 `QLineEdit` 实例。 |
| `log_output` | `LogOutput` | 一个 `LogOutput` 实例，用于向UI输出用户可见的日志（例如"配置已保存"）。 |
| `config_filename` | `str`, optional | 配置文件的名称。默认为 `"lineEdit_memory.json"`。 |
| `config_path` | `str`, optional | 配置文件所在的目录。默认为 `"GUI/config"`。 |
| `debounce_interval_ms`| `int`, optional | 防抖延迟时间（毫秒）。默认为 `500`。 |
| `parent` | `QObject`, optional | Qt父对象。 |
| `logger` | `logging.Logger`, optional| 日志记录器实例。 |

**使用示例:**
```python
import sys, os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QTextEdit
from global_tools.ui_tools.compnonent.line_edit import LineEditMemory
from global_tools.ui_tools.helper import LogOutput # 假设路径

# 确保配置目录存在
if not os.path.exists("GUI/config"):
    os.makedirs("GUI/config")

class MemoryDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LineEditMemory Demo")
        self.setObjectName("MemoryDemoWindow") # 为父窗口设置名称

        # 创建用于显示日志的QTextEdit
        log_display = QTextEdit()
        log_display.setReadOnly(True)
        # 必须提供一个LogOutput实例
        self.log_output = LogOutput(log_display)

        self.field1 = QLineEdit()
        self.field1.setObjectName("serverAddress")
        self.field1.setPlaceholderText("服务器地址 (输入后失焦试试)")

        self.field2 = QLineEdit()
        self.field2.setObjectName("apiKey")
        self.field2.setPlaceholderText("API Key (关闭再打开窗口看看)")

        layout = QVBoxLayout(self)
        layout.addWidget(self.field1)
        layout.addWidget(self.field2)
        layout.addWidget(log_display)

        # 初始化LineEditMemory
        # 它会自动从 'GUI/config/lineEdit_memory.json' 加载数据
        self.memory = LineEditMemory(
            line_edits=[self.field1, self.field2],
            log_output=self.log_output,
            parent=self
        )

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MemoryDemo()
    window.show()
    sys.exit(app.exec_())
```
*在上面的例子中，当你在 "服务器地址" 输入框中输入 "localhost:8080" 然后用鼠标点击 "API Key" 输入框时，你会看到日志输出 "配置已保存"。如果你关闭并重新运行程序，"localhost:8080" 会自动被加载回第一个输入框。*

### 信号

`LineEditMemory` 主要通过 `LogOutput` 和 `logging` 进行反馈，本身不向外暴露公共信号。

### 方法

#### `add_line_edit(line_edit)`
- **描述:** 在运行时动态地向管理器添加一个新的 `QLineEdit` 控件。添加后，管理器会立即尝试从已加载的配置中为其填充内容。
- **返回:** `bool` - 添加成功返回 `True`，如果控件已存在或添加失败则返回 `False`。

#### `remove_line_edit(line_edit)`
- **描述:** 从管理器中移除一个 `QLineEdit` 控件，并断开相关的事件监听。
- **返回:** `bool` - 移除成功返回 `True`。

#### `cleanup()`
- **描述:** 手动清理资源，尤其是在退出应用程序前确保待保存队列中的内容被写入文件。它会停止工作线程和防抖计时器。
- **注意**: 这个方法在对象被垃圾回收时会自动调用，但在主程序退出时，最好能显式调用以确保数据万无一失。

### 深度解析与设计哲学

- **异步保存与线程模型 (主线程-工作线程)**:
  - `LineEditMemory` 的核心设计目标是在不影响UI响应的前提下，可靠地将用户输入持久化。直接在主GUI线程中读写文件是UI开发的大忌，因为文件I/O操作可能会因为磁盘性能、文件大小等原因产生延迟，导致界面卡顿甚至假死。
  - **模型解析**:
    1.  **主线程 (UI Thread)**: 负责所有UI交互。当`QLineEdit`失去焦点时，主线程的工作非常轻量：仅仅是将需要保存的数据（键和值）放入一个内存中的队列 `__save_queue`，并启动或重置一个`QTimer`。这个过程耗时极短，用户完全无法察觉。
    2.  **工作线程 (`_LineEditSaveWorker`)**: 管理器在初始化时会创建一个`QThread`，并将`_LineEditSaveWorker`对象移动到该线程。这个工作线程独立于主线程运行。
    3.  **安全的任务派发**: 当主线程的`QTimer`倒计时结束，它会发射一个信号。这个信号连接着工作线程中的`_LineEditSaveWorker`的槽函数。Qt的信号槽机制（当连接跨线程时，默认为`QueuedConnection`）确保了这个槽函数的调用请求会被安全地放入工作线程的事件队列中，由工作线程在适当的时候执行。
    4.  **执行I/O**: `_LineEditSaveWorker`在其自己独立的线程中，从队列中取出数据，并执行实际的、可能耗时的文件写入操作（`json.dump`）。
    5.  **安全的回馈**: 保存完成后，工作线程如果需要更新UI（例如通过`LogOutput`显示"配置已保存"），它同样不会直接操作UI控件，而是通过发射一个信号，将结果安全地传回给主线程，由主线程的槽函数来更新界面。
  - **优势**: 这个模型彻底将耗时的I/O操作与UI更新分离开，保证了即使用户的配置文件很大，或者磁盘性能不佳，应用程序的UI界面也能保持绝对的流畅。

- **防抖机制 (Debounce) 的重要性**:
  - 如果没有防抖，当用户使用`Tab`键快速地从一个输入框切换到下一个时，每个`FocusOut`事件都会触发一次保存操作。如果用户连续切换了5个输入框，就会在短时间内触发5次文件写入，这是极大的资源浪费。
  - **`QTimer` 实现**: `LineEditMemory` 使用一个 `QTimer` 来实现防抖。每次`FocusOut`事件发生，它都会**重置**这个计时器。只有当用户停止切换焦点，使得计时器能够成功完成一次完整的倒计时（例如500ms），保存任务才会被真正派发到工作线程。
  - **效果**: 无论用户在短时间内触发了多少次`FocusOut`事件，最终都只会合并成**一次**文件写入操作。这不仅优化了性能，也减少了对物理磁盘的磨损。

- **健壮的键生成策略 (`_get_line_edit_key`)**:
  - 在大型应用中，不同的窗口或对话框很可能包含具有相同`objectName`的`QLineEdit`（例如，多个地方都有一个名为`"name_input"`的输入框）。如果仅用`objectName`作为JSON文件中的键，它们的配置值就会相互覆盖。
  - **父子组合键**: `LineEditMemory` 通过`_get_line_edit_key`方法生成一个更独特的键。它会向上查找`QLineEdit`的父控件，并将父控件的`objectName`与`QLineEdit`自身的`objectName`组合起来，形成如`"SettingsDialog_name_input"`这样的键。
  - **可靠性**: 这种`父控件名_子控件名`的策略，极大地降低了键名冲突的概率，使得`LineEditMemory`在复杂的、多窗口、多层级嵌套的应用中依然能够可靠地、无歧义地为每个输入框存取数据。

---

## `TextEditMemory`

### 概述

`TextEditMemory` 是一个专门为 `QTextEdit` 控件设计的持久化记忆工具类，它能够自动为多行文本框添加输入内容的持久化功能。与 `LineEditMemory` 类似，但专门针对多行文本编辑场景进行了优化。当用户在受管理的 `QTextEdit` 控件中输入内容后，只要该控件失去焦点且内容发生改变，其文本内容就会被自动、异步地保存到本地的JSON配置文件中。当应用程序下次启动时，该类会自动加载这些内容并恢复到对应的文本框中。

核心功能：
- **多行文本支持**: 专门为 `QTextEdit` 控件设计，完美支持多行文本、富文本格式的保存与恢复。
- **智能触发机制**: 支持失焦保存和 Enter 键触发保存（Shift+Enter 仍为正常换行）。
- **自动保存与加载**: 无需手动调用，自动在失去焦点时保存，在初始化时加载。
- **支持多个控件**: 可以同时管理单个或多个 `QTextEdit` 控件，每个控件独立管理。
- **异步写入**: 保存操作在后台工作线程中执行，完全不会阻塞UI主线程。
- **防抖机制 (Debounce)**: 内置计时器，避免在焦点快速切换时进行不必要的、频繁的文件写入操作。
- **可配置性**: 配置文件名、路径、防抖延迟时间均可自定义。
- **清晰的日志**: 提供面向用户的操作日志（通过 `LogOutput`）和面向开发者的调试日志。
- **健壮的键生成**: 自动为每个 `QTextEdit` 生成一个唯一的、可读性强的标识键（基于其 `objectName` 和其父控件的 `objectName`）。
- **动态管理**: 支持在运行时动态添加和移除被管理的控件。

### 初始化

**`__init__(self, text_edits, log_output, config_filename="textEdit_memory.json", ...)`**

构造函数负责初始化管理器，设置配置，并立即加载已保存的配置来填充文本框。

- **实现细节**:
  1.  初始化时，它会为自身创建一个工作线程 `QThread` 和一个 `_TextEditSaveWorker` 对象，并将后者移动到工作线程中。这是实现异步保存的关键。
  2.  它会连接一系列内部信号和槽，用于在主线程和工作线程之间安全地传递数据和指令。
  3.  它会遍历传入的 `text_edits`，为每个控件安装事件过滤器，以捕获 `FocusIn`、`FocusOut` 和 `KeyPress` 事件。
  4.  调用 `_load_config_and_populate` 方法，该方法会读取JSON配置文件，并根据其中存储的键值对，填充对应 `QTextEdit` 的文本。
  5.  使用 `QTimer` 实现防抖功能。当触发保存条件时，内容并不会立即保存，而是被添加到一个待处理队列中，并启动防抖计时器。只有当计时器倒计时结束（默认150ms内没有新的保存请求），才会真正触发后台保存。

**参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
| `text_edits` | `QTextEdit` \| `List[QTextEdit]` | 一个或多个要管理的 `QTextEdit` 实例。 |
| `log_output` | `LogOutput` | 一个 `LogOutput` 实例，用于向UI输出用户可见的日志（例如"配置已保存"）。 |
| `config_filename` | `str`, optional | 配置文件的名称。默认为 `"textEdit_memory.json"`。 |
| `config_path` | `str`, optional | 配置文件所在的目录。默认为 `"GUI/config"`。 |
| `debounce_interval_ms`| `int`, optional | 防抖延迟时间（毫秒）。默认为 `150`。 |
| `parent` | `QObject`, optional | Qt父对象。 |
| `logger` | `Logger`, optional| 日志记录器实例。 |

**使用示例:**
```python
import sys
import logging
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit

# 假设这些是你项目中的工具
# from global_tools.ui_tools import LogOutput
# from global_tools.utils.helper import Logger

class LogOutput: # 示例替代
    def __init__(self, text_widget):
        self.text_widget = text_widget
    def append(self, msg, color="black"):
        # 简单的实现，实际应处理颜色
        self.text_widget.append(msg)
    def append_multi_style(self, parts):
        html = ""
        for text, styles in parts:
            color = styles.get("color", "black")
            weight = "bold" if styles.get("bold") else "normal"
            html += f'<span style="color:{color}; font-weight:{weight};">{text}</span>'
        self.text_widget.insertHtml(html)
        self.text_widget.append("") # 换行

class MyWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QTextEdit Memory Test")
        self.setGeometry(100, 100, 500, 400)
        layout = QVBoxLayout(self)

        # 创建日志显示控件
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        # from global_tools.ui_tools import LogOutput
        self.log_output = LogOutput(self.log_display)

        # 创建输入框
        self.textEdit1 = QTextEdit()
        self.textEdit1.setObjectName("notesField")
        self.textEdit1.setPlaceholderText("写下你的笔记...")

        self.textEdit2 = QTextEdit()
        self.textEdit2.setObjectName("descriptionField")
        self.textEdit2.setPlaceholderText("输入详细描述...")

        layout.addWidget(self.textEdit1)
        layout.addWidget(self.textEdit2)
        layout.addWidget(self.log_display)

        # 为窗口设置 objectName，作为控件父级的标识
        self.setObjectName("MyTestWindow")

        # 实例化 TextEditMemory
        self.memory_handler = TextEditMemory(
            text_edits=[self.textEdit1, self.textEdit2],
            log_output=self.log_output,
            config_filename="my_app_text_edits.json"
        )

    def closeEvent(self, event):
        # 在窗口关闭前，确保所有待处理的更改都已保存
        self.memory_handler.cleanup()
        super().closeEvent(event)

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG,
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    app = QApplication(sys.argv)
    window = MyWindow()
    window.show()
    sys.exit(app.exec_())
```
*在上面的例子中，当你在 "笔记" 文本框中输入多行内容后按 Enter 键或点击其他地方使其失去焦点时，你会看到日志输出 "配置已保存"。如果你关闭并重新运行程序，你的多行文本内容会自动被加载回文本框。*

### 方法

#### `add_text_edit(text_edit)`
- **描述:** 在运行时动态地向管理器添加一个新的 `QTextEdit` 控件。添加后，管理器会立即尝试从已加载的配置中为其填充内容。
- **参数:** `text_edit` (`QTextEdit`) - 要添加的 `QTextEdit` 控件实例。
- **返回:** `bool` - 添加成功返回 `True`，如果控件已存在或添加失败则返回 `False`。
- **使用示例:**
  ```python
  # 动态创建新的文本框
  new_text_edit = QTextEdit()
  new_text_edit.setObjectName("dynamicNotes")

  # 添加到管理器
  if memory_handler.add_text_edit(new_text_edit):
      print("新文本框已添加到记忆管理")
  ```

#### `remove_text_edit(text_edit)`
- **描述:** 从管理器中移除一个 `QTextEdit` 控件，并断开相关的事件监听。移除后，该控件的内容变化将不再被自动保存。
- **参数:** `text_edit` (`QTextEdit`) - 要移除的 `QTextEdit` 控件实例。
- **返回:** `bool` - 移除成功返回 `True`。
- **使用示例:**
  ```python
  # 从管理器中移除文本框
  if memory_handler.remove_text_edit(self.textEdit1):
      print("文本框已从记忆管理中移除")
  ```

#### `cleanup()`
- **描述:** 手动清理资源，尤其是在退出应用程序前确保待保存队列中的内容被写入文件。它会停止工作线程和防抖计时器，并移除所有事件过滤器。
- **注意**: 这个方法在对象被垃圾回收时会自动调用，但在主程序退出时，最好能显式调用以确保数据万无一失。
- **使用示例:**
  ```python
  def closeEvent(self, event):
      # 确保在窗口关闭前保存所有待处理的更改
      self.memory_handler.cleanup()
      super().closeEvent(event)
  ```

### 深度解析与设计哲学

- **多行文本的特殊处理**:
  - 与 `LineEditMemory` 不同，`TextEditMemory` 需要处理多行文本的复杂场景。多行文本编辑器通常包含换行符、可能的富文本格式等，这些都需要在保存和加载时保持完整性。
  - **Enter 键智能处理**: `TextEditMemory` 实现了智能的 Enter 键处理机制。当用户按下 Enter 键时，如果同时按住 Shift 键（Shift+Enter），则执行正常的换行操作；如果只按 Enter 键，则清除焦点并触发保存操作。这种设计既保持了多行编辑的便利性，又提供了快速保存的方式。
  - **文本完整性**: 使用 `toPlainText()` 方法获取纯文本内容，确保保存的是用户实际输入的文本内容，而不是HTML格式的富文本标记。

- **异步保存与线程模型的继承**:
  - `TextEditMemory` 继承了 `LineEditMemory` 的优秀设计理念，同样采用主线程-工作线程的异步保存模型。
  - **工作线程 (`_TextEditSaveWorker`)**: 专门为 `QTextEdit` 设计的保存工作器，能够处理更大的文本内容和更复杂的保存逻辑。
  - **信号槽通信**: 通过 Qt 的信号槽机制实现线程间的安全通信，确保UI更新始终在主线程中执行。

- **防抖机制的优化**:
  - 考虑到多行文本编辑的特点，用户可能会进行更频繁的编辑操作（如连续输入、删除、格式调整等），`TextEditMemory` 的默认防抖延迟时间设置为150ms，比 `LineEditMemory` 更短，以提供更及时的保存响应。
  - **智能队列管理**: 使用 `deque` 数据结构管理待保存的数据队列，支持高效的队列操作。

- **健壮的键生成策略的延续**:
  - 继承了 `LineEditMemory` 的键生成策略，使用 `父控件名_子控件名` 的组合方式生成唯一标识符。
  - **多实例支持**: 在复杂应用中，可能存在多个窗口或对话框都包含文本编辑器，这种键生成策略确保了不同上下文中的文本框能够独立保存和恢复其内容。

- **事件过滤器的增强**:
  - **多事件类型处理**: 除了处理 `FocusIn` 和 `FocusOut` 事件外，还专门处理 `KeyPress` 事件，实现了 Enter 键的智能响应。
  - **精确的修饰键检测**: 通过检查 `event.modifiers() == QtCore.Qt.KeyboardModifier.ShiftModifier` 来精确识别 Shift+Enter 组合键，避免了在某些环境下可能出现的修饰键误判问题。

---

## `TextEditManager`

### 概述

`TextEditManager` 是一个专门为批量管理多个 `QTextEdit` 控件而设计的强大工具类，采用线程安全的单例模式实现。在复杂的应用程序中，经常需要对多个多行文本编辑器进行统一的内容管理、样式控制和状态监控。`TextEditManager` 正是为了解决这一问题而设计的，它通过 `objectName` 索引每一个 `QTextEdit`，并提供了一套完整、集中且线程安全的API。

核心功能：
- **单例模式管理**: 采用线程安全的单例模式，确保全局唯一实例，支持首次创建时传参和后续无参获取。
- **批量控件管理**: 可以通过构造函数一次性注册任意数量的 `QTextEdit` 控件，支持动态添加和移除。
- **线程安全操作**: 所有修改UI状态的公共方法都是线程安全的，通过内部信号槽机制确保UI操作在主线程执行。
- **丰富的文本操作**: 支持纯文本和HTML格式的内容设置、获取、追加、清空等操作。
- **批量操作支持**: 提供批量文本设置、批量状态管理、批量样式设置等高效的批量操作功能。
- **样式和属性管理**: 支持字体设置、样式表应用、启用/禁用状态、只读状态、可见性等全面的属性控制。
- **状态查询和验证**: 提供控件状态查询、存在性验证、统计信息获取等高级功能。
- **聚合信号系统**: 将所有受管 `QTextEdit` 的原生信号聚合到管理器层面，并注入控件名称信息。
- **生命周期安全**: 自动检测并处理已被销毁的 `QTextEdit` 控件，防止程序因悬空引用而崩溃。

### 初始化

**`__init__(self, *text_edits: Union[QTextEdit, List[QTextEdit]], parent: Optional[QObject] = None)`**

构造函数接收任意数量的 `QTextEdit` 实例作为参数，并立即将它们注册到管理器中。由于采用单例模式，首次创建时需要传入控件参数，后续调用可以不传参数。

- **实现细节**:
  1. 采用双重检查锁定模式确保线程安全的单例创建。
  2. 遍历所有传入的 `text_edits`，每个 `QTextEdit` **必须**设置一个唯一的 `objectName`，否则将被忽略。
  3. 为每个有效的 `QTextEdit` 建立内部管理映射，以 `objectName` 为键存储控件引用。
  4. 连接每个控件的原生信号到管理器的聚合信号，并在信号中注入 `objectName` 信息。
  5. 建立一套内部的私有信号/槽连接，用于实现线程安全的UI更新。
  6. 缓存构造参数，支持后续无参数调用和实例重置功能。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `*text_edits` | `QTextEdit` \| `List[QTextEdit]` | 一个或多个要管理的 `QTextEdit` 实例，或包含 `QTextEdit` 的列表。 |
| `parent` | `QObject`, optional | Qt的父对象，用于内存管理。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit
from global_tools.ui_tools.compnonent.qtext_edit import TextEditManager

class TextEditDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TextEditManager Demo")

        # 1. 创建多个文本编辑器并设置 objectName
        self.notes_edit = QTextEdit()
        self.notes_edit.setObjectName("notes")
        self.notes_edit.setPlaceholderText("输入笔记内容...")

        self.description_edit = QTextEdit()
        self.description_edit.setObjectName("description")
        self.description_edit.setPlaceholderText("输入描述信息...")

        self.log_edit = QTextEdit()
        self.log_edit.setObjectName("log")
        self.log_edit.setPlaceholderText("日志输出...")
        self.log_edit.setReadOnly(True)

        # 2. 布局设置
        layout = QVBoxLayout(self)
        layout.addWidget(self.notes_edit)
        layout.addWidget(self.description_edit)
        layout.addWidget(self.log_edit)

        # 3. 创建管理器实例（单例模式）
        # 方式1：直接创建（首次创建）
        self.manager = TextEditManager(self.notes_edit, self.description_edit, self.log_edit)

        # 方式2：使用推荐的类方法创建（首次创建）
        # self.manager = TextEditManager.get_instance(self.notes_edit, self.description_edit, self.log_edit)

        # 方式3：后续获取已创建的实例（无需参数）
        # self.manager = TextEditManager.get_instance()

        # 4. 连接信号
        self.manager.text_changed.connect(self.on_text_changed)
        self.manager.focus_changed.connect(self.on_focus_changed)

        print(f"管理器已创建，管理 {len(self.manager.get_all_names())} 个文本编辑器")

    def on_text_changed(self, name, text):
        print(f"文本编辑器 '{name}' 内容已变化，长度: {len(text)}")

    def on_focus_changed(self, name, has_focus):
        status = "获得" if has_focus else "失去"
        print(f"文本编辑器 '{name}' {status}焦点")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TextEditDemo()
    window.show()
    sys.exit(app.exec_())
```

### 信号

- **`text_changed(str, str)`**
  - **描述:** 当任何一个被管理的 `QTextEdit` 的文本内容发生改变时发出此信号。
  - **参数:**
    - `name` (`str`): 发生变化的 `QTextEdit` 的 `objectName`。
    - `text` (`str`): 当前的纯文本内容。
  - **使用示例:**
    ```python
    def on_text_changed(name, text):
        print(f"文本编辑器 '{name}' 内容变为: {text[:50]}...")

    manager.text_changed.connect(on_text_changed)
    ```

- **`cursor_position_changed(str, int)`**
  - **描述:** 当任何一个被管理的 `QTextEdit` 的光标位置发生改变时发出此信号。
  - **参数:**
    - `name` (`str`): 发生变化的 `QTextEdit` 的 `objectName`。
    - `position` (`int`): 新的光标位置。
  - **使用示例:**
    ```python
    def on_cursor_moved(name, position):
        print(f"文本编辑器 '{name}' 光标移动到位置: {position}")

    manager.cursor_position_changed.connect(on_cursor_moved)
    ```

- **`selection_changed(str, str)`**
  - **描述:** 当任何一个被管理的 `QTextEdit` 的选择内容发生改变时发出此信号。
  - **参数:**
    - `name` (`str`): 发生变化的 `QTextEdit` 的 `objectName`。
    - `selected_text` (`str`): 当前选中的文本内容。
  - **使用示例:**
    ```python
    def on_selection_changed(name, selected_text):
        if selected_text:
            print(f"文本编辑器 '{name}' 选中了: {selected_text}")

    manager.selection_changed.connect(on_selection_changed)
    ```

- **`focus_changed(str, bool)`**
  - **描述:** 当任何一个被管理的 `QTextEdit` 的焦点状态发生改变时发出此信号。
  - **参数:**
    - `name` (`str`): 发生变化的 `QTextEdit` 的 `objectName`。
    - `has_focus` (`bool`): 是否获得焦点。
  - **使用示例:**
    ```python
    def on_focus_changed(name, has_focus):
        status = "获得" if has_focus else "失去"
        print(f"文本编辑器 '{name}' {status}焦点")

    manager.focus_changed.connect(on_focus_changed)
    ```

- **`enabled_changed(str, bool)`**
  - **描述:** 当任何一个被管理的 `QTextEdit` 的启用状态发生改变时发出此信号。
  - **参数:**
    - `name` (`str`): 发生变化的 `QTextEdit` 的 `objectName`。
    - `enabled` (`bool`): 是否启用。

### 方法

#### 单例模式管理方法

**`get_instance(*text_edits, parent=None) -> TextEditManager`** (类方法)
- **描述:** 获取 `TextEditManager` 的单例实例。首次调用时需要传入 `QTextEdit` 控件参数，后续调用可以不传参数。
- **参数:**
  - `*text_edits`: 一个或多个 `QTextEdit` 实例（首次创建时需要）
  - `parent`: Qt父对象（可选）
- **返回:** `TextEditManager` 单例实例
- **使用示例:**
  ```python
  # 首次创建
  manager = TextEditManager.get_instance(edit1, edit2, edit3)

  # 后续获取
  manager = TextEditManager.get_instance()
  ```

**`is_instance_created() -> bool`** (类方法)
- **描述:** 检查单例实例是否已经创建且有效。
- **返回:** `bool` - 如果实例已创建且有效返回 `True`，否则返回 `False`
- **使用示例:**
  ```python
  if TextEditManager.is_instance_created():
      manager = TextEditManager.get_instance()
  else:
      manager = TextEditManager.get_instance(edit1, edit2)
  ```

**`reset_instance() -> None`** (类方法)
- **描述:** 重置单例实例，下次调用 `get_instance` 时将创建新实例。注意这会清除当前实例的所有状态和管理的控件。
- **使用示例:**
  ```python
  # 重置实例
  TextEditManager.reset_instance()

  # 创建新实例
  new_manager = TextEditManager.get_instance(new_edit1, new_edit2)
  ```

**`get_cached_args() -> tuple`** (类方法)
- **描述:** 获取缓存的构造参数。
- **返回:** `tuple` - (args, kwargs) 缓存的参数
- **使用示例:**
  ```python
  args, kwargs = TextEditManager.get_cached_args()
  print(f"缓存了 {len(args)} 个控件参数")
  ```

#### 控件管理方法

**`add_text_edit(text_edit: QTextEdit) -> bool`**
- **描述:** 添加一个 `QTextEdit` 控件到管理器。
- **参数:**
  - `text_edit`: 要添加的 `QTextEdit` 实例
- **返回:** `bool` - 添加成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  new_edit = QTextEdit()
  new_edit.setObjectName("new_notes")
  success = manager.add_text_edit(new_edit)
  ```

**`remove_text_edit(name: str) -> bool`**
- **描述:** 从管理器中移除指定名称的 `QTextEdit` 控件。
- **参数:**
  - `name`: 要移除的 `QTextEdit` 的 `objectName`
- **返回:** `bool` - 移除成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  success = manager.remove_text_edit("old_notes")
  ```

**`get_text_edit(name: str) -> Optional[QTextEdit]`**
- **描述:** 获取指定名称的 `QTextEdit` 控件对象。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `QTextEdit` 对象，如果不存在则返回 `None`
- **使用示例:**
  ```python
  notes_edit = manager.get_text_edit("notes")
  if notes_edit:
      # 直接使用 QTextEdit 的原生方法
      notes_edit.moveCursor(QTextCursor.End)
  ```

**`has_text_edit(name: str) -> bool`**
- **描述:** 检查是否存在指定名称的 `QTextEdit` 控件。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 存在返回 `True`，不存在返回 `False`
- **使用示例:**
  ```python
  if manager.has_text_edit("notes"):
      manager.set_text("notes", "新的笔记内容")
  ```

**`get_all_names() -> List[str]`**
- **描述:** 获取所有被管理的 `QTextEdit` 控件的名称列表。
- **返回:** `List[str]` - 所有控件的 `objectName` 列表
- **使用示例:**
  ```python
  names = manager.get_all_names()
  print(f"管理的控件: {', '.join(names)}")
  ```

**`get_count() -> int`**
- **描述:** 获取当前管理的 `QTextEdit` 控件数量。
- **返回:** `int` - 控件数量
- **使用示例:**
  ```python
  count = manager.get_count()
  print(f"当前管理 {count} 个文本编辑器")
  ```

#### 文本内容操作方法

**`set_text(name: str, text: str) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的纯文本内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `text`: 要设置的文本内容
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 设置笔记内容
  success = manager.set_text("notes", "这是一些重要的笔记内容")
  if success:
      print("文本设置成功")
  ```

**`set_html(name: str, html: str) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的HTML内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `html`: 要设置的HTML内容
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 设置HTML格式的描述
  html_content = "<h1>标题</h1><p>这是<b>粗体</b>文本</p>"
  success = manager.set_html("description", html_content)
  ```

**`get_text(name: str) -> Optional[str]`**
- **描述:** 获取指定 `QTextEdit` 的纯文本内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `str` - 文本内容，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 获取笔记内容
  notes_content = manager.get_text("notes")
  if notes_content:
      print(f"笔记内容: {notes_content}")
  ```

**`get_html(name: str) -> Optional[str]`**
- **描述:** 获取指定 `QTextEdit` 的HTML内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `str` - HTML内容，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 获取HTML格式的描述
  html_content = manager.get_html("description")
  if html_content:
      print(f"HTML内容: {html_content}")
  ```

**`append_text(name: str, text: str) -> bool`**
- **描述:** 向指定 `QTextEdit` 追加纯文本内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `text`: 要追加的文本内容
- **返回:** `bool` - 追加成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 追加日志信息
  success = manager.append_text("log", "\n新的日志条目")
  ```

**`append_html(name: str, html: str) -> bool`**
- **描述:** 向指定 `QTextEdit` 追加HTML内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `html`: 要追加的HTML内容
- **返回:** `bool` - 追加成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 追加格式化的日志
  log_html = "<p style='color: red;'>[ERROR] 发生错误</p>"
  success = manager.append_html("log", log_html)
  ```

**`clear(name: str) -> bool`**
- **描述:** 清空指定 `QTextEdit` 的所有内容。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 清空成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 清空笔记内容
  success = manager.clear("notes")
  ```

**`insert_text(name: str, text: str) -> bool`**
- **描述:** 在指定 `QTextEdit` 的当前光标位置插入文本。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `text`: 要插入的文本内容
- **返回:** `bool` - 插入成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 在光标位置插入文本
  success = manager.insert_text("notes", "插入的文本")
  ```

#### 批量操作方法

**`set_text_batch(text_dict: Dict[str, str]) -> Dict[str, bool]`**
- **描述:** 批量设置多个 `QTextEdit` 的文本内容。
- **参数:**
  - `text_dict`: 字典，键为控件名称，值为要设置的文本内容
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 批量设置文本
  results = manager.set_text_batch({
      "notes": "笔记内容",
      "description": "描述内容",
      "log": "日志内容"
  })
  for name, success in results.items():
      print(f"设置 '{name}': {'成功' if success else '失败'}")
  ```

**`get_text_batch(names: List[str]) -> Dict[str, Optional[str]]`**
- **描述:** 批量获取多个 `QTextEdit` 的文本内容。
- **参数:**
  - `names`: 要获取文本的控件名称列表
- **返回:** `Dict[str, Optional[str]]` - 字典，键为控件名称，值为文本内容
- **使用示例:**
  ```python
  # 批量获取文本
  texts = manager.get_text_batch(["notes", "description"])
  for name, text in texts.items():
      if text:
          print(f"{name}: {text[:50]}...")
  ```

**`get_all_texts() -> Dict[str, str]`**
- **描述:** 获取所有被管理的 `QTextEdit` 的文本内容。
- **返回:** `Dict[str, str]` - 字典，键为控件名称，值为文本内容
- **使用示例:**
  ```python
  # 获取所有文本内容
  all_texts = manager.get_all_texts()
  for name, text in all_texts.items():
      print(f"{name}: {len(text)} 个字符")
  ```

**`clear_all() -> Dict[str, bool]`**
- **描述:** 清空所有被管理的 `QTextEdit` 的内容。
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 清空所有内容
  results = manager.clear_all()
  success_count = sum(1 for success in results.values() if success)
  print(f"成功清空 {success_count} 个文本编辑器")
  ```

**`append_text_batch(text_dict: Dict[str, str]) -> Dict[str, bool]`**
- **描述:** 批量向多个 `QTextEdit` 追加文本内容。
- **参数:**
  - `text_dict`: 字典，键为控件名称，值为要追加的文本内容
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 批量追加文本
  results = manager.append_text_batch({
      "log": "\n[INFO] 系统启动",
      "notes": "\n补充说明"
  })
  ```

**`set_html_batch(html_dict: Dict[str, str]) -> Dict[str, bool]`**
- **描述:** 批量设置多个 `QTextEdit` 的HTML内容。
- **参数:**
  - `html_dict`: 字典，键为控件名称，值为要设置的HTML内容
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 批量设置HTML内容
  html_contents = {
      "description": "<h2>项目描述</h2><p>这是一个<b>重要</b>项目</p>",
      "notes": "<ul><li>要点1</li><li>要点2</li></ul>"
  }
  results = manager.set_html_batch(html_contents)
  ```

#### 样式和属性管理方法

**`set_font(name: str, font: QFont) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的字体。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `font`: `QFont` 对象
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  from PyQt5.QtGui import QFont

  # 设置字体
  font = QFont("Arial", 12)
  font.setBold(True)
  success = manager.set_font("notes", font)
  ```

**`set_style_sheet(name: str, style_sheet: str) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的样式表。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `style_sheet`: CSS样式表字符串
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 设置样式表
  style = """
  QTextEdit {
      background-color: #f0f0f0;
      border: 2px solid #cccccc;
      border-radius: 5px;
      padding: 5px;
  }
  """
  success = manager.set_style_sheet("notes", style)
  ```

**`set_enabled(name: str, enabled: bool) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的启用状态。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `enabled`: 是否启用
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 禁用文本编辑器
  success = manager.set_enabled("notes", False)
  ```

**`set_read_only(name: str, read_only: bool) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的只读状态。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `read_only`: 是否只读
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 设置为只读
  success = manager.set_read_only("log", True)
  ```

**`set_visible(name: str, visible: bool) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的可见性。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `visible`: 是否可见
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 隐藏文本编辑器
  success = manager.set_visible("notes", False)
  ```

**`set_placeholder_text(name: str, placeholder: str) -> bool`**
- **描述:** 设置指定 `QTextEdit` 的占位符文本。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
  - `placeholder`: 占位符文本
- **返回:** `bool` - 设置成功返回 `True`，失败返回 `False`
- **使用示例:**
  ```python
  # 设置占位符文本
  success = manager.set_placeholder_text("notes", "请输入您的笔记...")
  ```

**`set_enabled_batch(enabled_dict: Dict[str, bool]) -> Dict[str, bool]`**
- **描述:** 批量设置多个 `QTextEdit` 的启用状态。
- **参数:**
  - `enabled_dict`: 字典，键为控件名称，值为是否启用
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 批量设置启用状态
  results = manager.set_enabled_batch({
      "notes": True,
      "description": False,
      "log": True
  })
  ```

**`set_read_only_batch(read_only_dict: Dict[str, bool]) -> Dict[str, bool]`**
- **描述:** 批量设置多个 `QTextEdit` 的只读状态。
- **参数:**
  - `read_only_dict`: 字典，键为控件名称，值为是否只读
- **返回:** `Dict[str, bool]` - 字典，键为控件名称，值为操作是否成功
- **使用示例:**
  ```python
  # 批量设置只读状态
  results = manager.set_read_only_batch({
      "log": True,
      "notes": False
  })
  ```

#### 状态查询和验证方法

**`is_enabled(name: str) -> Optional[bool]`**
- **描述:** 检查指定 `QTextEdit` 是否启用。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 启用状态，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 检查启用状态
  enabled = manager.is_enabled("notes")
  if enabled is not None:
      print(f"笔记编辑器启用状态: {enabled}")
  ```

**`is_read_only(name: str) -> Optional[bool]`**
- **描述:** 检查指定 `QTextEdit` 是否为只读状态。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 只读状态，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 检查只读状态
  read_only = manager.is_read_only("log")
  if read_only:
      print("日志编辑器为只读状态")
  ```

**`is_visible(name: str) -> Optional[bool]`**
- **描述:** 检查指定 `QTextEdit` 是否可见。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 可见状态，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 检查可见状态
  visible = manager.is_visible("notes")
  if visible:
      print("笔记编辑器当前可见")
  ```

**`has_focus(name: str) -> Optional[bool]`**
- **描述:** 检查指定 `QTextEdit` 是否拥有焦点。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 焦点状态，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 检查焦点状态
  focused = manager.has_focus("notes")
  if focused:
      print("笔记编辑器当前拥有焦点")
  ```

**`is_empty(name: str) -> Optional[bool]`**
- **描述:** 检查指定 `QTextEdit` 的内容是否为空。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `bool` - 是否为空，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 检查内容是否为空
  empty = manager.is_empty("notes")
  if empty:
      print("笔记编辑器内容为空")
  ```

**`get_text_length(name: str) -> Optional[int]`**
- **描述:** 获取指定 `QTextEdit` 的文本长度。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `int` - 文本长度，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 获取文本长度
  length = manager.get_text_length("notes")
  if length is not None:
      print(f"笔记内容长度: {length} 个字符")
  ```

**`get_cursor_position(name: str) -> Optional[int]`**
- **描述:** 获取指定 `QTextEdit` 的光标位置。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `int` - 光标位置，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 获取光标位置
  position = manager.get_cursor_position("notes")
  if position is not None:
      print(f"光标位置: {position}")
  ```

**`get_selected_text(name: str) -> Optional[str]`**
- **描述:** 获取指定 `QTextEdit` 当前选中的文本。
- **参数:**
  - `name`: `QTextEdit` 的 `objectName`
- **返回:** `str` - 选中的文本，如果控件不存在则返回 `None`
- **使用示例:**
  ```python
  # 获取选中文本
  selected = manager.get_selected_text("notes")
  if selected:
      print(f"选中的文本: {selected}")
  ```

**`get_statistics() -> Dict[str, Any]`**
- **描述:** 获取管理器的统计信息。
- **返回:** `Dict[str, Any]` - 包含各种统计信息的字典
- **使用示例:**
  ```python
  # 获取统计信息
  stats = manager.get_statistics()
  print(f"管理的控件数量: {stats['total_count']}")
  print(f"启用的控件数量: {stats['enabled_count']}")
  print(f"只读控件数量: {stats['read_only_count']}")
  print(f"非空控件数量: {stats['non_empty_count']}")
  ```

### 深度解析与设计哲学

- **单例模式的线程安全实现**:
  - `TextEditManager` 采用了双重检查锁定（Double-Checked Locking）模式来实现线程安全的单例。这种模式在多线程环境下既保证了单例的唯一性，又避免了不必要的同步开销。
  - **实现细节**: 首先进行无锁检查，如果实例不存在才进入同步块，在同步块内再次检查以防止竞态条件。这种设计确保了在高并发场景下的性能和安全性。
  - **参数缓存机制**: 单例模式支持首次创建时传入参数，并将这些参数缓存起来，支持后续的实例重置和重新创建功能。

- **线程安全的UI操作模型**:
  - 继承了 `LineEditManager` 的线程安全设计理念，通过内部信号槽机制确保所有UI操作都在主线程中执行。
  - **信号槽转发**: 当从工作线程调用UI修改方法时，方法会发射内部信号，由主线程的槽函数接收并执行实际的UI操作。
  - **无缝体验**: 对于使用者来说，这个过程是完全透明的，可以从任何线程安全地调用管理器的方法。

- **批量操作的设计优势**:
  - 提供了丰富的批量操作方法，大大提高了处理多个控件时的效率。批量操作不仅减少了代码量，还通过减少信号发射次数来优化性能。
  - **原子性操作**: 批量操作在内部被设计为尽可能的原子性，即使某个控件操作失败，也不会影响其他控件的操作。
  - **详细的返回信息**: 批量操作方法返回详细的操作结果字典，使开发者能够精确了解每个操作的成功或失败状态。

- **控件生命周期管理**:
  - 通过 `sip.isdeleted()` 检查机制，自动检测并处理已被销毁的 `QTextEdit` 控件，防止程序因访问已销毁的对象而崩溃。
  - **自动清理**: 当检测到控件已被销毁时，管理器会自动从内部映射中移除该控件的引用，保持内部状态的一致性。
  - **优雅降级**: 当操作一个已销毁的控件时，方法会优雅地返回失败状态，而不是抛出异常。

- **信号聚合与事件统一处理**:
  - 将所有被管理控件的原生信号聚合到管理器层面，并在信号中注入控件的 `objectName` 信息，极大地简化了事件处理逻辑。
  - **统一的事件接口**: 开发者只需要连接管理器的聚合信号，就能处理所有被管理控件的事件，避免了为每个控件单独连接信号的繁琐工作。
  - **上下文信息**: 聚合信号携带了丰富的上下文信息，使事件处理函数能够准确识别事件来源和相关数据。

---

## `InputCompleter` & `InputCompleterCache`

这两个类协同工作，为 `QLineEdit` 提供了功能极其强大的自动补全和输入历史功能。

### `InputCompleter`

#### 概述

`InputCompleter` 为一个 `QLineEdit` 控件提供了一个高度可定制的、基于当前输入动态过滤的自动完成下拉列表。它解决了原生 `QCompleter` 的一些痛点，提供了更优的UI/UX体验。

核心功能：
- **实时过滤**: 用户输入时，下拉列表会即时显示匹配的补全项。
- **多种触发方式**: 不仅在输入时显示，当输入框获取焦点或内容被清空时，也会智能地显示所有可用补全项。
- **异步数据源**: 支持通过回调函数从数据库、网络或其他耗时操作中异步获取补全列表，不会冻结UI。
- **焦点保持**: 下拉列表本身不会抢占输入框的焦点，用户可以无缝地继续输入。
- **健壮的交互**: 全面支持键盘（上下箭头、回车、ESC）和鼠标交互，并解决了焦点冲突导致的点击失效问题。
- **失焦处理**: 能够捕获用户在输入框失去焦点时未被选择的内容，并通过回调进行处理。

#### `InputCompleterCache`

#### 概述

`InputCompleterCache` 在 `InputCompleter` 的基础上，增加了对用户输入历史的持久化管理。它会自动将用户的输入历史保存到本地JSON文件，并在下次启动时加载，为 `InputCompleter` 提供数据源。

核心功能：
- **历史记录**: 自动记录用户在 `QLineEdit` 中确认的输入。
- **持久化**: 将历史记录保存到本地文件，实现跨会话的输入记忆。
- **自动补全源**: 将加载的历史记录作为 `InputCompleter` 的补全项。
- **数量限制**: 自动管理历史记录的数量，默认最多20条，防止列表无限增长。
- **多实例支持**: 可以同时管理多个 `QLineEdit` 的历史记录，每个输入框有自己独立的记录。

---

### 组合使用示例

`InputCompleter` 和 `InputCompleterCache` 通常组合使用，以达到最佳效果。

```python
import sys, os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QLabel
from global_tools.ui_tools.compnonent.input_completer import InputCompleterCache # 假设路径

# 确保配置目录存在
if not os.path.exists("GUI/config"):
    os.makedirs("GUI/config")

class CompleterDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("InputCompleterCache Demo")
        
        # 创建两个输入框
        self.command_input = QLineEdit()
        self.command_input.setObjectName("commandInput")
        self.command_input.setPlaceholderText("输入命令 (e.g., 'start', 'stop')")

        self.path_input = QLineEdit()
        self.path_input.setObjectName("pathInput")
        self.path_input.setPlaceholderText("输入路径 (e.g., 'C:/Users')")

        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("命令历史输入框:"))
        layout.addWidget(self.command_input)
        layout.addWidget(QLabel("路径历史输入框:"))
        layout.addWidget(self.path_input)

        # 只需要实例化 InputCompleterCache 即可！
        # 它会在内部自动创建和管理 InputCompleter
        self.completer_cache = InputCompleterCache([self.command_input, self.path_input], parent=self)
        
        # (可选) 清空特定输入框的历史记录
        # self.completer_cache.clear_history("commandInput")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = CompleterDemo()
    window.show()
    sys.exit(app.exec_())
```
*在上面的例子中，你在 "命令历史输入框" 中输入 "start" 并按回车，然后输入 "stop" 并按回-车。下次你再点击这个输入框或清空它时，下拉列表中就会出现 "start" 和 "stop" 两个选项。即使关闭程序再打开，这些历史记录依然存在。 "路径历史输入框" 会有自己独立的一套历史记录。*

### `InputCompleter` 核心方法

虽然 `InputCompleterCache` 封装了大部分操作，但了解 `InputCompleter` 的核心方法有助于进行更高级的定制。

#### `__init__(line_edit, completion_items=None, parent=None)`
- **描述**: 初始化一个补全器，并将其附加到指定的 `QLineEdit`。
- **参数**:
  - `line_edit` (`QLineEdit`): 要附加补全器的输入框。
  - `completion_items` (`List[str]`, optional): 一个静态的字符串列表，作为初始的补全项。

#### `set_completion_items(items)`
- **描述**: 动态地更新补全项列表。
- **参数**: `items` (`List[str]`, optional): 新的补全项列表。

#### `set_completion_callback(callback_fn)`
- **描述**: 设置一个回调函数来异步地获取补全项。这会覆盖静态的 `completion_items`。
- **参数**: `callback_fn` (`Callable[[str], List[str]]`): 一个函数，接收当前输入框的文本 `str`，并应返回一个匹配的补全项列表 `List[str]`。
- **实现细节**: 当需要获取补全项时（如用户输入或获得焦点），`InputCompleter` 会创建一个 `__Worker` (基于 `QRunnable`)，在线程池中执行 `callback_fn`。完成后，`__Worker` 通过Qt信号将结果安全地传回主线程，然后由 `__handle_callback_result` 更新UI。

#### `set_focus_lost_callback(callback_fn)`
- **描述**: 设置一个回调函数，用于处理输入框失去焦点时，用户输入但未从下拉列表中选择的文本。
- **参数**: `callback_fn` (`Callable[[List[str]], None]`): 一个函数，接收一个包含所有未被选择的输入内容的列表。

### `InputCompleter` 深度解析

- **UI/UX 设计考量**:
  - `InputCompleter` 的设计核心在于提升用户体验，它解决了原生`QCompleter`的一些痛点：
    - **无缝交互**: 通过精心处理`focusOutEvent`和`mousePressEvent`，解决了"点击补全列表导致输入框失焦，列表消失从而无法选中"的经典问题。`InputCompleter`确保了在用户与补全列表交互时，逻辑上输入框仍然是"活跃"的，使得交互流畅自然。
    - **智能触发**: 除了在用户输入时显示补全，当输入框变为空或首次获得焦点时，它也会主动显示所有可用的历史记录或补全项。这为用户提供了一个即时的上下文提示，让他们知道有哪些可能的输入，而无需先盲打几个字符。
    - **非侵入式焦点**: 补全列表本身被设计为不获取键盘焦点（`setFocusPolicy(Qt.NoFocus)`）。这意味着即使用户打开了补全列表，他们仍然可以继续在`QLineEdit`中无缝地输入、编辑和使用左右方向键，而不会因为焦点被列表抢走而中断输入流程。

- **异步补全实现 (`set_completion_callback`)**:
  - 对于需要从网络、数据库或复杂计算中获取补全数据的场景，同步执行会阻塞UI。`InputCompleter`通过线程池和`QRunnable`实现了异步数据获取。
  - **模型解析**:
    1.  当需要获取补全项时，`InputCompleter`会实例化一个内部的`__Worker`（`QRunnable`的子类），并将用户的当前输入和回调函数传递给它。
    2.  `QRunnable`对象被提交到`QThreadPool.globalInstance()`（全局线程池）中执行。这使得耗时的回调函数在后台线程中运行。
    3.  `__Worker`在后台完成计算后，不会直接更新UI，而是通过一个`pyqtSignal`将结果（补全项列表）发射出去。
    4.  主线程中的`InputCompleter`有一个槽函数连接到此信号。由于信号跨线程，Qt会自动以队列连接方式确保槽函数在主线程中被安全调用。
    5.  主线程的槽函数接收到数据后，再安全地更新UI（即弹出或更新补全列表）。这个流程确保了无论数据源有多慢，UI始终保持响应。

### `InputCompleterCache` 深度解析

- **组合优于继承**:
  - `InputCompleterCache`并没有继承自`InputCompleter`，而是**持有**一个`InputCompleter`的实例。这是一种典型的"组合优于继承"设计模式的应用。
  - **优势**:
    - **灵活性**: `InputCompleter`负责UI交互，`InputCompleterCache`负责数据持久化和管理。两者职责单一。如果未来想用不同的缓存策略（例如LRU缓存）或不同的UI交互方式，可以方便地替换其中一个组件，而不会相互影响。
    - **清晰的API**: `InputCompleterCache`向外暴露的是一个更高级、更简洁的API（基本上是"即插即用"），它隐藏了`InputCompleter`的复杂配置细节。用户只需关心提供`QLineEdit`即可，无需了解其内部是如何创建和管理`InputCompleter`的。

- **历史记录管理策略**:
  - **持久化**: `InputCompleterCache`使用与`LineEditMemory`类似的异步JSON读写机制来持久化用户的输入历史，保证了UI的流畅性。
  - **去重与排序**: 当用户确认一个输入时，该输入项会被添加到历史记录的**顶部**。如果该项已存在于历史记录中，则会先从旧位置移除，再添加到顶部。这确保了最近、最常用的项目总是在补全列表的最上方，符合用户的直觉。
  - **数量限制**: 缓存会自动维护一个最大条目数（默认为20）。当新的历史记录导致总数超出限制时，会自动移除**最末尾**（即最旧、最不常用）的条目。这是一个简单的FIFO（先进先出）策略，有效防止了历史记录无限膨胀。

---

## `ButtonPressEffectEnhancer`

### 概述

`ButtonPressEffectEnhancer` 是一个简单而有效的UI增强类，它能为指定容器内的所有 `QPushButton` 添加一个视觉上的"按下凹陷"效果。当用户点击按钮时，按钮的样式会动态改变，模拟出一个物理按键被按下的感觉，释放后恢复原状。这为用户提供了即时的视觉反馈，提升了应用的交互体验。

核心功能：
- **自动应用**: 只需在初始化时指定一个容器，它会自动查找该容器内的所有 `QPushButton` 并应用效果。
- **非侵入式**: 它通过事件过滤器来监听按钮的按下和释放事件，不会修改按钮原有的样式表，只在按下时临时叠加一个效果样式。
- **动态刷新**: 提供了 `refresh` 方法，可以在UI动态添加新按钮后，重新扫描并为新按钮也应用上效果。
- **生命周期安全**: 会自动处理按钮被销毁时的清理工作。

### 初始化

**`__init__(self, container, parent=None)`**

构造函数接收一个容器控件，并立即开始查找并增强其中的所有按钮。

- **实现细节**:
  1.  构造函数调用 `__find_and_enhance_buttons`，该方法递归地遍历 `container` 的所有子控件。
  2.  当找到一个 `QPushButton` 时，调用 `__setup_button_effect` 为其安装事件过滤器。
  3.  事件过滤器会监听 `MouseButtonPress` 和 `MouseButtonRelease` 事件。
  4.  当 `Press` 事件发生时，调用 `__on_button_pressed`，它会通过 `__create_press_effect` 方法，在按钮现有样式的基础上，添加一个 `border-style: inset;` 的样式，从而产生凹陷效果。
  5.  当 `Release` 事件发生时，调用 `__on_button_released`，它会通过 `__remove_press_effect` 将刚刚添加的内嵌边框样式移除，使按钮恢复原状。

**参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
| `container` | `QWidget` | 任何 `QWidget` 类型的容器，如 `QWidget`, `QFrame`, `QGroupBox`, `QDialog` 等。 |
| `parent` | `QObject`, optional | Qt的父对象。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QGroupBox
from global_tools.ui_tools.compnonent.compnonent import ButtonPressEffectEnhancer # 假设路径

class EnhancerDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ButtonPressEffectEnhancer Demo")
        
        # 创建一些按钮
        button1 = QPushButton("点击我!")
        button2 = QPushButton("还有我!")
        button2.setStyleSheet("background-color: lightblue;") # 它会保留自己的背景色

        # 将按钮放入一个容器
        group = QGroupBox("一组按钮")
        group_layout = QVBoxLayout(group)
        group_layout.addWidget(button1)
        group_layout.addWidget(button2)

        main_layout = QVBoxLayout(self)
        main_layout.addWidget(group)

        # 初始化增强器，传入容器
        # 效果会自动应用到 group 内的所有按钮上
        self.enhancer = ButtonPressEffectEnhancer(group, parent=self)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = EnhancerDemo()
    window.show()
    sys.exit(app.exec_())
```

### 方法

#### `refresh()`
- **描述:** 重新扫描初始化时指定的容器，为任何动态添加的新按钮应用按下效果。这在动态生成UI的场景中非常有用。
- **使用示例:**
  ```python
  # ...在 EnhancerDemo 中添加一个方法...
  def add_new_button(self):
      new_button = QPushButton("我是新来的")
      # self.group_layout 是包含按钮的布局
      self.group_layout.addWidget(new_button)
      
      # 因为按钮是动态添加的，需要调用refresh来为它加上效果
      self.enhancer.refresh()
  ```

### 深度解析与设计哲学

- **非侵入式设计 (`EventFilter`)**:
  - `ButtonPressEffectEnhancer` 的核心价值在于它的"即插即用"特性。开发者无需修改任何现有的`QPushButton`代码或替换成自定义子类，只需将它们的顶层容器交给增强器即可。
  - 这是通过Qt的**事件过滤器 (`eventFilter`)**机制实现的。增强器为容器内的每个按钮安装了一个事件过滤器，使其能够"监听"这些按钮的事件流。当监听到`QEvent.MouseButtonPress`和`QEvent.MouseButtonRelease`事件时，它会执行相应的样式切换逻辑。
  - 这种方法的优点是**完全解耦**。按钮本身并不知道自己被"增强"了，增强器也只关心它需要监听的事件。这使得该工具可以轻松地应用到任何现有项目中，极大地提高了代码的复用性和可维护性。

- **样式叠加逻辑 (`__create_press_effect`)**:
  - 为了在不破坏按钮原有样式的前提下添加"凹陷"效果，增强器采用的是**样式叠加**而非**样式替换**的策略。
  - 当按钮被按下时，`__create_press_effect`方法会首先获取按钮**当前**的样式表（`button.styleSheet()`），这可能包含了用户自定义的背景色、字体等。然后，它将凹陷效果的样式字符串（`"border-style: inset;"`）**附加**到现有样式的末尾。
  - 当按钮被释放时，`__on_button_released`方法会简单地将之前保存的、**不包含**凹陷效果的原始样式表恢复回去。
  - 这个过程确保了无论按钮原本长什么样，按下效果都能正确应用，并且在释放后能精确地恢复原状，不会丢失任何用户自定义的样式。

--- 

## `LogOutput`

### 概述

`LogOutput` 是一个功能强大的日志显示类，它能将程序日志信息以富文本格式优雅地呈现在一个 `QTextEdit` 控件上。它不仅支持设置文本颜色、字体大小、粗体等样式，还提供了自动滚动、时间戳、多样式单行文本等高级功能，是构建带有实时日志窗口的应用的理想选择。

核心功能：
- **富文本日志**: 可以为每条日志指定颜色、大小、加粗、斜体等多种样式。
- **线程安全**: 所有 `append` 方法都是线程安全的，可以从任何线程直接调用来更新UI，内部通过信号槽机制确保UI操作在主线程执行。
- **自动滚动**: 可以开启或关闭自动滚动到底部的功能。当开启时，新的日志会使用户界面始终显示最新的信息。
- **时间戳**: 能够自动为每条日志添加格式化的时间戳。
- **多样式文本**: 支持在单行日志中混合使用多种不同的文本样式。
- **可配置性**: 默认颜色、时间戳颜色、缩进等多种显示效果均可自定义。

### 初始化

**`__init__(self, log_output_widget: QTextEdit)`**

构造函数接收一个 `QTextEdit` 实例，该实例将作为日志的显示目标。

- **实现细节**:
  1.  它将传入的 `QTextEdit` 存储起来，并设置其为只读模式。
  2.  初始化一个 `QMutex` 用于保护多线程访问。
  3.  创建一个内部的 `__LogSignalHandler` (继承自 `QObject`)，并将其移动到一个新的 `QThread` 中。这是实现线程安全的核心，所有来自外部线程的追加日志请求，都会通过Qt信号发送给这个处理器，由它在主线程事件循环中安全地更新 `QTextEdit`。

**参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
| `log_output_widget` | `QTextEdit` | 用于显示日志的 `QTextEdit` 控件。 |

**使用示例:**
```python
import sys, time, threading
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit, QPushButton
from global_tools.ui_tools.helper import LogOutput # 假设路径

class LogDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LogOutput Demo")
        
        self.log_display = QTextEdit()
        # 将QTextEdit交给LogOutput管理
        self.log_output = LogOutput(self.log_display)
        
        self.test_button = QPushButton("从工作线程打印日志")
        self.test_button.clicked.connect(self.start_logging_thread)

        layout = QVBoxLayout(self)
        layout.addWidget(self.log_display)
        layout.addWidget(self.test_button)

        # 直接在主线程打印日志
        self.log_output.append("程序已启动。", color="green")
        self.log_output.append_with_style("这是一条粗体警告！", style={'bold': True, 'color': 'orange'})

    def logging_worker(self):
        for i in range(5):
            # 从非GUI线程安全地追加日志
            self.log_output.append(f"工作线程报告：完成第 {i+1} 步。", color="#3498db")
            time.sleep(0.5)
        self.log_output.append("工作线程任务完成。", color="green")

    def start_logging_thread(self):
        thread = threading.Thread(target=self.logging_worker)
        thread.daemon = True
        thread.start()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = LogDemo()
    window.show()
    sys.exit(app.exec_())
```

### 方法

#### `append(message, color=None)`
- **描述:** 以指定的颜色追加一条简单的文本日志。
- **参数:**
  - `message` (`str`): 要显示的日志消息。
  - `color` (`str`, optional): 文本颜色，可以是颜色名（如 `'red'`）或十六进制值（如 `'#ff0000'`）。

#### `append_with_style(message, style=None)`
- **描述:** 以更丰富的样式追加一条日志。
- **参数:**
  - `message` (`str`): 日志消息。
  - `style` (`Dict`, optional): 一个字典，定义样式。支持的键包括 `'color'`, `'size'` (int), `'bold'` (bool), `'italic'` (bool), `'underline'` (bool)。

#### `append_multi_style(text_parts)`
- **描述:** 在一行中追加由多个不同样式的部分组成的复杂日志。
- **参数:** `text_parts` (`List[Tuple[str, Dict]]`): 一个元组列表，每个元组包含一个文本片段 `str` 和一个对应的样式字典 `Dict`。
- **使用示例:**
  ```python
  log_output.append_multi_style([
      ("状态: ", {'bold': True}),
      ("成功", {'color': 'green', 'bold': True}),
      (" (耗时: 500ms)", {'color': 'gray', 'italic': True})
  ])
  # 输出: 状态: 成功 (耗时: 500ms)  (且样式各不相同)
  ```

#### `set_auto_scroll(enable)`
- **描述:** 启用或禁用自动滚动功能。
- **参数:** `enable` (`bool`): `True` 表示启用，`False` 表示禁用。

#### `clear()`
- **描述:** 清空日志显示窗口的所有内容。

### 深度解析与设计哲学

- **线程安全模型 — "邮差"模式**:
  - `LogOutput` 的核心设计挑战在于，日志记录请求可能来自任何线程（主UI线程或多个工作线程），但所有对`QTextEdit`的更新操作都**必须**在主UI线程中执行。`LogOutput`通过一个优雅的"邮差"模式解决了这个问题。
  - **模型解析**:
    1.  **`append` 方法**: 任何线程都可以调用`append`系列方法。这些方法做的第一件事就是使用`QMutexLocker`锁住一个互斥体，确保线程安全。然后，它们并不直接操作`QTextEdit`，而是发射一个内部的`pyqtSignal`，并将日志内容和样式作为参数传递。
    2.  **`__LogSignalHandler`**: 在初始化时，`LogOutput`创建了一个`__LogSignalHandler`对象，并将其移动到了**主GUI线程**（通过`moveToThread(QApplication.instance().thread())`）。这个处理器专门负责监听上述的内部信号。
    3.  **`Qt.QueuedConnection`**: 由于信号的发射者（可能在工作线程）和接收者（在主线程）位于不同线程，Qt的信号槽机制会自动使用`Qt.QueuedConnection`。这意味着信号的发射会变成一个事件，被投递到主线程的事件队列中。
    4.  **主线程事件循环**: 主线程的事件循环在处理完其他UI事件后，会从队列中取出这个日志事件，并调用`__LogSignalHandler`中连接的槽函数。
    5.  **安全更新UI**: 因为这个槽函数是在主线程中被调用的，所以它可以安全地操作`QTextEdit`，调用其`append`或`insertHtml`等方法来更新界面。
  - **优势**: 这种模式将"日志请求"和"UI更新"完全解耦，`append`方法变成了一个轻量级的、线程安全的"信件投递员"，而实际的"收信和展示"工作则由主线程的"邮差"`__LogSignalHandler`来完成，完美地遵守了GUI编程的黄金法则。

- **富文本构建与性能考量**:
  - `LogOutput`支持丰富的HTML格式，但直接拼接大量HTML字符串可能会影响性能。
  - **`QTextCursor`**: `LogOutput`在内部（通过其处理器）使用`QTextCursor`来高效地插入和格式化文本。相比于反复调用`setHtml()`，使用`QTextCursor`移动到文档末尾（`movePosition(QTextCursor.End)`）然后`insertHtml()`是向`QTextEdit`追加内容的推荐方式，它通常具有更好的性能。
  - **`append_multi_style`**: 这个方法展示了如何通过一次`insertHtml`调用来构建一条包含多种样式的复杂日志。它在内部将元组列表（`[('text', {'style'}), ...]`）转换成一个单一的HTML字符串（例如`<p><b>状态: </b><font color='green'>成功</font>...</p>`），然后一次性插入。这比多次调用`insertHtml`来分别插入每个部分要高效得多。

---

## `SignalSlotManager`

### 概述

`SignalSlotManager` 提供了一个全局的、集中的、基于名称的信号/槽管理机制。它允许应用程序的不同部分在不直接相互引用的情况下进行通信，从而极大地降低了组件间的耦合度。你可以把它想象成一个全局的事件总线或发布/订阅系统。

核心功能：
- **动态信号注册**: 可以在运行时动态地注册任何名称的信号，并指定其数据类型。
- **类型安全**: 支持 `str`, `int`, `float`, `bool`, `list`, `dict`, `object` 等多种数据类型，管理器会确保发射信号时携带的数据类型与注册时一致。
- **解耦通信**: 发射者（Publisher）和接收者（Slot/Subscriber）只需知道一个共同的信号名称即可通信，无需相互持有对方的实例。
- **多对多连接**: 一个信号可以连接到多个槽函数，一个槽函数也可以连接到多个信号。
- **线程安全**: `emit_signal` 方法是线程安全的，可以放心地从工作线程发射信号来通知主线程更新UI或执行操作。

### 初始化

**`__init__(self)`**

构造函数非常简单，它在内部初始化用于存储信号定义和连接的字典。`SignalSlotManager` 通常被用作单例或在应用的顶层实例化，以便在全局范围内访问。

- **实现细节**:
  1.  内部维护一个 `__signals` 字典，用于存储每个已注册信号的信息，包括其类型、对应的Qt信号对象 (`pyqtSignal`) 以及所有连接到它的槽函数。
  2.  它使用了一个 `SignalContainer` 的内部类，这个类预先定义了所有支持类型的Qt信号。当注册一个新信号时，管理器会从 `SignalContainer` 中选择相应类型的信号进行关联。
  3.  发射信号时，它会查找信号名称，获取关联的Qt信号对象，然后安全地（考虑线程）发射出去。

**使用示例:**
```python
import sys, time, threading
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel

# 假设这是全局管理器实例
# 在实际应用中，可以通过单例模式或依赖注入来获取
signal_manager = SignalSlotManager()

class ComponentA(QWidget):
    """一个发射信号的组件"""
    def __init__(self):
        super().__init__()
        # 注册一个名为 'user_logged_in' 的信号，它会携带一个字符串（用户名）
        signal_manager.register_signal("user_logged_in", "str")
        
        self.button = QPushButton("模拟用户登录")
        self.button.clicked.connect(self.login)
        layout = QVBoxLayout(self)
        layout.addWidget(self.button)
    
    def login(self):
        username = "Alice"
        print(f"组件A: 用户 '{username}' 登录成功，发射信号...")
        # 发射信号
        signal_manager.emit_signal("user_logged_in", username)

class ComponentB(QWidget):
    """一个接收信号的组件"""
    def __init__(self):
        super().__init__()
        self.label = QLabel("当前状态: 未登录")
        layout = QVBoxLayout(self)
        layout.addWidget(self.label)
        
        # 将自己的方法连接到 'user_logged_in' 信号
        signal_manager.connect_slot("user_logged_in", self.on_user_login)
        
    def on_user_login(self, username):
        """这是一个槽函数"""
        print(f"组件B: 接收到信号，更新UI。")
        self.label.setText(f"当前状态: 用户 {username} 已登录")

class SignalManagerDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SignalSlotManager Demo")
        layout = QVBoxLayout(self)
        layout.addWidget(ComponentA())
        layout.addWidget(ComponentB())

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SignalManagerDemo()
    window.show()
    sys.exit(app.exec_())
```

### 方法

#### `register_signal(signal_name, signal_type)`
- **描述:** 注册一个新的信号。
- **参数:**
  - `signal_name` (`str`): 信号的唯一名称。
  - `signal_type` (`str`): 信号承载的数据类型。使用 `SignalType` 枚举或其字符串值: `'str'`, `'int'`, `'float'`, `'bool'`, `'list'`, `'dict'`, `'object'`。
- **返回:** `bool` - 注册成功返回 `True`，如果信号名已存在则返回 `False`。

#### `connect_slot(signal_name, slot_func)`
- **描述:** 将一个函数（槽）连接到一个已注册的信号。
- **参数:**
  - `signal_name` (`str`): 要连接的信号的名称。
  - `slot_func` (`Callable`): 要执行的槽函数。其参数签名必须与信号类型匹配。
- **返回:** `bool` - 连接成功返回 `True`。

#### `disconnect_slot(signal_name, slot_func)`
- **描述:** 断开一个槽函数与信号的连接。
- **返回:** `bool` - 断开成功返回 `True`。

#### `emit_signal(signal_name, *args, **kwargs)`
- **描述:** 发射一个信号，并传递相应的数据。此方法是线程安全的。
- **参数:**
  - `signal_name` (`str`): 要发射的信号的名称。
  - `*args`: 要传递的数据。数据的类型和数量必须与信号注册时定义的类型匹配。
- **返回:** `bool` - 发射成功返回 `True`。

### 深度解析与设计哲学

- **设计模式 — 发布/订阅 (Publish/Subscribe) 与 单例 (Singleton)**:
  - **发布/订阅模式**: `SignalSlotManager` 是一个典型的"发布/订阅"模式实现，也常被称为"事件总线"(Event Bus)。
    - **发布者 (Publisher)**: 调用`emit_signal`的代码是发布者。它只关心发布一个特定主题（`signal_name`）的事件，而无需知道谁在监听这个事件。
    - **订阅者 (Subscriber)**: 调用`connect_slot`的代码是订阅者。它只关心订阅特定主题的事件，而无需知道是谁发布的这个事件。
    - **事件总线 (Event Bus)**: `SignalSlotManager`实例本身就是总线，它作为发布者和订阅者之间的中介，彻底解除了两者之间的直接依赖。这对于构建大型、模块化的应用程序至关重要，因为它允许不同模块间进行通信，而无需相互引用，大大降低了耦合度。
  - **单例模式（建议用法）**: 虽然代码本身没有强制实现单例模式，但`SignalSlotManager`的设计意图是作为应用全局唯一的实例来使用。通过单例模式或依赖注入框架确保全局只有一个`SignalSlotManager`，可以保证它成为整个应用程序统一的通信中枢。

- **动态信号创建与类型安全**:
  - Qt原生的信号/槽机制要求信号必须在类定义时作为类变量（`pyqtSignal`）声明，这缺乏运行时动态创建的能力。`SignalSlotManager`巧妙地绕过了这个限制。
  - **`SignalContainer`**: 管理器内部使用一个`SignalContainer`类，这个类预先定义了所有支持的数据类型（`str`, `int`, `object`等）的`pyqtSignal`实例。
  - **动态注册 (`register_signal`)**: 当你调用`register_signal("my_signal", "str")`时，管理器并不是在动态生成新的`pyqtSignal`代码。实际上，它是在内部创建了一个记录，将字符串名称`"my_signal"`与`SignalContainer`中预先定义好的`str_signal`关联起来。同时，它会记录下`"my_signal"`期望的类型是`str`。
  - **类型安全检查**: 当调用`emit_signal("my_signal", data)`时，管理器会首先检查`"my_signal"`注册时要求的类型，并验证传入的`data`是否符合该类型。只有在类型匹配时，它才会通过之前关联的、正确的`pyqtSignal`（如`str_signal`）将数据发射出去。这个机制在提供动态性的同时，也保留了类型检查带来的健壮性。

- **线程安全 (`emit_signal`)**:
  - `emit_signal`的线程安全是通过`QMetaObject.invokeMethod`实现的，其原理与`LogOutput`中的"邮差"模式类似。
  - 当`emit_signal`在非GUI线程中被调用时，它不会直接发射Qt信号。相反，它使用`QMetaObject.invokeMethod`配合`Qt.QueuedConnection`，将"发射信号"这个动作本身打包成一个事件，投递到主线程的事件队列中。
  - 主线程的事件循环最终会执行这个事件，从而在正确的线程（主线程）中完成实际的`pyqtSignal`的`emit()`调用。这确保了所有连接到该信号的GUI槽函数都能在主线程中被安全地调用，避免了跨线程UI操作的风险。

---

## `NestedScrollAreaFixer`

### 概述

`NestedScrollAreaFixer` 是一个专门解决在Qt中常见的"嵌套滚动区域"问题的工具类。当一个 `QScrollArea` 内部包含另一个 `QScrollArea` 时，鼠标滚轮事件通常只会被最内层的滚动区域捕获，导致用户无法滚动外层的区域。此类通过巧妙地使用事件过滤器，实现了当内层滚动区域滚动到其顶部或底部时，将后续的滚轮事件"传递"给外层滚动区域，从而创建了一个无缝、自然的滚动体验，就像在网页中一样。

核心功能：
- **自动修复**: 只需指定根滚动区域，它会自动查找所有嵌套的子滚动区域并应用修复逻辑。
- **无缝滚动**: 当内层滚动区域到达边界时，鼠标滚轮会自动开始滚动父滚动区域。
- **动态适应**: 提供了 `rescan` 方法，以适应在运行时动态添加或删除滚动区域的场景。

### 初始化

**`__init__(self, root_scroll_area: QScrollArea, parent: Optional[QObject] = None)`**

构造函数接收顶层的 `QScrollArea` 作为参数，并立即开始应用修复。

- **实现细节**:
  1.  它调用 `__find_and_install_filters` 方法，递归地遍历 `root_scroll_area` 的所有子控件。
  2.  当找到一个 `QScrollArea` 时，它会为该滚动区域本身及其视口 (`viewport`) 都安装事件过滤器。
  3.  事件过滤器 (`eventFilter` 方法) 的核心逻辑是监听 `Wheel` 事件。
  4.  当一个滚轮事件发生时，它会检查事件源（通常是内层滚动区域）的滚动条是否已经到达了极限（顶部或底部）。
  5.  如果滚动条已在极限位置，并且滚轮滚动的方向是想"超越"这个极限（例如，在顶部时向上滚，在底部时向下滚），那么该事件就会被 `ignore()`。根据Qt的事件传播机制，被忽略的事件会继续向上传递给父控件，最终被外层的 `QScrollArea` 捕获并处理，从而实现了滚动的"传递"。

**参数:**
| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `root_scroll_area` | `QScrollArea` | 包含其他滚动区域的最外层 `QScrollArea`。 |
| `parent` | `QObject`, optional | Qt的父对象。 |

**使用示例:**
```python
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QScrollArea, QWidget, QVBoxLayout, QLabel

# 创建一个长内容，以便滚动
def create_long_content(title, num_items):
    widget = QWidget()
    layout = QVBoxLayout(widget)
    layout.addWidget(QLabel(f"--- {title} ---"))
    for i in range(num_items):
        layout.addWidget(QLabel(f"项目 {i+1}"))
    return widget

# 创建一个可滚动的区域
def create_scroll_area(content_widget):
    scroll_area = QScrollArea()
    scroll_area.setWidgetResizable(True)
    scroll_area.setWidget(content_widget)
    return scroll_area

class NestedScrollDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("NestedScrollAreaFixer Demo")

        # 1. 创建内层滚动区域
        inner_content = create_long_content("内层内容", 50)
        self.inner_scroll = create_scroll_area(inner_content)
        self.inner_scroll.setFixedHeight(200) # 给内层一个固定高度

        # 2. 创建外层的内容，并把内层滚动区域放进去
        outer_content = QWidget()
        outer_layout = QVBoxLayout(outer_content)
        outer_layout.addWidget(QLabel("一些外层顶部内容..."))
        outer_layout.addWidget(self.inner_scroll) # <-- 嵌套发生在这里
        outer_layout.addWidget(QLabel("一些外层底部内容..."))

        # 3. 创建外层滚动区域
        self.outer_scroll = create_scroll_area(outer_content)
        self.setCentralWidget(self.outer_scroll)

        # 4. 初始化修复器！
        # 传入最外层的滚动区域
        self.fixer = NestedScrollAreaFixer(self.outer_scroll, parent=self)
        
        # 现在，当你用鼠标滚轮在内层区域滚动到底部后，
        # 继续向下滚动，外层区域就会开始滚动。

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = NestedScrollDemo()
    window.resize(400, 400)
    window.show()
    sys.exit(app.exec_())
```

### 方法

#### `rescan()`
- **描述:** 重新扫描根滚动区域，为任何动态添加的新滚动区域安装事件过滤器。

#### `cleanup()`
- **描述:** 手动移除所有已安装的事件过滤器，并清理资源。

### 深度解析与设计哲学

- **Qt事件传播与过滤机制**:
  - `NestedScrollAreaFixer` 的实现原理巧妙地利用了Qt的事件传播模型，堪称一个经典的事件处理范例。
  - **事件传播 (Event Propagation)**: 在Qt中，当一个事件（如鼠标滚轮事件）发生时，它首先被发送到事件发生时鼠标指针所在的、最内层的控件。如果这个控件不处理该事件（即调用`event.ignore()`），那么事件会"冒泡"向上传播给它的父控件，父控件再决定是否处理或继续向上传播，直到事件被某个控件处理（`event.accept()`）或到达顶层窗口。
  - **事件过滤器 (`eventFilter`)**: `NestedScrollAreaFixer`为内层的`QScrollArea`安装了一个事件过滤器。这个过滤器赋予了修复器"预处理"内层滚动区事件的能力。
  - **核心逻辑**:
    1.  当用户在内层滚动区滚动鼠标滚轮时，`eventFilter`会捕获到这个`QWheelEvent`。
    2.  修复器首先检查内层滚动区的垂直滚动条（`verticalScrollBar`）的状态。
    3.  **关键判断**:
        - 如果滚轮是**向下**滚动的（`event.angleDelta().y() < 0`），并且滚动条已经到达了**最大值**（`scroll_bar.value() == scroll_bar.maximum()`），这意味着内层已经滚到底了，用户还想继续向下滚。
        - 如果滚轮是**向上**滚动的（`event.angleDelta().y() > 0`），并且滚动条已经到达了**最小值**（`scroll_bar.value() == scroll_bar.minimum()`），这意味着内层已经滚到顶了，用户还想继续向上滚。
    4.  **事件忽略 (`event.ignore()`)**: 在上述任一关键条件下，`eventFilter`会调用`event.ignore()`。这相当于内层滚动区对这个滚轮事件说："这个我处理不了（或不想处理），交给我的上级吧。"
    5.  **事件冒泡**: 被忽略的事件随即向上传播，最终被外层的`QScrollArea`捕获。由于外层滚动区并未滚动到其极限位置，它会正常处理这个滚轮事件，从而实现了外层区域的滚动。
  - **效果**: 这种"在极限位置放弃事件处理权"的策略，无缝地将内外两个独立的滚动操作连接了起来，创造了如同单个连续页面的滚动体验。

--- 

## `SelectAllCheckBoxManager`

`SelectAllCheckBoxManager` 是一个专门用于实现UI中常见的"全选/取消全选"功能的辅助类。它能自动将一个"全选"复选框与同一容器内的其他所有"子项"复选框进行逻辑绑定，并支持同时管理多个独立的容器。

### 核心功能

-   **独立的容器逻辑**: 每个指定的容器（如 `QGroupBox`）内的"全选"逻辑都是完全隔离的，一个容器的操作不会影响其他容器。
-   **自动发现与角色分配**: 根据指定的文本（如 "全选"），自动在容器内查找并识别"全选"复选框和"子项"复选框。
-   **双向状态联动**:
    -   勾选/取消"全选"框，会自动同步所有"子项"框的状态。
    -   手动更改任何"子项"框的状态，会重新计算并更新"全选"框的状态。
-   **智能信号管理**: 内置了防信号循环机制，避免在程序化更新状态时导致无限递归。
-   **自动状态初始化**: 在设置逻辑时，会自动根据当前"子项"的选中状态来初始化"全选"框的状态。

### 快速上手

下面的示例展示了如何使用 `SelectAllCheckBoxManager` 同时管理两个独立的 `QGroupBox`，每个都有自己的"全选"逻辑。

```python
import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QGroupBox, QCheckBox)
from global_tools.ui_tools.compnonent.qcheckbox import SelectAllCheckBoxManager # 假设路径正确

class DemoWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SelectAllCheckBoxManager Demo")
        self.main_layout = QVBoxLayout(self)

        # 创建第一个功能组
        group1 = QGroupBox("功能模块 A")
        layout1 = QVBoxLayout()
        layout1.addWidget(QCheckBox("全选"))
        layout1.addWidget(QCheckBox("功能 A-1"))
        layout1.addWidget(QCheckBox("功能 A-2"))
        layout1.addWidget(QCheckBox("功能 A-3"))
        group1.setLayout(layout1)
        self.main_layout.addWidget(group1)

        # 创建第二个功能组
        group2 = QGroupBox("功能模块 B")
        layout2 = QVBoxLayout()
        layout2.addWidget(QCheckBox("全选"))
        layout2.addWidget(QCheckBox("功能 B-1"))
        layout2.addWidget(QCheckBox("功能 B-2"))
        group2.setLayout(layout2)
        self.main_layout.addWidget(group2)
        
        # 使用一个管理器实例同时管理两个独立的组
        # 它会分别在 group1 和 group2 中查找名为 "全选" 的复选框并建立逻辑
        self.select_all_manager = SelectAllCheckBoxManager(
            container=[group1, group2], 
            select_all_text="全选",
            logger=None # 在实际使用中传入您的日志记录器
        )

    def closeEvent(self, event):
        # 在窗口关闭时清理资源，断开信号连接
        self.select_all_manager.cleanup()
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    demo = DemoWidget()
    demo.show()
    sys.exit(app.exec_())
```

### API 详解

#### `__init__(container, select_all_text, parent=None, logger=None)`
- **功能描述:** 初始化管理器，并自动完成所有查找、分类和信号连接工作。这是与该类交互的主要入口点。
- **内部流程:**
    1.  接收 `container` 参数，如果不是列表，则将其包装成单元素列表。
    2.  调用 `__setup_all_containers` 方法，开始对列表中的每个容器进行设置。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `container` | `Union[QWidget, QLayout, List[...]]` | 单个或多个容器。管理器将在每个容器内独立查找控件并建立"全选"逻辑。 |
  | `select_all_text` | `str` | 用于通过文本识别"全选"复选框的唯一标识符。 **此参数不能为空。** |
  | `parent` | `Optional[QObject]` | 父QObject对象，用于Qt的对象树管理，有助于自动内存回收。 |
  | `logger` | `Optional[Logger]` | 日志记录器实例，用于输出内部工作状态和警告信息。 |

#### `cleanup()`
- **功能描述:** 清理资源，安全地断开所有由该管理器建立的信号连接。
- **重要性:** **必须**在父窗口关闭或不再需要此逻辑时调用此方法。否则，由于Python的垃圾回收和Qt的信号槽机制之间的差异，可能导致程序在关闭时崩溃或出现`wrapped C/C++ object has been deleted`的运行时错误。
- **内部流程:**
    1.  遍历内部存储的 `__connections` 字典。
    2.  对字典中的每个 `QCheckBox` 对象，调用 `stateChanged.disconnect()` 并传入对应的处理函数。
    3.  使用 `try...except` 包裹断开连接的操作，以防止因对象已被销毁而产生的异常。
    4.  清空 `__connections` 和 `__container_map` 字典，释放引用。

### 内部属性与方法详解

#### 核心属性

| 属性名 | 类型 | 描述 |
| --- | --- | --- |
| `__container_map` | `Dict[Union[QWidget, QLayout], Dict[str, Any]]` | **核心数据结构**。一个字典，键(key)是用户传入的容器对象，值(value)是另一个字典，包含`'select_all'` (全选框对象) 和 `'children'` (子项框列表)。该结构是实现多容器逻辑隔离的关键。 |
| `__is_updating` | `bool` | 一个简单的布尔标志，用作**信号锁**。在程序化更新复选框状态时，此标志被设为`True`，以防止后续的信号触发再次执行更新逻辑，从而避免无限递归循环。 |
| `__connections` | `Dict[QCheckBox, Callable]` | 用于存储所有已建立的信号连接。键是`QCheckBox`对象，值是通过`functools.partial`包装后的槽函数。此字典是`cleanup()`方法能够精确断开连接的基础。 |

#### 内部方法 (调用链)

1.  **`__setup_all_containers(containers, select_all_text)`**
    -   **职责:** 顶层设置循环。
    -   **描述:** 遍历 `__init__` 中传入的容器列表，并对每个容器调用 `__setup_single_container`，从而启动对每个容器的独立设置流程。

2.  **`__setup_single_container(container, select_all_text)`**
    -   **职责:** 对单个容器进行查找、分类和连接。
    -   **描述:** 这是逻辑设置的核心步骤。
        1.  **查找**: 调用 `__get_checkboxes_from_layout` 或 `findChildren` 来获取容器内所有复选框。
        2.  **分类**: 遍历复选框列表，根据文本是否匹配 `select_all_text`，将它们分为一个"全选"框和多个"子项"框。
        3.  **存储**: 如果找到了有效的组合，则将这个组合（全选框和子项列表）存入 `__container_map` 中，以容器对象为键。
        4.  **连接**: 调用 `__connect_signals_for_group` 为这组控件建立信号连接。
        5.  **初始化**: 调用 `__on_child_state_changed` 来根据子项的当前状态，正确设置"全选"框的初始状态。

3.  **`__get_checkboxes_from_layout(layout)`**
    -   **职责:** 从 `QLayout` 中递归地获取所有 `QCheckBox`。
    -   **描述:** `findChildren` 方法只对 `QWidget` 有效。当容器是 `QLayout` 时，此辅助方法会遍历布局中的所有项，并递归地深入子布局和子控件，以确保找到所有 `QCheckBox`。

4.  **`__connect_signals_for_group(select_all_cb, child_cbs)`**
    -   **职责:** 为特定的一组"全选"和"子项"复选框连接信号。
    -   **描述:** 此方法使用 `functools.partial` 将上下文（即需要操作的复选框列表）"预绑定"到槽函数上。这使得槽函数是通用的，无需为每个连接创建唯一的函数，极大地简化了代码。

5.  **`__on_select_all_state_changed(child_checkboxes, state)`**
    -   **职责:** 处理"全选"框的状态变化（槽函数）。
    -   **描述:** 当"全选"框被操作时，此函数被调用。它会获取通过`partial`传入的`child_checkboxes`列表，并将它们的状态与"全选"框的新状态同步。这是"从上到下"的逻辑流。

6.  **`__on_child_state_changed(select_all_checkbox, sibling_checkboxes, state)`**
    -   **职责:** 处理"子项"框的状态变化（槽函数）。
    -   **描述:** 当任何一个"子项"框被操作时，此函数被调用。它会检查其所有`sibling_checkboxes`（兄弟姐妹）的状态，如果所有子项都被选中，则勾选`select_all_checkbox`，否则取消勾选。这是"从下到上"的逻辑流。

7.  **`__is_valid_pyqt_object(obj)`**
    -   **职责:** 安全地检查一个PyQt对象是否仍然有效。
    -   **描述:** 在操作任何Qt对象（特别是槽函数中）之前，调用此方法以确保该对象没有被C++后端删除，防止程序崩溃。

### 设计理念与实现细节

-   **容器化隔离 (`__container_map`)**: 该类设计的核心是能够独立管理多个UI区域。`__container_map` 数据结构是实现这一目标的关键。通过将每个容器的"全选"框和"子项"框列表映射到容器对象本身，确保了每个组的逻辑完全独立，互不干扰。

-   **上下文绑定的信号 (`functools.partial`)**: 为了避免为每个信号连接编写冗余的 `lambda` 函数或类方法，该类巧妙地利用了 `functools.partial`。在连接信号时，它将槽函数所需要的上下文（例如，需要被控制的子项列表或需要被更新的全选框）作为参数"冻结"起来。这使得槽函数 `__on_select_all_state_changed` 和 `__on_child_state_changed` 成为可重用的通用处理器，它们在被调用时能准确地知道自己应该操作哪些对象，从而使代码更简洁、更富表达力。

-   **信号循环预防 (`__is_updating` 锁)**: 在一个双向绑定的系统中，信号循环是一个常见陷阱：A的改变触发B，B的改变又触发A，导致无限循环。`__is_updating` 标志是一个简单的互斥锁（Mutex），它打破了这个循环。当一个槽函数开始以编程方式修改其他复选框的状态时，它首先将此标志设为 `True`。如果这些修改触发了新的信号，那么被调用的槽函数会检查这个标志，发现它为 `True`，便立即返回，从而有效地中断了递归调用链。操作完成后，标志被重置为 `False`，为下一次用户交互做准备。

--- 

## `QProgressBarHelper` - 动画进度条控制器

### 概述

`QProgressBarHelper` 是一个强大且灵活的 `QProgressBar` 管理类，旨在简化对一个或多个进度条的复杂交互和动画控制。此类通过`objectName`来唯一标识和管理每个进度条，提供了线程安全的接口来更新进度值、设置属性以及播放平滑的过渡动画。

它特别适用于以下场景：
- 需要在UI中同时管理多个进度条，例如文件下载、任务处理等。
- 希望为进度条的数值变化添加平滑的动画效果，提升用户体验。
- 需要从后台工作线程安全地更新UI上的进度条，而无需手动处理复杂的线程通信。

### 核心特性

- **批量管理**: 通过 `objectName` 集中管理任意数量的 `QProgressBar` 实例。
- **平滑动画**: `set_value` 方法内置支持 `QPropertyAnimation`，可轻松实现带有自定义时长和缓动曲线的动画效果。
- **线程安全**: 内建了跨线程UI更新机制。从非UI线程调用 `set_value` 时，会自动通过信号槽机制将更新操作派发至主线程执行，保证UI操作的线程安全。
- **丰富的API**: 提供了一系列便捷的方法，用于设置/获取进度条的值、范围、格式、方向等。
- **信号驱动**: 拥有完善的信号系统（如 `value_changed`, `animation_started`, `animation_finished`），便于外部逻辑响应进度条的状态变化。
- **健壮性**: 自动处理已被销毁的Qt对象，并对输入值进行范围验证，提高了应用的稳定性。

### 初始化与使用示例

`QProgressBarHelper` 可以在初始化时接收一个进度条列表，也可以在之后通过 `add_progress_bar` 方法逐个添加。

**`__init__(self, progress_bars: List[QProgressBar] = None, parent: QObject = None)`**

- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `progress_bars` | `List[QProgressBar]`, optional | 一个 `QProgressBar` 对象列表。每个进度条都**必须**设置一个唯一的 `objectName`。默认为 `None`。 |
  | `parent` | `QObject`, optional | Qt的父对象，用于内存管理。默认为 `None`。 |

**完整使用示例:**
```python
import sys
import threading
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QProgressBar
from PyQt5.QtCore import QEasingCurve
# 假设 QProgressBarHelper 已经导入
# from global_tools.ui_tools.compnonent.qprogress_bar import QProgressBarHelper

class ProgressDemo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("QProgressBarHelper Demo")
        self.layout = QVBoxLayout(self)

        # 创建两个进度条并设置 objectName
        self.pbar_task = QProgressBar()
        self.pbar_task.setObjectName("taskProgress")
        self.pbar_task.setFormat("任务进度: %p%")

        self.pbar_overall = QProgressBar()
        self.pbar_overall.setObjectName("overallProgress")
        self.pbar_overall.setFormat("总进度: %v / %m")

        self.layout.addWidget(self.pbar_task)
        self.layout.addWidget(self.pbar_overall)

        # 初始化 Helper
        self.progress_helper = QProgressBarHelper([self.pbar_task, self.pbar_overall], parent=self)

        # 连接信号
        self.progress_helper.value_changed.connect(self.on_value_changed)
        self.progress_helper.animation_finished.connect(self.on_animation_finished)

        # 启动演示
        self.start_demo()

    def on_value_changed(self, name, value):
        print(f"信号捕获: 进度条 '{name}' 的目标值变为 {value}")

    def on_animation_finished(self, name):
        print(f"信号捕获: 进度条 '{name}' 的动画已完成。")

    def start_demo(self):
        # 1. 设置初始范围和值 (无动画)
        self.progress_helper.set_range("overallProgress", 0, 200)
        self.progress_helper.set_value("taskProgress", 10, animate=False)

        # 2. 启动一个工作线程来更新 taskProgress
        threading.Thread(target=self.worker_thread_task, daemon=True).start()

        # 3. 批量更新，并使用自定义动画
        print("\n--- 批量更新 (带动画) ---")
        self.progress_helper.batch_update(
            {"taskProgress": 50, "overallProgress": 75},
            duration_ms=1500,
            easing_curve=QEasingCurve.OutBounce
        )

    def worker_thread_task(self):
        print("\n--- 工作线程启动，将更新 taskProgress ---")
        for i in range(51, 101):
            # 直接从工作线程调用 set_value，Helper会自动处理线程安全
            self.progress_helper.set_value("taskProgress", i)
            time.sleep(0.05)
        print("--- 工作线程结束 ---")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    demo = ProgressDemo()
    demo.show()
    sys.exit(app.exec_())
```

### API 详解

#### 公共方法

**`add_progress_bar(self, progress_bar: QProgressBar) -> bool`**
- **描述:** 向管理器中添加一个新的 `QProgressBar`。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `progress_bar` | `QProgressBar` | 要添加的进度条对象，必须已设置 `objectName`。 |
- **返回:** `bool` - 添加成功返回 `True`，如果 `objectName` 为空或重复则返回 `False`。

**`set_value(self, name: str, value: int, animate: bool = True, duration_ms: Optional[int] = None, easing_curve: Optional[QEasingCurve] = None) -> bool`**
- **描述:** 设置指定进度条的值，支持动画和跨线程调用。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `name` | `str` | 进度条的 `objectName`。 |
  | `value` | `int` | 要设置的新值。 |
  | `animate` | `bool`, optional | 是否使用动画。默认为 `True`。 |
  | `duration_ms` | `int`, optional | 动画时长(毫秒)。默认为内部预设值(300ms)。 |
  | `easing_curve` | `QEasingCurve`, optional | 动画缓动曲线。默认为 `QEasingCurve.InOutQuad`。 |
- **返回:** `bool` - 操作成功启动返回 `True`，否则 `False`。

**`get_value(self, name: str) -> Optional[int]`**
- **描述:** 获取指定进度条的当前值。
- **返回:** `Optional[int]` - 当前值，如果找不到进度条则返回 `None`。

**`batch_update(self, values: Dict[str, int], animate: bool = True, **kwargs) -> Dict[str, bool]`**
- **描述:** 批量更新多个进度条的值。
- **参数:**
  | 名称 | 类型 | 描述 |
  | --- | --- | --- |
  | `values` | `Dict[str, int]` | 一个字典，键为 `objectName`，值为新的进度值。 |
  | `animate` | `bool`, optional | 是否对所有更新使用动画。默认为 `True`。 |
  | `**kwargs` | - | 其他传递给 `set_value` 的动画参数（如 `duration_ms`）。 |
- **返回:** `Dict[str, bool]` - 一个字典，报告每个进度条的更新是否成功。

**`set_range(self, name: str, minimum: int, maximum: int) -> bool`**
- **描述:** 设置进度条的范围（最小值和最大值）。
- **返回:** `bool` - 操作是否成功。

**`get_range(self, name: str) -> Optional[Tuple[int, int]]`**
- **描述:** 获取进度条的范围。
- **返回:** `Optional[Tuple[int, int]]` - 一个 `(min, max)` 元组，如果找不到则返回 `None`。

**`reset(self, name: str, animate: bool = False, **kwargs) -> bool`**
- **描述:** 将进度条的值重置为其最小值。
- **返回:** `bool` - 操作是否成功。

**`stop_animation(self, name: str) -> None`**
- **描述:** 立即停止指定进度条上正在进行的动画。

**`remove_progress_bar(self, name: str) -> bool`**
- **描述:** 从管理器中移除一个进度条及其关联的动画。
- **返回:** `bool` - 移除成功返回 `True`。

**`cleanup(self) -> None`**
- **描述:** 清理所有资源，停止所有动画并清除所有内部引用。

#### 信号

**`value_changed(object_name: str, new_value: int)`**
- **触发时机:** 当进度条的值通过 `set_value` 成功改变时发出（对于动画，在动画开始时就会以目标值发出）。
- **参数:** `object_name` (进度条名称), `new_value` (新的目标值)。

**`animation_started(object_name: str)`**
- **触发时机:** 当一个值动画开始播放时发出。
- **参数:** `object_name` (进度条名称)。

**`animation_finished(object_name: str)`**
- **触发时机:** 当一个值动画播放完成时发出。
- **参数:** `object_name` (进度条名称)。

**`range_changed(object_name: str, min_value: int, max_value: int)`**
- **触发时机:** 当进度条的范围通过 `set_range` 成功改变时发出。
- **参数:** `object_name`, `min_value`, `max_value`。

### 线程安全

`QProgressBarHelper` 的一个核心优势是其内建的线程安全机制。在PyQt中，所有对UI元素的修改都必须在主线程（GUI线程）中进行。直接从工作线程修改UI会导致程序不稳定甚至崩溃。

该类通过以下方式解决了这个问题：
1.  **自动线程检测:** 在 `set_value` 方法内部，它会使用 `QThread.currentThread()` 和 `QApplication.instance().thread()` 来判断调用是否来自主线程。
2.  **信号槽转发:** 如果检测到调用来自工作线程，它不会直接修改 `QProgressBar`。相反，它会发射一个内部的私有信号 `__set_value_signal`，并将所有参数（名称、值、动画设置等）作为信号的负载。
3.  **主线程执行:** 这个私有信号连接到一个私有槽 `__set_value_impl_slot`。由于Qt的信号槽机制默认是跨线程安全的（使用 `QueuedConnection`），这个槽函数保证会在主线程的事件循环中被执行。
4.  **无缝体验:** 最终，真正修改UI的代码 (`__set_value_impl`) 总是在主线程中运行。对于使用者来说，这个过程是完全透明的，他们可以像调用普通函数一样从任何线程调用 `helper.set_value()`，而无需关心底层的线程切换逻辑。

--- 