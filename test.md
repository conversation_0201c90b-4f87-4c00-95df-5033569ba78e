编写 global_tools packages(包)的文档，包的每个文件夹就是一个子模块，每个子模块都要有详细的说明，包括模块的功能、使用方法、注意事项等。
编写 global_tools packages(包) window_operation子模块文档，包括模块的功能、使用方法、注意事项等；详细的阅读子模块的内容。
1：文档的格式要清晰，便于阅读。
2：文档使用 markdown 格式。
3：文档写入 doc.md 文件中。
4：文档要详细，包括模块的每个函数、类都要有说明。
5：目录要清晰，便于阅读。
6：如果文档存在，则更新文档，如果文档不存在，则创建文档。
8：每个暴露的公共函数都要有详细的说明，包括参数、返回值、注意事项等，无论是类还是函数。
9：每个方法或者类都要有详细的使用示例说明。

Logger 类添加一个参数控制是否输出日志到文件，默认是不输出日志到文件。
1：其他不相关的代码不要删除或修改。


在 CheckBoxManager 中添加一个函数，用于容器中有多个复选框只能选择一个。
1：其他不相关的代码不要删除或修改。
2：添加的函数要添加详细的说明，包括参数、返回值、注意事项等。


添加一个方法，按照 CheckBox ObjectName 查找 CheckBox ：
1：添加一个参数，用于确认当前 CheckBox 是否选中；如果不传入参数则返回已查到到的 CheckBox 对象。
2：其他不相关的代码不要删除或修改。


实现一个函数，用于返回迭代对象中，所有相同的元素：
1：参数
    参数1: 迭代对象
    参数2: 
        1：可以是一个函数，如果提供函数，则传入每个迭代对象的元素给函数，使用函数返回值与之前的函数返回值进行比较。
        2：可以是一个值，如果提供值，则直接比较迭代对象的元素和值是否相等。
2：返回值两个值：
    返回值1: 相同的元素
    返回值2: 不相同的元素也返回
3：添加详细的说明，包括参数、返回值、注意事项等。

    
实现一个性能最佳的排序算法函数：
1：参数
    参数1: 迭代对象
    参数2: 函数或值
        1：可以是一个函数，如果提供函数，则传入每个迭代对象的元素给函数，使用函数返回值与之前的函数返回值进行比较。
        2：可以是一个值，如果提供值，则直接比较迭代对象的元素和值是否相等。
2：添加详细的说明，包括参数、返回值、注意事项等。

实现一个扁平化函数：
1：如 
    arr = [
    [
        {"label": "3", "value": 3},
        {"label": "1", "value": 1},
        {"label": "2", "value": 2},
        {"label": "4", "value": 4},
        {"label": "1", "value": 1},
        {"label": "1", "value": 1},
        {"label": "4", "value": 4},
    ],
    [
        {"label": "3", "value": 3},
        {"label": "1", "value": 1},
        1,
        2,
        3,
        4,
        {"label": "2", "value": 2},
        {"label": "4", "value": 4},
    ],
]
    返回:
    [
        {"label": "3", "value": 3},
        {"label": "1", "value": 1},
        {"label": "2", "value": 2},
        {"label": "4", "value": 4},
        {"label": "1", "value": 1},
        {"label": "1", "value": 1},
        {"label": "4", "value": 4},
        {"label": "3", "value": 3},
        {"label": "1", "value": 1},
        ...
    ]
2：添加详细的说明，包括参数、返回值、注意事项等。


在 global_tools\utils\event.py 中实现一个完整的事件驱动类：
1、实现的类一定要完善，功能够多，健壮性、易用性必须极强。
2、有详细的使用示例、详细的代码注释。

EventEmitter 类支持单利模式：
1、可以通过两个静态函数获取和创建。
2、功能一定要完善，健壮性、易用性必须极强。


global_tools\utils\helper.py 添加一个新的函数，读取文件夹所有文件内容:
1、可以指定后缀名获取指定的文件，也可以排除指定后缀名排序的文件。
2、是否递归获取子文件夹的文件，通过参数控制。
3、如果可能的话，尽可能的返回更多的详细信息结果。
4、实现的功能函数必须足够健壮、易用性极强。
5、函数必须添加详细的注释说明。


在 global_tools\utils\helper.py 中添加一个新函数，功能如下描述:
1、接收参数:
    参数1: 一个 list 数据
    参数2: 一个整数
2、根据传入的 “参数2” 整数对 list 进行平均划分。
3、必须拥有完整的健壮性、稳定性、易用性、可靠性，详细的注释说明。





# 类: ManagedMultiProcess

## 1. 初始化 (`__init__`)

输入参数:
  - input_data (对应 args1): 可迭代的任务数据 (例如: list)
  - callback_func (对应 args2): 用户定义的进程工作函数
  - num_processes (对应 args3, 可选, 默认=3): 启动的进程数量
  - *custom_args (对应 args2_args4 的可变参数): 传递给 callback_func 的额外参数

内部变量初始化:
  - `manager` = 启动 `multiprocessing.Manager()`
  - `shared_data_proxy` = 创建一个 Manager 管理的代理对象 (例如 `manager.dict()`) 来存储结果。
      - *思考: 为了支持 list/tuple append, 可能需要一个包装类或直接在代理对象上实现方法。*
      - 定义 `shared_data_proxy` 上的方法:
          - `set(key, value)`: 直接写入 manager dict
          - `append_to_list(key, item)`:
              - 检查 `key` 是否存在且为 manager list
              - 如果不存在，创建 `manager.list()` 并存入 `key`
              - 追加 `item` 到该 list (需要考虑并发追加到同一个 list 的原子性或加锁)
          - `append_to_tuple(key, item)`:
              - 检查 `key` 是否存在且为 tuple (或 manager list 模拟)
              - 如果不存在，创建包含 `item` 的 tuple (或 `manager.list([item])`) 并存入 `key`
              - 如果存在，获取旧 tuple/list，创建新 tuple (旧 + (item,)) 或追加到 list，然后写回 `key` (注意元组的不可变性，写回是替换操作)
  - `task_queue` = 创建 `multiprocessing.JoinableQueue()` 用于存放 `input_data`
  - `process_lock` = 创建 `multiprocessing.Lock()` (对应 args2_args3)
  - `stop_event` = 创建 `multiprocessing.Event()` 用于通知进程停止
  - `process_status_dict` = 创建 `manager.dict()` 用于存储每个进程的状态 {pid: status_string}
  - `worker_processes` = 创建一个空 list 用于存放启动的 `multiprocessing.Process` 对象
  - `user_callback` = 存储 `callback_func`
  - `extra_args` = 存储 `custom_args`

执行流程:
  - 记录日志: "开始初始化 ManagedMultiProcess..."
  - 校验参数: 检查 `num_processes` 是否 > 0, `callback_func` 是否可调用。如果无效则记录错误并抛出异常。
  - 数据入队: 将 `input_data` 中的每个元素放入 `task_queue`。
  - 创建并启动进程:
    - 循环 `num_processes` 次:
        - 创建 `multiprocessing.Process` 实例:
            - `target` = `self._worker_loop` (内部工作循环函数)
            - `args` = ( `task_queue`, `shared_data_proxy`, `process_lock`, `stop_event`, `process_status_dict`, `user_callback`, `extra_args` )
        - 将创建的 Process 对象添加到 `worker_processes` 列表
        - 调用 Process 对象的 `start()` 方法
        - 记录日志: "已启动进程 PID: {process.pid}"
  - 记录日志: "ManagedMultiProcess 初始化完成，已启动 {num_processes} 个进程。"

## 2. 内部工作循环 (`_worker_loop`)

输入参数:
  - queue: 任务队列 (`task_queue`)
  - shared_data: 共享数据代理对象 (`shared_data_proxy`)
  - lock: 进程锁 (`process_lock`)
  - stop_flag: 停止事件 (`stop_event`)
  - status_dict: 进程状态字典 (`process_status_dict`)
  - callback: 用户回调函数 (`user_callback`)
  - custom_init_args: 自定义参数 (`extra_args`)

执行流程:
  - 获取当前进程 PID: `pid = os.getpid()`
  - 更新状态: `status_dict[pid] = 'running'`
  - 记录日志: "进程 {pid} 开始运行。"
  - 进入主循环 `while not stop_flag.is_set()`:
    - `try`:
        - 从 `queue` 获取任务: `task_element = queue.get(timeout=1)` # 使用超时以便周期性检查 stop_flag
    - `except queue.Empty`:
        - # 队列暂时为空，继续循环检查 stop_flag
        - `continue`
    - `except Exception as e`:
        - # 获取任务时发生意外错误
        - 记录错误日志: "进程 {pid} 从队列获取任务失败: {e}", 包含 traceback
        - 更新状态: `status_dict[pid] = 'error'`
        - 跳出循环: `break`
    - # 如果成功获取到 task_element
    - `try`:
        - 记录日志: "进程 {pid} 开始处理任务: {task_element}"
        - 执行用户回调: `callback(shared_data, task_element, lock, *custom_init_args)`
    - `except Exception as e`:
        - # 用户回调函数执行出错
        - 记录错误日志: "进程 {pid} 执行回调函数时出错: {e}", 包含 traceback
        - 更新状态: `status_dict[pid] = 'error'`
        - # 标记任务完成，即使出错也要标记，防止 `queue.join()` 死锁
        - `queue.task_done()`
        - # 根据策略决定是否继续处理下一个任务，这里选择停止该错误进程
        - 跳出循环: `break`
    - `else`:
        - # 回调函数成功执行
        - 记录日志: "进程 {pid} 完成处理任务: {task_element}"
    - `finally`:
        - # 无论成功或失败（只要不是获取任务失败），都要调用 task_done
        - # 这个 finally 块确保了即使回调出错，task_done 也会被调用
        - 如果 `task_element` 成功获取:
           `queue.task_done()`

  - # 循环结束 (收到停止信号 或 发生错误 或 队列处理完毕（理论上需要所有进程协作判断，但 JoinableQueue 机制处理了）)
  - `if stop_flag.is_set()`:
    - 记录日志: "进程 {pid} 收到停止信号，准备退出。"
    - 更新状态: `status_dict[pid] = 'stopping'` # 或者直接 'stopped'
  - `else if status_dict.get(pid) != 'error'`: # 正常处理完队列中的任务退出
     记录日志: "进程 {pid} 处理完所有分配的任务，正常退出。"
     更新状态: `status_dict[pid] = 'stopped'`
  - `else`: # 因错误退出
     记录日志: "进程 {pid} 因错误退出。"
     # 状态已在错误处理中设置为 'error'

  - 记录日志: "进程 {pid} 退出。"

## 3. 停止所有进程 (`stop_all`)

执行流程:
  - 记录日志: "请求停止所有工作进程..."
  - 设置停止信号: `stop_event.set()`
  - 记录日志: "停止信号已发送。"
  - *注意: 此方法仅发送信号，不保证进程立即停止。进程将在其循环检查点检测到信号。*
  - *思考: 是否需要在此处短暂 join 等待？或者让用户显式调用 wait_all？目前倾向于仅发信号。*

## 4. 等待所有进程完成 (`wait_all`)

执行流程:
  - 记录日志: "等待所有任务处理完成..."
  - 等待队列为空: `task_queue.join()`
    - *说明: 这会阻塞，直到 `task_queue` 中所有元素都被 `get` 并且对应的 `task_done` 被调用。*
  - 记录日志: "所有任务已处理完毕。等待所有工作进程退出..."
  - 循环遍历 `worker_processes` 列表中的每个 process 对象 `p`:
    - 等待进程终止: `p.join()`
  - 记录日志: "所有工作进程已退出。"

## 5. 获取进程状态 (`get_status`)

执行流程:
  - 记录日志: "查询进程状态..."
  - 创建状态副本: `current_status = dict(process_status_dict)` # 从 Manager dict 复制
  - (可选增强) 遍历 `worker_processes` 列表中的每个 process 对象 `p`:
      - 如果 `p.is_alive()` 且 `current_status.get(p.pid)` 不是 'running' 或 'stopping'，可能需要更新状态 (例如，进程仍在运行但字典未更新？理论上不应发生)。或者简单地添加 `is_alive` 信息。
      - `current_status[p.pid] = {'status': current_status.get(p.pid, 'unknown'), 'is_alive': p.is_alive()}`
  - 返回 `current_status`

## 6. 获取结果 (`get_results`)

执行流程:
  - 记录日志: "获取共享数据结果..."
  - `try`:
      - 将 Manager 管理的 `shared_data_proxy` (例如 `manager.dict`) 转换为普通的 Python 对象 (例如 `dict`)。
      - 如果 `shared_data_proxy` 内部包含 `manager.list` 或其他代理类型，也需要递归转换。
      - `result_copy = _convert_manager_proxy(shared_data_proxy)` # 需要一个辅助函数来做这个转换
      - 返回 `result_copy`
  - `except Exception as e`:
      - 记录错误日志: "获取或转换结果时出错: {e}", 包含 traceback
      - 返回一个空字典或根据策略处理 `{}`

## 7. 辅助函数: `_convert_manager_proxy` (内部使用)

输入参数:
  - proxy_obj: Manager 返回的代理对象 (dict, list 等)

执行流程:
  - 如果 `proxy_obj` 是 `manager.dict`:
    - 创建空字典 `normal_dict = {}`
    - 遍历 `proxy_obj.items()`:
        - `normal_dict[key] = _convert_manager_proxy(value)` # 递归转换
    - 返回 `normal_dict`
  - 如果 `proxy_obj` 是 `manager.list`:
    - 创建空列表 `normal_list = []`
    - 遍历 `proxy_obj`:
        - `normal_list.append(_convert_manager_proxy(item))` # 递归转换
    - 返回 `normal_list`
  - 如果 `proxy_obj` 是其他基本类型 (int, str, float, tuple 等):
    - 直接返回 `proxy_obj`
  - 其他未知类型:
    - 记录警告日志: "遇到未知代理类型，直接返回: {type(proxy_obj)}"
    - 返回 `proxy_obj`

## 8. 日志和错误处理

- 使用 Python `logging` 模块进行配置和记录。
- 在所有关键操作点（初始化、启动进程、任务处理、停止、等待、获取结果、错误捕获）添加详细日志。
- 在 `try...except` 块中捕获预期和意外的异常。
- 使用 `traceback.format_exc()` 记录详细的错误堆栈信息。

## 9. 资源清理 (可选，如果需要显式清理)

- 可以考虑添加一个 `close` 或 `__del__` 方法来确保 manager 被关闭 (`manager.shutdown()`)，尽管 Python 的垃圾回收通常会处理。但显式关闭更好。