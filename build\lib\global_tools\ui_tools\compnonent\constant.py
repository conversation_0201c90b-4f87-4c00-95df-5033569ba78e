LOGGER_GUI = "LOGGER_GUI"
LOGGER_GUI_DEBUG = "LOGGER_GUI_DEBUG"

# Windows路径验证正则表达式
# 可匹配以下Windows路径格式:
# 1. 绝对路径: C:\Users\<USER>\Documents
# 2. 网络路径: \\server\share\folder
# 3. 相对路径: folder\subfolder
# 4. 带驱动器号路径: C:
# 5. 带斜杠的路径: C:/Users/<USER>
# 6. 带空格的路径: C:\Program Files\App Name
# 7. 带特殊字符的路径: C:\Users\<USER>\file-name_01.txt
#
# 示例:
# - "C:\Users\<USER>\Documents" -> 匹配
# - "D:\Project Files\data" -> 匹配
# - "\\server\share" -> 匹配
# - "folder\subfolder" -> 匹配
# - "file-name_01" -> 匹配
# - "C:/" 或 "C:/Users" -> 不匹配 (使用了正斜杠)
# - "C:\file*name" -> 不匹配 (包含非法字符*)
WINDOWS_PATH_REGEX = r'^([a-zA-Z]:|\\\\[^\\/:*?"<>|]+)(\\[^\\/:*?"<>|]+)*\\?$|^(\\[^\\/:*?"<>|]+)+\\?$|^[^\\/:*?"<>|]+(\\[^\\/:*?"<>|]+)*\\?$'

# Windows路径或文件验证正则表达式(包含文件扩展名)
# 除了匹配上述路径格式外，还可匹配文件名及其扩展名
#
# 示例:
# - "C:\Users\<USER>\Documents\report.docx" -> 匹配
# - "D:\Project Files\data.csv" -> 匹配
# - "\\server\share\image.jpg" -> 匹配
# - "folder\subfolder\config.ini" -> 匹配
# - "file-name_01.txt" -> 匹配
# - "report.pdf" -> 匹配
# - "C:\file*name.txt" -> 不匹配 (包含非法字符*)
WINDOWS_PATH_OR_FILE_REGEX = r'^([a-zA-Z]:|\\\\[^\\/:*?"<>|]+)(\\[^\\/:*?"<>|]+)*\\?$|^(\\[^\\/:*?"<>|]+)+\\?$|^[^\\/:*?"<>|]+(\\[^\\/:*?"<>|]+)*(\.[a-zA-Z0-9]+)?$'

# 允许正斜杠(/)替代反斜杠(\)的Windows路径验证正则表达式
#
# 示例:
# - "C:\Users\<USER>\Documents" -> 匹配
# - "C:/Users/<USER>/Documents" -> 匹配
# - "D:\Project Files/data" -> 匹配 (混合使用斜杠)
# - "\\server\share" -> 匹配
# - "//server/share" -> 匹配
# - "folder/subfolder" -> 匹配
# - "C:\file*name" -> 不匹配 (包含非法字符*)
WINDOWS_PATH_FLEXIBLE_REGEX = r'^([a-zA-Z]:|\\\\[^\\/:*?"<>|]+)([\\/][^\\/:*?"<>|]+)*[\\/]?$|^([\\/][^\\/:*?"<>|]+)+[\\/]?$|^[^\\/:*?"<>|]+([\\/][^\\/:*?"<>|]+)*[\\/]?$'

# URL形式的Windows网络路径验证正则表达式
#
# 示例:
# - "\\server\share" -> 匹配
# - "\\192.168.1.100\public" -> 匹配
# - "\\domain\department\documents" -> 匹配
# - "C:\Users" -> 不匹配 (不是网络路径)
# - "\\server*name\share" -> 不匹配 (包含非法字符*)
WINDOWS_UNC_PATH_REGEX = r'^\\\\([^\\/:*?"<>|]+)(\\[^\\/:*?"<>|]+)*\\?$'

# 驱动器盘符验证正则表达式
#
# 示例:
# - "C:" -> 匹配
# - "D:" -> 匹配
# - "Z:" -> 匹配
# - "5:" -> 不匹配 (非字母驱动器)
# - "CD:" -> 不匹配 (多字母驱动器)
# - "C:\" -> 不匹配 (包含路径分隔符)
WINDOWS_DRIVE_REGEX = r'^[a-zA-Z]:$'

# 匹配中文字符、英文字母、数字、下划线和连字符的正则表达式
# 增加了对空格 (\s) 的支持
CHINESE_ENGLISH_CONNECTOR_REGEX = '^[\u4e00-\u9fa5a-zA-Z0-9\s_-]+$'

# 匹配比例格式，例如 "1:2" 或 "80:20"
RATIO_REGEX = r"^(0(\.\d+)?|1(\.0+)?)$"

# 匹配十六进制颜色代码
HEX_COLOR_REGEX = r'^#([0-9a-fA-F]{3}){1,2}$'

# ===================== 数值类型正则表达式 =====================

# 严格整数正则表达式
# 匹配: 0, -123, +456
# 不匹配: 01, 001, 123.0, --1, +, -
INTEGER_REGEX = r'^[+-]?(0|[1-9][0-9]*)$'
# 示例:
# "0" -> 匹配
# "-123" -> 匹配
# "+456" -> 匹配
# "01" -> 不匹配（前导零）
# "123.0" -> 不匹配
# "-" -> 不匹配

# 匹配浮点数（包括整数）的正则表达式
FLOAT_REGEX = r"[-+]?[0-9]*\.?[0-9]+"
# 示例:
# "0.0" -> 匹配
# "-0.123" -> 匹配
# "+123.456" -> 匹配
# ".5" -> 匹配
# "1." -> 匹配
# "1.2.3" -> 不匹配
# "." -> 不匹配

# 严格科学计数法正则表达式
# 匹配: 1e10, -1.23E-4, +0.5e+8, 1.e3, .5e-2
# 不匹配: 1e, e10, 1.2.3e4, 01e2
SCIENTIFIC_REGEX = r'^[+-]?((0|[1-9][0-9]*)?(\.[0-9]+)?|[0-9]+\.)([eE][+-]?[0-9]+)$'
# 示例:
# "1e10" -> 匹配
# "-1.23E-4" -> 匹配
# "+0.5e+8" -> 匹配
# "1.e3" -> 匹配
# ".5e-2" -> 匹配
# "1e" -> 不匹配
# "e10" -> 不匹配
# "1.2.3e4" -> 不匹配
# "01e2" -> 不匹配

# 综合数值正则表达式（整数、浮点数、科学计数法三者之一）
# 匹配: 123, -0.5, 1.2e3, .5, 1., -123, +456, 0, 1e10, -1.23E-4
# 不匹配: abc, 1.2.3, 01, 1e, e10
NUMBER_REGEX = r'^([+-]?(0|[1-9][0-9]*)|[+-]?((0|[1-9][0-9]*)?\.[0-9]+|[0-9]+\.)|[+-]?((0|[1-9][0-9]*)?(\.[0-9]+)?|[0-9]+\.)([eE][+-]?[0-9]+))$'
# 示例:
# "123" -> 匹配
# "-0.5" -> 匹配
# "1.2e3" -> 匹配
# ".5" -> 匹配
# "1." -> 匹配
# "-123" -> 匹配
# "+456" -> 匹配
# "0" -> 匹配
# "1e10" -> 匹配
# "-1.23E-4" -> 匹配
# "abc" -> 不匹配
# "1.2.3" -> 不匹配
# "01" -> 不匹配
# "1e" -> 不匹配
# "e10" -> 不匹配

