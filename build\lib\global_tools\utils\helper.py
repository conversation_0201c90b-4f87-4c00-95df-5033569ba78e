import weakref
import threading
import time
import os
import inspect
import datetime
import sys
import re
from enum import Enum
from typing import Optional, List, Dict, Union, Callable, Any
from colorama import init, Fore, Back, Style, AnsiToWin32
import traceback
import fnmatch
import hashlib
from datetime import datetime

import stat
import mimetypes
import chardet
from pathlib import Path
from typing import *
import ctypes

# 将导入移到全局作用域，避免每次调用函数时导入
import math
from itertools import islice
from .colors import Colors


def hex_to_ansi(hex_color: str) -> str:
    """
    将16进制颜色转换为ANSI转义序列

    Args:
        hex_color: 16进制颜色代码，例如 "#ffffff" 或 "ffffff"

    Returns:
        ANSI转义序列字符串
    """
    # 移除 # 前缀（如果有）
    hex_color = hex_color.lstrip("#")

    # 验证颜色格式
    if not re.match(r"^[0-9a-fA-F]{6}$", hex_color):
        raise ValueError(f"无效的16进制颜色代码: {hex_color}")

    # 转换为RGB值
    r = int(hex_color[:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:], 16)

    # 返回ANSI转义序列
    return f"\033[38;2;{r};{g};{b}m"


class LogLevel(Enum):
    """
    日志级别枚举，用于控制日志输出的级别

    每个级别包含四个元素：(颜色, 名称, 高亮颜色, 权重)
    权重越大表示级别越高，只有大于等于设置级别的日志才会输出

    级别说明：
    - DEBUG (0): 调试信息，最低级别
    - INFO (1): 一般信息
    - WARNING (2): 警告信息
    - ERROR (3): 错误信息
    - CRITICAL (4): 严重错误信息
    - OFF (999): 特殊级别，设置后将关闭所有日志输出
    """
    DEBUG = (Fore.MAGENTA, "调试", None, 0)  # 最低级别
    INFO = (Fore.BLUE, "信息", None, 1)
    WARNING = (Fore.YELLOW, "警告", None, 2)
    ERROR = (Fore.RED, "错误", None, 3)
    CRITICAL = (Fore.RED + Style.BRIGHT, "严重", None, 4)
    OFF = (Style.RESET_ALL, "关闭", None, 999)  # 特殊级别，用于完全关闭日志输出


class Logger:
    """
    日志记录类

    特性：
    1. 支持彩色输出
    2. 记录文件名和行号
    3. 记录时间戳
    4. 支持多种日志级别
    5. 支持日志文件写入
    6. 支持日志缓存
    7. 支持日志文件大小限制
    8. 支持日志文件数量限制
    9. 支持自定义消息颜色
    10. 支持在IDE中点击跳转到代码位置
    11. 支持设置日志级别过滤（全局和实例级别）
    12. 支持自定义日志格式（全局和实例级别）
    13. 支持类似logging.basicConfig的配置
    14. 支持完全关闭日志输出(LogLevel.OFF)

    使用示例：
    ==========================================================

    1. 基本使用：创建Logger实例并记录不同级别的日志
    ```python
    # 创建默认配置的Logger实例
    logger = Logger()

    # 记录不同级别的日志
    logger.debug("这是一条调试信息")
    logger.info("这是一条普通信息")
    logger.warning("这是一条警告信息")
    logger.error("这是一条错误信息")
    logger.critical("这是一条严重错误信息")
    ```

    2. 使用自定义配置创建Logger
    ```python
    # 创建自定义配置的Logger实例
    logger = Logger(
        cache_size=50,              # 缓存50条日志后写入文件
        enable_file_logging=True,   # 启用文件日志
        log_dir="my_logs",          # 自定义日志目录
        max_file_size_mb=50.0,      # 单个日志文件最大50MB
        max_file_count=5,           # 最多保留5个日志文件
        truncate_length=500,        # 控制台显示的消息截断长度
        ide_link_format="pycharm"   # IDE链接格式
    )
    ```

    3. 使用自定义颜色和消息截断
    ```python
    from colorama import Fore, Back

    # 使用colorama颜色
    logger.info("使用蓝色文本", color=Fore.BLUE)
    logger.warning("使用黄色背景", color=Back.YELLOW)

    # 使用16进制颜色
    logger.debug("使用自定义颜色", color="#FF5733")

    # 不截断消息
    long_message = "这是一条非常长的日志消息..." * 10
    logger.info(long_message, truncate_length=-1)

    # 自定义截断长度
    logger.info(long_message, truncate_length=50)
    ```

    4. 使用日志级别设置（全局和实例级别）
    ```python
    # 全局设置：影响所有未设置实例级别的日志记录器
    Logger.setLevel(LogLevel.WARNING)

    # 创建两个日志记录器
    logger1 = Logger()  # 使用全局级别 (WARNING)
    logger2 = Logger()  # 使用全局级别 (WARNING)

    # 设置实例级别：只影响特定实例
    logger2.set_instance_level(LogLevel.DEBUG)  # 实例2使用DEBUG级别

    # 影响表现
    logger1.debug("此消息不会显示，因为全局级别是WARNING")  # 不显示
    logger1.warning("此警告消息会显示")  # 显示

    logger2.debug("此消息会显示，因为实例级别是DEBUG")  # 显示
    logger2.info("此消息也会显示")  # 显示

    # 更改全局级别
    Logger.setLevel(LogLevel.INFO)

    # 影响未设置实例级别的实例，但不影响设置了实例级别的实例
    logger1.info("现在这条信息可以显示了，因为全局级别变为INFO")  # 显示
    logger2.debug("实例级别仍然优先，此DEBUG消息仍然显示")  # 显示

    # 完全关闭实例2的日志输出
    logger2.set_instance_level(LogLevel.OFF)
    logger2.critical("此消息不会显示，因为实例级别设置为OFF")  # 不显示

    # 全局关闭所有日志输出（未设置实例级别的实例）
    Logger.setLevel(LogLevel.OFF)
    logger1.critical("此消息不会显示，因为全局级别设置为OFF")  # 不显示
    ```

    5. 使用全局配置方法
    ```python
    # 配置全局默认设置
    Logger.basicConfig(
        level=LogLevel.INFO,        # 全局日志级别
        filename="app.log",         # 启用文件日志并指定文件名
        log_dir="app_logs",         # 自定义日志目录
        max_file_size_mb=20,        # 单个日志文件最大20MB
        max_file_count=3,           # 最多保留3个日志文件
        truncate_length=100         # 控制台消息截断长度
    )

    # 创建的实例会自动继承全局配置
    logger1 = Logger()  # 使用全局配置
    logger2 = Logger(truncate_length=200)  # 覆盖部分全局配置

    # 全局关闭所有日志
    Logger.basicConfig(level=LogLevel.OFF)
    ```

    6. 自定义日志格式化器（全局和实例级别）
    ```python
    # 定义一个自定义格式化器
    def my_simple_formatter(record, color, truncate_length):
        # 创建简单的日志格式: [时间] 级别: 消息
        time_str = record["time"].strftime("%H:%M:%S")

        # 控制台输出
        console = f"[{time_str}] {record['levelname']}: {record['message']}"

        # 文件输出
        file = f"[{time_str}] {record['levelname']}: {record['message']}"

        return console, file

    # 设置全局格式化器（影响所有未设置实例格式化器的日志记录器）
    Logger.setFormatter(my_simple_formatter)

    # 创建两个记录器
    logger1 = Logger()  # 使用全局格式化器
    logger2 = Logger()  # 使用全局格式化器

    # 为第二个记录器设置不同的实例级别格式化器
    def instance_formatter(record, color, truncate_length):
        time_str = record["time"].strftime("%H:%M:%S.%f")[:-3]
        return f"实例2 - {time_str} - {record['message']}", f"{time_str} - {record['message']}"

    logger2.set_instance_formatter(instance_formatter)  # 只影响logger2

    # 使用不同的格式
    logger1.info("使用全局格式")  # 使用my_simple_formatter
    logger2.info("使用实例特定格式")  # 使用instance_formatter

    # 恢复默认格式
    Logger.setFormatter(None)  # 恢复全局默认格式
    logger2.set_instance_formatter(None)  # 恢复实例默认格式（将使用全局格式）
    ```

    7. 将日志刷新到文件
    ```python
    # 创建支持文件日志的Logger
    logger = Logger(enable_file_logging=True)

    # 记录一些日志
    for i in range(10):
        logger.info(f"日志消息 {i}")

    # 强制将缓存中的日志写入文件
    logger.flush()
    ```

    8. 使用上下文管理器自动刷新日志
    ```python
    # 使用with语句自动刷新日志
    with Logger(enable_file_logging=True) as logger:
        logger.info("这条日志会在上下文退出时自动写入文件")
        logger.debug("这条调试日志也会自动写入文件")
    # 退出上下文后，日志已自动写入文件
    ```

    9. 错误处理示例
    ```python
    try:
        # 尝试一些可能会失败的操作
        result = 10 / 0
    except Exception as e:
        # 记录异常信息
        logger.error(f"发生错误: {str(e)}")

        # 记录详细的异常堆栈
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
    ```

    10. 综合示例
    ```python
    # 设置全局配置
    Logger.basicConfig(
        level=LogLevel.INFO,
        enable_file_logging=True,
        log_dir="logs",
        max_file_size_mb=10,
        truncate_length=200
    )

    # 创建Logger实例
    logger = Logger()

    # 设置实例特定配置
    logger.set_instance_level(LogLevel.DEBUG)  # 此实例将显示DEBUG级别消息，即使全局级别是INFO

    # 记录应用启动信息
    logger.info("应用程序启动", color="#00FF00")

    try:
        # 应用程序逻辑
        logger.debug("执行某项操作...")  # 这条消息会显示，因为实例级别是DEBUG

        # 业务处理
        logger.info("处理用户请求")

        # 模拟错误
        if True:  # 某些条件
            logger.warning("检测到异常情况")
            raise ValueError("示例错误")
    except Exception as e:
        # 记录错误
        logger.error(f"处理请求时出错: {str(e)}")
    finally:
        # 确保日志被写入文件
        logger.flush()
        logger.info("应用程序关闭")
    ```
    ==========================================================
    """
    # 新增类级别变量，用于设置全局日志级别和格式器
    _level = LogLevel.DEBUG  # 默认记录所有级别日志
    _formatter = None  # 自定义格式化器
    _global_config = {
        "cache_size": 100,
        "enable_file_logging": False,
        "log_dir": "logs",
        "max_file_size_mb": 100.0,
        "max_file_count": 10,
        "truncate_length": 300,
        "ide_link_format": "pycharm",
    }

    def __init__(
        self,
        cache_size: int = None,
        enable_file_logging: bool = None,
        log_dir: str = None,
        max_file_size_mb: float = None,
        max_file_count: int = None,
        truncate_length: int = None,
        ide_link_format: str = None,  # 新增参数，支持不同IDE的链接格式
    ):
        """
        初始化日志记录器

        Args:
            cache_size (int, optional):
                日志缓存大小，当缓存中的日志数量达到此值时，会自动写入文件。
                较大的缓存可以减少文件 I/O 操作，但会占用更多内存。
                默认值: 100，可通过basicConfig全局配置

            enable_file_logging (bool, optional):
                是否启用日志文件记录功能。
                如果设为 False，日志只会输出到控制台，不会写入文件。
                默认值: False，可通过basicConfig全局配置

            log_dir (str, optional):
                日志文件保存的目录路径。
                可以是相对路径或绝对路径，如果目录不存在会自动创建。
                默认值: "logs"，可通过basicConfig全局配置

            max_file_size_mb (float, optional):
                单个日志文件的最大大小，单位为 MB。
                当日志文件大小超过此值时，会自动创建新的日志文件。
                默认值: 100.0 (100MB)，可通过basicConfig全局配置

            max_file_count (int, optional):
                最大日志文件数量。
                当日志文件数量超过此值时，会自动删除最旧的日志文件。
                默认值: 10，可通过basicConfig全局配置

            truncate_length (int, optional):
                控制台显示的消息截取长度。
                设置为-1表示不截取消息。
                默认值: 300，可通过basicConfig全局配置

            ide_link_format (str, optional):
                IDE链接格式，用于在日志中创建可点击链接。
                支持的值: "pycharm" (默认), "vscode", "terminal", "none"
                - "pycharm": 使用PyCharm标准格式 "file_path:line:column"
                - "vscode": 使用VS Code格式
                - "terminal": 使用终端ANSI链接格式
                - "none": 不使用可点击链接
                可通过basicConfig全局配置

        Example:
            >>> # 使用默认配置
            >>> logger = Logger()

            >>> # 自定义配置
            >>> logger = Logger(
            ...     cache_size=50,           # 50条日志写入一次文件
            ...     enable_file_logging=True, # 启用文件日志
            ...     log_dir="my_logs",       # 自定义日志目录
            ...     max_file_size_mb=50.0,   # 单个日志文件最大50MB
            ...     max_file_count=5,        # 最多保留5个日志文件
            ...     truncate_length=500,     # 消息截断长度设为500
            ...     ide_link_format="pycharm" # 使用PyCharm链接格式
            ... )

            >>> # 全局配置后创建实例，会自动继承全局配置
            >>> Logger.basicConfig(
            ...     level=LogLevel.INFO,
            ...     enable_file_logging=True,
            ...     log_dir="app_logs"
            ... )
            >>> logger = Logger()  # 自动使用全局配置
        """
        # 使用全局配置或参数提供的值
        self._cache_size = cache_size if cache_size is not None else Logger._global_config["cache_size"]
        self._log_cache: List[Dict] = []
        self._log_count = 0
        self._truncate_length = truncate_length if truncate_length is not None else Logger._global_config["truncate_length"]
        self._ide_link_format = (ide_link_format or Logger._global_config["ide_link_format"]).lower()  # 保存IDE链接格式设置

        # 新增参数
        self._enable_file_logging = enable_file_logging if enable_file_logging is not None else Logger._global_config["enable_file_logging"]
        self._log_dir = os.path.abspath(log_dir or Logger._global_config["log_dir"])
        self._max_file_size = (max_file_size_mb or Logger._global_config["max_file_size_mb"]) * 1024 * 1024  # 转换为字节
        self._max_file_count = max_file_count if max_file_count is not None else Logger._global_config["max_file_count"]
        self._current_log_file = None

        # 添加实例级别的日志级别和格式化器属性
        self._instance_level = None  # 实例级别的日志级别，None表示使用全局级别
        self._instance_formatter = None  # 实例级别的格式化器，None表示使用全局格式化器

        # 如果启用文件日志，确保日志目录存在
        if self._enable_file_logging:
            os.makedirs(self._log_dir, exist_ok=True)
            self._init_log_file()

    def _init_log_file(self):
        """初始化日志文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self._current_log_file = os.path.join(self._log_dir, f"log_{timestamp}.txt")

        # 检查并清理旧的日志文件
        self._cleanup_old_logs()

    def _cleanup_old_logs(self):
        """清理旧的日志文件"""
        try:
            # 获取所有日志文件
            log_files = []
            for file in os.listdir(self._log_dir):
                if file.startswith("log_") and file.endswith(".txt"):
                    full_path = os.path.join(self._log_dir, file)
                    log_files.append((full_path, os.path.getmtime(full_path)))

            # 按修改时间排序
            log_files.sort(key=lambda x: x[1], reverse=True)

            # 删除超出数量限制的旧文件
            for file_path, _ in log_files[self._max_file_count:]:
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"删除旧日志文件失败: {str(e)}")
                    traceback.print_exc()
        except Exception as e:
            print(f"清理日志文件失败: {str(e)}")
            traceback.print_exc()

    def _check_file_size(self) -> bool:
        """检查当前日志文件大小，如果超过限制则创建新文件"""
        if not self._current_log_file or not os.path.exists(self._current_log_file):
            self._init_log_file()
            return True

        try:
            current_size = os.path.getsize(self._current_log_file)
            if current_size >= self._max_file_size:
                self._init_log_file()
                return True
        except Exception as e:
            print(f"检查文件大小失败: {str(e)}")
            traceback.print_exc()
            self._init_log_file()
            return True

        return False

    def _get_caller_info(self) -> tuple:
        """获取调用者信息"""
        current_frame = inspect.currentframe()
        frames = inspect.getouterframes(current_frame)

        # 获取工作目录
        workspace_root = os.path.abspath(os.path.curdir)

        # 尝试获取 Logger 类定义文件名，兼容内置类和特殊情况
        try:
            logger_file = inspect.getfile(self.__class__)
        except TypeError:
            # fallback: 使用当前文件名（即本文件）
            logger_file = __file__

        # 查找实际调用日志方法的位置
        for frame_info in frames:
            # 跳过日志类内部的调用
            try:
                if frame_info.filename != logger_file:
                    abs_path = os.path.abspath(frame_info.filename)
                    # 处理不同驱动器的情况
                    try:
                        rel_path = os.path.relpath(abs_path, workspace_root)
                    except ValueError:
                        # 如果在不同驱动器上，直接使用绝对路径
                        rel_path = abs_path
                    filename = os.path.basename(frame_info.filename)
                    lineno = frame_info.lineno
                    return filename, lineno, rel_path
            except Exception:
                continue  # 某些 frame 可能没有 filename 属性，跳过

        return "unknown", 0, "unknown"

    def _format_log(self, level: LogLevel, message: str, message_color, truncate_length: int) -> tuple:  # type: ignore
        """格式化日志消息"""
        filename, lineno, rel_path = self._get_caller_info()
        # 获取当前时间，兼容解释器关闭时 ImportError
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        except ImportError:
            current_time = "shutting down"

        # 处理颜色
        if message_color:
            if isinstance(message_color, str) and message_color.startswith(("#", "0x")):
                # 16进制颜色
                try:
                    msg_color = hex_to_ansi(message_color)
                except ValueError as e:
                    print(f"颜色格式错误: {e}")
                    msg_color = Style.RESET_ALL
            else:
                # colorama颜色
                msg_color = message_color
        else:
            msg_color = level.value[2] or Style.RESET_ALL

        # 控制台显示消息（截断处理）
        display_message = (
            message
            if truncate_length == -1 or len(message) <= truncate_length
            else message[:truncate_length] + "..."
        )

        # 获取绝对路径用于创建可点击链接
        workspace_root = os.path.abspath(os.path.curdir)
        abs_path = os.path.abspath(os.path.join(workspace_root, rel_path))

        # 创建简化显示路径
        def simplify_path(path):
            """简化路径显示，保留后面的重要部分"""
            # 获取路径各个部分
            parts = path.split(os.sep)

            # 如果路径很短，直接返回
            if len(parts) <= 3:
                return path

            # 保留最后的3个路径部分，其余部分用...替代
            shortened = os.path.join("...", *parts[-3:])
            return shortened

        # 简化显示用的路径
        display_path = simplify_path(abs_path)

        # 创建不同IDE格式的链接
        location_info = ""

        if self._ide_link_format == "pycharm":
            # PyCharm格式: "文件路径:行号"，输出时格式为：File "文件路径", line 行号
            # 这是PyCharm更容易识别的格式
            # 使用完整路径确保点击跳转功能，但显示简化路径
            location_info = f'File "{abs_path}", line {lineno}'

            # 替换显示的路径部分为简化版本，保持点击跳转功能
            if os.sep == "\\":  # Windows系统
                quoted_path = abs_path.replace("\\", "\\\\")  # 处理反斜杠转义
                quoted_display_path = display_path.replace("\\", "\\\\")
                location_info = location_info.replace(
                    f'"{quoted_path}"', f'"{quoted_display_path}"'
                )
            else:  # 非Windows系统
                location_info = location_info.replace(
                    f'"{abs_path}"', f'"{display_path}"'
                )

        elif self._ide_link_format == "vscode":
            # VS Code格式: "文件路径:行号:列号"
            # 使用简化路径显示，但保留实际点击跳转链接
            location_info = f"{display_path}:{lineno}:1"  # 列号默认为1

            # 在VS Code中需要特殊处理，确保链接仍然有效
            # 方法是使用特殊格式: <文件路径>:行号:列号
            # 显示为简化路径，但点击时使用完整路径
            location_info = f"<{abs_path}>:{lineno}:1 ({display_path}:{lineno})"

        elif self._ide_link_format == "terminal":
            # 终端ANSI链接格式
            # 使用完整路径创建链接，但显示简化路径
            file_uri = f"file://{abs_path.replace(os.sep, '/')}:{lineno}"
            location_info = (
                f"\033]8;;{file_uri}\033\\{display_path}:{lineno}\033]8;;\033\\"
            )

        else:  # "none"或其他值
            # 不使用可点击链接，只显示简化路径
            location_info = f"{display_path}:{lineno}"

        # 控制台彩色输出（带可点击链接）
        color_output = (
            f"{Fore.YELLOW}[{location_info}]{Style.RESET_ALL} "
            f"{Fore.GREEN}{current_time}{Style.RESET_ALL} "
            f"{level.value[0]}{level.value[1]}{Style.RESET_ALL}:"
            f"\t{msg_color}{display_message}{Style.RESET_ALL}"
        )

        # 文件纯文本输出（保持原格式，完整消息）
        file_output = (
            f"[{rel_path}:{lineno}] "
            f"{current_time} "
            f"{level.value[1]}:\n"
            f"\t{message}"
        )

        return color_output, file_output

    def _write_to_file(self):
        """将缓存的日志写入文件"""
        if not self._enable_file_logging or not self._log_cache:
            return

        try:
            # 检查文件大小并在需要时创建新文件
            self._check_file_size()

            # 写入日志
            with open(self._current_log_file, "a", encoding="utf-8") as f:
                for log in self._log_cache:
                    f.write(log["file_output"] + "\n")

            self._log_cache.clear()
            self._log_count = 0
        except Exception as e:
            print(f"写入日志文件失败: {str(e)}")
            traceback.print_exc()

    def _log(
        self, level: LogLevel, message: str, color=None, truncate_length: int = None
    ):
        """
        内部日志记录方法

        Args:
            level: 日志级别
            message: 日志消息
            color: 消息颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        try:
            # 优先使用实例级别的日志级别，如果未设置则使用全局级别
            effective_level = self._instance_level if self._instance_level is not None else Logger._level

            # 增加日志级别判断，如果消息级别低于设置的级别，则不记录
            if level.value[3] < effective_level.value[3]:
                return

            message = str(message)
            # 使用实例的默认值（如果未指定）
            actual_truncate_length = (
                truncate_length
                if truncate_length is not None
                else self._truncate_length
            )

            # 优先使用实例级别的格式化器，如果未设置则使用全局格式化器
            effective_formatter = self._instance_formatter if self._instance_formatter is not None else Logger._formatter

            # 如果设置了有效的格式化器，则使用该格式化器
            if effective_formatter is not None:
                try:
                    # 获取调用者信息
                    filename, lineno, rel_path = self._get_caller_info()
                    # 准备格式化器需要的信息
                    format_info = {
                        "level": level,
                        "levelname": level.value[1],
                        "filename": filename,
                        "lineno": lineno,
                        "pathname": rel_path,
                        "message": message,
                        "time": datetime.now(),
                        # 添加额外信息，确保格式化器清楚截断设置
                        "truncate_disabled": actual_truncate_length == -1,
                        "raw_message": message,  # 添加原始消息
                    }
                    # 使用有效的格式化器
                    color_output, file_output = effective_formatter(format_info, color, actual_truncate_length)
                except Exception as e:
                    print(f"使用自定义格式化器失败: {str(e)}")
                    traceback.print_exc()
                    # 格式化失败时使用默认格式化
                    color_output, file_output = self._format_log(
                        level, message, color, actual_truncate_length
                    )
            else:
                # 格式化日志消息
                color_output, file_output = self._format_log(
                    level, message, color, actual_truncate_length
                )

            # 输出到控制台
            print(color_output)

            # 添加到缓存
            if self._enable_file_logging:
                self._log_cache.append(
                    {"level": level, "message": message, "file_output": file_output}
                )
                self._log_count += 1

                # 达到缓存大小时写入文件
                if self._log_count >= self._cache_size:
                    self._write_to_file()

        except Exception as e:
            print(f"日志记录出错: {str(e)}")
            traceback.print_exc()

    def debug(self, message: str, color=Colors.DEBUG_HIGHLIGHT, truncate_length: int = -1):  # type: ignore
        """
        记录调试级别日志

        Args:
            message: 日志消息
            color: 消息颜色，可以是以下格式：
                  - 16进制颜色代码，如 "#ffffff" 或 "ffffff"
                  - colorama颜色常量，如 Fore.RED
                  默认为 None 表示使用默认颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        self._log(LogLevel.DEBUG, message, color, truncate_length)

    def info(self, message: str, color=None, truncate_length: int = None):  # type: ignore
        """
        记录信息级别日志

        Args:
            message: 日志消息
            color: 消息颜色，可以是以下格式：
                  - 16进制颜色代码，如 "#ffffff" 或 "ffffff"
                  - colorama颜色常量，如 Fore.RED
                  默认为 None 表示使用默认颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        self._log(LogLevel.INFO, message, color, truncate_length)

    def warning(self, message: str, color=Colors.YELLOW, truncate_length: int = None, **kwargs):  # type: ignore
        """
        记录警告级别日志

        Args:
            message: 日志消息
            color: 消息颜色，可以是以下格式：
                  - 16进制颜色代码，如 "#ffffff" 或 "ffffff"
                  - colorama颜色常量，如 Fore.RED
                  默认为 None 表示使用默认颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        self._log(LogLevel.WARNING, message, color, truncate_length)

    def error(self, message: str, color=Colors.ERROR, truncate_length: int = None, **kwargs):  # type: ignore
        """
        记录错误级别日志

        Args:
            message: 日志消息
            color: 消息颜色，可以是以下格式：
                  - 16进制颜色代码，如 "#ffffff" 或 "ffffff"
                  - colorama颜色常量，如 Fore.RED
                  默认为 None 表示使用默认颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        self._log(LogLevel.ERROR, message, color, truncate_length)

    def critical(self, message: str, color=None, truncate_length: int = None):  # type: ignore
        """
        记录严重级别日志

        Args:
            message: 日志消息
            color: 消息颜色，可以是以下格式：
                  - 16进制颜色代码，如 "#ffffff" 或 "ffffff"
                  - colorama颜色常量，如 Fore.RED
                  默认为 None 表示使用默认颜色
            truncate_length: 控制台显示的消息截取长度
                           如果为None，则使用实例的默认值
                           设置为-1表示不截取消息
        """
        self._log(LogLevel.CRITICAL, message, color, truncate_length)

    def flush(self):
        """强制将缓存的日志写入文件"""
        self._write_to_file()

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出时写入所有缓存的日志"""
        self.flush()

    def set_instance_level(self, level: LogLevel):
        """
        设置实例级别的日志级别

        此方法只影响当前实例的日志级别设置，不影响其他实例。
        要设置全局日志级别，请使用类方法 Logger.setLevel()。

        Args:
            level: 日志级别，必须是LogLevel枚举值之一
                  设置为LogLevel.OFF可完全关闭日志输出

        Example:
            >>> # 实例级别设置
            >>> logger = Logger()
            >>> logger.set_instance_level(LogLevel.INFO)  # 只影响这个实例
            >>>
            >>> # 全局级别设置
            >>> Logger.setLevel(LogLevel.WARNING)  # 影响所有未设置实例级别的实例
            >>>
            >>> # 完全关闭日志输出
            >>> logger.set_instance_level(LogLevel.OFF)  # 关闭这个实例的所有日志输出

        Raises:
            ValueError: 如果日志级别不是LogLevel枚举值之一
        """
        try:
            if not isinstance(level, LogLevel):
                raise ValueError(f"日志级别必须是LogLevel枚举值之一，而不是{type(level)}")

            # 设置实例级别的日志级别
            self._instance_level = level
        except Exception as e:
            print(f"设置实例日志级别失败: {str(e)}")
            traceback.print_exc()

    def set_instance_formatter(self, formatter):
        """
        设置实例级别的日志格式化器

        此方法只影响当前实例的格式化器设置，不影响其他实例。
        要设置全局格式化器，请使用类方法 Logger.setFormatter()。

        Args:
            formatter: 格式化函数，接收日志记录字典和颜色设置、截断长度参数，返回(控制台输出, 文件输出)元组
                      设置为None时使用默认格式化器

        格式化函数接口:
            def custom_formatter(record, color, truncate_length):
                # record: 包含日志记录信息的字典，包含如下字段:
                #   - level: LogLevel枚举值
                #   - levelname: 日志级别名称
                #   - filename: 日志调用者文件名
                #   - lineno: 日志调用者行号
                #   - pathname: 日志调用者文件相对路径
                #   - message: 日志消息
                #   - time: 日志记录时间(datetime对象)
                #   - truncate_disabled: 布尔值，表示是否禁用截断（当truncate_length=-1时）
                #   - raw_message: 原始消息内容，未经处理
                # color: 消息颜色设置
                # truncate_length: 控制台消息截断长度
                #
                # 返回值: (控制台输出字符串, 文件输出字符串)的元组

        Example:
            >>> # 自定义实例级别格式化器
            >>> def my_instance_formatter(record, color, truncate_length):
            ...     time_str = record["time"].strftime("%H:%M:%S")
            ...     message = record["message"]
            ...     if not record.get("truncate_disabled", False) and truncate_length > 0 and len(message) > truncate_length:
            ...         message = message[:truncate_length] + "..."
            ...     console = f"[{time_str}] {record['levelname']}: {message}"
            ...     file = f"[{time_str}] {record['levelname']}: {record['raw_message']}"
            ...     return console, file
            ...
            >>> # 设置实例格式化器
            >>> logger = Logger()
            >>> logger.set_instance_formatter(my_instance_formatter)  # 只影响这个实例
            >>>
            >>> # 设置全局格式化器
            >>> Logger.setFormatter(another_formatter)  # 影响所有未设置实例格式化器的实例
            >>>
            >>> # 恢复默认格式（实例级别）
            >>> logger.set_instance_formatter(None)

        Raises:
            ValueError: 如果formatter不是可调用对象且不是None
        """
        try:
            if formatter is not None and not callable(formatter):
                raise ValueError(f"格式化器必须是可调用对象或None，而不是{type(formatter)}")

            # 设置实例级别的格式化器
            self._instance_formatter = formatter
        except Exception as e:
            print(f"设置实例格式化器失败: {str(e)}")
            traceback.print_exc()

    @staticmethod
    def setLevel(level: LogLevel):
        """
        设置全局日志输出级别，低于此级别的日志将不会被记录

        当作为类方法调用时，会设置全局日志级别，影响所有未设置实例级别的实例。
        要设置单个实例的日志级别，请使用实例方法：logger.setLevel()

        Args:
            level: 日志级别，必须是LogLevel枚举值之一
                  设置为LogLevel.OFF可完全关闭所有日志输出

        Example:
            >>> # 设置只输出INFO及以上级别的日志（全局设置）
            >>> Logger.setLevel(LogLevel.INFO)
            >>>
            >>> # 设置只输出WARNING及以上级别的日志（全局设置）
            >>> Logger.setLevel(LogLevel.WARNING)
            >>>
            >>> # 恢复默认，输出所有级别的日志（全局设置）
            >>> Logger.setLevel(LogLevel.DEBUG)
            >>>
            >>> # 完全关闭所有日志输出（全局设置）
            >>> Logger.setLevel(LogLevel.OFF)
            >>>
            >>> # 设置单个实例的日志级别
            >>> logger = Logger()
            >>> logger.setLevel(LogLevel.ERROR)  # 只影响这个实例
        """
        try:
            if not isinstance(level, LogLevel):
                raise ValueError(f"日志级别必须是LogLevel枚举值之一，而不是{type(level)}")

            Logger._level = level
        except Exception as e:
            print(f"设置全局日志级别失败: {str(e)}")
            traceback.print_exc()

    @staticmethod
    def setFormatter(formatter):
        """
        设置全局日志消息的统一格式化器

        当作为类方法调用时，会设置全局格式化器，影响所有未设置实例格式化器的实例。
        要设置单个实例的格式化器，请使用实例方法：logger.setFormatter()

        Args:
            formatter: 格式化函数，接收日志记录字典和颜色设置、截断长度参数，返回(控制台输出, 文件输出)元组
                     设置为None时使用默认格式化器

        格式化函数接口:
            def custom_formatter(record, color, truncate_length):
                # record: 包含日志记录信息的字典，包含如下字段:
                #   - level: LogLevel枚举值
                #   - levelname: 日志级别名称
                #   - filename: 日志调用者文件名
                #   - lineno: 日志调用者行号
                #   - pathname: 日志调用者文件相对路径
                #   - message: 日志消息
                #   - time: 日志记录时间(datetime对象)
                #   - truncate_disabled: 布尔值，表示是否禁用截断（当truncate_length=-1时）
                #   - raw_message: 原始消息内容，未经处理
                # color: 消息颜色设置
                # truncate_length: 控制台消息截断长度
                #
                # 返回值: (控制台输出字符串, 文件输出字符串)的元组

        Example:
            >>> # 自定义全局格式化器
            >>> def my_formatter(record, color, truncate_length):
            ...     # 简单格式: [时间] 级别: 消息
            ...     time_str = record["time"].strftime("%H:%M:%S")
            ...     # 根据truncate_disabled决定是否截断
            ...     message = record["message"]
            ...     if not record.get("truncate_disabled", False) and truncate_length > 0 and len(message) > truncate_length:
            ...         message = message[:truncate_length] + "..."
            ...     console = f"[{time_str}] {record['levelname']}: {message}"
            ...     file = f"[{time_str}] {record['levelname']}: {record['raw_message']}"
            ...     return console, file
            ...
            >>> # 设置全局格式化器
            >>> Logger.setFormatter(my_formatter)
            >>>
            >>> # 设置单个实例的格式化器
            >>> logger = Logger()
            >>> logger.setFormatter(another_formatter)  # 只影响这个实例
            >>>
            >>> # 恢复默认全局格式
            >>> Logger.setFormatter(None)
        """
        try:
            if formatter is not None and not callable(formatter):
                raise ValueError(f"格式化器必须是可调用对象或None，而不是{type(formatter)}")

            Logger._formatter = formatter
        except Exception as e:
            print(f"设置全局日志格式化器失败: {str(e)}")
            traceback.print_exc()

    @staticmethod
    def basicConfig(**kwargs):
        """
        配置日志系统的基本参数，类似于logging.basicConfig

        此方法设置全局日志配置，会影响所有未特别指定实例级别设置的Logger实例。
        已经设置了实例级别的日志级别或格式化器的实例将保持其实例级别的设置。

        支持的参数:
            level: 日志级别，LogLevel枚举值之一，默认为DEBUG
                  设置为LogLevel.OFF可完全关闭所有日志输出
            format: 自定义格式化器，可调用对象
            filename: 日志文件名，设置此参数将启用文件日志
            filemode: 文件打开模式，默认为'a'（追加）
            log_dir: 日志目录，默认为'logs'
            cache_size: 日志缓存大小，默认为100
            max_file_size_mb: 单个日志文件最大大小(MB)，默认为100.0
            max_file_count: 最大日志文件数量，默认为10
            truncate_length: 控制台消息截断长度，默认为300
            ide_link_format: IDE链接格式，默认为'pycharm'

        Example:
            >>> # 基本配置（影响所有新创建的和未设置实例级别的实例）
            >>> Logger.basicConfig(
            ...     level=LogLevel.INFO,  # 设置全局日志级别为INFO
            ...     filename="app.log",   # 启用文件日志
            ...     log_dir="my_logs",    # 自定义日志目录
            ...     max_file_size_mb=10,  # 单个日志文件最大10MB
            ...     truncate_length=500   # 控制台消息截断长度500
            ... )
            >>>
            >>> # 创建两个日志记录器
            >>> logger1 = Logger()  # 使用全局配置
            >>> logger2 = Logger()  # 也使用全局配置
            >>>
            >>> # 设置实例级别配置
            >>> logger2.set_instance_level(LogLevel.DEBUG)  # 仅影响logger2
            >>>
            >>> # 关闭所有日志输出
            >>> Logger.basicConfig(level=LogLevel.OFF)  # 全局关闭日志
            >>>
            >>> # 使用自定义格式化器
            >>> def simple_format(record, color, truncate_length):
            ...     time_str = record["time"].strftime("%H:%M:%S")
            ...     console = f"[{time_str}] {record['levelname']}: {record['message']}"
            ...     file = console
            ...     return console, file
            ...
            >>> # 设置全局格式化器
            >>> Logger.basicConfig(
            ...     level=LogLevel.WARNING,
            ...     format=simple_format
            ... )
            >>>
            >>> # 记录日志
            >>> logger1.debug("此消息不显示，全局级别是WARNING")  # 不显示
            >>> logger2.debug("此消息显示，因为实例级别是DEBUG")  # 显示
        """
        try:
            # 处理日志级别
            if 'level' in kwargs:
                level = kwargs.pop('level')
                Logger.setLevel(level)

            # 处理格式化器
            if 'format' in kwargs:
                formatter = kwargs.pop('format')
                Logger.setFormatter(formatter)

            # 更新全局配置
            for key, value in kwargs.items():
                if key in Logger._global_config:
                    Logger._global_config[key] = value
                elif key == 'filename' and value:
                    # 使用文件名启用文件日志
                    Logger._global_config['enable_file_logging'] = True
                    if not os.path.isabs(value):
                        # 如果是相对路径，则放在log_dir下
                        log_dir = Logger._global_config.get('log_dir', 'logs')
                        if not os.path.exists(log_dir):
                            os.makedirs(log_dir, exist_ok=True)
                        value = os.path.join(log_dir, value)
                    # 创建初始日志文件
                    log_dir = os.path.dirname(value)
                    if not os.path.exists(log_dir):
                        os.makedirs(log_dir, exist_ok=True)
                    # 根据filemode创建或追加文件
                    filemode = kwargs.get('filemode', 'a')
                    try:
                        with open(value, filemode, encoding='utf-8') as f:
                            # 添加初始时间戳
                            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            f.write(f"=== 日志开始于 {timestamp} ===\n")
                    except Exception as e:
                        print(f"创建日志文件失败: {str(e)}")
                        traceback.print_exc()
                elif key == 'filemode':
                    # filemode已在filename处理中使用了
                    pass
                else:
                    print(f"未知的配置项: {key}")
        except Exception as e:
            print(f"配置日志系统失败: {str(e)}")
            traceback.print_exc()


class Singleton:
    """
    改进的单例模式装饰器

    特性：
    1. 每个类使用独立的锁确保线程安全
    2. 使用弱引用字典避免内存泄漏
    3. 支持多个类的单例管理
    4. 提供实例管理和查询功能
    """

    _instances = weakref.WeakValueDictionary()  # 使用弱引用字典避免内存泄漏
    _locks = weakref.WeakKeyDictionary()  # 为每个类使用独立的锁
    _global_lock = threading.Lock()  # 用于管理锁字典的全局锁

    def __init__(self, cls):
        self._cls = cls
        # 在初始化时为类创建独立的锁
        with self._global_lock:
            if cls not in self._locks:
                self._locks[cls] = threading.Lock()

    def __call__(self, *args, **kwargs):
        if self._cls not in self._instances:
            with self._locks[self._cls]:  # 使用类特定的锁
                if self._cls not in self._instances:  # 双重检查
                    instance = self._cls(*args, **kwargs)
                    self._instances[self._cls] = instance
        return self._instances[self._cls]

    def __getattr__(self, name):
        return getattr(self._cls, name)

    @classmethod
    def clear_instance(cls, target_cls):
        """
        清除指定类的单例实例
        """
        with cls._global_lock:
            if target_cls in cls._instances:
                del cls._instances[target_cls]

    @classmethod
    def clear_all_instances(cls):
        """
        清除所有单例实例
        """
        with cls._global_lock:
            cls._instances.clear()
            cls._locks.clear()

    @classmethod
    def get_instance(cls, target_cls):
        """
        获取指定类的单例实例
        """
        with cls._global_lock:
            return cls._instances.get(target_cls)

    @classmethod
    def has_instance(cls, target_cls):
        """
        检查指定类是否已创建单例实例
        """
        with cls._global_lock:
            return target_cls in cls._instances

    def get_instance_info(self):
        """
        获取当前类实例的详细信息
        """
        with self._global_lock:
            instance = self._instances.get(self._cls)
            if instance is None:
                return None
            return {
                "create_time": time.time(),
                "args": (),
                "kwargs": {},
                "instance": instance,
            }

    @classmethod
    def get_all_instances(cls):
        """
        获取所有类的实例信息
        """
        with cls._global_lock:
            result = {}
            for target_cls in cls._instances:
                instance = cls._instances.get(target_cls)
                if instance is not None:
                    result[target_cls] = {
                        "create_time": time.time(),
                        "args": (),
                        "kwargs": {},
                        "instance": instance,
                    }
            return result

    @classmethod
    def get_instance_count(cls):
        """
        获取当前存在的单例实例数量
        """
        with cls._global_lock:
            return len(cls._instances)


class ClassInstanceManager:
    """
    类实例管理器

    用于创建、存储和获取类的实例化对象，通过键值标识不同的实例。

    特性：
    1. 静态方法实现，无需实例化即可使用
    2. 线程安全的实例管理
    3. 支持异常处理和日志记录
    4. 支持通过key值标识和获取不同的实例

    使用示例：
    ==========================================================

    1. 基本使用：创建并获取实例
    ```python
    # 定义一个示例类
    class MyClass:
        def __init__(self, name, value=0):
            self.name = name
            self.value = value

        def get_info(self):
            return f"Name: {self.name}, Value: {self.value}"

    # 创建实例
    ClassInstanceManager.create_instance(MyClass, "instance1", "测试实例", value=42)

    # 获取实例
    instance = ClassInstanceManager.get_instance("instance1")

    # 使用实例
    if instance:
        print(instance.get_info())  # 输出: Name: 测试实例, Value: 42
    ```

    2. 高级用法：错误处理
    ```python
    # 尝试创建重复的实例
    try:
        ClassInstanceManager.create_instance(MyClass, "instance1", "新实例")
        print("实例创建成功")
    except ValueError as e:
        print(f"错误: {e}")  # 输出: 错误: 键值'instance1'已存在，请使用不同的键值或先删除现有实例

    # 尝试获取不存在的实例
    instance = ClassInstanceManager.get_instance("not_exist")
    if instance is None:
        print("实例不存在")  # 输出: 实例不存在
    ```

    3. 管理多个不同类的实例
    ```python
    class AnotherClass:
        def __init__(self, data):
            self.data = data

    # 创建不同类的实例
    ClassInstanceManager.create_instance(MyClass, "my_instance", "MyClass实例")
    ClassInstanceManager.create_instance(AnotherClass, "another_instance", "数据")

    # 获取不同类的实例
    instance1 = ClassInstanceManager.get_instance("my_instance")
    instance2 = ClassInstanceManager.get_instance("another_instance")

    print(instance1.get_info())  # 输出: Name: MyClass实例, Value: 0
    print(instance2.data)  # 输出: 数据
    ```

    4. 删除和检查实例
    ```python
    # 检查实例是否存在
    exists = ClassInstanceManager.has_instance("my_instance")
    print(f"实例存在: {exists}")  # 输出: 实例存在: True

    # 删除实例
    ClassInstanceManager.remove_instance("my_instance")

    # 再次检查
    exists = ClassInstanceManager.has_instance("my_instance")
    print(f"实例存在: {exists}")  # 输出: 实例存在: False
    ```

    5. 获取所有实例和实例数量
    ```python
    # 创建多个实例
    ClassInstanceManager.create_instance(MyClass, "instance1", "实例1")
    ClassInstanceManager.create_instance(MyClass, "instance2", "实例2")

    # 获取实例数量
    count = ClassInstanceManager.get_instance_count()
    print(f"实例数量: {count}")  # 输出: 实例数量: 2

    # 获取所有实例键值
    keys = ClassInstanceManager.get_all_instance_keys()
    print(f"所有实例键值: {keys}")  # 输出: 所有实例键值: ['instance1', 'instance2']

    # 获取所有实例字典
    instances = ClassInstanceManager.get_all_instances()
    for key, instance in instances.items():
        print(f"键值: {key}, 实例: {instance}")
    ```
    ==========================================================
    ==========================================================
    新增：
    - enable_log: 控制是否输出日志，默认不输出。
    - set_enable_log(enable: bool): 动态设置日志开关。
    ==========================================================
    """
    # 存储所有实例的字典
    _instances = {}

    # 用于线程安全操作的锁
    _lock = threading.Lock()

    # 获取当前类的logger
    _logger = None
    try:
        _logger = Logger()
    except Exception:
        # 如果Logger不可用，使用简单的打印函数
        pass

    # 新增：日志开关，默认不输出
    enable_log = False

    @classmethod
    def set_enable_log(cls, enable: bool):
        """
        设置是否输出日志。
        Args:
            enable (bool): True 启用日志，False 禁用日志。
        用法：
            ClassInstanceManager.set_enable_log(True)
        """
        cls.enable_log = bool(enable)

    @classmethod
    def __log_info(cls, message):
        """内部方法：记录信息日志（受 enable_log 控制）"""
        if not cls.enable_log:
            return
        if cls._logger:
            cls._logger.info(message)
        else:
            print(f"[INFO] ClassInstanceManager: {message}")

    @classmethod
    def __log_error(cls, message):
        """内部方法：记录错误日志（受 enable_log 控制）"""
        if not cls.enable_log:
            return
        if cls._logger:
            cls._logger.error(message)
        else:
            print(f"[ERROR] ClassInstanceManager: {message}")
            traceback.print_exc()

    @classmethod
    def create_instance(cls, class_obj, key, *args, **kwargs):
        """
        创建并存储类的实例

        Args:
            class_obj: 要实例化的类对象
            key: 用于标识实例的键值（必须是可哈希的类型，如字符串或数字）
            *args: 传递给类构造函数的位置参数
            **kwargs: 传递给类构造函数的关键字参数

        Returns:
            None

        Raises:
            ValueError: 当键值已存在时
            TypeError: 当class_obj不是可实例化的类型或key不是可哈希类型时
            Exception: 当实例化过程中发生其他异常时
        """
        try:
            # 验证参数
            if not isinstance(key, (str, int, float, bool, tuple, frozenset)):
                raise TypeError(f"键值必须是可哈希类型，而不是 {type(key)}")

            if not callable(class_obj):
                raise TypeError(f"class_obj必须是可调用的类型，而不是 {type(class_obj)}")

            with cls._lock:
                # 检查键值是否已存在
                if key in cls._instances:
                    raise ValueError(f"键值'{key}'已存在，请使用不同的键值或先删除现有实例")

                # 创建实例
                instance = class_obj(*args, **kwargs)

                # 存储实例
                cls._instances[key] = instance

                cls.__log_info(f"成功创建并存储了键值为'{key}'的{class_obj.__name__}实例")

        except (TypeError, ValueError) as e:
            cls.__log_error(f"创建实例失败: {str(e)}")
            raise
        except Exception as e:
            cls.__log_error(f"创建实例时发生未预期的错误: {str(e)}")
            raise RuntimeError(f"创建实例时发生错误: {str(e)}") from e

    @classmethod
    def get_instance(cls, key):
        """
        获取指定键值的实例

        Args:
            key: 实例的标识键值

        Returns:
            保存的实例对象，如果键值不存在则返回None
        """
        try:
            with cls._lock:
                instance = cls._instances.get(key)

                if instance is None:
                    cls.__log_info(f"未找到键值为'{key}'的实例")
                else:
                    cls.__log_info(f"成功获取键值为'{key}'的实例")

                return instance

        except Exception as e:
            cls.__log_error(f"获取实例时发生错误: {str(e)}")
            return None

    @classmethod
    def has_instance(cls, key):
        """
        检查指定键值的实例是否存在

        Args:
            key: 实例的标识键值

        Returns:
            bool: 如果实例存在则返回True，否则返回False
        """
        try:
            with cls._lock:
                return key in cls._instances
        except Exception as e:
            cls.__log_error(f"检查实例存在性时发生错误: {str(e)}")
            return False

    @classmethod
    def remove_instance(cls, key):
        """
        移除指定键值的实例

        Args:
            key: 实例的标识键值

        Returns:
            bool: 如果成功移除则返回True，如果键值不存在则返回False
        """
        try:
            with cls._lock:
                if key in cls._instances:
                    del cls._instances[key]
                    cls.__log_info(f"成功移除键值为'{key}'的实例")
                    return True
                else:
                    cls.__log_info(f"无法移除不存在的键值'{key}'")
                    return False
        except Exception as e:
            cls.__log_error(f"移除实例时发生错误: {str(e)}")
            return False

    @classmethod
    def clear_all_instances(cls):
        """
        清除所有存储的实例

        Returns:
            int: 清除的实例数量
        """
        try:
            with cls._lock:
                count = len(cls._instances)
                cls._instances.clear()
                cls.__log_info(f"已清除所有实例，共{count}个")
                return count
        except Exception as e:
            cls.__log_error(f"清除所有实例时发生错误: {str(e)}")
            return 0

    @classmethod
    def get_instance_count(cls):
        """
        获取当前存储的实例数量

        Returns:
            int: 实例数量
        """
        try:
            with cls._lock:
                return len(cls._instances)
        except Exception as e:
            cls.__log_error(f"获取实例数量时发生错误: {str(e)}")
            return 0

    @classmethod
    def get_all_instance_keys(cls):
        """
        获取所有实例的键值列表

        Returns:
            List: 所有实例键值的列表
        """
        try:
            with cls._lock:
                return list(cls._instances.keys())
        except Exception as e:
            cls.__log_error(f"获取所有实例键值时发生错误: {str(e)}")
            return []

    @classmethod
    def get_all_instances(cls):
        """
        获取所有实例的字典

        Returns:
            Dict: 键值到实例的映射字典
        """
        try:
            with cls._lock:
                # 返回字典的浅拷贝，避免外部修改影响内部状态
                return cls._instances.copy()
        except Exception as e:
            cls.__log_error(f"获取所有实例时发生错误: {str(e)}")
            return {}


def find_same_elements_groups(iterable, comparator=None):
    """
    找到迭代对象中所有相同的元素组。

    参数：
      iterable: 迭代对象，例如列表、元组或集合。
      comparator: 可选的比较器。
        1. 如果是函数，则将迭代对象的每个元素传入该函数，
           并使用函数的返回值进行分组。
        2. 如果未提供 (None)，则直接比较迭代对象的元素。

    返回值：
      一个列表的列表，其中每个内部列表包含一组相同的元素。
      如果不提供比较器, 元素本身作为key进行分组。
      如果提供比较器, 比较器的返回值作为key进行分组。

    注意事项：
      - 如果迭代对象为空，则返回空列表。
      - 如果比较器是函数，则确保该函数接受一个参数并返回一个可哈希的值。
    """

    if not iterable:
        return []

    groups = {}

    if callable(comparator):
        # 使用函数作为比较器
        for element in iterable:
            key = comparator(element)
            if key not in groups:
                groups[key] = []
            groups[key].append(element)
    else:
        # 直接比较元素
        for element in iterable:
            if element not in groups:
                groups[element] = []
            groups[element].append(element)

    return list(groups.values())


class OptimalSorter:
    """
    一个提供最佳排序算法的类。
    """

    @staticmethod
    def sort(iterable, key=None):
        """
        实现一个性能最佳的排序算法函数。

        参数：
            iterable: 待排序的迭代对象。
            key: 可选参数，用于指定排序的依据。
                 - 如果是函数，则将迭代对象的每个元素传入该函数，使用函数的返回值进行比较。
                 - 如果是值，则直接将迭代对象的元素与该值进行比较（注意：这里应该是用于过滤，而不是排序的依据）。
                 - 如果不提供，则使用迭代对象元素本身进行比较。

        返回值：
            排序后的列表。

        注意事项：
            - 此函数针对不同数据规模和类型采用不同的排序策略。
            - 对于小型列表（长度 < 64），使用插入排序。
            - 对于中等大小列表，使用 Python 内置的 Timsort (sorted 函数)。
            - 对于大型列表且元素为数值类型，考虑使用基数排序（如果适用）。
            - 如果提供了 key 函数，将使用 key 的返回值进行比较。
            - 如果 key 是一个值，则执行过滤操作，而不是排序。
        """
        if key is not None and not callable(key):
            # 如果 key 是一个值，则执行过滤操作
            return [item for item in iterable if item == key]

        list_to_sort = list(iterable)  # 转换为列表以支持索引
        length = len(list_to_sort)

        if length < 64:
            # 小型列表：插入排序
            return OptimalSorter._insertion_sort(list_to_sort, key)
        elif all(isinstance(item, (int, float)) for item in list_to_sort):
            # 大型列表且元素为数值类型：考虑基数排序（如果适用）
            # （此处省略基数排序实现，因为需要根据具体数值范围进行优化）
            # 在实际应用中，可以根据数据特征决定是否使用基数排序
            return sorted(list_to_sort, key=key)  # 默认使用 Timsort
        else:
            # 中等大小或非数值类型列表：使用 Timsort
            return sorted(list_to_sort, key=key)

    @staticmethod
    def _insertion_sort(lst, key=None):
        """
        插入排序实现（私有静态方法）。
        """
        for i in range(1, len(lst)):
            current_value = lst[i]
            position = i

            if key:
                while position > 0 and key(lst[position - 1]) > key(current_value):
                    lst[position] = lst[position - 1]
                    position -= 1
            else:
                while position > 0 and lst[position - 1] > current_value:
                    lst[position] = lst[position - 1]
                    position -= 1

            lst[position] = current_value
        return lst


def flatten(nested_list):
    """
    将嵌套列表扁平化为一个单一列表。

    参数：
        nested_list: 包含嵌套列表、元组、集合或其他可迭代对象的列表。

    返回值：
        一个包含所有元素的新列表，所有嵌套结构都被移除。

    注意事项：
        - 此函数递归地处理嵌套结构。
        - 对于非可迭代对象（除了字符串），将直接添加到结果列表中。
        - 字符串被视为单个元素，而不是字符序列。
        - 能够处理混合类型的嵌套结构（例如，列表、元组和集合的混合）。
        - 循环引用可能会导致 RecursionError。
    """
    result = []
    for item in nested_list:
        if isinstance(item, (list, tuple, set)):
            result.extend(flatten(item))  # 递归调用
        elif isinstance(item, str):
            result.append(item)  # 字符串直接添加
        elif hasattr(item, "__iter__"):
            result.extend(flatten(list(item)))  # 其他可迭代对象，转换为list
        else:
            result.append(item)
    return result


def flatten_preserve_types(nested_iterable):
    """
    将嵌套的可迭代对象扁平化为一个单一列表，同时保留原始元素的类型。

    参数：
        nested_iterable: 包含嵌套可迭代对象（如列表、元组、集合）的结构。

    返回值：
        一个包含所有元素的新列表，所有嵌套结构都被移除，但元素的原始类型被保留。

    注意事项：
        - 此函数递归地处理嵌套结构。
        - 对于非可迭代对象（除了字符串），将直接添加到结果列表中。
        - 字符串被视为单个元素，而不是字符序列。
        - 能够处理混合类型的嵌套结构。
        - 循环引用可能会导致 RecursionError。
    """
    result = []
    for item in nested_iterable:
        if isinstance(item, (list, tuple, set)):
            result.extend(flatten_preserve_types(item))  # 递归调用
        elif isinstance(item, str):
            result.append(item)
        elif hasattr(item, "__iter__"):
            result.extend(flatten_preserve_types(item))  # 不再转换为list
        else:
            result.append(item)
    return result


def flatten_preserve_types_no_dict(nested_iterable):
    """
    将嵌套的可迭代对象扁平化为一个单一列表，同时保留原始元素的类型，但不对字典进行扁平化处理。

    参数：
        nested_iterable: 包含嵌套可迭代对象（如列表、元组、集合）的结构，可能包含字典。

    返回值：
        一个包含所有元素的新列表，所有嵌套结构都被移除（字典除外），元素的原始类型被保留。

    注意事项：
        - 此函数递归地处理嵌套结构（字典除外）。
        - 对于非可迭代对象（除了字符串）和字典，将直接添加到结果列表中。
        - 字符串被视为单个元素，而不是字符序列。
        - 能够处理混合类型的嵌套结构。
        - 循环引用可能会导致 RecursionError。
    """
    result = []
    for item in nested_iterable:
        if isinstance(item, (list, tuple, set)):
            result.extend(flatten_preserve_types_no_dict(item))  # 递归调用
        elif isinstance(item, str):
            result.append(item)
        elif isinstance(item, dict):  # 新增：如果是字典，直接添加
            result.append(item)
        elif hasattr(item, "__iter__"):
            result.extend(flatten_preserve_types_no_dict(item))
        else:
            result.append(item)
    return result


def clean_directory(
    directory_path: str,
    ignore_patterns: List[str] = None,
    recursive: bool = True,
    ignore_errors: bool = False,
    logger: Optional[Logger] = None,
) -> tuple[int, int, List[str]]:
    """
    清理指定文件夹的内容。

    参数：
        directory_path (str):
            需要清理的文件夹路径。
            可以是相对路径或绝对路径。

        ignore_patterns (List[str], optional):
            要忽略的文件或文件夹模式列表。
            支持通配符，例如 ["*.log", "temp*"]。
            默认为 None，表示不忽略任何文件。

        recursive (bool, optional):
            是否递归删除子文件夹。
            如果为 True，将删除所有子文件夹及其内容。
            如果为 False，只删除当前文件夹中的文件。
            默认为 True。

        ignore_errors (bool, optional):
            是否忽略删除过程中的错误。
            如果为 True，遇到错误时将继续执行。
            如果为 False，遇到错误时将抛出异常。
            默认为 False。

        logger (Logger, optional):
            日志记录器实例。
            如果提供，将记录删除操作的详细信息。
            默认为 None。

    返回：
        Tuple[int, int, List[str]]:
            - 第一个值：成功删除的文件数量
            - 第二个值：成功删除的文件夹数量
            - 第三个值：删除失败的文件或文件夹列表

    异常：
        - FileNotFoundError: 指定的文件夹不存在
        - PermissionError: 没有足够的权限执行删除操作
        - OSError: 其他操作系统相关错误

    示例：
        >>> # 基本用法
        >>> clean_directory("temp_folder")

        >>> # 忽略特定文件
        >>> clean_directory("logs", ignore_patterns=["*.log", "temp*"])

        >>> # 不递归删除
        >>> clean_directory("cache", recursive=False)

        >>> # 使用日志记录
        >>> logger = Logger()
        >>> clean_directory("temp", logger=logger)

    注意事项：
        1. 此函数会直接删除文件，请谨慎使用
        2. 建议在删除重要文件夹之前进行备份
        3. 如果提供了logger，将记录所有操作
        4. 对于被系统锁定或正在使用的文件，可能无法删除
    """
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"目录不存在: {directory_path}")

    if not os.path.isdir(directory_path):
        raise NotADirectoryError(f"路径不是目录: {directory_path}")

    # 编译忽略模式
    ignore_patterns = ignore_patterns or []
    import fnmatch

    def should_ignore(name):
        return any(fnmatch.fnmatch(name, pattern) for pattern in ignore_patterns)

    # 统计信息
    files_deleted = 0
    dirs_deleted = 0
    failed_items = []

    def log_info(message):
        if logger:
            logger.info(message)

    def log_error(message):
        if logger:
            logger.error(message)
        if not ignore_errors:
            raise

    try:
        for root, dirs, files in os.walk(directory_path, topdown=False):
            # 删除文件
            for name in files:
                if should_ignore(name):
                    continue

                file_path = os.path.join(root, name)
                try:
                    os.remove(file_path)
                    files_deleted += 1
                    log_info(f"已删除文件: {file_path}")
                except Exception as e:
                    failed_items.append(file_path)
                    log_error(f"删除文件失败 {file_path}: {str(e)}")
                    if not ignore_errors:
                        raise

            # 删除空文件夹（如果启用递归）
            if recursive and root != directory_path:
                if should_ignore(os.path.basename(root)):
                    continue

                try:
                    os.rmdir(root)
                    dirs_deleted += 1
                    log_info(f"已删除文件夹: {root}")
                except Exception as e:
                    failed_items.append(root)
                    log_error(f"删除文件夹失败 {root}: {str(e)}")
                    if not ignore_errors:
                        raise

        # 如果不是递归模式，只删除当前目录中的文件
        if not recursive:
            for name in os.listdir(directory_path):
                if should_ignore(name):
                    continue

                path = os.path.join(directory_path, name)
                if os.path.isfile(path):
                    try:
                        os.remove(path)
                        files_deleted += 1
                        log_info(f"已删除文件: {path}")
                    except Exception as e:
                        failed_items.append(path)
                        log_error(f"删除文件失败 {path}: {str(e)}")
                        if not ignore_errors:
                            raise

    except Exception as e:
        if not ignore_errors:
            raise

    return files_deleted, dirs_deleted, failed_items


def delete_files(
    directory_path: Optional[str] = None,
    patterns: Union[str, List[str]] = None,
    recursive: bool = True,
    ignore_patterns: List[str] = None,
    ignore_errors: bool = False,
    dry_run: bool = False,
    file_list: List[str] = None,
    delete_same_basename: bool = False,
    logger: Optional[Logger] = None,
) -> tuple[List[str], List[str], List[str]]:
    """
    按照指定模式删除文件。

    参数：
        directory_path (Optional[str], optional):
            要处理的目录路径。
            可以是相对路径或绝对路径。
            如果提供了file_list且都是绝对路径，则此参数可以为None。
            默认为None。

        patterns (Union[str, List[str]], optional):
            要删除的文件模式。支持以下格式：
            - 单个字符串模式："*.txt"
            - 多个模式列表：["*.log", "temp*"]
            - None: 表示删除所有文件（除非被ignore_patterns排除）
            支持的通配符：
            - *: 匹配任意字符序列
            - ?: 匹配单个字符
            - [seq]: 匹配seq中的任意字符
            - [!seq]: 匹配不在seq中的任意字符
            注意：如果指定了file_list参数，patterns将被忽略。
            注意：如果未指定file_list，则directory_path不能为None。

        recursive (bool, optional):
            是否递归处理子目录。
            默认为True。

        ignore_patterns (List[str], optional):
            要忽略的文件模式列表。
            这些模式的优先级高于patterns。
            默认为None。
            注意：如果指定了file_list参数，ignore_patterns将被忽略。

        ignore_errors (bool, optional):
            是否忽略错误继续执行。
            如果为False，遇到错误时将抛出异常。
            默认为False。

        dry_run (bool, optional):
            是否模拟运行而不实际删除文件。
            用于预览将要删除的文件。
            默认为False。

        file_list (List[str], optional):
            要删除的具体文件列表。
            列表中的路径可以是相对于directory_path的路径，也可以是绝对路径。
            如果提供此参数，将忽略patterns和ignore_patterns参数。
            如果指定了此参数且不是dry_run模式，则必须确保列表中的所有文件都被成功删除，
            否则会抛出FileNotDeletedError异常。
            如果所有路径都是绝对路径，则directory_path可以为None。
            默认为None。

        delete_same_basename (bool, optional):
            是否删除所有具有相同文件名的文件。
            仅当file_list包含绝对路径时有效。
            如果为True，则对于file_list中的每个文件，
            将搜索并删除所有与之同名的文件。
            此选项可能会显著增加操作时间，请谨慎使用。
            如果需要使用此功能，directory_path不能为None。
            默认为False。

        logger (Logger, optional):
            日志记录器实例。
            如果提供，将记录所有操作细节。
            默认为None。

    返回：
        Tuple[List[str], List[str], List[str]]:
            - 第一个列表：成功删除的文件列表
            - 第二个列表：被忽略的文件列表
            - 第三个列表：删除失败的文件列表

    异常：
        - FileNotFoundError: 指定的目录不存在
        - NotADirectoryError: 指定的路径不是目录
        - PermissionError: 没有足够的权限
        - OSError: 其他操作系统相关错误
        - FileNotDeletedError: 当指定file_list时，如果有文件未能成功删除
        - ValueError: 当未指定file_list且directory_path为None时

    示例：
        >>> # 删除所有.txt文件
        >>> delete_files("docs", "*.txt")

        >>> # 删除多种类型的文件
        >>> delete_files("temp", ["*.tmp", "*.temp", "test*"])

        >>> # 删除所有文件，但保留特定文件
        >>> delete_files("logs", ignore_patterns=["important.log"])

        >>> # 预览要删除的文件（不实际删除）
        >>> delete_files("data", "*.bak", dry_run=True)

        >>> # 使用日志记录删除操作
        >>> logger = Logger()
        >>> delete_files("cache", "*.cache", logger=logger)

        >>> # 递归删除子目录中的特定文件
        >>> delete_files("project", "*.pyc", recursive=True)

        >>> # 删除指定的文件列表
        >>> delete_files("project", file_list=["file1.txt", "subdir/file2.txt"])

        >>> # 使用绝对路径指定要删除的文件，不需要directory_path
        >>> delete_files(file_list=["/tmp/file1.txt", "/var/log/file2.log"])

        >>> # 删除指定文件以及所有同名文件
        >>> delete_files("/", file_list=["/tmp/file1.txt"], delete_same_basename=True)

    注意事项：
        1. 此函数会直接删除匹配的文件，请谨慎使用
        2. 建议先使用dry_run=True预览要删除的文件
        3. 对于重要数据，建议先备份
        4. 被系统锁定的文件可能无法删除
        5. 如果没有指定patterns和file_list，且ignore_patterns为None，将删除所有文件
        6. 当使用file_list时，必须确保所有文件都能被成功删除，否则会抛出异常
        7. 使用delete_same_basename=True时务必谨慎，可能会删除系统中所有同名文件
        8. 建议在使用delete_same_basename=True时先使用dry_run=True预览要删除的文件
        9. 当指定file_list且均为绝对路径时，directory_path可以为None
        10. 当需要递归查找同名文件时，directory_path不能为None
    """
    # 参数验证
    if file_list is None and directory_path is None:
        raise ValueError("当未指定file_list时，directory_path不能为None")

    if directory_path is not None:
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        if not os.path.isdir(directory_path):
            raise NotADirectoryError(f"路径不是目录: {directory_path}")

    # 规范化patterns参数
    if patterns is None and file_list is None:
        patterns = ["*"]
    elif isinstance(patterns, str):
        patterns = [patterns]

    # 初始化ignore_patterns
    ignore_patterns = ignore_patterns or []

    # 编译模式匹配函数
    def should_delete(name):
        # 首先检查是否应该忽略
        if any(fnmatch.fnmatch(name, pattern) for pattern in ignore_patterns):
            return False
        # 然后检查是否匹配删除模式
        return any(fnmatch.fnmatch(name, pattern) for pattern in patterns)

    # 为日志函数创建占位符
    def log_info(message):
        if logger:
            logger.info(message)

    def log_error(message):
        if logger:
            logger.error(message)

    # 初始化结果列表
    deleted_files = []
    ignored_files = []
    failed_files = []

    # 如果提供了file_list，规范化路径
    normalized_file_list = []
    if file_list:
        # 检查是否所有路径都是绝对路径
        all_absolute = all(os.path.isabs(file_path) for file_path in file_list)

        # 如果有相对路径但directory_path为None，抛出错误
        if not all_absolute and directory_path is None:
            raise ValueError("当file_list包含相对路径时，directory_path不能为None")

        # 转换file_list中的相对路径为绝对路径
        for file_path in file_list:
            if os.path.isabs(file_path):
                normalized_file_list.append(file_path)
            elif directory_path is not None:
                normalized_file_list.append(os.path.join(directory_path, file_path))

        # 如果需要删除同名文件，但directory_path为None，抛出错误
        if delete_same_basename and directory_path is None:
            raise ValueError("当需要查找同名文件时，directory_path不能为None")

        # 如果需要删除同名文件，构建文件名字典
        if (
            delete_same_basename
            and any(os.path.isabs(file_path) for file_path in file_list)
            and directory_path is not None
        ):
            basenames_to_delete = {}
            for file_path in normalized_file_list:
                if os.path.isabs(file_path) and os.path.exists(file_path):
                    basename = os.path.basename(file_path)
                    if basename not in basenames_to_delete:
                        basenames_to_delete[basename] = []
                    basenames_to_delete[basename].append(file_path)

            # 如果启用了删除同名文件，扩展文件列表
            if basenames_to_delete and delete_same_basename:
                expanded_file_list = []
                for basename, original_files in basenames_to_delete.items():
                    # 记录已有的文件路径
                    existing_paths = set(original_files)

                    # 递归查找所有同名文件
                    if recursive:
                        log_info(f"正在查找所有名为 '{basename}' 的文件...")
                        for root, _, files in os.walk(directory_path):
                            for file_name in files:
                                if file_name == basename:
                                    file_path = os.path.join(root, file_name)
                                    if file_path not in existing_paths:
                                        expanded_file_list.append(file_path)
                                        existing_paths.add(file_path)
                    else:
                        # 非递归模式下只在当前目录查找
                        for item in os.listdir(directory_path):
                            if item == basename:
                                file_path = os.path.join(directory_path, item)
                                if (
                                    os.path.isfile(file_path)
                                    and file_path not in existing_paths
                                ):
                                    expanded_file_list.append(file_path)
                                    existing_paths.add(file_path)

                # 添加扩展的文件列表
                if expanded_file_list:
                    log_info(
                        f"找到 {len(expanded_file_list)} 个同名文件将被添加到删除列表"
                    )
                    normalized_file_list.extend(expanded_file_list)

    def process_file(file_path: str, is_from_list: bool = False):
        try:
            if not os.path.exists(file_path):
                if is_from_list:
                    error_msg = f"指定要删除的文件不存在: {file_path}"
                    log_error(error_msg)
                    failed_files.append(file_path)
                    return False
                return True

            file_name = os.path.basename(file_path)

            # 根据来源决定删除策略
            should_delete_file = is_from_list or should_delete(file_name)

            if should_delete_file:
                if dry_run:
                    log_info(f"将要删除文件: {file_path}")
                    deleted_files.append(file_path)
                    return True
                else:
                    try:
                        os.remove(file_path)
                        deleted_files.append(file_path)
                        log_info(f"已删除文件: {file_path}")
                        return True
                    except Exception as e:
                        failed_files.append(file_path)
                        error_msg = f"删除文件失败 {file_path}: {str(e)}"
                        log_error(error_msg)
                        if not ignore_errors:
                            if is_from_list:
                                # 记录下错误，但不立即抛出，后面统一检查
                                return False
                            else:
                                raise RuntimeError(error_msg) from e
                        return False
            else:
                ignored_files.append(file_path)
                log_info(f"已忽略文件: {file_path}")
                # 如果是来自file_list的文件被忽略，应当视为失败
                return not is_from_list

        except Exception as e:
            failed_files.append(file_path)
            error_msg = f"处理文件失败 {file_path}: {str(e)}"
            log_error(error_msg)
            if not ignore_errors:
                if is_from_list:
                    # 记录下错误，但不立即抛出，后面统一检查
                    return False
                else:
                    raise RuntimeError(error_msg) from e
            return False

    try:
        # 根据是否提供file_list，采取不同的处理策略
        if file_list:
            log_info(f"开始删除指定的 {len(normalized_file_list)} 个文件")

            # 跟踪每个文件的删除结果
            deletion_results = {}
            for file_path in normalized_file_list:
                result = process_file(file_path, is_from_list=True)
                deletion_results[file_path] = result

            # 如果不是dry_run模式，检查是否所有文件都删除成功
            if not dry_run and not all(deletion_results.values()) and not ignore_errors:
                failed_files_list = [
                    f for f, result in deletion_results.items() if not result
                ]
                failed_count = len(failed_files_list)
                error_msg = f"未能成功删除指定的所有文件。共 {failed_count} 个文件删除失败: {', '.join(failed_files_list[:5])}"
                if failed_count > 5:
                    error_msg += f" 等 {failed_count} 个文件"
                log_error(error_msg)
                raise RuntimeError(error_msg)
        else:
            # 使用模式匹配方式处理文件
            # 必须有directory_path才能继续
            if directory_path is None:
                raise ValueError("使用模式匹配方式时，directory_path不能为None")

            if recursive:
                for root, _, files in os.walk(directory_path):
                    for file_name in files:
                        file_path = os.path.join(root, file_name)
                        process_file(file_path)
            else:
                for item in os.listdir(directory_path):
                    file_path = os.path.join(directory_path, item)
                    if os.path.isfile(file_path):
                        process_file(file_path)

    except Exception as e:
        if not ignore_errors:
            raise
        log_error(f"遍历目录失败: {str(e)}\n{traceback.format_exc()}")

    return deleted_files, ignored_files, failed_files


def get_file_info(file_path: str, follow_symlinks: bool = True) -> Dict[str, Any]:
    """
    获取文件的详细信息。

    参数：
        file_path (str):
            文件路径。
            可以是相对路径或绝对路径。
            支持文件和目录。

        follow_symlinks (bool, optional):
            是否跟踪符号链接。
            如果为True，将返回链接指向的文件信息。
            如果为False，将返回链接本身的信息。
            默认为True。

    返回：
        Dict[str, any]: 包含以下键值对的字典：
            - exists (bool): 文件是否存在
            - type (str): 文件类型，可能的值：
                - 'file': 普通文件
                - 'directory': 目录
                - 'symlink': 符号链接
                - 'special': 特殊文件（设备文件、管道等）
                - 'unknown': 未知类型
            - name (str): 文件名（不含路径）
            - stem (str): 文件名主干（不含扩展名）
            - extension (str): 文件扩展名（含点号）
            - parent_dir (str): 父目录的完整路径
            - absolute_path (str): 文件的绝对路径
            - size (int): 文件大小（字节）
            - created_time (datetime): 创建时间
            - modified_time (datetime): 最后修改时间
            - accessed_time (datetime): 最后访问时间
            - owner (str): 文件所有者名称
            - group (str): 文件所属组名称
            - permissions (Dict): 权限信息，包含：
                - mode (int): 权限模式（八进制）
                - readable (bool): 是否可读
                - writable (bool): 是否可写
                - executable (bool): 是否可执行
            - is_hidden (bool): 是否是隐藏文件
            - symlink_target (str, optional): 如果是符号链接，指向的目标路径
            - mime_type (str, optional): MIME类型（如果可以确定）
            - encoding (str, optional): 文件编码（如果是文本文件且可以确定）
            - hash_md5 (str, optional): 文件的MD5哈希值（仅对普通文件）
            - error (str, optional): 如果获取某些信息时出错，这里会包含错误信息

    异常：
        此函数不抛出异常，所有错误都会在返回的字典中的'error'字段中说明。

    示例：
        >>> # 获取普通文件信息
        >>> info = get_file_info("document.txt")
        >>> print(f"文件大小: {info['size']} 字节")
        >>> print(f"修改时间: {info['modified_time']}")

        >>> # 获取目录信息
        >>> info = get_file_info("my_folder")
        >>> if info['type'] == 'directory':
        ...     print("这是一个目录")

        >>> # 检查文件权限
        >>> info = get_file_info("script.py")
        >>> if info['permissions']['executable']:
        ...     print("此文件可执行")

        >>> # 获取符号链接信息
        >>> info = get_file_info("link.txt", follow_symlinks=False)
        >>> if info['type'] == 'symlink':
        ...     print(f"链接指向: {info['symlink_target']}")

    注意事项：
        1. 某些信息可能因操作系统限制而无法获取
        2. 大文件的MD5计算可能会比较耗时
        3. 某些特殊文件可能无法获取全部信息
        4. 权限不足可能导致某些信息无法访问
        5. 所有者和组信息在Windows和Unix系统上的获取方式不同
    """
    # 根据系统类型导入相应模块
    is_windows = os.name == "nt"
    if not is_windows:
        import pwd
        import grp
    else:
        import win32security
        import win32api
        import win32con

    # 初始化结果字典
    result = {
        "exists": False,
        "type": "unknown",
        "name": "",
        "stem": "",
        "extension": "",
        "parent_dir": "",
        "absolute_path": "",
        "size": 0,
        "created_time": None,
        "modified_time": None,
        "accessed_time": None,
        "owner": "",
        "group": "",
        "permissions": {
            "mode": 0,
            "readable": False,
            "writable": False,
            "executable": False,
        },
        "is_hidden": False,
        "error": None,
    }

    try:
        # 使用pathlib处理路径
        path = Path(file_path)

        # 基本信息
        result["exists"] = path.exists()
        if not result["exists"]:
            result["error"] = "文件不存在"
            return result

        result["name"] = path.name
        result["stem"] = path.stem
        result["extension"] = path.suffix
        result["parent_dir"] = str(path.parent.absolute())
        result["absolute_path"] = str(path.absolute())

        # 根据系统类型判断隐藏属性
        if is_windows:
            try:
                attrs = win32api.GetFileAttributes(str(path))
                result["is_hidden"] = bool(attrs & win32con.FILE_ATTRIBUTE_HIDDEN)
            except BaseException:
                result["is_hidden"] = path.name.startswith(".")
        else:
            result["is_hidden"] = path.name.startswith(".")

        # 文件类型判断
        if path.is_symlink():
            result["type"] = "symlink"
            result["symlink_target"] = (
                str(path.resolve()) if follow_symlinks else str(os.readlink(path))
            )
            if not follow_symlinks:
                return result
        elif path.is_file():
            result["type"] = "file"
        elif path.is_dir():
            result["type"] = "directory"
        else:
            result["type"] = "special"

        # 获取文件状态信息
        stat_info = path.stat()

        # 时间信息
        result["created_time"] = datetime.fromtimestamp(stat_info.st_ctime)
        result["modified_time"] = datetime.fromtimestamp(stat_info.st_mtime)
        result["accessed_time"] = datetime.fromtimestamp(stat_info.st_atime)

        # 大小信息
        result["size"] = stat_info.st_size

        # 根据系统类型获取所有者和组信息
        if is_windows:
            try:
                # 获取Windows安全描述符
                sd = win32security.GetFileSecurity(
                    str(path),
                    win32security.OWNER_SECURITY_INFORMATION
                    | win32security.GROUP_SECURITY_INFORMATION,
                )

                # 获取所有者SID
                owner_sid = sd.GetSecurityDescriptorOwner()
                # 获取组SID
                group_sid = sd.GetSecurityDescriptorGroup()

                # 转换SID为名称
                owner_name, owner_domain, _ = win32security.LookupAccountSid(
                    None, owner_sid
                )
                group_name, group_domain, _ = win32security.LookupAccountSid(
                    None, group_sid
                )

                result["owner"] = f"{owner_domain}\\{owner_name}"
                result["group"] = f"{group_domain}\\{group_name}"
            except Exception as e:
                result["owner"] = "Unknown"
                result["group"] = "Unknown"
                result["error"] = f"获取Windows所有者信息失败: {str(e)}"
        else:
            try:
                result["owner"] = pwd.getpwuid(stat_info.st_uid).pw_name
            except BaseException:
                result["owner"] = str(stat_info.st_uid)

            try:
                result["group"] = grp.getgrgid(stat_info.st_gid).gr_name
            except BaseException:
                result["group"] = str(stat_info.st_gid)

        # 权限信息
        mode = stat_info.st_mode
        result["permissions"]["mode"] = oct(mode & 0o777)

        if is_windows:
            try:
                # 获取Windows文件权限
                readable = os.access(file_path, os.R_OK)
                writable = os.access(file_path, os.W_OK)
                executable = os.access(file_path, os.X_OK)

                result["permissions"]["readable"] = readable
                result["permissions"]["writable"] = writable
                result["permissions"]["executable"] = executable
            except BaseException:
                pass
        else:
            result["permissions"]["readable"] = bool(mode & stat.S_IRUSR)
            result["permissions"]["writable"] = bool(mode & stat.S_IWUSR)
            result["permissions"]["executable"] = bool(mode & stat.S_IXUSR)

        # 对普通文件的额外处理
        if result["type"] == "file":
            # MIME类型
            result["mime_type"] = mimetypes.guess_type(file_path)[0]

            # 尝试检测文件编码（仅对小于10MB的可能是文本的文件）
            if result["size"] < 10_000_000 and (
                result["mime_type"] is None or "text" in result["mime_type"]
            ):
                try:
                    with open(file_path, "rb") as f:
                        raw_data = f.read(4096)  # 只读取前4KB来检测编码
                        result["encoding"] = chardet.detect(raw_data)["encoding"]
                except BaseException:
                    pass

            # 计算MD5（仅对小于100MB的文件）
            if result["size"] < 100_000_000:
                try:
                    md5_hash = hashlib.md5()
                    with open(file_path, "rb") as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            md5_hash.update(chunk)
                    result["hash_md5"] = md5_hash.hexdigest()
                except BaseException:
                    pass

    except Exception as e:
        traceback.print_exc()
        result["error"] = f"获取文件信息时出错: {str(e)}"

    return result


def read_directory_files(
    directory_path: str,
    include_extensions: Union[str, List[str]] = None,
    exclude_extensions: Union[str, List[str]] = None,
    recursive: bool = True,
    max_file_size: int = 10_000_000,  # 默认限制10MB
    encoding: str = "utf-8",
    include_binary: bool = False,
    ignore_errors: bool = False,
    return_metadata: bool = True,
    content_preview_length: int = 1000,  # 二进制文件的预览长度
    logger: Optional[Logger] = None,
) -> List[Dict[str, Any]]:
    """
    读取指定目录中所有符合条件的文件内容和元数据信息

    本函数遍历指定目录，读取符合条件的文件内容及其元数据信息。可以通过扩展名过滤文件，
    支持递归遍历子目录，并提供详细的文件信息。对于文本文件，会读取其全部内容；对于二进制
    文件，可以选择是否包含，并提供部分内容预览。

    Args:
        directory_path (str): 要读取的目录路径
        include_extensions (Union[str, List[str]], optional): 要包含的文件扩展名，例如 ".txt" 或 [".txt", ".py"]
        exclude_extensions (Union[str, List[str]], optional): 要排除的文件扩展名，例如 ".exe" 或 [".exe", ".dll"]
        recursive (bool, optional): 是否递归读取子目录中的文件，默认为True
        max_file_size (int, optional): 读取的最大文件大小（字节），默认为10MB
        encoding (str, optional): 读取文本文件的编码，默认为utf-8
        include_binary (bool, optional): 是否包含二进制文件，默认为False
        ignore_errors (bool, optional): 是否忽略读取过程中的错误，默认为False
        return_metadata (bool, optional): 是否返回文件的元数据信息，默认为True
        content_preview_length (int, optional): 二进制文件的内容预览长度，默认为1000字节
        logger (Logger, optional): 用于记录操作的日志器实例

    Returns:
        List[Dict[str, Any]]: 包含文件信息的字典列表，每个字典包含以下字段：
            - path: 文件的完整路径
            - name: 文件名
            - extension: 文件扩展名
            - size: 文件大小（字节）
            - content: 文件内容（文本文件为字符串，二进制文件为字节序列或预览）
            - is_binary: 是否为二进制文件
            - content_type: 内容类型（'text', 'binary', 'binary_preview', 'error'）
            - error: 读取文件时发生的错误（如果有）

            如果return_metadata=True，还会包含以下字段：
            - created_time: 文件创建时间
            - modified_time: 文件最后修改时间
            - accessed_time: 文件最后访问时间
            - owner: 文件所有者（如果能获取）
            - permissions: 文件权限（如果能获取）
            - hash_md5: 文件MD5哈希值（仅对小于10MB的文件）

    示例:
        # 读取所有Python文件
        python_files = read_directory_files("/path/to/project", include_extensions=".py")

        # 读取除了图片和视频以外的所有文件
        files = read_directory_files(
            "/path/to/data",
            exclude_extensions=[".jpg", ".png", ".gif", ".mp4", ".avi"]
        )

        # 仅读取当前目录中的文本文件
        text_files = read_directory_files("/path/to/docs", recursive=False)

        # 读取包括二进制文件在内的所有文件
        all_files = read_directory_files("/path/to/folder", include_binary=True)

    注意:
        - 对于超过max_file_size的文件，将不会读取内容，但会包含其元数据
        - 二进制文件默认不包含在结果中，除非指定include_binary=True
        - 如果文件读取出错，且ignore_errors=True，将记录错误信息但不会抛出异常
    """
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"目录不存在: {directory_path}")

    if not os.path.isdir(directory_path):
        raise NotADirectoryError(f"指定路径不是目录: {directory_path}")

    # 处理日志
    def log_info(message):
        if logger:
            logger.info(message)

    def log_error(message):
        if logger:
            logger.error(message)
        if not ignore_errors:
            print(f"错误: {message}")

    # 处理扩展名列表
    def normalize_extensions(extensions):
        if extensions is None:
            return None
        if isinstance(extensions, str):
            extensions = [extensions]
        # 确保所有扩展名都以.开头
        return [ext if ext.startswith(".") else f".{ext}" for ext in extensions]

    include_extensions = normalize_extensions(include_extensions)
    exclude_extensions = normalize_extensions(exclude_extensions)

    # 判断文件是否符合扩展名条件
    def is_file_included(filename):
        # 获取扩展名（小写）
        _, ext = os.path.splitext(filename.lower())

        # 如果指定了包含扩展名，且文件扩展名不在列表中，则排除
        if include_extensions and ext not in include_extensions:
            return False

        # 如果指定了排除扩展名，且文件扩展名在列表中，则排除
        if exclude_extensions and ext in exclude_extensions:
            return False

        return True

    # 判断文件是否可能是二进制文件
    def is_likely_binary(filename):
        # 常见的二进制文件扩展名
        binary_extensions = [
            ".exe",
            ".dll",
            ".so",
            ".dylib",
            ".bin",
            ".dat",
            ".img",
            ".iso",
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".tiff",
            ".webp",
            ".mp3",
            ".wav",
            ".ogg",
            ".flac",
            ".mp4",
            ".avi",
            ".mkv",
            ".mov",
            ".zip",
            ".rar",
            ".7z",
            ".tar",
            ".gz",
            ".bz2",
            ".xz",
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".db",
            ".sqlite",
            ".mdb",
            ".accdb",
        ]

        _, ext = os.path.splitext(filename.lower())
        return ext in binary_extensions

    # 检查文件内容的前几个字节是否包含空字节（二进制特征）
    def has_binary_content(filepath, check_size=1024):
        try:
            with open(filepath, "rb") as f:
                content = f.read(check_size)
                return b"\0" in content
        except Exception:
            return False

    # 获取文件元数据
    def get_file_metadata(filepath):
        if not return_metadata:
            return {}

        try:
            file_stat = os.stat(filepath)
            metadata = {
                "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "accessed_time": datetime.fromtimestamp(file_stat.st_atime).isoformat(),
                "permissions": oct(file_stat.st_mode)[-3:],  # 文件权限（八进制形式）
            }

            # 尝试获取文件所有者（仅在Unix系统上可用）
            try:
                import pwd

                metadata["owner"] = pwd.getpwuid(file_stat.st_uid).pw_name
            except (ImportError, KeyError, AttributeError):
                try:
                    # Windows尝试使用wmi获取
                    import win32security

                    sid = win32security.GetFileSecurity(
                        filepath, win32security.OWNER_SECURITY_INFORMATION
                    ).GetSecurityDescriptorOwner()
                    name, domain, type = win32security.LookupAccountSid(None, sid)
                    metadata["owner"] = f"{domain}\\{name}"
                except Exception:
                    metadata["owner"] = str(file_stat.st_uid)

            # 计算MD5哈希（仅对小文件）
            if file_stat.st_size < 10_000_000:  # 小于10MB的文件
                try:
                    md5_hash = hashlib.md5()
                    with open(filepath, "rb") as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            md5_hash.update(chunk)
                    metadata["hash_md5"] = md5_hash.hexdigest()
                except Exception as e:
                    log_error(f"计算哈希值时出错 {filepath}: {str(e)}")

            return metadata
        except Exception as e:
            log_error(f"获取文件元数据时出错 {filepath}: {str(e)}")
            return {}

    results = []

    # 遍历目录
    for root, dirs, files in os.walk(directory_path):
        # 如果不递归且不是顶级目录，则跳过
        if not recursive and root != directory_path:
            continue

        for filename in files:
            if not is_file_included(filename):
                continue

            filepath = os.path.join(root, filename)
            rel_path = os.path.relpath(filepath, directory_path)

            try:
                file_size = os.path.getsize(filepath)

                # 创建基本的文件信息字典
                file_info = {
                    "path": filepath,
                    "relative_path": rel_path,
                    "name": filename,
                    "extension": os.path.splitext(filename)[1].lower(),
                    "size": file_size,
                    "dir": root,
                }

                # 检查是否是二进制文件
                is_binary = is_likely_binary(filename) or has_binary_content(filepath)
                file_info["is_binary"] = is_binary

                # 如果是二进制文件且不包含二进制文件，则跳过读取内容
                if is_binary and not include_binary:
                    file_info["content"] = None
                    file_info["content_type"] = "binary_excluded"
                    file_info.update(get_file_metadata(filepath))
                    results.append(file_info)
                    continue

                # 如果文件太大，也跳过读取内容
                if file_size > max_file_size:
                    file_info["content"] = None
                    file_info["content_type"] = "too_large"
                    file_info["error"] = f"文件超过大小限制 ({max_file_size} 字节)"
                    file_info.update(get_file_metadata(filepath))
                    results.append(file_info)
                    continue

                # 读取文件内容
                if is_binary:
                    # 对于二进制文件，只读取预览
                    with open(filepath, "rb") as f:
                        content = f.read(content_preview_length)
                    file_info["content"] = content
                    file_info["content_type"] = "binary_preview"
                    file_info["content_preview_length"] = len(content)
                    file_info["content_hex"] = content.hex()[:200]  # 提供16进制预览
                else:
                    # 对于文本文件，尝试不同的编码
                    encodings_to_try = [encoding, "utf-8", "latin1", "cp1252", "gbk"]
                    content = None
                    used_encoding = None

                    for enc in encodings_to_try:
                        try:
                            with open(filepath, "r", encoding=enc) as f:
                                content = f.read()
                            used_encoding = enc
                            break
                        except UnicodeDecodeError:
                            continue

                    if content is not None:
                        file_info["content"] = content
                        file_info["content_type"] = "text"
                        file_info["encoding"] = used_encoding
                    else:
                        # 如果所有编码都失败，则作为二进制读取
                        with open(filepath, "rb") as f:
                            content = f.read(content_preview_length)
                        file_info["content"] = content
                        file_info["content_type"] = "binary_fallback"
                        file_info["error"] = f"无法以文本方式读取文件，以二进制模式读取"
                        file_info["content_preview_length"] = len(content)

                # 获取文件元数据
                file_info.update(get_file_metadata(filepath))

                # 添加到结果列表
                results.append(file_info)

            except Exception as e:
                error_message = f"读取文件时出错 {filepath}: {str(e)}"
                log_error(error_message)

                if ignore_errors:
                    # 仍然添加基本信息和错误信息
                    results.append(
                        {
                            "path": filepath,
                            "relative_path": rel_path,
                            "name": filename,
                            "extension": os.path.splitext(filename)[1].lower(),
                            "content": None,
                            "content_type": "error",
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    )
                else:
                    raise RuntimeError(error_message) from e

    log_info(f"读取目录完成: {directory_path}，共读取 {len(results)} 个文件")
    return results


def bytes_to_mb(size_in_bytes: int, decimal_places: int = 2) -> float:
    """
    将字节大小转换为兆字节表示

    Args:
        size_in_bytes (int): 字节大小
        decimal_places (int): 保留小数位数，默认为2

    Returns:
        float: 兆字节大小
    """
    try:
        if size_in_bytes == 0:
            return 0.0
        mb_size = size_in_bytes / (1024 * 1024)
        return round(mb_size, decimal_places)
    except Exception:
        return 0.0


def list_directory_files(
    directory_path: str,
    include_extensions: Union[str, List[str]] = None,
    exclude_extensions: Union[str, List[str]] = None,
    recursive: bool = True,
    include_hidden: bool = False,
    return_metadata: bool = True,
    follow_symlinks: bool = True,
    relative_paths: bool = True,
    sort_by: str = None,  # 可选值: 'name', 'size', 'created', 'modified'
    reverse: bool = False,
    ignore_errors: bool = False,
    group_by_basename: bool = False,  # 是否按文件基本名称分组
    group_sort_by: str = None,  # 组内排序依据，可选值与sort_by相同
    group_reverse: bool = False,  # 组内排序方向
    min_group_size: int = 1,  # 最小分组大小，小于此大小的分组会被过滤掉
    size_decimal_places: int = 2,  # 文件大小转换为MB时的小数位数
    logger: Optional[Logger] = None,
) -> Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
    """
    获取指定目录中所有符合条件的文件信息，不读取文件内容

    本函数遍历指定目录，收集符合条件的文件信息和元数据。可以通过扩展名过滤文件，
    支持递归遍历子目录，并提供详细的文件元数据。此函数专注于高效率地获取文件信息，
    而不读取文件内容。还可以根据文件基本名称分组，将同名不同后缀的文件归为一组。

    Args:
        directory_path (str): 要读取的目录路径
        include_extensions (Union[str, List[str]], optional): 要包含的文件扩展名，例如 ".txt" 或 [".txt", ".py"]
        exclude_extensions (Union[str, List[str]], optional): 要排除的文件扩展名，例如 ".exe" 或 [".exe", ".dll"]
        recursive (bool, optional): 是否递归读取子目录中的文件，默认为True
        include_hidden (bool, optional): 是否包含隐藏文件，默认为False
        return_metadata (bool, optional): 是否返回文件的元数据信息，默认为True
        follow_symlinks (bool, optional): 是否跟踪符号链接，默认为True
        relative_paths (bool, optional): 是否返回相对路径，默认为True
        sort_by (str, optional): 结果排序依据，可选值: 'name', 'size', 'created', 'modified'，默认为None(不排序)
        reverse (bool, optional): 是否反向排序，默认为False
        ignore_errors (bool, optional): 是否忽略处理过程中的错误，默认为False
        group_by_basename (bool, optional): 是否按文件基本名称分组，默认为False
        group_sort_by (str, optional): 组内排序依据，与sort_by参数相同，默认为None
        group_reverse (bool, optional): 组内排序方向，默认为False
        min_group_size (int, optional): 当按基本名分组时，只返回至少包含此数量文件的组，默认为1(不过滤)
        size_decimal_places (int, optional): 文件大小转换为MB时的小数位数，默认为2
        logger (Logger, optional): 用于记录操作的日志器实例

    Returns:
        Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
            当group_by_basename=False时，返回包含文件信息的字典列表；
            当group_by_basename=True时，返回一个字典，键为文件基本名，值为具有相同基本名的文件信息字典列表。

            每个文件信息字典包含以下字段：
            - path: 文件的完整路径
            - relative_path: 相对于目录的路径（如果relative_paths=True）
            - name: 文件名
            - basename: 不含扩展名的文件基本名
            - extension: 文件扩展名
            - size: 文件大小（字节）
            - size_mb: 文件大小（兆字节）
            - is_hidden: 是否为隐藏文件

            如果return_metadata=True，还会包含以下字段：
            - created_time: 文件创建时间
            - modified_time: 文件最后修改时间
            - accessed_time: 文件最后访问时间
            - owner: 文件所有者（如果能获取）
            - permissions: 文件权限（如果能获取）
            - hash_md5: 文件MD5哈希值（仅对小于10MB的文件计算）
            - mime_type: 文件MIME类型（基于扩展名猜测）
            - is_symlink: 是否为符号链接
            - symlink_target: 如果是符号链接，指向的目标路径

    示例:
        # 获取所有Python文件
        python_files = list_directory_files("/path/to/project", include_extensions=".py")

        # 获取除了图片和视频以外的所有文件
        files = list_directory_files(
            "/path/to/data",
            exclude_extensions=[".jpg", ".png", ".gif", ".mp4", ".avi"]
        )

        # 仅获取当前目录中的文件（不递归）
        current_files = list_directory_files("/path/to/docs", recursive=False)

        # 按文件大小排序获取所有文件
        files_by_size = list_directory_files("/path/to/folder", sort_by="size", reverse=True)

        # 将同名不同后缀的文件分组（例如：查找所有图片的不同格式版本）
        grouped_files = list_directory_files(
            "/path/to/images",
            include_extensions=[".jpg", ".png", ".webp", ".gif"],
            group_by_basename=True,
            group_sort_by="size"
        )
        # 结果格式: {"image1": [{"name": "image1.jpg", "size_mb": 1.25, ...}, {"name": "image1.png", ...}], ...}

        # 查找至少有2个不同格式的文件组
        multi_format_files = list_directory_files(
            "/path/to/data",
            group_by_basename=True,
            min_group_size=2
        )

        # 按文件大小（MB）格式化数值
        large_files = list_directory_files(
            "/path/to/data",
            size_decimal_places=3  # 保留3位小数
        )
        print(f"文件大小: {large_files[0]['size_mb']} MB")

    注意:
        - 此函数不读取文件内容，专注于高效地获取文件信息
        - 如果同时指定include_extensions和exclude_extensions，将先应用include然后再排除
        - 计算MD5哈希值可能会影响性能，特别是对于大量文件的目录
        - 当使用group_by_basename时，返回值结构会从列表变为字典
        - size字段保存字节单位的文件大小，size_mb字段保存兆字节单位的文件大小
    """
    if not os.path.exists(directory_path):
        raise FileNotFoundError(f"目录不存在: {directory_path}")

    if not os.path.isdir(directory_path):
        raise NotADirectoryError(f"指定路径不是目录: {directory_path}")

    # 初始化MIME类型
    mimetypes.init()

    # 处理日志
    def log_info(message):
        if logger:
            logger.info(message)

    def log_error(message):
        if logger:
            logger.error(message)
        if not ignore_errors:
            print(f"错误: {message}")

    # 处理扩展名列表
    def normalize_extensions(extensions):
        if extensions is None:
            return None
        if isinstance(extensions, str):
            extensions = [extensions]
        # 确保所有扩展名都以.开头
        return [
            ext.lower() if ext.startswith(".") else f".{ext.lower()}"
            for ext in extensions
        ]

    include_extensions = normalize_extensions(include_extensions)
    exclude_extensions = normalize_extensions(exclude_extensions)

    # 判断文件是否符合扩展名条件
    def is_file_included(filename):
        # 检查是否是隐藏文件
        if not include_hidden and (
            filename.startswith(".")
            or (
                os.name == "nt"
                and bool(
                    os.stat(os.path.join(directory_path, filename)).st_file_attributes
                    & 0x2
                )
            )
        ):
            return False

        # 获取扩展名（小写）
        _, ext = os.path.splitext(filename.lower())

        # 如果指定了包含扩展名，且文件扩展名不在列表中，则排除
        if include_extensions and ext not in include_extensions:
            return False

        # 如果指定了排除扩展名，且文件扩展名在列表中，则排除
        if exclude_extensions and ext in exclude_extensions:
            return False

        return True

    # 获取文件元数据
    def get_file_metadata(filepath):
        if not return_metadata:
            return {}

        try:
            file_stat = os.stat(filepath, follow_symlinks=follow_symlinks)
            metadata = {
                "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "accessed_time": datetime.fromtimestamp(file_stat.st_atime).isoformat(),
                "permissions": oct(file_stat.st_mode)[-3:],  # 文件权限（八进制形式）
                "size": file_stat.st_size,
                "mime_type": mimetypes.guess_type(filepath)[0],
                "is_symlink": os.path.islink(filepath),
            }

            # 如果是符号链接，获取目标
            if metadata["is_symlink"]:
                metadata["symlink_target"] = os.readlink(filepath)

            # 尝试获取文件所有者（仅在Unix系统上可用）
            try:
                import pwd

                metadata["owner"] = pwd.getpwuid(file_stat.st_uid).pw_name
            except (ImportError, KeyError, AttributeError):
                try:
                    # Windows尝试使用win32security获取
                    import win32security

                    sid = win32security.GetFileSecurity(
                        filepath, win32security.OWNER_SECURITY_INFORMATION
                    ).GetSecurityDescriptorOwner()
                    name, domain, _ = win32security.LookupAccountSid(None, sid)

                    metadata["owner"] = f"{domain}\\{name}"
                except Exception:
                    metadata["owner"] = str(file_stat.st_uid)

            # 计算MD5哈希（仅对小文件）
            if file_stat.st_size < 10_000_000:  # 小于10MB的文件
                try:
                    md5_hash = hashlib.md5()
                    with open(filepath, "rb") as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            md5_hash.update(chunk)
                    metadata["hash_md5"] = md5_hash.hexdigest()
                except Exception as e:
                    log_error(f"计算哈希值时出错 {filepath}: {str(e)}")

            return metadata
        except Exception as e:
            log_error(f"获取文件元数据时出错 {filepath}: {str(e)}")
            return {"error": str(e)}

    results = []

    # 是否是Windows系统
    is_windows = os.name == "nt"

    # 遍历目录
    for root, dirs, files in os.walk(
        directory_path, topdown=True, followlinks=follow_symlinks
    ):
        # 如果不递归且不是顶级目录，则跳过
        if not recursive and root != directory_path:
            dirs[:] = []  # 清空dirs以防止继续递归
            continue

        # 处理隐藏目录
        if not include_hidden:
            # 在Windows上检查目录属性
            if is_windows:
                dirs[:] = [
                    d
                    for d in dirs
                    if not (
                        d.startswith(".")
                        or bool(os.stat(os.path.join(root, d)).st_file_attributes & 0x2)
                    )
                ]
            else:
                # 在类Unix系统上只检查名称
                dirs[:] = [d for d in dirs if not d.startswith(".")]

        for filename in files:
            filepath = os.path.join(root, filename)

            # 检查文件是否是隐藏文件
            is_hidden = filename.startswith(".")
            if is_windows:
                try:
                    file_attrs = os.stat(filepath).st_file_attributes
                    is_hidden = bool(file_attrs & 0x2)  # 隐藏属性位
                except BaseException:
                    pass

            # 跳过隐藏文件如果不包括它们
            if is_hidden and not include_hidden:
                continue

            # 检查扩展名
            if not is_file_included(filename):
                continue

            rel_path = (
                os.path.relpath(filepath, directory_path)
                if relative_paths
                else filepath
            )
            basename, extension = os.path.splitext(filename)

            try:
                # 创建基本的文件信息字典
                file_info = {
                    "path": filepath,
                    "relative_path": rel_path,
                    "name": filename,
                    "basename": basename,  # 添加基本名称
                    "extension": extension.lower(),
                    "is_hidden": is_hidden,
                }

                # 获取文件元数据并更新文件信息
                if return_metadata:
                    metadata = get_file_metadata(filepath)
                    file_info.update(metadata)
                else:
                    # 即使不返回完整元数据，也至少获取文件大小
                    file_info["size"] = os.path.getsize(filepath)

                # 添加兆字节单位的文件大小
                file_info["size_mb"] = bytes_to_mb(
                    file_info["size"], size_decimal_places
                )

                # 添加到结果列表
                results.append(file_info)

            except Exception as e:
                error_message = f"处理文件时出错 {filepath}: {str(e)}"
                log_error(error_message)

                if ignore_errors:
                    # 仍然添加基本信息和错误信息
                    file_size = 0
                    try:
                        file_size = os.path.getsize(filepath)
                    except BaseException:
                        pass

                    results.append(
                        {
                            "path": filepath,
                            "relative_path": rel_path,
                            "name": filename,
                            "basename": basename,
                            "extension": os.path.splitext(filename)[1].lower(),
                            "size": file_size,
                            "size_mb": bytes_to_mb(file_size, size_decimal_places),
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    )
                else:
                    raise RuntimeError(error_message) from e

    # 排序结果
    if sort_by and not group_by_basename:
        try:
            if sort_by == "name":
                results.sort(key=lambda x: x["name"].lower(), reverse=reverse)
            elif sort_by == "size" and all("size" in item for item in results):
                results.sort(key=lambda x: x.get("size", 0), reverse=reverse)
            elif sort_by == "size_mb" and all("size_mb" in item for item in results):
                results.sort(key=lambda x: x.get("size_mb", 0), reverse=reverse)
            elif sort_by == "created" and all(
                "created_time" in item for item in results
            ):
                results.sort(key=lambda x: x.get("created_time", ""), reverse=reverse)
            elif sort_by == "modified" and all(
                "modified_time" in item for item in results
            ):
                results.sort(key=lambda x: x.get("modified_time", ""), reverse=reverse)
            elif sort_by == "extension":
                results.sort(
                    key=lambda x: x.get("extension", "").lower(), reverse=reverse
                )
            else:
                log_error(f"无法按 '{sort_by}' 排序，因为一些文件缺少此属性")
        except Exception as e:
            log_error(f"排序时出错: {str(e)}")

    # 按基本名分组
    if group_by_basename:
        grouped_results = {}
        for file_info in results:
            basename = file_info["basename"]
            if basename not in grouped_results:
                grouped_results[basename] = []
            grouped_results[basename].append(file_info)

        # 组内排序
        if group_sort_by:
            for basename, file_group in grouped_results.items():
                try:
                    if group_sort_by == "name":
                        file_group.sort(
                            key=lambda x: x["name"].lower(), reverse=group_reverse
                        )
                    elif group_sort_by == "size" and all(
                        "size" in item for item in file_group
                    ):
                        file_group.sort(
                            key=lambda x: x.get("size", 0), reverse=group_reverse
                        )
                    elif group_sort_by == "size_mb" and all(
                        "size_mb" in item for item in file_group
                    ):
                        file_group.sort(
                            key=lambda x: x.get("size_mb", 0), reverse=group_reverse
                        )
                    elif group_sort_by == "created" and all(
                        "created_time" in item for item in file_group
                    ):
                        file_group.sort(
                            key=lambda x: x.get("created_time", ""),
                            reverse=group_reverse,
                        )
                    elif group_sort_by == "modified" and all(
                        "modified_time" in item for item in file_group
                    ):
                        file_group.sort(
                            key=lambda x: x.get("modified_time", ""),
                            reverse=group_reverse,
                        )
                    elif group_sort_by == "extension":
                        file_group.sort(
                            key=lambda x: x.get("extension", "").lower(),
                            reverse=group_reverse,
                        )
                except Exception as e:
                    log_error(f"组内排序时出错 ({basename}): {str(e)}")

        # 根据最小分组大小过滤
        if min_group_size > 1:
            grouped_results = {
                k: v for k, v in grouped_results.items() if len(v) >= min_group_size
            }

        # 全局排序（按组名）
        if sort_by == "name":
            # 转换为有序字典，按键排序
            import collections

            sorted_items = sorted(
                grouped_results.items(), key=lambda x: x[0].lower(), reverse=reverse
            )
            grouped_results = collections.OrderedDict(sorted_items)

        log_info(
            f"获取目录文件完成: {directory_path}，共获取 {len(results)} 个文件，分组为 {len(grouped_results)} 个组"
        )
        return grouped_results

    log_info(f"获取目录文件完成: {directory_path}，共获取 {len(results)} 个文件")
    return results


def split_list_evenly(data_list: List[Any], num_groups: int) -> List[List[Any]]:
    """
    将列表按照每组固定元素个数进行划分。

    本函数接收一个列表和一个整数，将列表划分为多个分组，每个分组包含指定数量的元素。
    如果最后一个分组元素不足指定数量，则保持不足状态。

    Args:
        data_list (List[Any]):
            要划分的列表数据。
            可以是任何元素类型的列表。
            如果列表为空，将返回空列表。

        num_groups (int):
            每个分组中的元素个数。
            必须是正整数，否则会抛出ValueError异常。
            此参数指定每个分组应包含多少个元素，而不是要划分成多少个分组。

    Returns:
        List[List[Any]]:
            包含划分后子列表的列表。
            返回的列表长度取决于原始列表长度和指定的每组元素个数。
            如果输入列表为空，则返回空列表。

    Raises:
        TypeError: 如果data_list不是列表或可迭代对象，或num_groups不是整数类型
        ValueError: 如果num_groups小于或等于0

    Examples:
        >>> # 基本用法：每组3个元素
        >>> split_list_evenly([1, 2, 3, 4, 5, 6, 7, 8], 3)
        [[1, 2, 3], [4, 5, 6], [7, 8]]

        >>> # 当最后一组元素不足时
        >>> split_list_evenly([1, 2, 3, 4, 5], 2)
        [[1, 2], [3, 4], [5]]

        >>> # 当每组元素个数大于列表长度时
        >>> split_list_evenly([1, 2, 3], 5)
        [[1, 2, 3]]

        >>> # 当列表为空时
        >>> split_list_evenly([], 3)
        []

    注意事项:
        1. 本函数不修改原始列表
        2. 除了最后一组外，每组元素个数都是固定的
        3. 最后一组元素可能少于指定数量
        4. 对于非常大的列表，函数处理效率较高，内存占用较小
        5. 返回的是新列表，不与原列表共享内存
        6. 如果输入的num_groups不合法（如负数或0），将抛出ValueError异常
        7. 性能已优化，适用于各种规模的数据集
    """
    # 重命名参数以更符合实际语义
    group_size = num_groups

    # 快速参数验证（直接使用位运算和比较，避免函数调用开销）
    if group_size <= 0:
        raise ValueError("group_size参数必须是正整数")

    # 性能优化：尝试快速检查列表是否为空
    try:
        # 尝试获取第一个元素，如果没有则列表为空
        first = next(iter(data_list), None)
        if first is None:
            return []  # 空列表快速返回
    except TypeError:
        raise TypeError("data_list参数必须是列表或其他可迭代对象")

    # 优化：对列表类型直接处理，避免isinstance检查
    if hasattr(data_list, "__getitem__") and hasattr(data_list, "__len__"):
        # 快速路径：支持随机访问和长度查询的对象（如列表、元组）
        list_length = len(data_list)

        # 如果每组元素个数大于或等于列表长度，则整个列表作为一个分组
        if list_length <= group_size:
            # 直接返回数据的浅拷贝以避免修改原始数据
            return [data_list[:]]

        # 计算需要的分组数量（使用整数除法和加法代替math.ceil，性能更好）
        num_of_groups = (list_length + group_size - 1) // group_size

        # 使用切片高效创建分组
        # 注意：这比列表推导式更快，因为避免了range对象的创建和迭代
        result = []
        for i in range(num_of_groups):
            start = i * group_size
            end = min(start + group_size, list_length)  # 避免超出边界
            result.append(data_list[start:end])
        return result

    # 非直接支持随机访问的对象（如迭代器）
    # 使用islice高效地切片迭代器，避免完全转换为列表
    data_list = iter(data_list)  # 确保是迭代器

    # 对迭代器使用chunk模式
    def chunk_iterator(it, size):
        while True:
            chunk = list(islice(it, size))
            if not chunk:
                break
            yield chunk

    # 转换生成器为列表并返回
    return list(chunk_iterator(data_list, group_size))


# 辅助函数，确保logger在未提供时不会出错
def _log_error(logger, message):
    if logger:
        logger.error(message)


# 辅助函数，确保logger在未提供时不会出错
def _log_info(logger, message):
    if logger:
        logger.info(message)


def find_latest(
    directory_path: str,
    file_extensions: Union[str, List[str], None] = None,
    recursive: bool = True,
    find_latest_folder_first: bool = False,
    include_folders: bool = False,
    max_depth: int = None,
    follow_symlinks: bool = False,
    include_hidden: bool = False,
    sorting_key: str = "modified",  # 'modified', 'created', 'accessed'
    metadata: bool = False,
    relative_paths: bool = False,
    ignore_errors: bool = True,
    logger: Optional[Logger] = None,
) -> Union[str, Dict[str, Any], None]:
    """
    查找指定目录中的最新文件或文件夹。

    本函数用于查找指定目录中的最新文件，可以按照修改时间、创建时间或访问时间排序。
    可以选择递归搜索子目录、按文件后缀名筛选，以及多种高级选项。

    Args:
        directory_path (str):
            要搜索的目录路径。
            如果路径不存在或不是目录，将抛出相应异常（除非ignore_errors=True）。

        file_extensions (Union[str, List[str], None]):
            要查找的文件后缀名。
            可以是单个字符串（如".txt"）或字符串列表（如[".jpg", ".png"]）。
            后缀名不区分大小写。
            None表示不筛选后缀名，返回任何类型的文件。
            默认值: None

        recursive (bool):
            是否递归搜索子目录。
            如果为True，将搜索指定目录及其所有子目录。
            如果为False，只搜索指定目录。
            默认值: False

        find_latest_folder_first (bool):
            是否先找最新的文件夹，然后在该文件夹中找最新的文件。
            如果为True，先找到最新的子文件夹，然后仅在该文件夹中查找最新文件。
            如果为False，直接在整个搜索范围内查找最新文件。
            默认值: False

        include_folders (bool):
            是否将文件夹也视为"文件"进行排序和返回。
            如果为True，返回的结果可能是文件夹。
            如果为False，只返回文件。
            注意：当find_latest_folder_first=True时，此参数影响第一步查找中对文件夹的考虑。
            默认值: False

        max_depth (int, optional):
            递归搜索的最大深度。
            None表示不限制深度。
            0表示只搜索当前目录（与recursive=False相同）。
            1表示搜索当前目录及其直接子目录，依此类推。
            默认值: None

        follow_symlinks (bool):
            是否跟踪符号链接。
            如果为True，将跟踪符号链接指向的目录和文件。
            如果为False，将忽略符号链接。
            小心：如果存在循环链接，跟踪符号链接可能导致无限循环。
            默认值: False

        include_hidden (bool):
            是否包含隐藏文件和文件夹。
            在Windows上，隐藏文件是具有隐藏属性的文件。
            在Unix/Linux上，隐藏文件是名称以点（.）开头的文件。
            默认值: False

        sorting_key (str):
            用于确定"最新"的时间属性。
            'modified': 最后修改时间 (默认)
            'created': 创建时间
            'accessed': 最后访问时间
            默认值: "modified"

        metadata (bool):
            是否返回文件的元数据。
            如果为True，返回包含文件路径和元数据的字典。
            如果为False，只返回文件路径字符串。
            默认值: True

        relative_paths (bool):
            返回的路径是否为相对路径。
            如果为True，返回相对于directory_path的路径。
            如果为False，返回绝对路径。
            默认值: False

        ignore_errors (bool):
            是否忽略错误。
            如果为True，遇到错误时将记录日志并继续执行。
            如果为False，遇到错误时将抛出异常。
            默认值: True

        logger (Optional[Logger]):
            日志记录器实例。
            如果提供，将使用它记录信息和错误。
            如果为None，不记录日志。
            默认值: None

    Returns:
        Union[str, Dict[str, Any], None]:
            如果找到最新的文件，返回以下之一：
            - 当metadata=True时：返回包含文件路径和元数据的字典
            - 当metadata=False时：返回文件路径字符串
            如果没有找到符合条件的文件，返回None。

    Raises:
        ValueError: 当提供的参数无效时
        FileNotFoundError: 当指定的目录不存在且ignore_errors=False时
        PermissionError: 当没有访问权限且ignore_errors=False时
        OSError: 当发生其他操作系统错误且ignore_errors=False时

    Examples:
        >>> # 基本用法：查找目录中的最新文件
        >>> find_latest("/path/to/directory")
        {'path': '/path/to/directory/latest_file.txt', 'size': 1024, 'modified': datetime(...), ...}

        >>> # 仅返回文件路径，不返回元数据
        >>> find_latest("/path/to/directory", metadata=False)
        '/path/to/directory/latest_file.txt'

        >>> # 按文件后缀名筛选
        >>> find_latest("/path/to/directory", file_extensions=[".jpg", ".png"])
        {'path': '/path/to/directory/latest_image.jpg', ...}

        >>> # 先找最新的文件夹，然后在该文件夹中找最新的文件
        >>> find_latest("/path/to/directory", find_latest_folder_first=True)
        {'path': '/path/to/directory/latest_folder/latest_file.txt', ...}

        >>> # 包含文件夹作为可能的结果
        >>> find_latest("/path/to/directory", include_folders=True)
        {'path': '/path/to/directory/latest_folder', 'is_dir': True, ...}

        >>> # 递归搜索，但限制深度为2
        >>> find_latest("/path/to/directory", recursive=True, max_depth=2)
        {'path': '/path/to/directory/subfolder/latest_file.txt', ...}

    注意事项:
        1. 时间比较使用的是文件系统提供的时间戳，不同操作系统可能有不同的精度和行为
        2. 在Windows上，创建时间总是可用的；在某些Unix系统上，创建时间可能不可用或不准确
        3. 当目录中有大量文件时，此函数可能需要较长时间执行
        4. 文件访问操作可能因权限问题失败，尤其是在递归遍历时
        5. 如果找到的最新文件是一个损坏的符号链接，且follow_symlinks=True，可能会抛出错误
    """

    # 创建日志函数
    def _log_info(message):
        if logger:
            logger.info(message)

    def _log_error(message):
        if logger:
            logger.error(message)

    # 参数验证和预处理
    if not directory_path:
        raise ValueError("directory_path 不能为空")

    if not os.path.exists(directory_path):
        error_msg = f"目录不存在: {directory_path}"
        if ignore_errors:
            _log_error(logger, error_msg)
            return None
        raise FileNotFoundError(error_msg)

    if not os.path.isdir(directory_path):
        error_msg = f"指定的路径不是目录: {directory_path}"
        if ignore_errors:
            _log_error(logger, error_msg)
            return None
        raise NotADirectoryError(error_msg)

    # 确保directory_path是绝对路径，便于后续处理
    directory_path = os.path.abspath(directory_path)

    # 标准化文件扩展名
    extensions = None
    if file_extensions is not None:
        extensions = []
        if isinstance(file_extensions, str):
            extensions = [file_extensions.lower()]
        else:
            extensions = [ext.lower() for ext in file_extensions]
        # 确保所有扩展名都以点开头
        extensions = [(ext if ext.startswith(".") else f".{ext}") for ext in extensions]

    # 验证sorting_key参数
    valid_sorting_keys = {"modified", "created", "accessed"}
    if sorting_key not in valid_sorting_keys:
        raise ValueError(
            f"无效的sorting_key: {sorting_key}，有效值为: {', '.join(valid_sorting_keys)}"
        )

    # 设置找到最新文件夹后的搜索深度
    latest_folder_search_depth = (
        0 if not recursive else (max_depth if max_depth is not None else float("inf"))
    )

    # 判断文件是否应该被包含在搜索结果中
    def is_included(path, is_dir=False):
        # 如果是目录，根据include_folders决定是否包含
        if is_dir:
            return include_folders

        # 检查是否是隐藏文件
        filename = os.path.basename(path)
        is_hidden = False

        # Windows上检查文件属性
        if os.name == "nt":
            try:
                attrs = ctypes.windll.kernel32.GetFileAttributesW(str(path))
                is_hidden = bool(attrs & 2)  # 2是FILE_ATTRIBUTE_HIDDEN
            except (AttributeError, OSError):
                # 如果无法获取属性，假设不是隐藏文件
                pass
        # Unix/Linux上检查文件名是否以.开头
        else:
            is_hidden = filename.startswith(".")

        if not include_hidden and is_hidden:
            return False

        # 检查文件扩展名
        if extensions is not None:
            _, ext = os.path.splitext(filename)
            return ext.lower() in extensions

        return True

    # 获取文件或文件夹的时间属性
    def get_time_attr(path):
        try:
            stat_result = os.stat(path, follow_symlinks=follow_symlinks)
            if sorting_key == "modified":
                return stat_result.st_mtime
            elif sorting_key == "created":
                # st_birthtime在某些系统上不可用，使用st_ctime作为替代
                return getattr(stat_result, "st_birthtime", stat_result.st_ctime)
            elif sorting_key == "accessed":
                return stat_result.st_atime
        except (FileNotFoundError, PermissionError, OSError) as e:
            if not ignore_errors:
                raise
            _log_error(f"获取文件时间属性时出错: {path}, 错误: {str(e)}")
            return 0  # 返回最小时间，确保错误文件不会成为"最新"
        return 0

    # 获取文件或文件夹的元数据
    def get_metadata(path, is_dir=False):
        if not metadata:
            return path if not relative_paths else os.path.relpath(path, directory_path)

        try:
            stat_result = os.stat(path, follow_symlinks=follow_symlinks)
            result = {
                "path": (
                    path
                    if not relative_paths
                    else os.path.relpath(path, directory_path)
                ),
                "name": os.path.basename(path),
                "is_dir": is_dir,
                "size": stat_result.st_size,
                "modified": datetime.fromtimestamp(stat_result.st_mtime),
                "accessed": datetime.fromtimestamp(stat_result.st_atime),
                "created": datetime.fromtimestamp(
                    getattr(stat_result, "st_birthtime", stat_result.st_ctime)
                ),
                "is_symlink": os.path.islink(path),
            }

            if is_dir:
                try:
                    # 获取目录中的文件和子目录数量
                    items = os.listdir(path)
                    result["item_count"] = len(items)
                    result["file_count"] = sum(
                        1 for item in items if os.path.isfile(os.path.join(path, item))
                    )
                    result["dir_count"] = sum(
                        1 for item in items if os.path.isdir(os.path.join(path, item))
                    )
                except (PermissionError, OSError) as e:
                    if not ignore_errors:
                        raise
                    _log_error(f"获取目录内容时出错: {path}, 错误: {str(e)}")
                    result["item_count"] = 0
                    result["file_count"] = 0
                    result["dir_count"] = 0

            return result
        except (FileNotFoundError, PermissionError, OSError) as e:
            if not ignore_errors:
                raise
            _log_error(f"获取文件元数据时出错: {path}, 错误: {str(e)}")
            return None

    # 查找最新的文件或文件夹
    def find_latest_item(path, max_depth_left=None, only_files=True):
        if max_depth_left is not None and max_depth_left < 0:
            return None

        latest_item = None
        latest_time = 0

        try:
            items = os.listdir(path)
        except (PermissionError, OSError) as e:
            if not ignore_errors:
                raise
            _log_error(f"访问目录时出错: {path}, 错误: {str(e)}")
            return None

        # 遍历当前目录中的所有项目
        for item_name in items:
            item_path = os.path.join(path, item_name)
            is_dir = False

            try:
                is_dir = os.path.isdir(item_path)

                # 如果是符号链接且不跟踪符号链接，跳过
                if os.path.islink(item_path) and not follow_symlinks:
                    continue

                # 检查是否应该包含此项目
                if only_files and is_dir:
                    # 当只查找文件时，仍需递归搜索目录
                    if recursive and (max_depth_left is None or max_depth_left > 0):
                        next_depth = (
                            None if max_depth_left is None else max_depth_left - 1
                        )
                        dir_latest = find_latest_item(item_path, next_depth, only_files)
                        if dir_latest is not None:
                            item_time = get_time_attr(
                                dir_latest["path"]
                                if isinstance(dir_latest, dict)
                                else dir_latest
                            )
                            if item_time > latest_time:
                                latest_time = item_time
                                latest_item = dir_latest
                    continue

                if not is_included(item_path, is_dir):
                    continue

                # 获取时间属性并比较
                item_time = get_time_attr(item_path)
                if item_time > latest_time:
                    latest_time = item_time
                    latest_item = get_metadata(item_path, is_dir)

            except (FileNotFoundError, PermissionError, OSError) as e:
                if not ignore_errors:
                    raise
                _log_error(f"处理文件/目录时出错: {item_path}, 错误: {str(e)}")
                continue

        return latest_item

    # 主执行逻辑
    try:
        if find_latest_folder_first:
            # 先找最新的文件夹
            _log_info(f"在 {directory_path} 中查找最新的文件夹...")
            latest_folder = find_latest_item(
                directory_path, latest_folder_search_depth, only_files=False
            )

            if latest_folder is None:
                _log_info("未找到任何文件夹")
                return None

            folder_path = (
                latest_folder["path"]
                if isinstance(latest_folder, dict)
                else latest_folder
            )
            if not os.path.isdir(folder_path):
                folder_path = os.path.dirname(folder_path)

            _log_info(f"找到最新的文件夹: {folder_path}，现在在其中查找最新的文件...")

            # 在最新文件夹中查找最新文件
            return find_latest_item(folder_path, None, not include_folders)
        else:
            # 直接在整个搜索范围内查找最新文件
            _log_info(
                f"在 {directory_path} 中查找最新的{'文件' if not include_folders else '文件或文件夹'}..."
            )
            return find_latest_item(directory_path, max_depth, not include_folders)

    except Exception as e:
        if not ignore_errors:
            raise
        _log_error(f"查找最新文件时发生未预期的错误: {str(e)}")
        return None


def find_latest_created_folder(
    directory_path: str, logger: Optional[Logger] = None
) -> Optional[str]:
    """
    在指定目录下查找最新创建的文件夹。

    本函数遍历指定目录下的所有条目，找出创建时间最新的文件夹，并返回其绝对路径。
    它会优先使用 'st_birthtime' (在支持的系统上，如 macOS、FreeBSD)，
    如果不可用，则回退到使用 'st_ctime' (在 Linux 上通常表示元数据更改时间，
    在 Windows 上表示创建时间)。

    Args:
        directory_path (str):
            要搜索的目录路径。
            必须提供一个有效的、存在的目录路径。

        logger (Optional[Logger]):
            可选的日志记录器实例。
            如果提供，将使用它记录信息和错误。

    Returns:
        Optional[str]:
            如果找到，返回最新创建文件夹的绝对路径字符串。
            如果没有找到任何文件夹，或者在过程中发生不可恢复的错误，则返回 None。

    Raises:
        无: 本函数设计为不直接抛出异常，而是通过日志记录错误并返回 None 来处理问题，
            以增强健壮性和稳定性。调用者应检查返回值是否为 None。

    Examples:
        >>> # 查找 /tmp 目录下最新创建的文件夹
        >>> latest_folder = find_latest_created_folder("/tmp")
        >>> if latest_folder:
        ...     print(f"最新创建的文件夹是: {latest_folder}")
        ... else:
        ...     print("未能找到任何文件夹或发生错误。")

        >>> # 使用日志记录器
        >>> my_logger = Logger()
        >>> latest_folder = find_latest_created_folder("/var/log", logger=my_logger)

    注意事项:
        1. 创建时间的准确性依赖于文件系统和操作系统。'st_ctime' 在不同系统上的含义可能不同。
        2. 函数仅检查指定目录 (`directory_path`) 下的一级条目，不进行递归搜索。
        3. 如果目录包含大量条目，函数执行可能需要一些时间。
        4. 文件系统权限可能会阻止访问某些目录或获取其状态信息。函数会尝试处理这些错误。
    """

    # 创建日志辅助函数，避免重复检查 logger 是否存在
    def _log_info(msg):
        if logger:
            logger.info(msg)

    def _log_error(msg):
        if logger:
            # 使用 traceback 打印详细错误堆栈
            logger.error(f"{msg}\n{traceback.format_exc()}")
        else:
            # 如果没有 logger，仍然打印错误和 traceback 到 stderr
            print(f"错误: {msg}", file=sys.stderr)
            traceback.print_exc()

    # 1. 参数验证
    if not directory_path:
        _log_error(
            "函数 'find_latest_created_folder' 的 'directory_path' 参数不能为空。"
        )
        return None

    if not os.path.exists(directory_path):
        _log_error(f"指定的目录不存在: '{directory_path}'")
        return None

    if not os.path.isdir(directory_path):
        _log_error(f"指定的路径不是一个有效的目录: '{directory_path}'")
        return None

    # 确保是绝对路径，以便后续处理和返回
    try:
        absolute_directory_path = os.path.abspath(directory_path)
    except Exception as e:
        _log_error(f"无法获取目录的绝对路径 '{directory_path}': {e}")
        return None

    _log_info(f"开始在目录 '{absolute_directory_path}' 中查找最新创建的文件夹...")

    # 2. 初始化变量
    latest_folder_path: Optional[str] = None
    # 使用 0.0 作为初始时间戳，任何有效时间戳都会大于它
    latest_creation_time: float = 0.0

    # 3. 遍历目录条目
    try:
        # 使用 os.scandir() 以获得更好的性能和直接访问 stat 结果
        with os.scandir(absolute_directory_path) as entries:
            for entry in entries:
                try:
                    # 检查是否为目录，并且不是符号链接
                    # entry.is_dir(follow_symlinks=False) 避免跟踪符号链接
                    if entry.is_dir(follow_symlinks=False):
                        # 获取文件状态信息，entry.stat() 比 os.stat(entry.path) 更高效
                        stat_result = entry.stat(follow_symlinks=False)

                        # 获取创建时间: 优先 st_birthtime, 其次 st_ctime
                        creation_time = getattr(
                            stat_result, "st_birthtime", stat_result.st_ctime
                        )

                        # 比较并更新最新文件夹信息
                        if creation_time > latest_creation_time:
                            latest_creation_time = creation_time
                            # entry.path 直接是绝对路径
                            latest_folder_path = entry.path
                            # 记录更详细的调试信息，有助于追踪比较过程
                            # _log_info(f"找到潜在最新文件夹: '{entry.name}' (创建时间戳: {creation_time})")

                except OSError as e:
                    # 捕获访问单个条目时可能发生的权限错误等
                    _log_error(f"处理目录条目 '{entry.name}' 时出错: {e}")
                    # 继续处理下一个条目，增强健壮性
                    continue
                except Exception as e:
                    # 捕获其他意外错误
                    _log_error(f"处理目录条目 '{entry.name}' 时发生意外错误: {e}")
                    continue  # 继续处理

    except PermissionError as e:
        _log_error(f"访问目录 '{absolute_directory_path}' 时权限不足: {e}")
        return None
    except FileNotFoundError:  # 虽然前面检查过，但在遍历期间目录可能被删除
        _log_error(f"在遍历过程中目录 '{absolute_directory_path}' 已不存在。")
        return None
    except OSError as e:
        _log_error(
            f"访问或读取目录 '{absolute_directory_path}' 时发生操作系统错误: {e}"
        )
        return None
    except Exception as e:
        # 捕获所有其他可能的意外异常
        _log_error(f"在查找最新创建文件夹的过程中发生未预期的错误: {e}")
        return None

    # 4. 返回结果
    if latest_folder_path:
        # 确保返回的是绝对路径 (os.scandir 应该已经提供了绝对路径)
        final_path = os.path.abspath(latest_folder_path)
        _log_info(
            f"查找完成。最新创建的文件夹是: '{final_path}' (创建于: {datetime.fromtimestamp(latest_creation_time)})"
        )
        return final_path
    else:
        _log_info(f"在目录 '{absolute_directory_path}' 中未能找到任何文件夹。")
        return None

