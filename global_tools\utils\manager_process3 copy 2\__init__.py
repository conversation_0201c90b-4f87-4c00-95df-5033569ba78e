from global_tools.utils import Class<PERSON>nstanceMana<PERSON>,<PERSON><PERSON>, LogLevel

ClassInstanceManager.create_instance(Logger, key="Logger")

logger: Logger = ClassInstanceManager.get_instance(key="Logger")
logger.set_instance_level(LogLevel.OFF)


from .shared_data_manager import SharedDataManager
from .process_event_manager import ProcessEventManager
from .managed_multi_process import ManagedMultiProcess




__all__ = [
    'ManagedMultiProcess',
    'ProcessEventManager',
    'SharedDataManager',
]
