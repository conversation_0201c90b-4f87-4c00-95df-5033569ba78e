import numpy as np
import cv2
import dxcam
import time
from typing import Optional, Tuple, List, Union
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DXCamCore")

class DXCamCore:
    """
    使用DXcam库对指定显示器进行图像捕获并转换为OpenCV格式的工具类
    """
    
    def __init__(self, 
                 monitor_index: int = 0, 
                 output_color: str = "BGR",
                 region: Optional[Tuple[int, int, int, int]] = None):
        """
        初始化DXCamCore类
        
        Args:
            monitor_index (int): 显示器索引，0表示主显示器
            output_color (str): 输出图像的颜色格式，可选值为"BGR"(OpenCV默认)或"RGB"
            region (tuple, optional): 捕获区域 (left, top, right, bottom)，None表示整个显示器
            
        Raises:
            ValueError: 当显示器索引无效或无法获取显示器信息时
            RuntimeError: 当DXcam创建失败时
            
        示例:
            # 创建一个捕获主显示器的DXCamCore实例
            capture = DXCamCore(monitor_index=0)
            
            # 创建一个捕获第二个显示器特定区域的DXCamCore实例
            capture = DXCamCore(monitor_index=1, region=(100, 100, 500, 500))
        """
        self.__monitor_index = monitor_index
        self.__output_color = output_color
        self.__region = region
        self.__camera = None
        self.__monitors_info = []
        
        # 初始化DXcam
        try:
            # 获取设备和输出信息
            device_info_str = dxcam.device_info()
            output_info_str = dxcam.output_info()
            
            if not device_info_str:
                error_msg = "无法获取设备信息"
                logger.error(error_msg)
                raise ValueError(error_msg)
                
            if not output_info_str:
                error_msg = "无法获取输出信息"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 记录原始信息供调试
            logger.info(f"设备信息: {device_info_str}")
            logger.info(f"输出信息: {output_info_str}")
            
            # 解析输出信息以获取显示器列表
            self.__parse_output_info(output_info_str)
            
            if not self.__monitors_info:
                error_msg = "未检测到可用显示器"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            logger.info(f"检测到 {len(self.__monitors_info)} 个显示器")
            
            # 验证显示器索引是否有效
            if monitor_index >= len(self.__monitors_info):
                error_msg = f"无效的显示器索引: {monitor_index}，可用范围为 0-{len(self.__monitors_info)-1}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            elif monitor_index < 0:
                error_msg = f"显示器索引不能为负数: {monitor_index}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 获取当前显示器信息
            target_monitor = self.__monitors_info[monitor_index]
            logger.info(f"使用显示器 {monitor_index}: {target_monitor}")
            
            # 验证必要的显示器参数是否存在
            required_keys = ["device_idx", "output_idx", "width", "height", "left", "top"]
            missing_keys = [key for key in required_keys if key not in target_monitor]
            if missing_keys:
                error_msg = f"显示器信息缺少必要参数: {missing_keys}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 如果指定了区域，则使用指定区域，否则使用整个显示器
            region_to_use = self.__region
            if region_to_use is None:
                # 使用整个显示器
                width = target_monitor["width"]
                height = target_monitor["height"]
                left = target_monitor["left"]
                top = target_monitor["top"]
                region_to_use = (left, top, left + width, top + height)
            else:
                # 验证区域参数
                if len(region_to_use) != 4:
                    error_msg = f"无效的区域参数: {region_to_use}，应为 (left, top, right, bottom)"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 确保区域是整数坐标
                try:
                    region_to_use = tuple(int(x) for x in region_to_use)
                except (ValueError, TypeError) as e:
                    error_msg = f"区域坐标必须是整数: {region_to_use}, 错误: {str(e)}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 确保区域有效
                if region_to_use[0] >= region_to_use[2] or region_to_use[1] >= region_to_use[3]:
                    error_msg = f"无效的区域: {region_to_use}，右下角必须大于左上角"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            
            # 创建相机实例
            try:
                self.__camera = dxcam.create(
                    device_idx=target_monitor["device_idx"], 
                    output_idx=target_monitor["output_idx"],
                    output_color=output_color,
                    region=region_to_use
                )
            except Exception as cam_e:
                error_msg = f"相机创建失败: {str(cam_e)}"
                logger.error(error_msg)
                raise RuntimeError(error_msg) from cam_e
            
            if self.__camera is None:
                error_msg = "DXcam create返回了None"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
            
            logger.info(f"DXcam 初始化成功，区域: {region_to_use}")
            
        except Exception as e:
            logger.error(f"DXcam 初始化失败: {str(e)}")
            # 记录详细的异常信息
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise
    
    def __parse_output_info(self, output_info_str: str) -> None:
        """
        解析dxcam.output_info()返回的字符串，提取显示器信息
        
        Args:
            output_info_str (str): dxcam.output_info()返回的字符串
            
        格式示例:
        'Device[0] Output[0]: Res:(1920, 1080) Rot:0 Primary:True\nDevice[0] Output[1]: Res:(1920, 1080) Rot:0 Primary:False\n'
        """
        self.__monitors_info = []
        
        # 正则表达式匹配输出信息
        pattern = r'Device\[(\d+)\] Output\[(\d+)\]: Res:\((\d+), (\d+)\) Rot:(\d+) Primary:(True|False)'
        matches = re.finditer(pattern, output_info_str)
        
        for match in matches:
            device_idx = int(match.group(1))
            output_idx = int(match.group(2))
            width = int(match.group(3))
            height = int(match.group(4))
            rotation = int(match.group(5))
            is_primary = match.group(6) == "True"
            
            # 计算left和top（这可能需要根据实际情况调整）
            # 简单假设主显示器在(0,0)，非主显示器在右侧
            left = 0
            top = 0
            
            # 将解析结果添加到显示器列表
            monitor_info = {
                "device_idx": device_idx,
                "output_idx": output_idx,
                "width": width,
                "height": height,
                "rotation": rotation, 
                "is_primary": is_primary,
                "left": left,
                "top": top
            }
            
            self.__monitors_info.append(monitor_info)
    
    def get_monitors_info(self) -> List[dict]:
        """
        获取所有可用显示器的信息
        
        Returns:
            List[dict]: 显示器信息列表，每个字典包含显示器的详细信息
            
        示例:
            # 获取所有显示器信息
            monitors = capture.get_monitors_info()
            for i, monitor in enumerate(monitors):
                print(f"显示器 {i}: {monitor}")
        """
        if not self.__monitors_info:
            return []
        return self.__monitors_info
    
    def get_current_monitor_info(self) -> dict:
        """
        获取当前使用的显示器信息
        
        Returns:
            dict: 当前显示器的详细信息
            
        示例:
            # 获取当前使用的显示器信息
            monitor_info = capture.get_current_monitor_info()
            print(f"当前使用的显示器: {monitor_info}")
        """
        if not self.__monitors_info or self.__monitor_index >= len(self.__monitors_info):
            logger.error("无法获取当前显示器信息：无效的显示器索引")
            return {}
        return self.__monitors_info[self.__monitor_index]
    
    def capture(self) -> np.ndarray:
        """
        捕获当前显示器的图像并返回OpenCV格式的图像数据
        
        Returns:
            np.ndarray: OpenCV格式的图像数据
            
        Raises:
            RuntimeError: 当相机无法初始化或捕获时
            
        示例:
            # 捕获一帧图像
            frame = capture.capture()
            # 使用OpenCV处理图像
            cv2.imshow("Captured Frame", frame)
            cv2.waitKey(0)
        """
        # 初始化相机如果尚未初始化
        if self.__camera is None:
            logger.error("相机未初始化，尝试初始化")
            try:
                # 重新获取显示器信息
                device_info_str = dxcam.device_info()
                output_info_str = dxcam.output_info()
                
                if not device_info_str or not output_info_str:
                    error_msg = "无法获取显示器信息"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 解析输出信息以获取显示器列表
                self.__parse_output_info(output_info_str)
                
                if not self.__monitors_info:
                    error_msg = "未检测到可用显示器"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 使用有效的显示器索引
                valid_index = min(self.__monitor_index, len(self.__monitors_info) - 1)
                if valid_index != self.__monitor_index:
                    logger.warning(f"显示器索引 {self.__monitor_index} 无效，使用索引 {valid_index}")
                    self.__monitor_index = valid_index
                
                # 获取当前显示器信息
                target_monitor = self.__monitors_info[self.__monitor_index]
                
                # 确定捕获区域
                region_to_use = self.__region
                if region_to_use is None:
                    # 使用整个显示器
                    width = target_monitor["width"]
                    height = target_monitor["height"]
                    left = target_monitor["left"]
                    top = target_monitor["top"]
                    region_to_use = (left, top, left + width, top + height)
                
                # 创建相机实例
                self.__camera = dxcam.create(
                    device_idx=target_monitor["device_idx"],
                    output_idx=target_monitor["output_idx"],
                    output_color=self.__output_color,
                    region=region_to_use
                )
                
                if self.__camera is None:
                    error_msg = "DXcam 相机创建失败"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)
                    
                logger.info(f"DXcam 初始化成功，区域: {region_to_use}")
            except Exception as e:
                error_msg = f"DXcam 初始化失败: {str(e)}"
                logger.error(error_msg)
                # 初始化失败时返回黑色图像
                return self.__create_empty_image()
        
        # 进行屏幕捕获
        try:
            # 直接使用DXcam进行截图
            img = self.__camera.grab()
            
            # 如果截图成功，直接返回
            if img is not None and img.size > 0:
                return img
            
            # 如果截图失败，尝试使用不同方式捕获（DXcam可能有全局实例缓存问题）
            logger.warning("屏幕捕获返回空图像，尝试使用备用捕获方式")
            
            # 确保我们有有效的显示器信息
            if not self.__monitors_info or self.__monitor_index >= len(self.__monitors_info):
                logger.error("无效的显示器信息")
                return self.__create_empty_image()
                
            target_monitor = self.__monitors_info[self.__monitor_index]
            
            # 尝试使用相机对象的属性获取动态捕获一帧
            try:
                # 访问可能存在的内部方法
                if hasattr(self.__camera, '_grab_cv2'):
                    logger.info("尝试使用_grab_cv2方法捕获")
                    img = self.__camera._grab_cv2()
                    if img is not None and img.size > 0:
                        return img
            except Exception as e:
                logger.warning(f"使用_grab_cv2方法捕获失败: {str(e)}")
            
            # 尝试使用录制模式捕获
            # 这是DXcam的另一种捕获机制，在grab失败时可能仍然有效
            try:
                logger.info("尝试使用录制模式捕获")
                if hasattr(self.__camera, 'is_capturing') and self.__camera.is_capturing:
                    # 如果已经在录制，直接获取帧
                    img = self.__camera.get_latest_frame()
                else:
                    # 开始录制
                    self.__camera.start(target_fps=60, video_mode=True)
                    # 等待短暂时间以确保录制开始
                    time.sleep(0.02)
                    # 获取帧
                    img = self.__camera.get_latest_frame()
                    # 停止录制
                    self.__camera.stop()
                
                # 检查获取的图像
                if img is not None and img.size > 0:
                    logger.info("录制模式捕获成功")
                    return img
            except Exception as e:
                logger.warning(f"使用录制模式捕获失败: {str(e)}")
            
            # 尝试使用refresh方法刷新全局实例
            try:
                # 检查是否有refresh方法
                if hasattr(self.__camera, 'refresh'):
                    logger.info("尝试刷新相机状态")
                    self.__camera.refresh()
                    # 重新尝试捕获
                    img = self.__camera.grab()
                    if img is not None and img.size > 0:
                        logger.info("刷新后捕获成功")
                        return img
            except Exception as e:
                logger.warning(f"刷新相机失败: {str(e)}")
                
            # 如果所有方法都失败，返回黑色图像
            logger.error("所有捕获方法都失败，返回黑色图像")
            return self.__create_empty_image()
            
        except Exception as e:
            error_msg = f"屏幕捕获出错: {str(e)}"
            logger.error(error_msg)
            
            # 检查并记录相机状态
            if self.__camera:
                try:
                    camera_status = "已初始化"
                    if hasattr(self.__camera, 'is_capturing'):
                        camera_status += f", 录制状态: {self.__camera.is_capturing}"
                    logger.error(f"相机状态: {camera_status}")
                except:
                    logger.error("无法读取相机状态")
            else:
                logger.error("相机对象为空")
                
            return self.__create_empty_image()
    
    def capture_with_timeout(self, timeout_ms: int = 1000) -> np.ndarray:
        """
        在指定的超时时间内捕获图像
        
        Args:
            timeout_ms (int): 超时时间，单位为毫秒
            
        Returns:
            np.ndarray: OpenCV格式的图像数据
            
        Raises:
            RuntimeError: 当相机无法初始化或捕获出错时
            
        示例:
            # 尝试在500毫秒内捕获图像
            frame = capture.capture_with_timeout(timeout_ms=500)
            cv2.imshow("Captured Frame", frame)
            cv2.waitKey(0)
        """
        # 确保相机已初始化
        if self.__camera is None:
            logger.error("相机未初始化，调用capture方法尝试初始化")
            # 调用capture方法会自动尝试初始化相机
            return self.capture()
        
        # 尝试在超时时间内捕获一次
        try:
            img = self.__camera.grab()
            if img is not None and img.size > 0:
                return img
            
            # 截图失败，记录详细日志
            error_msg = f"超时捕获返回空图像 (timeout_ms: {timeout_ms})"
            logger.error(error_msg)
            return self.__create_empty_image()
            
        except Exception as e:
            error_msg = f"带超时捕获图像时出错: {str(e)}, timeout_ms: {timeout_ms}"
            logger.error(error_msg)
            
            # 检查并记录相机状态
            if self.__camera:
                try:
                    camera_status = "已初始化"
                    if hasattr(self.__camera, 'is_capturing'):
                        camera_status += f", 录制状态: {self.__camera.is_capturing}"
                    logger.error(f"相机状态: {camera_status}")
                except:
                    logger.error("无法读取相机状态")
            else:
                logger.error("相机对象为空")
                
            return self.__create_empty_image()
    
    def capture_multiple(self, num_frames: int = 3, interval_ms: int = 33) -> List[np.ndarray]:
        """
        捕获多帧图像
        
        Args:
            num_frames (int): 捕获的帧数
            interval_ms (int): 每帧之间的时间间隔，单位为毫秒
            
        Returns:
            List[np.ndarray]: OpenCV格式的图像列表，每一帧都是有效图像
            
        示例:
            # 捕获3帧图像，间隔33毫秒(约30fps)
            frames = capture.capture_multiple(num_frames=3, interval_ms=33)
            for i, frame in enumerate(frames):
                cv2.imshow(f"Frame {i}", frame)
            cv2.waitKey(0)
        """
        # 确保相机已初始化
        if self.__camera is None:
            # 先捕获一帧来初始化相机
            self.capture()
            # 再次检查相机是否已初始化
            if self.__camera is None:
                # 如果仍未初始化，返回指定数量的黑色图像
                logger.error("无法初始化相机，返回黑色图像")
                return [self.__create_empty_image() for _ in range(num_frames)]
        
        # 使用DXcam直接捕获多帧
        result = []
        for i in range(num_frames):
            # 对每一帧使用单帧捕获方法（它已经处理了所有的错误情况）
            frame = self.capture()
            result.append(frame)
            
            # 如果不是最后一帧，则按指定间隔等待
            if i < num_frames - 1:
                time.sleep(interval_ms / 1000.0)
        
        return result
    
    def set_capture_region(self, region: Tuple[int, int, int, int]) -> bool:
        """
        设置捕获区域
        
        Args:
            region (tuple): 捕获区域 (left, top, right, bottom)
            
        Returns:
            bool: 是否设置成功
            
        示例:
            # 设置捕获区域为屏幕左上角的100x100像素区域
            success = capture.set_capture_region((0, 0, 100, 100))
            if success:
                frame = capture.capture()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return False
        
        try:
            # 设置新的捕获区域
            self.__camera.stop()
            self.__camera = None
            
            # 获取当前显示器信息
            current_monitor = self.__monitors_info[self.__monitor_index]
            
            # 重新创建相机实例，使用新的区域
            self.__region = region
            self.__camera = dxcam.create(
                device_idx=current_monitor["device_idx"],
                output_idx=current_monitor["output_idx"],
                output_color=self.__output_color,
                region=region
            )
            
            logger.info(f"捕获区域已更新为: {region}")
            return True
            
        except Exception as e:
            logger.error(f"设置捕获区域时出错: {str(e)}")
            return False
    
    def start_recording(self, target_fps: int = 30) -> bool:
        """
        开始录制模式
        
        Args:
            target_fps (int): 目标帧率
            
        Returns:
            bool: 是否成功开始录制
            
        示例:
            # 开始以30fps的速率录制
            if capture.start_recording(target_fps=30):
                # 获取一些帧
                for _ in range(10):
                    frame = capture.get_latest_frame()
                    # 处理帧...
                # 停止录制
                capture.stop_recording()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return False
        
        try:
            self.__camera.start(target_fps=target_fps)
            logger.info(f"开始录制，目标帧率: {target_fps} fps")
            return True
            
        except Exception as e:
            logger.error(f"开始录制时出错: {str(e)}")
            return False
    
    def __create_empty_image(self) -> np.ndarray:
        """
        创建一个空的黑色图像，用于在捕获失败时返回
        
        Returns:
            np.ndarray: 黑色图像
        """
        monitor_info = self.get_current_monitor_info()
        if monitor_info:
            width = monitor_info.get("width", 1920)
            height = monitor_info.get("height", 1080)
            return np.zeros((height, width, 3), dtype=np.uint8)
        else:
            return np.zeros((1080, 1920, 3), dtype=np.uint8)
    
    def get_latest_frame(self) -> np.ndarray:
        """
        在录制模式下获取最新的一帧
        
        Returns:
            np.ndarray: 最新的一帧图像，即使遇到问题也会返回有效图像
            
        Raises:
            RuntimeError: 当相机无法初始化或不在录制模式下时抛出异常
            
        示例:
            # 获取录制模式下的最新帧
            frame = capture.get_latest_frame()
            cv2.imshow("Latest Frame", frame)
            cv2.waitKey(1)
        """
        # 确保相机已初始化并处于录制模式
        if self.__camera is None:
            error_msg = "相机未初始化，尝试初始化"
            logger.error(error_msg)
            # 调用capture方法初始化相机，它会处理初始化失败情况
            return self.capture()
        
        try:
            # 检查相机是否处于录制模式
            if not getattr(self.__camera, 'is_capturing', False):
                error_msg = "相机没有在录制模式下，尝试启动录制"
                logger.warning(error_msg)
                try:
                    self.__camera.start(target_fps=30, video_mode=True)
                except Exception as e:
                    error_msg = f"启动录制模式失败: {str(e)}"
                    logger.error(error_msg)
                    # 记录详细信息
                    if hasattr(self.__camera, 'device_idx'):
                        logger.error(f"相机设备索引: {self.__camera.device_idx}")
                    if hasattr(self.__camera, 'output_idx'):
                        logger.error(f"相机输出索引: {self.__camera.output_idx}")
                    # 如果无法启动录制，返回黑色图像
                    return self.__create_empty_image()
            
            # 尝试获取最新帧
            img = self.__camera.get_latest_frame()
            if img is not None and img.size > 0:
                return img
            
            # 如果无法获取有效帧，记录详细错误日志
            error_msg = "无法获取最新帧，返回黑色图像"
            logger.error(error_msg)
            # 记录额外的相机状态信息
            if hasattr(self.__camera, 'target_fps'):
                logger.error(f"目标帧率: {self.__camera.target_fps}")
            if hasattr(self.__camera, 'video_mode'):
                logger.error(f"视频模式: {self.__camera.video_mode}")
                
            return self.__create_empty_image()
            
        except Exception as e:
            error_msg = f"获取最新帧时出错: {str(e)}"
            logger.error(error_msg)
            # 记录详细的相机状态
            if self.__camera:
                try:
                    camera_attrs = [attr for attr in dir(self.__camera) if not attr.startswith('_') and not callable(getattr(self.__camera, attr))]
                    logger.error(f"相机属性: {camera_attrs}")
                except:
                    logger.error("无法获取相机属性")
            
            # 出错时，返回黑色图像
            return self.__create_empty_image()
    
    def stop_recording(self) -> None:
        """
        停止录制模式
        
        示例:
            # 停止之前开始的录制
            capture.stop_recording()
        """
        if self.__camera is None:
            logger.error("相机未初始化")
            return
        
        try:
            self.__camera.stop()
            logger.info("录制已停止")
            
        except Exception as e:
            logger.error(f"停止录制时出错: {str(e)}")
    
    def release(self) -> None:
        """
        释放相机资源
        
        示例:
            # 使用完毕后释放资源
            capture.release()
        """
        if self.__camera is not None:
            try:
                self.__camera.stop()
                self.__camera = None
                logger.info("相机资源已释放")
                
            except Exception as e:
                logger.error(f"释放相机资源时出错: {str(e)}")
    
    def __enter__(self):
        """上下文管理器入口方法，用于支持with语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出方法，确保资源被正确释放"""
        self.release()

    def record_screen(self, 
                     output_file: str, 
                     fps: int = 30, 
                     quality: int = 95, 
                     codec: str = 'mp4v', 
                     duration: Optional[float] = None,
                     max_frames: Optional[int] = None,
                     buffer_size: int = 120,
                     skip_duplicate_frames: bool = True,
                     show_preview: bool = False) -> bool:
        """
        录制指定显示器的屏幕并保存为视频文件
        
        Args:
            output_file (str): 输出视频文件路径，支持.mp4, .avi等格式
            fps (int): 录制的目标帧率，默认30fps
            quality (int): 视频质量，范围0-100，默认95
            codec (str): 视频编解码器，默认'mp4v'，其他选项如'avc1', 'xvid', 'mjpg'
            duration (float, optional): 录制时长（秒），None表示无限制直到手动停止
            max_frames (int, optional): 最大录制帧数，None表示无限制
            buffer_size (int): 帧缓冲区大小，默认120帧
            skip_duplicate_frames (bool): 是否跳过重复帧以节省空间，默认True
            show_preview (bool): 是否在录制时显示预览窗口，默认False
            
        Returns:
            bool: 录制是否成功
            
        Raises:
            ValueError: 当参数无效时
            RuntimeError: 当录制过程中出错时
            
        示例:
            # 录制主显示器30秒，保存为output.mp4
            capture = DXCamCore(monitor_index=0)
            capture.record_screen("output.mp4", fps=30, duration=30)
            
            # 录制直到手动停止
            capture = DXCamCore(monitor_index=1)
            try:
                capture.record_screen("screen_record.mp4", fps=60, show_preview=True)
            except KeyboardInterrupt:
                # 用户按Ctrl+C停止录制
                pass
        """
        # 参数验证
        if not output_file:
            error_msg = "输出文件路径不能为空"
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        if fps <= 0:
            error_msg = f"帧率必须大于0，当前值: {fps}"
            logger.error(error_msg)
            raise ValueError(error_msg)
            
        if quality < 0 or quality > 100:
            error_msg = f"质量必须在0-100范围内，当前值: {quality}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 确保相机已初始化
        if self.__camera is None:
            logger.error("相机未初始化，尝试初始化")
            try:
                # 捕获一帧来初始化相机
                self.capture()
                if self.__camera is None:
                    error_msg = "无法初始化相机，录制失败"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)
            except Exception as e:
                error_msg = f"初始化相机失败: {str(e)}"
                logger.error(error_msg)
                raise RuntimeError(error_msg) from e
        
        # 设置视频编解码器
        fourcc = cv2.VideoWriter_fourcc(*codec)
        
        # 获取当前显示器信息，确定视频尺寸
        monitor_info = self.get_current_monitor_info()
        if not monitor_info:
            error_msg = "无法获取显示器信息"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
            
        # 确定视频尺寸
        if self.__region is None:
            # 使用整个显示器
            width = monitor_info["width"]
            height = monitor_info["height"]
        else:
            # 使用指定区域
            width = self.__region[2] - self.__region[0]
            height = self.__region[3] - self.__region[1]
        
        # 创建视频写入器
        try:
            video_writer = cv2.VideoWriter(
                output_file, 
                fourcc, 
                fps, 
                (width, height),
                True  # isColor
            )
        except Exception as e:
            error_msg = f"创建视频写入器失败: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
            
        if not video_writer.isOpened():
            error_msg = f"无法打开输出文件进行写入: {output_file}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        # 设置录制参数
        try:
            # 设置视频质量（仅部分编解码器支持）
            video_writer.set(cv2.VIDEOWRITER_PROP_QUALITY, quality)
        except:
            logger.warning(f"无法设置视频质量为 {quality}，使用默认值")
        
        # 开始录制
        logger.info(f"开始录制屏幕，输出文件: {output_file}")
        
        try:
            # 录制变量
            frames_recorded = 0
            start_time = time.time()
            last_frame = None
            frame_times = []  # 用于计算实际帧率
            
            # 创建帧缓冲区
            frame_buffer = []
            
            # 录制循环
            try:
                while True:
                    # 检查是否达到最大帧数
                    if max_frames is not None and frames_recorded >= max_frames:
                        logger.info(f"已达到最大帧数 {max_frames}，停止录制")
                        break
                        
                    # 检查是否达到最大时长
                    current_time = time.time()
                    elapsed_time = current_time - start_time
                    if duration is not None and elapsed_time >= duration:
                        logger.info(f"已达到指定时长 {duration} 秒，停止录制")
                        break
                    
                    # 使用grab()方法捕获当前帧
                    frame_start_time = time.time()
                    frame = self.__camera.grab()
                    
                    # 如果帧为空，尝试使用capture方法获取
                    if frame is None or frame.size == 0:
                        logger.warning("grab()方法获取帧失败，尝试使用capture方法")
                        frame = self.capture()
                    
                    # 记录帧时间用于计算实际帧率
                    frame_capture_time = time.time()
                    frame_times.append(frame_capture_time)
                    if len(frame_times) > buffer_size:
                        frame_times.pop(0)
                    
                    # 跳过重复帧（如果启用）
                    if skip_duplicate_frames and last_frame is not None:
                        if np.array_equal(frame, last_frame):
                            # 计算并等待下一帧的时间
                            frame_time = frame_capture_time - frame_start_time
                            sleep_time = (1.0 / fps) - frame_time
                            if sleep_time > 0:
                                time.sleep(sleep_time)
                            continue
                    
                    # 将帧添加到缓冲区
                    frame_buffer.append(frame)
                    last_frame = frame
                    
                    # 当缓冲区达到一定大小时批量写入
                    if len(frame_buffer) >= min(30, buffer_size // 4):
                        for buffered_frame in frame_buffer:
                            video_writer.write(buffered_frame)
                            frames_recorded += 1
                        frame_buffer.clear()
                    
                    # 显示预览（如果启用）
                    if show_preview:
                        cv2.imshow("Screen Recording Preview", frame)
                        # 检查是否按下ESC键停止录制
                        if cv2.waitKey(1) == 27:  # ESC键
                            logger.info("用户按下ESC键，停止录制")
                            break
                    
                    # 计算和显示实际帧率（每秒更新一次）
                    if elapsed_time % 1 < 0.1 and len(frame_times) > 1:
                        actual_fps = (len(frame_times) - 1) / (frame_times[-1] - frame_times[0])
                        logger.info(f"已录制 {frames_recorded} 帧，实际帧率: {actual_fps:.2f} fps")
                    
                    # 控制循环速度，避免CPU使用率过高
                    frame_time = time.time() - frame_start_time
                    sleep_time = (1.0 / fps) - frame_time
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                        
            except KeyboardInterrupt:
                logger.info("用户中断，停止录制")
            finally:
                # 写入剩余的缓冲帧
                for buffered_frame in frame_buffer:
                    video_writer.write(buffered_frame)
                    frames_recorded += 1
                
                # 关闭预览窗口
                if show_preview:
                    cv2.destroyAllWindows()
                
                # 关闭视频写入器
                video_writer.release()
                
                # 计算总录制时间和平均帧率
                total_time = time.time() - start_time
                avg_fps = frames_recorded / total_time if total_time > 0 else 0
                
                logger.info(f"录制完成: {output_file}")
                logger.info(f"总帧数: {frames_recorded}, 总时长: {total_time:.2f} 秒, 平均帧率: {avg_fps:.2f} fps")
                
                return True
                
        except Exception as e:
            error_msg = f"录制过程中出错: {str(e)}"
            logger.error(error_msg)
            
            # 确保资源被释放
            try:
                video_writer.release()
            except:
                pass
                
            if show_preview:
                cv2.destroyAllWindows()
                
            raise RuntimeError(error_msg) from e
