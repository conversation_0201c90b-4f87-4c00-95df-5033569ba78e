import traceback
import threading
from typing import Callable, Dict, List, Tuple, Optional, Any
from PyQt5.QtWidgets import (
    QLabel,
)
from PyQt5.QtCore import (
    QRegExp,
    QThread,
    pyqtSignal,
    QObject,
    Qt,
    QTimer,
    QEvent,
    QTime,
)
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5 import sip
from PyQt5.QtWidgets import *
import re
from typing import *
import logging
from functools import partial
from global_tools.utils import ClassInstanceManager,Logger

class LabelEventFilter(QObject):
    """
    一个简单的事件过滤器，用于捕获标签上的鼠标点击事件并发出信号。
    """
    label_clicked = pyqtSignal(str)

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        """
        过滤事件，仅处理鼠标按下事件。

        Args:
            watched (QObject): 正在观察的对象。
            event (QEvent): 发生的事件。

        Returns:
            bool: 如果事件被处理则返回 True，否则返回 False。
        """
        if event.type() == QEvent.Type.MouseButtonPress:
            if isinstance(watched, QLabel):
                self.label_clicked.emit(watched.objectName())
                # 返回 True 表示事件已处理，不再进一步分发
                return True
        # 对于所有其他事件，返回 False，以便它们可以被正常处理
        return super().eventFilter(watched, event)


class QLabelManager(QObject):
    """
    QLabelManager 类用于管理一组 QLabel 控件，提供便捷的交互接口和信号通知（单例模式）。

    相比 LabelManager，本类的改进点：
    1. 直接使用 QLabel 控件的名称作为索引
    2. 接收方式更简单，直接传入一组 QLabel 控件
    3. 公共方法接收 QLabel 控件名称作为参数
    4. 实现了线程安全的单例模式

    此类通过 QLabel 的名称来精确查找、修改和查询其各种属性，
    如文本、可见性、样式表和对齐方式。当相关属性变化时，会发出相应的信号。

    ===== 单例模式说明 =====
    该类实现了线程安全的单例模式：
    - 首次创建时需要传入QLabel控件列表参数
    - 后续调用可以不传参数，直接返回已创建的实例
    - 支持重置单例实例，重新创建新的管理器
    - 在多线程环境下保证线程安全

    ===== 使用场景示例 =====

    1. 创建和初始化（单例模式）:
       ```python
       # 创建多个QLabel
       status_label = QLabel("状态")
       status_label.setObjectName("status")

       info_label = QLabel("信息")
       info_label.setObjectName("info")

       error_label = QLabel("错误")
       error_label.setObjectName("error")

       # 方式1：直接创建管理器（首次创建）
       manager = QLabelManager([status_label, info_label, error_label])

       # 方式2：使用推荐的类方法创建（首次创建）
       manager = QLabelManager.get_instance([status_label, info_label, error_label])

       # 方式3：后续获取已创建的实例（无需参数）
       manager = QLabelManager.get_instance()
       # 或者
       manager = QLabelManager()
       ```

    2. 设置标签内容和样式:
       ```python
       # 设置文本内容
       manager.set_text("status", "运行中")
       manager.set_text("info", "系统正常")

       # 设置样式
       manager.set_stylesheet("status", "color: green; font-weight: bold;")
       manager.set_stylesheet("error", "color: red; background: #ffe0e0;")

       # 设置可见性
       manager.set_visible("error", False)  # 隐藏错误标签
       ```

    3. 连接信号处理:
       ```python
       # 连接文本变化信号
       def on_text_changed(label_name, new_text):
           print(f"标签 '{label_name}' 的文本已更改为: {new_text}")

       manager.text_changed.connect(on_text_changed)

       # 连接点击信号
       def on_label_clicked(label_name):
           print(f"标签 '{label_name}' 被点击了！")

       manager.label_clicked.connect(on_label_clicked)
       ```

    4. 动画效果:
       ```python
       # 启动淡入淡出动画
       manager.start_fade_animation("status", 200)  # 200ms动画
       ```

    5. 单例模式管理:
       ```python
       # 检查实例是否已创建
       if QLabelManager.is_instance_created():
           manager = QLabelManager.get_instance()
       else:
           # 首次创建
           manager = QLabelManager.get_instance([label1, label2])

       # 获取实例信息
       info = QLabelManager.get_instance_info()
       if info:
           print(f"管理的标签数量: {info['label_count']}")

       # 重置单例实例（在需要重新初始化时）
       QLabelManager.reset_instance()
       new_manager = QLabelManager.get_instance([new_label1, new_label2])
       ```

    信号:
    - text_changed(label_name: str, new_text: str): 当标签文本通过 set_text 成功改变时发出
    - visibility_changed(label_name: str, is_visible: bool): 当标签可见性通过 set_visible 成功改变时发出
    - stylesheet_changed(label_name: str, new_stylesheet: str): 当标签样式表通过 set_stylesheet 成功改变时发出
    - alignment_changed(label_name: str, new_alignment: Qt.Alignment): 当标签对齐方式通过 set_alignment 成功改变时发出
    - label_clicked(label_name: str): 当标签被点击时发出
    """

    # =================================================================================
    # 单例模式相关类变量
    # =================================================================================
    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _initialized = False  # 初始化标志
    _cached_args = None  # 缓存的构造参数
    _cached_kwargs = None  # 缓存的关键字参数

    # 定义信号
    text_changed = pyqtSignal(str, str)
    visibility_changed = pyqtSignal(str, bool)
    stylesheet_changed = pyqtSignal(str, str)
    alignment_changed = pyqtSignal(str, Qt.Alignment)
    label_clicked = pyqtSignal(str)

    # 定义动画参数 (可配置)
    FADE_DURATION_MS = 50  # 默认动画时长
    FADE_EASING_CURVE = QEasingCurve.InOutQuad

    # 内部信号，用于线程安全的UI更新
    __update_text_signal = pyqtSignal(str, str)
    __update_visibility_signal = pyqtSignal(str, bool)
    __update_stylesheet_signal = pyqtSignal(str, str)
    __update_alignment_signal = pyqtSignal(str, Qt.Alignment)
    __start_fade_animation_signal = pyqtSignal(str, int)

    def __new__(cls, labels: List[QLabel] = None, parent: Optional[QObject] = None):
        """
        单例模式的 __new__ 方法

        实现线程安全的单例模式：
        - 如果实例不存在，创建新实例
        - 如果实例已存在且有效，直接返回现有实例
        - 如果实例已存在但无效（被删除），重新创建实例

        Args:
            labels (List[QLabel], optional): QLabel控件列表（首次创建时需要）
            parent (Optional[QObject]): 父对象（可选）

        Returns:
            QLabelManager: 单例实例
        """
        # 双重检查锁定模式，确保线程安全
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建新实例
                    cls._instance = super(QLabelManager, cls).__new__(cls)
                    cls._initialized = False
                    # 缓存参数供后续使用
                    cls._cached_args = labels
                    cls._cached_kwargs = {'parent': parent}
        else:
            # 检查现有实例是否仍然有效
            try:
                if sip.isdeleted(cls._instance):
                    # 实例已被删除，重新创建
                    with cls._lock:
                        if sip.isdeleted(cls._instance):
                            cls._instance = super(QLabelManager, cls).__new__(cls)
                            cls._initialized = False
                            # 如果没有传入新参数，使用缓存的参数
                            if labels is None and cls._cached_args:
                                cls._cached_args = cls._cached_args
                                cls._cached_kwargs = cls._cached_kwargs
                            else:
                                cls._cached_args = labels
                                cls._cached_kwargs = {'parent': parent}
            except RuntimeError:
                # 处理可能的运行时错误
                with cls._lock:
                    cls._instance = super(QLabelManager, cls).__new__(cls)
                    cls._initialized = False
                    cls._cached_args = labels if labels is not None else cls._cached_args
                    cls._cached_kwargs = {'parent': parent}

        return cls._instance

    def __init__(self, labels: List[QLabel] = None, parent: Optional[QObject] = None):
        """
        初始化 QLabelManager（单例模式）

        Args:
            labels (List[QLabel], optional): QLabel实例列表，每个 QLabel 必须有唯一的 objectName
                                           首次创建时必须提供，后续调用可以为空（使用缓存的参数）
            parent (Optional[QObject]): 父对象，默认为 None

        注意：
            - 如果实例已经初始化过，此方法会直接返回，不会重复初始化
            - 如果没有传入参数且有缓存参数，会使用缓存的参数进行初始化
            - 可以通过 reset_instance() 方法重置单例实例

        示例:
            ```python
            # 方式1：直接创建管理器（首次创建）
            label1 = QLabel("标签1")
            label1.setObjectName("label1")
            label2 = QLabel("标签2")
            label2.setObjectName("label2")
            manager = QLabelManager([label1, label2])

            # 方式2：使用推荐的类方法创建（首次创建）
            manager = QLabelManager.get_instance([label1, label2])

            # 方式3：后续获取已创建的实例（无需参数）
            manager = QLabelManager.get_instance()
            # 或者
            manager = QLabelManager()

            # 连接点击信号
            def on_label_clicked(name):
                print(f"标签 '{name}' 被点击了！")
            manager.label_clicked.connect(on_label_clicked)
            ```
        """
        # 检查是否已经初始化过
        if self.__class__._initialized:
            return

        # 如果没有传入参数，尝试使用缓存的参数
        if labels is None and self.__class__._cached_args:
            labels = self.__class__._cached_args
            if self.__class__._cached_kwargs and 'parent' in self.__class__._cached_kwargs:
                parent = self.__class__._cached_kwargs['parent']

        # 如果仍然没有参数，记录警告但继续初始化
        if labels is None:
            # 延迟获取logger，避免循环依赖
            try:
                logger = ClassInstanceManager.get_instance(key="ui_logger")
                if logger:
                    logger.warning("QLabelManager 初始化时未提供 QLabel 控件，将创建空的管理器")
            except:
                pass  # 如果获取logger失败，忽略警告
            labels = []

        super().__init__(parent)

        # 连接内部信号到槽函数
        self.__update_text_signal.connect(self.__on_update_text)
        self.__update_visibility_signal.connect(self.__on_update_visibility)
        self.__update_stylesheet_signal.connect(self.__on_update_stylesheet)
        self.__update_alignment_signal.connect(self.__on_update_alignment)
        self.__start_fade_animation_signal.connect(self.__on_start_fade_animation)

        self.__labels: Dict[str, QLabel] = {}
        self.__animation_data: Dict[str, Tuple[QGraphicsOpacityEffect, QSequentialAnimationGroup]] = {}
        self.__logger: Logger = ClassInstanceManager.get_instance(key="ui_logger")  # type: ignore
        self.__logger.debug("初始化 QLabelManager...")

        # 创建并连接事件过滤器
        self.__event_filter = LabelEventFilter(self)
        self.__event_filter.label_clicked.connect(self.label_clicked)

        # 验证并处理传入的标签列表
        if not isinstance(labels, list):
            self.__logger.error(f"labels 参数必须是列表类型，实际类型: {type(labels)}")
            labels = []

        # 处理传入的标签
        for i, label in enumerate(labels):
            if isinstance(label, QLabel):
                self.__add_label(label)
            else:
                self.__logger.warning(f"labels[{i}] 不是 QLabel 类型，已跳过: {type(label)}")

        # 标记为已初始化
        self.__class__._initialized = True
        self.__logger.debug(f"QLabelManager 初始化完成，共管理 {len(self.__labels)} 个标签: {list(self.__labels.keys())}")

    def __add_label(self, label: QLabel) -> None:
        """(私有) 验证并添加单个 QLabel 到管理器中。"""
        if sip.isdeleted(label):
            self.__logger.warning("尝试添加一个已被删除的 QLabel，已跳过。")
            return

        object_name = label.objectName()

        if not object_name:
            self.__logger.error(f"尝试添加一个未设置 objectName 的 QLabel，已跳过。请为所有管理的 QLabel 设置唯一的 objectName。")
            return

        if object_name in self.__labels:
            self.__logger.warning(f"尝试添加具有重复 objectName ('{object_name}') 的 QLabel，已跳过。确保 objectName 唯一。")
            return

        self.__labels[object_name] = label
        # 为新标签安装事件过滤器以捕获点击
        label.installEventFilter(self.__event_filter)
        self.__logger.debug(f"成功添加 QLabel '{object_name}' 到管理器。")

    def add_label(self, label: QLabel) -> bool:
        """
        添加一个新的 QLabel 到管理器中。

        Args:
            label (QLabel): 要添加的 QLabel 实例，必须有唯一的 objectName

        Returns:
            bool: 添加成功返回 True，否则返回 False

        示例:
            ```python
            label3 = QLabel("新标签")
            label3.setObjectName("label3")
            success = manager.add_label(label3)
            # 现在 label3 会在被点击时通过 manager 发出 label_clicked 信号
            ```
        """
        try:
            if not isinstance(label, QLabel):
                self.__logger.error(f"传入的参数不是 QLabel 类型: {type(label)}")
                return False

            if sip.isdeleted(label):
                self.__logger.warning("尝试添加一个已被删除的 QLabel")
                return False

            object_name = label.objectName()

            if not object_name:
                self.__logger.error("尝试添加一个未设置 objectName 的 QLabel")
                return False

            if object_name in self.__labels:
                self.__logger.warning(f"标签名称 '{object_name}' 已存在")
                return False

            self.__add_label(label)
            return object_name in self.__labels
        except Exception as e:
            self.__logger.error(f"添加标签时发生错误: {e}")
            traceback.print_exc()
            return False

    def remove_label(self, label_name: str) -> bool:
        """
        从管理器中显式移除一个 QLabel。

        Args:
            label_name (str): 要移除的 QLabel 的 objectName

        Returns:
            bool: 移除成功返回 True，如果标签不存在则返回 False

        示例:
            ```python
            was_removed = manager.remove_label("label1")
            if was_removed:
                print("标签 'label1' 已被移除")
            ```
        """
        self.__logger.debug(f"请求移除标签 '{label_name}'")
        label = self.__labels.get(label_name)

        if label is None:
            self.__logger.warning(f"尝试移除一个不存在的标签: '{label_name}'")
            return False
            
        if sip.isdeleted(label):
            self.__logger.warning(f"尝试移除一个已经被删除的标签: '{label_name}'")
        else:
            # 从标签上移除事件过滤器
            try:
                label.removeEventFilter(self.__event_filter)
            except Exception as e:
                self.__logger.warning(f"移除标签 '{label_name}' 的事件过滤器时出错: {e}")

        # 清理动画数据
        if label_name in self.__animation_data:
            self.__cleanup_animation_data(label_name)

        # 从主字典中删除
        if label_name in self.__labels:
            del self.__labels[label_name]
            self.__logger.debug(f"成功移除标签 '{label_name}'")
            return True
            
        return False

    def get_label(self, label_name: str) -> Optional[QLabel]:
        """
        通过标签名称获取 QLabel 实例。

        Args:
            label_name (str): QLabel 的 objectName

        Returns:
            Optional[QLabel]: 找到的 QLabel 实例，如果未找到或已被删除则返回 None

        示例:
            ```python
            label = manager.get_label("label1")
            if label:
                print(f"找到标签: {label.text()}")
            ```
        """
        self.__logger.debug(f"尝试获取名称为 '{label_name}' 的 QLabel 实例")
        label = self.__labels.get(label_name)

        if label is None:
            self.__logger.warning(f"未在管理器中找到名称为 '{label_name}' 的 QLabel")
            if label_name in self.__animation_data:
                self.__cleanup_animation_data(label_name)
            return None

        if sip.isdeleted(label):
            self.__logger.warning(f"名称为 '{label_name}' 的 QLabel 已被删除，将从管理器中移除")
            # 自动清理
            self.remove_label(label_name)
            return None

        return label

    def has_label(self, label_name: str) -> bool:
        """
        判断是否存在指定名称的标签。

        Args:
            label_name (str): QLabel 的 objectName

        Returns:
            bool: 如果标签存在且有效返回 True，否则返回 False

        示例:
            ```python
            if manager.has_label("label1"):
                print("标签存在")
            else:
                print("标签不存在")
            ```
        """
        self.__logger.debug(f"检查是否存在名称为 '{label_name}' 的有效 QLabel")
        label = self.__labels.get(label_name)
        
        if label is None:
            self.__logger.debug(f"未在管理器中找到名称为 '{label_name}' 的 QLabel")
            return False
            
        if sip.isdeleted(label):
            self.__logger.debug(f"名称为 '{label_name}' 的 QLabel 已被删除，将从管理器中移除")
            # 自动清理
            self.remove_label(label_name)
            return False
            
        return True

    def __cleanup_animation_data(self, label_name: str) -> None:
        """(私有) 清理指定名称标签的动画效果和动画对象。"""
        if label_name in self.__animation_data:
            self.__logger.debug(f"清理标签 '{label_name}' 的动画数据")
            effect, anim_group = self.__animation_data[label_name]

            if anim_group.state() == QPropertyAnimation.Running: # type: ignore
                anim_group.stop()

            # 找到拥有该效果的标签，并移除效果
            # 这个标签可能在 __labels 中，也可能已经被删除了
            label = self.__labels.get(label_name)
            if label and not sip.isdeleted(label) and label.graphicsEffect() == effect:
                label.setGraphicsEffect(None)

            # 确保效果对象被删除
            if not sip.isdeleted(effect):
                effect.deleteLater()
            
            if not sip.isdeleted(anim_group):
                anim_group.deleteLater()

            del self.__animation_data[label_name]

    def set_text(self, label_name: str, text: str) -> bool:
        """
        通过信号槽机制设置指定标签的文本，确保线程安全。

        Args:
            label_name (str): 标签的名称 (objectName)
            text (str): 要设置的文本内容

        Returns:
            bool: 请求发送成功返回 True，标签不存在则返回 False

        示例:
            ```python
            # 设置标签文本
            success = manager.set_text("label1", "新文本内容")
            ```
        """
        self.__logger.debug(f"请求通过信号槽设置标签 '{label_name}' 的文本")
        if not self.has_label(label_name):
            return False
        self.__update_text_signal.emit(label_name, text)
        return True

    def __ensure_label_visible(self, label: QLabel) -> None:
        """(私有) 确保标签的效果透明度为 1.0。"""
        name = label.objectName()
        if name in self.__animation_data:
            effect, anim_group = self.__animation_data[name]
            if not sip.isdeleted(effect) and effect.opacity() != 1.0:
                if anim_group.state() == QPropertyAnimation.Running: # type: ignore
                    anim_group.stop()
                effect.setOpacity(1.0)
                self.__logger.debug(f"确保标签 '{name}' 透明度重置为 1.0")
        elif label.graphicsEffect() is not None and isinstance(label.graphicsEffect(), QGraphicsOpacityEffect):
            try:
                label.graphicsEffect().setOpacity(1.0) # type: ignore
            except Exception as e:
                self.__logger.warning(f"尝试重置外部图形效果透明度时出错 for label '{name}': {e}")

    def start_fade_animation(self, label_name: str, duration_ms: Optional[int] = None) -> bool:
        """
        通过信号槽机制为指定标签启动淡出再淡入的动画效果，确保线程安全。

        Args:
            label_name (str): 标签的名称 (objectName)
            duration_ms (Optional[int]): 动画时长（毫秒），默认为 FADE_DURATION_MS (50ms)

        Returns:
            bool: 请求发送成功返回 True，标签不存在则返回 False

        示例:
            ```python
            # 使用默认时长启动动画
            manager.start_fade_animation("label1")

            # 指定时长启动动画
            manager.start_fade_animation("label2", 200)
            ```
        """
        duration = self.FADE_DURATION_MS if duration_ms is None else int(duration_ms)
        self.__logger.debug(f"请求通过信号槽为标签 '{label_name}' 启动动画，时长: {duration}ms")

        if not self.has_label(label_name):
            return False

        self.__start_fade_animation_signal.emit(label_name, duration)
        return True

    def __get_or_create_animation_group(self, label: QLabel) -> Tuple[QGraphicsOpacityEffect, QSequentialAnimationGroup]:
        """(私有) 获取或创建标签的动画效果和顺序动画组。"""
        name = label.objectName()
        if name in self.__animation_data:
            effect, anim_group = self.__animation_data[name]
            # 健壮性检查: 效果是否还在标签上，对象是否已被删除？
            if not sip.isdeleted(effect) and not sip.isdeleted(anim_group) and label.graphicsEffect() == effect:
                self.__logger.debug(f"复用 '{name}' 的现有动画数据")
                return effect, anim_group
            else:
                self.__logger.warning(f"标签 '{name}' 的动画数据无效或丢失，将重新创建")
                self.__cleanup_animation_data(name)

        # 创建新的动画数据
        self.__logger.debug(f"为 '{name}' 创建新的动画效果和顺序动画组")
        effect = QGraphicsOpacityEffect(label)
        label.setGraphicsEffect(effect)

        # 淡出动画
        fade_out_anim = QPropertyAnimation(effect, b"opacity", label)
        fade_out_anim.setEndValue(0.0)
        fade_out_anim.setEasingCurve(self.FADE_EASING_CURVE)

        # 淡入动画
        fade_in_anim = QPropertyAnimation(effect, b"opacity", label)
        fade_in_anim.setStartValue(0.0)
        fade_in_anim.setEndValue(1.0)
        fade_in_anim.setEasingCurve(self.FADE_EASING_CURVE)

        # 创建顺序动画组
        anim_group = QSequentialAnimationGroup(label)
        anim_group.addAnimation(fade_out_anim)
        anim_group.addAnimation(fade_in_anim)

        self.__animation_data[name] = (effect, anim_group)
        return effect, anim_group

    def get_text(self, label_name: str) -> Optional[str]:
        """
        获取指定标签的文本内容。

        Args:
            label_name (str): 标签的名称 (objectName)

        Returns:
            Optional[str]: 标签的文本内容，如果标签不存在则返回 None

        示例:
            ```python
            text = manager.get_text("label1")
            print(f"标签内容: {text}")
            ```
        """
        self.__logger.debug(f"尝试获取标签 '{label_name}' 的文本")
        label = self.get_label(label_name)
        return label.text() if label else None

    def set_visible(self, label_name: str, visible: bool) -> bool:
        """
        通过信号槽机制设置指定标签的可见性，确保线程安全。

        Args:
            label_name (str): 标签的名称 (objectName)
            visible (bool): True 表示可见，False 表示隐藏

        Returns:
            bool: 请求发送成功返回 True，标签不存在则返回 False

        示例:
            ```python
            # 隐藏标签
            manager.set_visible("label1", False)

            # 显示标签
            manager.set_visible("label1", True)
            ```
        """
        self.__logger.debug(f"请求通过信号槽设置标签 '{label_name}' 的可见性为: {visible}")
        if not self.has_label(label_name):
            return False
        self.__update_visibility_signal.emit(label_name, visible)
        return True

    def is_visible(self, label_name: str) -> Optional[bool]:
        """
        获取指定标签的可见性状态。

        Args:
            label_name (str): 标签的名称 (objectName)

        Returns:
            Optional[bool]: 标签是否可见，标签不存在则返回 None

        示例:
            ```python
            visible = manager.is_visible("label1")
            print(f"标签是否可见: {visible}")
            ```
        """
        self.__logger.debug(f"尝试获取标签 '{label_name}' 的可见性")
        label = self.get_label(label_name)
        return label.isVisible() if label else None

    def set_stylesheet(self, label_name: str, stylesheet: str) -> bool:
        """
        通过信号槽机制设置指定标签的样式表，确保线程安全。

        Args:
            label_name (str): 标签的名称 (objectName)
            stylesheet (str): 要设置的样式表

        Returns:
            bool: 请求发送成功返回 True，标签不存在则返回 False

        示例:
            ```python
            # 设置红色文本样式
            style = "color: red; font-weight: bold;"
            manager.set_stylesheet("label1", style)
            ```
        """
        self.__logger.debug(f"请求通过信号槽设置标签 '{label_name}' 的样式表")
        if not self.has_label(label_name):
            return False
        self.__update_stylesheet_signal.emit(label_name, stylesheet)
        return True

    def get_stylesheet(self, label_name: str) -> Optional[str]:
        """
        获取指定标签的样式表。

        Args:
            label_name (str): 标签的名称 (objectName)

        Returns:
            Optional[str]: 标签的样式表，标签不存在则返回 None

        示例:
            ```python
            style = manager.get_stylesheet("label1")
            print(f"标签样式: {style}")
            ```
        """
        self.__logger.debug(f"尝试获取标签 '{label_name}' 的样式表")
        label = self.get_label(label_name)
        return label.styleSheet() if label else None

    def set_alignment(self, label_name: str, alignment: Qt.Alignment) -> bool:
        """
        通过信号槽机制设置指定标签的对齐方式，确保线程安全。

        Args:
            label_name (str): 标签的名称 (objectName)
            alignment (Qt.Alignment): 要设置的对齐方式，如 Qt.AlignCenter

        Returns:
            bool: 请求发送成功返回 True，标签不存在则返回 False

        示例:
            ```python
            # 设置居中对齐
            manager.set_alignment("label1", Qt.AlignCenter)

            # 设置左对齐
            manager.set_alignment("label2", Qt.AlignLeft)
            ```
        """
        self.__logger.debug(f"请求通过信号槽设置标签 '{label_name}' 的对齐方式")
        if not self.has_label(label_name):
            return False
        self.__update_alignment_signal.emit(label_name, alignment)
        return True

    def get_alignment(self, label_name: str) -> Optional[Qt.Alignment]:
        """
        获取指定标签的对齐方式。

        Args:
            label_name (str): 标签的名称 (objectName)

        Returns:
            Optional[Qt.Alignment]: 标签的对齐方式，标签不存在则返回 None

        示例:
            ```python
            alignment = manager.get_alignment("label1")
            if alignment == Qt.AlignCenter:
                print("标签是居中对齐的")
            ```
        """
        self.__logger.debug(f"尝试获取标签 '{label_name}' 的对齐方式")
        label = self.get_label(label_name)
        return label.alignment() if label else None

    def clear_text(self, label_name: str) -> bool:
        """
        清空指定标签的文本。

        Args:
            label_name (str): 标签的名称 (objectName)

        Returns:
            bool: 清空成功返回 True，失败返回 False

        示例:
            ```python
            manager.clear_text("label1")
            ```
        """
        self.__logger.debug(f"尝试清空标签 '{label_name}' 的文本")
        return self.set_text(label_name, "")

    def clear_all_texts(self) -> None:
        """
        清空所有标签的文本。

        示例:
            ```python
            manager.clear_all_texts()
            ```
        """
        self.__logger.debug("尝试清空所有标签的文本")
        names = list(self.__labels.keys())
        count = 0
        for name in names:
            if self.clear_text(name):
                count += 1
        self.__logger.debug(f"清空文本操作完成，成功清空 {count} 个标签")

    def get_all_label_names(self) -> List[str]:
        """
        获取所有标签的名称列表。

        Returns:
            List[str]: 标签名称列表

        示例:
            ```python
            names = manager.get_all_label_names()
            print(f"管理的标签: {names}")
            ```
        """
        self.__logger.debug("获取所有管理的标签名称列表")
        valid_names = [name for name in self.__labels if not sip.isdeleted(self.__labels[name])]

        # 清理不再有效的标签动画数据
        active_anim_names = set(self.__animation_data.keys())
        valid_label_names = set(valid_names)
        names_to_cleanup = active_anim_names - valid_label_names

        for name in names_to_cleanup:
            self.__logger.warning(f"发现标签 '{name}' 已不在管理器中，但仍有动画数据，进行清理")
            self.__cleanup_animation_data(name)

        return valid_names

    def cleanup(self) -> None:
        """
        清理管理器，停止所有动画并释放资源。
        建议在不再需要管理器时调用，或在父对象销毁前调用。

        示例:
            ```python
            # 在窗口关闭时清理资源
            manager.cleanup()
            ```
        """
        self.__logger.debug("开始清理 QLabelManager...")
        # 移除所有标签上的事件过滤器并清理
        all_names = list(self.__labels.keys())
        for name in all_names:
            self.remove_label(name)
        
        self.__animation_data.clear()
        self.__labels.clear()
        self.__logger.debug("QLabelManager 清理完成")

    # =================================================================================
    # Private Slots for Thread-Safe UI Updates
    # =================================================================================

    def __on_update_text(self, label_name: str, text: str) -> None:
        """(私有槽) 在GUI线程中安全地更新标签文本。"""
        label = self.get_label(label_name)
        if not label:
            return

        try:
            if label.text() != text:
                label.setText(text)
                self.__logger.debug(f"成功在GUI线程设置标签 '{label_name}' 的文本")
                self.text_changed.emit(label_name, text)
            else:
                self.__logger.debug(f"标签 '{label_name}' 的文本已是目标文本，无需更改")
        except Exception as e:
            self.__logger.error(f"在槽函数中设置标签 '{label_name}' 文本时出错: {e}")
            traceback.print_exc()

    def __on_start_fade_animation(self, label_name: str, duration: int) -> None:
        """(私有槽) 在GUI线程中安全地启动动画。"""
        label = self.get_label(label_name)
        if not label:
            return

        if duration <= 0:
            self.__logger.debug(f"动画时长 ({duration}ms) 无效或为0，跳过动画，仅确保标签 '{label_name}' 可见")
            self.__ensure_label_visible(label)
            return

        try:
            effect, anim_group = self.__get_or_create_animation_group(label)

            # 如果动画正在运行，则停止它
            if anim_group.state() == QPropertyAnimation.Running: # type: ignore
                anim_group.stop()

            # 更新动画时长
            fade_out_anim = anim_group.animationAt(0)
            fade_in_anim = anim_group.animationAt(1)
            fade_out_anim.setDuration(duration) # type: ignore
            fade_in_anim.setDuration(duration) # type: ignore

            # 设置淡出动画的起始值，确保从当前不透明度开始
            fade_out_anim.setStartValue(effect.opacity()) # type: ignore

            # 启动动画组
            anim_group.start()
            self.__logger.debug(f"'{label_name}' 顺序动画已在GUI线程启动")
        except Exception as e:
            self.__logger.error(f"在槽函数中为标签 '{label_name}' 启动动画时出错: {e}")
            traceback.print_exc()

    def __on_update_visibility(self, label_name: str, visible: bool) -> None:
        """(私有槽) 在GUI线程中安全地更新标签可见性。"""
        label = self.get_label(label_name)
        if not label:
            return

        try:
            if label.isVisible() != visible:
                label.setVisible(visible)
                self.__logger.debug(f"成功在GUI线程设置标签 '{label_name}' 的可见性为 {visible}")
                self.visibility_changed.emit(label_name, visible)
                if visible:
                    self.__ensure_label_visible(label)
            else:
                self.__logger.debug(f"标签 '{label_name}' 的可见性已是 {visible}，无需更改")
                if visible:
                    self.__ensure_label_visible(label)
        except Exception as e:
            self.__logger.error(f"在槽函数中设置标签 '{label_name}' 可见性时出错: {e}")
            traceback.print_exc()

    def __on_update_stylesheet(self, label_name: str, stylesheet: str) -> None:
        """(私有槽) 在GUI线程中安全地更新标签样式表。"""
        label = self.get_label(label_name)
        if not label:
            return

        try:
            if label.styleSheet() != stylesheet:
                label.setStyleSheet(stylesheet)
                self.__logger.debug(f"成功在GUI线程设置标签 '{label_name}' 的样式表")
                self.stylesheet_changed.emit(label_name, stylesheet)
            else:
                self.__logger.debug(f"标签 '{label_name}' 的样式表已是目标样式，无需更改")
        except Exception as e:
            self.__logger.error(f"在槽函数中设置标签 '{label_name}' 样式表时出错: {e}")
            traceback.print_exc()

    def __on_update_alignment(self, label_name: str, alignment: Qt.Alignment) -> None:
        """(私有槽) 在GUI线程中安全地更新标签对齐方式。"""
        label = self.get_label(label_name)
        if not label:
            return

        try:
            if label.alignment() != alignment:
                label.setAlignment(alignment)
                self.__logger.debug(f"成功在GUI线程设置标签 '{label_name}' 的对齐方式")
                self.alignment_changed.emit(label_name, alignment)
            else:
                self.__logger.debug(f"标签 '{label_name}' 的对齐方式已是目标方式，无需更改")
        except Exception as e:
            self.__logger.error(f"在槽函数中设置标签 '{label_name}' 对齐方式时出错: {e}")
            traceback.print_exc()

    # =================================================================================
    # 单例模式管理方法
    # =================================================================================

    @classmethod
    def get_instance(cls, labels: List[QLabel] = None, parent: Optional[QObject] = None) -> 'QLabelManager':
        """
        获取QLabelManager的单例实例

        这是推荐的获取实例的方法，相比直接调用构造函数更加明确。

        Args:
            labels (List[QLabel], optional): QLabel控件列表（首次创建时需要）
            parent (Optional[QObject]): 父对象（可选）

        Returns:
            QLabelManager: 单例实例

        使用示例:
            ```python
            # 首次创建，需要传入控件列表
            label1 = QLabel("标签1")
            label1.setObjectName("label1")
            label2 = QLabel("标签2")
            label2.setObjectName("label2")
            manager = QLabelManager.get_instance([label1, label2])

            # 后续获取，可以不传参数
            manager = QLabelManager.get_instance()
            ```
        """
        return cls(labels, parent=parent)

    @classmethod
    def reset_instance(cls) -> None:
        """
        重置单例实例

        清理当前实例并重置单例状态，下次调用时会创建新的实例。
        这在需要重新初始化管理器或在测试中很有用。

        使用示例:
            ```python
            # 重置单例实例
            QLabelManager.reset_instance()

            # 下次调用会创建新的实例
            new_manager = QLabelManager.get_instance([new_label1, new_label2])
            ```
        """
        with cls._lock:
            if cls._instance is not None:
                try:
                    # 尝试清理现有实例
                    if not sip.isdeleted(cls._instance):
                        cls._instance.cleanup()
                except Exception as e:
                    # 如果清理失败，记录错误但继续重置
                    try:
                        logger = ClassInstanceManager.get_instance(key="ui_logger")
                        if logger:
                            logger.warning(f"重置QLabelManager实例时清理失败: {str(e)}")
                    except:
                        pass

            # 重置所有单例相关的类变量
            cls._instance = None
            cls._initialized = False
            cls._cached_args = None
            cls._cached_kwargs = None

    @classmethod
    def is_instance_created(cls) -> bool:
        """
        检查单例实例是否已创建且有效

        Returns:
            bool: 如果实例已创建且有效返回True，否则返回False

        使用示例:
            ```python
            if QLabelManager.is_instance_created():
                manager = QLabelManager.get_instance()
            else:
                manager = QLabelManager.get_instance([label1, label2])
            ```
        """
        if cls._instance is None:
            return False

        try:
            return not sip.isdeleted(cls._instance) and cls._initialized
        except RuntimeError:
            return False

    @classmethod
    def get_instance_info(cls) -> Optional[Dict[str, Any]]:
        """
        获取单例实例的状态信息

        Returns:
            Optional[Dict[str, Any]]: 包含实例信息的字典，如果实例不存在则返回None

        返回的字典包含以下键：
            - instance: 实例对象
            - initialized: 是否已初始化
            - cached_args: 缓存的参数
            - cached_kwargs: 缓存的关键字参数
            - label_count: 管理的标签数量
            - label_names: 标签名称列表

        使用示例:
            ```python
            info = QLabelManager.get_instance_info()
            if info:
                print(f"管理的标签数量: {info['label_count']}")
                print(f"标签名称: {info['label_names']}")
            ```
        """
        if cls._instance is None:
            return None

        try:
            if sip.isdeleted(cls._instance):
                return None

            return {
                "instance": cls._instance,
                "initialized": cls._initialized,
                "cached_args": cls._cached_args,
                "cached_kwargs": cls._cached_kwargs,
                "label_count": len(cls._instance.__labels) if hasattr(cls._instance, '_QLabelManager__labels') else 0,
                "label_names": list(cls._instance.__labels.keys()) if hasattr(cls._instance, '_QLabelManager__labels') else []
            }
        except (RuntimeError, AttributeError):
            return None

    @classmethod
    def get_cached_args(cls) -> Tuple[Optional[List[QLabel]], Optional[Dict[str, Any]]]:
        """
        获取缓存的构造参数

        Returns:
            Tuple[Optional[List[QLabel]], Optional[Dict[str, Any]]]: 缓存的标签列表和关键字参数

        使用示例:
            ```python
            labels, kwargs = QLabelManager.get_cached_args()
            print(f"缓存的标签数量: {len(labels) if labels else 0}")
            print(f"缓存的参数: {kwargs}")
            ```
        """
        return (cls._cached_args, cls._cached_kwargs)

