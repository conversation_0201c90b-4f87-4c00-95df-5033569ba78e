[TOC]

# global_tools/ui_tools/compnonent 组件库文档

## 项目概述

`global_tools/ui_tools/compnonent` 是一个基于 PyQt5 的 UI 组件增强库，提供了一系列工具类和管理器，用于简化 PyQt5 应用程序中常见 UI 组件的管理和交互。该库通过扩展 PyQt5 的原生组件，添加了更丰富的功能、更便捷的接口以及更强大的事件处理机制，使开发者能够更高效地构建复杂的用户界面。

核心特性：
- 组件批量管理：同时管理多个同类型组件，通过统一接口操作
- 增强的事件处理：提供更丰富的信号和事件处理机制
- 动画效果：为组件添加平滑的动画和视觉效果
- 样式管理：自动处理组件在不同状态下的样式切换
- 线程安全：支持在多线程环境下安全操作 UI 组件

## compnonent.py

该文件实现了 `ButtonPressEffectEnhancer` 类，用于为 QPushButton 按钮添加按下时的视觉反馈效果。

### 主要组件分析

#### 类: ButtonPressEffectEnhancer

**用途**: 为容器中的所有 QPushButton 添加按下时的凹陷效果，提升用户体验。

**主要属性**:
- `__container`: 包含按钮的容器控件
- `__enhanced_buttons`: 存储已增强按钮及其原始位置的字典
- `__logger`: 日志记录器实例

**主要方法**:
- `__init__(container, parent=None)`: 初始化增强器，接收包含按钮的容器控件
- `__find_and_enhance_buttons(container)`: 递归查找容器中的所有 QPushButton 并应用效果
- `__setup_button_effect(button)`: 为单个按钮设置按下效果
- `__apply_default_style(button)`: 应用默认样式到按钮
- `__on_button_pressed(button)`: 处理按钮按下事件
- `__on_button_released(button)`: 处理按钮释放事件
- `__on_button_clicked(button)`: 处理按钮点击事件
- `__create_press_effect(button)`: 创建按钮按下效果
- `__remove_press_effect(button)`: 移除按钮按下效果
- `__cleanup_button(button)`: 清理按钮资源
- `refresh()`: 刷新按钮增强效果，查找新添加的按钮并应用效果

**工作原理**:
该类通过监听按钮的 pressed 和 released 信号，在按钮按下时应用微小的位移和样式变化，创造按钮被"按下"的视觉反馈。当按钮释放时，恢复按钮的原始状态。

## input_completer.py

该文件实现了 `InputCompleter` 和 `InputCompleterCache` 类，为 QLineEdit 提供自动完成和历史记录功能。

### 主要组件分析

#### 类: InputCompleter

**用途**: 为 QLineEdit 提供基于输入内容动态过滤的自动完成下拉列表。

**主要属性**:
- `__line_edit`: 关联的 QLineEdit 控件
- `__popup_list`: 自动完成的下拉列表
- `__completion_items`: 补全项列表
- `__completion_callback`: 异步获取补全项的回调函数
- `__thread_pool`: 用于执行异步回调的线程池
- `__focus_lost_queue`: 存储失焦时的文本内容
- `__focus_lost_callback`: 处理失焦内容的回调函数

**主要方法**:
- `__init__(line_edit, completion_items=None, parent=None)`: 初始化自动完成器
- `set_completion_callback(callback_fn)`: 设置异步获取补全项的回调函数
- `set_completion_items(items)`: 设置或更新补全项列表
- `__update_completion_list()`: 根据当前输入过滤并更新下拉列表
- `__update_popup_geometry()`: 更新下拉列表的位置和大小
- `__on_text_changed(text)`: 处理文本变化事件
- `__on_item_clicked(item)`: 处理列表项点击事件
- `__on_focus_in()`: 处理获取焦点事件
- `eventFilter(watched, event)`: 事件过滤器，处理键盘导航和焦点事件
- `set_focus_lost_callback(callback_fn)`: 设置失焦内容处理回调
- `process_focus_lost_queue()`: 处理失焦内容队列

#### 类: InputCompleterCache

**用途**: 与 InputCompleter 结合使用，提供输入历史的持久化和自动补全功能。

**主要属性**:
- `__config_path`: 配置文件路径
- `__completion_cache`: 缓存字典，存储每个 QLineEdit 的历史记录
- `__line_edits`: 存储关联的 QLineEdit 实例
- `__completers`: 存储关联的 InputCompleter 实例

**主要方法**:
- `__init__(line_edit_or_list, config_path=None)`: 初始化缓存管理器
- `__add_line_edit(line_edit)`: 添加单个 QLineEdit 到管理器
- `__setup_auto_completion(line_edit)`: 为 QLineEdit 设置自动补全功能
- `__load_completion_cache()`: 从配置文件加载补全缓存
- `__save_completion_cache()`: 将补全缓存保存到配置文件
- `__get_completion_items_for_line_edit(text, line_edit)`: 获取匹配的补全项
- `__handle_focus_lost_text_for_line_edit(text_list, line_edit)`: 处理失焦文本
- `clear_history(line_edit_key=None)`: 清除历史记录
- `get_history(line_edit_key=None)`: 获取历史记录

**工作原理**:
InputCompleter 为 QLineEdit 提供实时的自动补全功能，当用户输入文本时，会显示一个下拉列表，列出匹配的补全项。用户可以通过键盘上下箭头导航列表，按 Enter 键或鼠标点击选择一个项目。InputCompleterCache 则负责管理和持久化这些补全项的历史记录。

## qlabel.py

该文件实现了 `QLabelManager` 类，用于管理一组 QLabel 控件。

### 主要组件分析

#### 类: QLabelManager

**用途**: 管理一组 QLabel 控件，提供便捷的交互接口和信号通知。

**主要信号**:
- `text_changed(label_name, new_text)`: 标签文本变化时发出
- `visibility_changed(label_name, is_visible)`: 标签可见性变化时发出
- `stylesheet_changed(label_name, new_stylesheet)`: 标签样式表变化时发出
- `alignment_changed(label_name, new_alignment)`: 标签对齐方式变化时发出

**主要属性**:
- `__labels`: 存储标签的字典，键为标签名称，值为 QLabel 实例
- `__animation_data`: 存储标签动画数据的字典
- `FADE_DURATION_MS`: 默认动画时长
- `FADE_EASING_CURVE`: 默认缓动曲线

**主要方法**:
- `__init__(*labels, parent=None)`: 初始化管理器，接收一组 QLabel 实例
- `add_label(label)`: 添加一个新的 QLabel 到管理器
- `get_label(label_name)`: 通过标签名称获取 QLabel 实例
- `set_text(label_name, text)`: 设置指定标签的文本
- `start_fade_animation(label_name, duration_ms=None)`: 为指定标签启动淡入淡出动画
- `set_visible(label_name, visible)`: 设置指定标签的可见性
- `set_stylesheet(label_name, stylesheet)`: 设置指定标签的样式表
- `set_alignment(label_name, alignment)`: 设置指定标签的对齐方式
- `clear_text(label_name)`: 清空指定标签的文本
- `clear_all_texts()`: 清空所有标签的文本
- `get_all_label_names()`: 获取所有标签的名称列表
- `cleanup()`: 清理资源，停止所有动画

**工作原理**:
QLabelManager 通过标签的 objectName 来管理多个 QLabel 控件，提供统一的接口来修改标签的文本、可见性、样式等属性，并在属性变化时发出相应的信号。此外，它还提供了淡入淡出动画效果，增强用户体验。

## qline_edit.py

该文件实现了 `LineEditManager` 类，用于管理一组 QLineEdit 控件。

### 主要组件分析

#### 类: LineEditManager

**用途**: 批量管理多个 QLineEdit 控件，提供统一的接口进行操作。

**主要信号**:
- `text_changed(name, text)`: 文本变化时触发
- `validation_changed(name, valid)`: 验证状态变化时触发
- `focus_in(name)`: 获取焦点时触发
- `focus_out(name, text)`: 失去焦点时触发
- `return_pressed(name, text)`: 按下回车键时触发
- `enabled_changed(name, enabled)`: 启用状态变化时触发
- `style_changed(name, style)`: 样式变化时触发
- `read_only_changed(name, read_only)`: 只读状态变化时触发
- `echo_mode_changed(name, mode)`: 回显模式变化时触发

**主要属性**:
- `__line_edit_controls`: 存储每个 QLineEdit 对应的控制器

**主要方法**:
- `__init__(*line_edits, parent=None)`: 初始化管理器，接收一组 QLineEdit 实例
- `__add_line_edit(line_edit)`: 添加一个 QLineEdit 到管理器
- `get_line_edit(name)`: 获取指定名称的 QLineEdit 实例
- `has_line_edit(name)`: 检查是否存在指定名称的 QLineEdit
- `get_all_names()`: 获取所有被管理的 QLineEdit 的名称列表
- `set_input_validator(name, validator_type, **kwargs)`: 设置输入验证器
- `set_callback(name, callback)`: 设置文本变化回调函数
- `set_focus_out_callback(name, callback)`: 设置失去焦点回调函数
- `get_text(name)`: 获取输入框的文本
- `set_text(name, text)`: 设置输入框的文本
- `clear(name)`: 清空输入框
- `clear_all()`: 清空所有输入框
- `set_enabled(name, enabled)`: 设置输入框是否启用
- `set_custom_style(name, normal=None, focus=None, error=None, disabled=None)`: 设置自定义样式
- `set_read_only(name, read_only)`: 设置输入框是否只读
- `set_echo_mode(name, mode)`: 设置输入框的回显模式

**工作原理**:
LineEditManager 通过组合模式，内部使用 LineEditControl 实例管理每个 QLineEdit 控件，提供统一的接口进行批量操作。它支持输入验证、样式管理、事件处理等功能，并为每个 QLineEdit 的信号添加转发，额外传递控件名称，方便识别信号来源。

## qprogress_bar.py

该文件实现了 `QProgressBarHelper` 类，用于管理一组 QProgressBar 控件。

### 主要组件分析

#### 类: QProgressBarHelper

**用途**: 管理一组 QProgressBar 控件，提供便捷的交互接口、动画效果和信号通知。

**主要信号**:
- `value_changed(object_name, new_value)`: 进度条值变化时发出
- `range_changed(object_name, min_value, max_value)`: 进度条范围变化时发出
- `format_changed(object_name, new_format)`: 进度条格式变化时发出
- `text_visibility_changed(object_name, is_visible)`: 文本可见性变化时发出
- `orientation_changed(object_name, orientation)`: 方向变化时发出
- `animation_started(object_name)`: 值动画开始时发出
- `animation_finished(object_name)`: 值动画完成时发出

**主要属性**:
- `__progress_bars`: 存储进度条的字典，键为进度条名称，值为 QProgressBar 实例
- `__animations`: 存储动画的字典，键为进度条名称，值为 QPropertyAnimation 实例
- `__DEFAULT_ANIMATION_DURATION`: 默认动画时长
- `__DEFAULT_EASING_CURVE`: 默认缓动曲线

**主要方法**:
- `__init__(progress_bars=None, parent=None)`: 初始化管理器，接收一组 QProgressBar 实例
- `add_progress_bar(progress_bar)`: 添加一个 QProgressBar 到管理器
- `get_progress_bar(name)`: 通过名称获取 QProgressBar 实例
- `get_all_progress_bar_names()`: 获取所有进度条的名称列表
- `set_value(name, value, animate=True, duration_ms=None, easing_curve=None)`: 设置进度条的值
- `get_value(name)`: 获取进度条的当前值
- `set_range(name, minimum, maximum)`: 设置进度条的范围
- `get_range(name)`: 获取进度条的范围
- `set_format(name, format_str)`: 设置进度条的显示格式字符串
- `set_text_visible(name, visible)`: 设置进度条的文本是否可见
- `set_orientation(name, orientation)`: 设置进度条的方向
- `reset(name, animate=False, **kwargs)`: 将进度条的值重置为其最小值
- `batch_update(values, animate=True, **kwargs)`: 批量更新多个进度条的值

**工作原理**:
QProgressBarHelper 使用 QProgressBar 的 objectName 作为键值，管理多个进度条控件。它提供了设置值、范围、格式等功能，并支持平滑的动画效果。此外，它还支持多线程环境下的安全更新，通过信号槽机制确保 UI 更新在主线程中执行。

## qpush_button.py

该文件实现了 `QPushButtonManager` 类，用于管理多个 QPushButton 控件。

### 主要组件分析

#### 类: QPushButtonManager

**用途**: 管理多个 QPushButton 控件，增强其功能和交互体验。

**主要信号**:
- `button_clicked(name, checked)`: 按钮被点击时发出
- `enabled_changed(name, enabled)`: 按钮启用状态变化时发出
- `enable_button_requested(name, enabled)`: 请求更改按钮启用状态时发出
- `visibility_changed(name, visible)`: 按钮可见性变化时发出
- `text_changed(name, text)`: 按钮文本变化时发出
- `tooltip_changed(name, text)`: 按钮工具提示变化时发出
- `icon_changed(name)`: 按钮图标变化时发出
- `checkable_changed(name, checkable)`: 按钮可检查状态变化时发出
- `checked_changed(name, checked)`: 按钮选中状态变化时发出
- `all_enabled_changed(enabled, count)`: 所有按钮的启用状态被批量设置时发出

**主要属性**:
- `__buttons`: 存储按钮实例的字典
- `__opacity_effects`: 存储透明度效果的字典
- `__opacity_animations_in`: 存储鼠标进入动画的字典
- `__opacity_animations_out`: 存储鼠标离开动画的字典
- `__debounce_timers`: 存储防抖动定时器的字典
- `__original_stylesheets`: 存储原始样式表的字典
- `__disabled_stylesheets`: 存储禁用样式表的字典
- `__auto_style_enabled`: 存储自动样式状态的字典
- `__preserve_focus`: 是否在启用/禁用按钮时保持当前焦点
- `__focus_to_parent`: 是否在禁用按钮时将焦点设置到父容器

**主要方法**:
- `__init__(buttons=None, parent=None, auto_style=False)`: 初始化管理器，接收按钮列表
- `add_button(button, auto_style=False)`: 添加一个按钮到管理器
- `remove_button(button_name)`: 从管理器中移除指定名称的按钮
- `has_button(button_name)`: 检查指定名称的按钮是否存在
- `get_button(button_name)`: 获取指定名称的按钮实例
- `get_all_button_names()`: 获取所有按钮的名称列表
- `set_text(button_name, text)`: 设置指定按钮的文本
- `set_tooltip(button_name, text)`: 设置指定按钮的工具提示
- `set_icon(button_name, icon, size=None)`: 设置指定按钮的图标
- `set_enabled(button_name, enabled)`: 启用或禁用指定按钮
- `set_visible(button_name, visible)`: 设置指定按钮的可见性
- `set_checkable(button_name, checkable)`: 设置指定按钮是否可检查
- `set_checked(button_name, checked)`: 设置指定按钮的选中状态
- `enable_auto_style(button_name, enable=True)`: 为指定按钮启用或禁用自动样式功能
- `add_hover_opacity_effect(button_name, start_opacity=1.0, end_opacity=0.7, duration=150)`: 为指定按钮添加鼠标悬停透明度效果
- `enable_debounce(button_name, interval_ms=200)`: 为指定按钮启用点击防抖动功能
- `connect_clicked_signal(button_name, slot, connection_type=Qt.AutoConnection)`: 连接指定按钮的clicked信号到槽函数
- `disconnect_clicked_signal(button_name, slot=None)`: 断开指定按钮的clicked信号连接
- `set_all_enabled(enabled)`: 设置所有按钮的启用状态
- `set_disabled_style(button_name, style_sheet)`: 设置指定按钮禁用状态的样式表
- `reset_to_original_style(button_name)`: 重置指定按钮样式到初始状态
- `set_focus_to_parent_on_disable(enable)`: 设置是否在禁用按钮时将焦点转移到父容器
- `set_parent_focus_policy(policy)`: 设置父容器在接收焦点时使用的焦点策略

**工作原理**:
QPushButtonManager 通过按钮的 objectName 来管理多个 QPushButton 控件，提供统一的接口来修改按钮的文本、可见性、启用状态等属性，并在属性变化时发出相应的信号。此外，它还提供了自动样式切换、悬停透明度效果、点击防抖动等高级功能，增强用户体验。

## 依赖关系

所有组件都依赖于 PyQt5 库，特别是以下模块：
- PyQt5.QtCore: 提供核心的非GUI功能，如信号槽机制、事件处理等
- PyQt5.QtWidgets: 提供GUI控件，如按钮、标签、输入框等
- PyQt5.QtGui: 提供GUI相关的类，如图标、字体、颜色等

此外，所有组件都依赖于 `global_tools.utils` 模块中的 `ClassInstanceManager` 类，用于获取日志记录器实例。

组件之间的依赖关系：
- `InputCompleterCache` 依赖于 `InputCompleter`
- `LineEditManager` 依赖于外部的 `LineEditControl` 类

## 代码结构和设计模式

1. **组合模式**: 所有管理器类都使用组合模式，将多个同类型的控件组合在一起进行管理。

2. **代理模式**: 管理器类充当控件的代理，通过提供统一的接口来操作控件，隐藏了底层实现细节。

3. **观察者模式**: 通过 Qt 的信号槽机制实现，当控件状态变化时，发出相应的信号通知观察者。

4. **策略模式**: 一些类如 `InputCompleter` 允许通过回调函数设置不同的策略，如获取补全项的方式。

5. **单例模式**: 通过 `ClassInstanceManager` 获取日志记录器实例，确保全局只有一个日志记录器。

整体代码结构清晰，各个类职责明确，通过统一的命名规范和接口设计，使得库的使用非常直观和一致。异常处理和日志记录机制完善，提高了代码的健壮性。

## 总结

`global_tools/ui_tools/compnonent` 库提供了一系列强大的 UI 组件管理工具，通过扩展 PyQt5 的原生组件，添加了更丰富的功能和更便捷的接口。这些工具类使得开发者能够更高效地构建复杂的用户界面，提升用户体验。

主要组件包括：
- `ButtonPressEffectEnhancer`: 为按钮添加按下效果
- `InputCompleter` 和 `InputCompleterCache`: 为输入框提供自动完成和历史记录功能
- `QLabelManager`: 管理标签控件
- `LineEditManager`: 管理输入框控件
- `QProgressBarHelper`: 管理进度条控件
- `QPushButtonManager`: 管理按钮控件

这些组件都提供了丰富的功能和事件处理机制，使得 UI 开发更加高效和灵活。通过统一的接口设计和命名规范，使得库的使用非常直观和一致。完善的异常处理和日志记录机制，提高了代码的健壮性。 