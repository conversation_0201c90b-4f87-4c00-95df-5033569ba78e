import win32api
import win32process

import win32con
import win32ui
import win32gui
from PIL import Image, ImageGrab, ImageOps
import ctypes
from ctypes import wintypes, Structure, POINTER, sizeof
import random
import logging



# 预声明系统API
user32 = ctypes.WinDLL('user32')
gdi32 = ctypes.WinDLL('gdi32')

# 定义Windows结构体
class BITMAPINFOHEADER(Structure):
    _fields_ = [
        ("biSize", wintypes.DWORD),
        ("biWidth", wintypes.LONG),
        ("biHeight", wintypes.LONG),
        ("biPlanes", wintypes.WORD),
        ("biBitCount", wintypes.WORD),
        ("biCompression", wintypes.DWORD),
        ("biSizeImage", wintypes.DWORD),
        ("biXPelsPerMeter", wintypes.LONG),
        ("biYPelsPerMeter", wintypes.LONG),
        ("biClrUsed", wintypes.DWORD),
        ("biClrImportant", wintypes.DWORD)
    ]


class BITMAPINFO(Structure):
    _fields_ = [
        ("bmiHeader", BITMAPINFOHEADER),
        ("bmiColors", wintypes.DWORD * 3)
    ]


# 定义 Windows API 函数的参数类型
user32.GetDC.argtypes = [wintypes.HWND]
user32.GetDC.restype = wintypes.HDC
user32.ReleaseDC.argtypes = [wintypes.HWND, wintypes.HDC]
user32.ReleaseDC.restype = ctypes.c_int
user32.PrintWindow.argtypes = [wintypes.HWND, wintypes.HDC, wintypes.UINT]
user32.PrintWindow.restype = wintypes.BOOL
gdi32.CreateCompatibleDC.argtypes = [wintypes.HDC]
gdi32.CreateCompatibleDC.restype = wintypes.HDC
gdi32.CreateDIBSection.argtypes = [wintypes.HDC, ctypes.POINTER(BITMAPINFO), ctypes.c_uint,
                                   ctypes.POINTER(ctypes.c_void_p), ctypes.c_void_p, ctypes.c_uint]
gdi32.CreateDIBSection.restype = wintypes.HBITMAP
gdi32.SelectObject.argtypes = [wintypes.HDC, wintypes.HGDIOBJ]
gdi32.SelectObject.restype = wintypes.HGDIOBJ
gdi32.DeleteObject.argtypes = [wintypes.HGDIOBJ]
gdi32.DeleteObject.restype = ctypes.c_bool
gdi32.DeleteDC.argtypes = [wintypes.HDC]
gdi32.DeleteDC.restype = ctypes.c_bool

# 常量定义
PW_RENDERFULLCONTENT = 2
DIB_RGB_COLORS = 0
BI_RGB = 0


class WindowCaptureException(Exception):
    """窗口截图异常基类"""
    pass


class WindowCaptureError(WindowCaptureException):
    """窗口截图错误"""
    pass

class WindowHandleException(Exception):
    """窗口操作异常基类"""
    pass


class WindowNotFoundError(WindowHandleException):
    """窗口未找到异常"""
    pass


class WindowOperationError(WindowHandleException):
    """窗口操作失败异常"""
    pass

def random_point_in_obb(obb_points, edge_margin_ratio=None):
    """
    随机获取YOLO11 OBB定向标注框内的任意一点坐标（最优算法，保证均匀分布，可控制距离边缘的最小百分比）。
    算法原理：
        - 将凸四边形分割为两个三角形（0-1-2, 0-2-3），分别计算面积。
        - 按面积加权随机选择一个三角形。
        - 在三角形内用重心坐标法（Barycentric coordinates）均匀采样。
        - 若设置 edge_margin_ratio，则采样点距离三角形任意边的距离不少于该边长度的百分比。
    :param obb_points: list/tuple，格式为[x1, y1, x2, y2, x3, y3, x4, y4]，顺时针或逆时针排列
    :param edge_margin_ratio: float, 可选，采样点距离边缘的最小百分比（0~0.5），如0.1表示距离任意边不少于10%。None为不限制。
    :return: (x, y) tuple，随机点坐标，保证在四边形内
    :raises: ValueError 输入格式错误
    :raises: WindowOperationError 采样失败

    使用示例：
    ------------------
    from global_tools.window_operation.helper import random_point_in_obb
    import logging

    # 建议配置日志，便于调试
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

    obb = [562.4934692382812, 414.1869201660156, 570.8715209960938, 330.4595642089844,
           486.95953369140625, 322.0630187988281, 478.58148193359375, 405.7903747558594]

    # 基本用法：无边缘距离限制
    point = random_point_in_obb(obb)
    print('无边缘限制采样点:', point)

    # 设置采样点距离边缘最小为10%
    point2 = random_point_in_obb(obb, edge_margin_ratio=0.1)
    print('距离边缘至少10%的采样点:', point2)

    # 异常处理示例
    try:
        bad_obb = [1, 2, 3]  # 错误输入
        random_point_in_obb(bad_obb)
    except Exception as e:
        print('采样异常:', e)
    ------------------
    日志会记录采样过程、输入输出、异常，便于调试和追踪。
    """
    logger = logging.getLogger("window_operation.helper.random_point_in_obb")
    # 输入校验
    if not (isinstance(obb_points, (list, tuple)) and len(obb_points) == 8):
        logger.error(f"OBB输入长度或类型错误: {obb_points}")
        raise ValueError("OBB点坐标必须为8个数值的list或tuple")
    if edge_margin_ratio is not None:
        if not isinstance(edge_margin_ratio, (int, float)):
            logger.error(f"edge_margin_ratio类型错误: {edge_margin_ratio}")
            raise ValueError("edge_margin_ratio 必须为float或int")
        if not (0 <= edge_margin_ratio < 0.5):
            logger.error(f"edge_margin_ratio取值范围错误: {edge_margin_ratio}")
            raise ValueError("edge_margin_ratio 必须在[0, 0.5)区间内")
    try:
        # 解析四个点
        pts = [(float(obb_points[i]), float(obb_points[i+1])) for i in range(0, 8, 2)]
        # 分割为两个三角形（0-1-2, 0-2-3）
        tri1 = [pts[0], pts[1], pts[2]]
        tri2 = [pts[0], pts[2], pts[3]]
        def triangle_area(a, b, c):
            # 计算三角形面积
            return abs((a[0]*(b[1]-c[1]) + b[0]*(c[1]-a[1]) + c[0]*(a[1]-b[1])) / 2.0)
        area1 = triangle_area(*tri1)
        area2 = triangle_area(*tri2)
        total_area = area1 + area2
        if total_area == 0:
            logger.error(f"OBB四边形面积为0: {obb_points}")
            raise WindowOperationError("OBB四边形面积为0，无法采样")
        # 按面积加权随机选择三角形
        r = random.uniform(0, total_area)
        if r < area1:
            tri = tri1
            logger.debug(f"选择三角形1: {tri1}, 面积: {area1}")
        else:
            tri = tri2
            logger.debug(f"选择三角形2: {tri2}, 面积: {area2}")
        # 在三角形内用重心坐标法均匀采样，支持边缘安全距离
        def random_point_in_triangle(a, b, c, margin=None, max_retry=100):
            """
            在三角形内均匀采样，若margin不为None，则采样点距离三边的重心坐标分量均>=margin。
            :param a, b, c: 三角形三个点
            :param margin: float, [0, 1/3)，重心坐标分量下界
            :param max_retry: int, 最大重采次数
            :return: (x, y)
            """
            for _ in range(max_retry):
                s = random.random()
                t = random.random()
                if s + t > 1:
                    s = 1 - s
                    t = 1 - t
                u = 1 - s - t
                v = s
                w = t
                if margin is not None:
                    if u < margin or v < margin or w < margin:
                        continue  # 不满足边缘距离，重采
                x = a[0] * u + b[0] * v + c[0] * w
                y = a[1] * u + b[1] * v + c[1] * w
                return (x, y)
            logger.error(f"三角形采样{max_retry}次未满足边缘距离要求，margin={margin}")
            raise WindowOperationError(f"三角形采样{max_retry}次未满足边缘距离要求，margin={margin}")
        margin = edge_margin_ratio if edge_margin_ratio is not None else None
        pt = random_point_in_triangle(*tri, margin=margin)
        logger.info(f"OBB采样点: {pt} in {obb_points}, edge_margin_ratio={edge_margin_ratio}")
        return pt
    except Exception as e:
        logger.exception(f"OBB采样异常: {e}")
        raise WindowOperationError(f"OBB采样异常: {e}")

"""
使用说明：
------------------
from global_tools.window_operation.helper import random_point_in_obb

obb = [562.4934692382812, 414.1869201660156, 570.8715209960938, 330.4595642089844, 486.95953369140625, 322.0630187988281, 478.58148193359375, 405.7903747558594]
# 默认采样（无边缘距离限制）
point = random_point_in_obb(obb)
print(point)
# 设置采样点距离边缘最小为10%
point2 = random_point_in_obb(obb, edge_margin_ratio=0.1)
print(point2)
------------------
详细注释和日志，便于调试和追踪。
"""