# DXCamCore 屏幕录制功能文档

## 概述

`DXCamCore` 类的屏幕录制功能允许用户使用 DXcam 库高效地录制指定显示器或显示器区域的屏幕内容，并将其保存为视频文件。该功能提供了丰富的参数配置选项，支持多种录制场景，包括定时录制、帧数限制录制、区域录制等。

## 功能特点

- **高性能录制**：利用 DXcam 库的高性能屏幕捕获能力，实现高帧率、低延迟的屏幕录制
- **灵活的参数配置**：支持配置帧率、视频质量、编解码器等参数
- **多种录制模式**：支持定时录制、帧数限制录制、手动停止录制等模式
- **区域录制**：可以录制整个显示器或指定的屏幕区域
- **帧缓冲管理**：通过帧缓冲区优化写入性能，减少磁盘I/O压力
- **重复帧跳过**：可选择性地跳过重复帧，减小输出文件大小
- **实时预览**：可在录制过程中显示预览窗口
- **健壮的错误处理**：完善的异常处理和日志记录机制

## 方法签名

```python
def record_screen(self, 
                 output_file: str, 
                 fps: int = 30, 
                 quality: int = 95, 
                 codec: str = 'mp4v', 
                 duration: Optional[float] = None,
                 max_frames: Optional[int] = None,
                 buffer_size: int = 120,
                 skip_duplicate_frames: bool = True,
                 show_preview: bool = False) -> bool:
    """录制指定显示器的屏幕并保存为视频文件"""
```

## 参数详解

### 基本参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `output_file` | str | 必填 | 输出视频文件路径，支持.mp4, .avi等格式 |
| `fps` | int | 30 | 录制的目标帧率 |
| `quality` | int | 95 | 视频质量，范围0-100 |
| `codec` | str | 'mp4v' | 视频编解码器，其他选项如'avc1', 'xvid', 'mjpg' |

### 录制控制参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `duration` | float | None | 录制时长（秒），None表示无限制直到手动停止 |
| `max_frames` | int | None | 最大录制帧数，None表示无限制 |
| `buffer_size` | int | 120 | 帧缓冲区大小，影响内存使用和录制稳定性 |
| `skip_duplicate_frames` | bool | True | 是否跳过重复帧以节省空间 |
| `show_preview` | bool | False | 是否在录制时显示预览窗口 |

## 返回值

- **成功**：返回 `True`，表示录制成功完成
- **失败**：抛出异常，包括 `ValueError` 和 `RuntimeError`

## 异常处理

- **`ValueError`**：当参数无效时抛出，如文件路径为空、帧率小于等于0等
- **`RuntimeError`**：当录制过程中出错时抛出，如无法初始化相机、无法创建视频写入器等
- **`KeyboardInterrupt`**：用户可以通过键盘中断（Ctrl+C）停止录制

## 使用示例

### 基本录制

```python
from global_tools.window_operation.dxcam_core import DXCamCore

# 创建一个捕获主显示器的DXCamCore实例
capture = DXCamCore(monitor_index=0)

# 录制10秒的屏幕，保存为output.mp4
capture.record_screen(
    output_file="output.mp4",
    fps=30,
    duration=10
)
```

### 高质量录制

```python
# 高质量录制
capture.record_screen(
    output_file="high_quality.mp4",
    fps=60,
    quality=100,
    codec='avc1'  # H.264编码
)
```

### 区域录制

```python
# 创建一个捕获特定区域的DXCamCore实例
region_capture = DXCamCore(
    monitor_index=0,
    region=(100, 100, 500, 500)  # 左上角(100,100)到右下角(500,500)的区域
)

# 录制区域
region_capture.record_screen(
    output_file="region_recording.mp4",
    fps=30,
    duration=5
)
```

### 带预览的手动停止录制

```python
# 带预览的录制，直到用户按ESC键停止
try:
    capture.record_screen(
        output_file="manual_stop.mp4",
        fps=30,
        show_preview=True  # 显示预览窗口
    )
except KeyboardInterrupt:
    # 用户按Ctrl+C停止录制
    print("录制已停止")
```

## 性能优化建议

1. **调整帧率**：根据实际需求设置适当的帧率，过高的帧率可能导致性能问题
2. **缓冲区大小**：对于高分辨率录制，可能需要增加缓冲区大小
3. **编解码器选择**：不同的编解码器有不同的性能和兼容性特点
   - `mp4v`：兼容性好，但压缩率较低
   - `avc1`：H.264编码，压缩率高，但可能需要额外安装编解码器
   - `xvid`：较好的兼容性和压缩率平衡
   - `mjpg`：Motion JPEG，每帧独立压缩，适合需要逐帧编辑的场景
4. **跳过重复帧**：对于变化不大的屏幕内容，启用跳过重复帧可以显著减小文件大小

## 常见问题解答

### Q: 录制的视频没有声音，如何添加音频？
A: 当前版本仅支持视频录制，不包含音频。如需录制音频，需要使用其他工具如PyAudio捕获音频，然后使用FFmpeg等工具合并。

### Q: 录制性能不佳，帧率低于预期，如何优化？
A: 尝试以下方法：
   - 降低目标帧率
   - 减小录制区域
   - 关闭预览窗口
   - 使用更高效的编解码器
   - 在更高性能的硬件上运行

### Q: 如何在录制过程中暂停和恢复？
A: 当前版本不直接支持暂停/恢复功能。如需此功能，可以分段录制然后使用视频编辑工具合并。

### Q: 支持哪些视频格式？
A: 支持的格式取决于OpenCV的VideoWriter支持的格式，常见的包括：
   - .mp4（推荐，兼容性最好）
   - .avi
   - .mov
   - .mkv（部分编解码器支持）

## 技术实现细节

### 录制流程

1. **初始化阶段**：
   - 验证参数有效性
   - 确保相机已初始化
   - 创建OpenCV VideoWriter对象

2. **录制阶段**：
   - 启动DXcam录制模式
   - 循环获取最新帧
   - 将帧添加到缓冲区
   - 批量写入视频文件
   - 监控录制状态（时长、帧数）

3. **结束阶段**：
   - 写入剩余缓冲帧
   - 停止DXcam录制
   - 关闭视频写入器和预览窗口
   - 计算并输出录制统计信息

### 性能优化技术

- **帧缓冲**：使用帧缓冲区减少磁盘I/O次数
- **重复帧检测**：通过比较连续帧避免存储重复内容
- **动态睡眠**：根据处理时间动态调整线程睡眠时间，确保稳定帧率
- **异常恢复**：当DXcam的get_latest_frame()失败时，自动切换到capture()方法确保录制连续性

## 限制和注意事项

1. **资源占用**：高分辨率、高帧率录制会占用大量CPU和内存资源
2. **文件大小**：长时间录制可能产生非常大的文件，注意磁盘空间
3. **编解码器依赖**：某些编解码器可能需要额外安装系统级依赖
4. **多显示器支持**：虽然支持多显示器，但录制多个显示器需要创建多个DXCamCore实例
5. **录制区域限制**：区域必须是矩形，且坐标必须在显示器范围内

## 未来改进方向

1. **音频支持**：添加系统音频捕获和同步功能
2. **硬件加速**：利用GPU加速视频编码
3. **暂停/恢复功能**：支持录制过程中的暂停和恢复
4. **实时压缩选项**：提供更多视频压缩选项和预设
5. **多显示器同时录制**：支持在单个实例中同时录制多个显示器 