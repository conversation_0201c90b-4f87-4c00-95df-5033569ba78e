"""
PostgreSQL数据库客户端模块

提供与PostgreSQL数据库交互的主要接口，支持：
1. 连接池管理
2. 表操作（创建、修改、删除）
3. 数据操作（插入、更新、查询、删除）
4. 事务管理
5. 错误处理和日志记录

此模块通过面向对象的方式封装了PostgreSQL的操作，使数据库交互更加简单、安全和高效。

本文件所有日志输出均统一使用 global_tools.utils.Logger 实例 logger，颜色参数使用 global_tools.utils.Colors，禁止其他日志实现。
"""

import logging
import time
import traceback
from typing import Any, Dict, List, Optional, Tuple, Union, Set

import psycopg2
from psycopg2 import pool

from global_tools.utils import Logger, Colors,LogLevel

from .connection_pool import get_connection_pool
from .logger import get_logger, configure_logger
from .config import DBConfig
from .exceptions import ConnectionError, ExecutionError, ConfigurationError, ConditionParseError
from .sql_condition_parser import parse_condition
from .db_type_converter import convert_to_pg_type, adapt_value_for_db
from .data_operations_1 import DataOperations1
from .data_operations_2 import DataOperations2
from .data_operations_fetch import DataOperationsFetch
from .db_operations import DBOperations

# ========== 日志统一方案：全局 Logger 实例，所有日志输出均使用 global_tools.utils.Logger，颜色参数用 Colors ===========
logger = Logger()
logger.set_instance_level(LogLevel.OFF)

class PostgreSQLClient:
    """
    PostgreSQL数据库客户端类，支持连接池、表操作和数据操作
    
    该类通过聚合多个功能模块，提供了完整的PostgreSQL数据库操作功能。
    使用JSON格式描述数据，支持复杂的查询条件和数据操作。
    """
    
    # 静态字典，用于存储已实例化的实例，以(host, database, user)为key
    _instances = {}
    logger=logger
    @classmethod
    def get_instance(cls, database: str, host: str = None, user: str = None) -> Optional['PostgreSQLClient']:
        """
        获取指定数据库的PostgreSQLClient实例（单例模式）。

        功能：
            - 根据数据库名、主机名、用户名获取已存在的PostgreSQLClient实例。
            - 如果未找到，则返回None。
            - 支持只根据数据库名模糊查找，也支持三元组精确查找。

        Args:
            database (str): 数据库名称。
            host (str, optional): 主机名。默认None。
            user (str, optional): 用户名。默认None。

        Returns:
            Optional[PostgreSQLClient]: 已存在的PostgreSQLClient实例，若不存在则返回None。

        示例::
            # 精确查找
            client = PostgreSQLClient.get_instance(database="testdb", host="localhost", user="postgres")
            # 只根据数据库名查找
            client = PostgreSQLClient.get_instance(database="testdb")
        """
        # 如果只提供了数据库名称，尝试查找任何匹配的实例
        if host is None and user is None:
            for key in cls._instances:
                if key[1] == database:
                    return cls._instances[key]
            return None
            
        # 如果提供了主机名和用户名，查找精确匹配的实例
        key = (host, database, user)
        return cls._instances.get(key)
        
    def __new__(cls, host: str, database: str, user: str, password: str, port: int = 5432, **kwargs):
        """
        创建新实例或返回已存在的实例
        
        Args:
            host: 数据库主机
            database: 数据库名称
            user: 用户名
            password: 密码
            port: 端口号，默认5432
            **kwargs: 其他连接参数
        
        Returns:
            PostgreSQLClient实例
        """
        # 生成实例键
        key = (host, database, user)
        instance = cls._instances.get(key)
        # ========== 关键修复：参数不一致时强制重新创建实例 ==========
        if instance:
            # 检查关键参数是否一致
            min_connections = kwargs.get('min_connections', 1)
            max_connections = kwargs.get('max_connections', 10)
            if (
                getattr(instance, 'password', None) != password or
                getattr(instance, 'port', None) != port or
                getattr(instance, 'min_connections', 1) != min_connections or
                getattr(instance, 'max_connections', 10) != max_connections
            ):
                # 删除旧实例，强制重新创建
                del cls._instances[key]
                instance = None
        if not instance:
            instance = super().__new__(cls)
        return instance
        
    def __init__(self, host: str, database: str, user: str, password: str, port: int = 5432, 
                 min_connections: int = 1, max_connections: int = 10, 
                 log_level: int = logging.INFO, config_file: str = None, **kwargs):
        """
        初始化PostgreSQLClient实例。

        功能：
            - 初始化数据库连接池，保存连接参数。
            - 支持通过配置文件、参数、kwargs多种方式配置。
            - 单例模式，避免重复初始化。

        Args:
            host (str): 数据库主机。
            database (str): 数据库名称。
            user (str): 用户名。
            password (str): 密码。
            port (int, optional): 端口号，默认5432。
            min_connections (int, optional): 连接池最小连接数，默认1。
            max_connections (int, optional): 连接池最大连接数，默认10。
            log_level (int, optional): 日志级别，默认logging.INFO。
            config_file (str, optional): 配置文件路径。
            **kwargs: 其他连接参数。

        Returns:
            None

        示例::
            client = PostgreSQLClient(
                host="localhost", database="testdb", user="postgres", password="123456"
            )
        """
        # 生成实例键
        key = (host, database, user)
        
        # 如果实例已存在，避免重复初始化
        if key in self.__class__._instances:
            return
        
        # 初始化参数
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.port = port
        # ========== 关键修复：优先从kwargs取连接池参数，确保**kwargs和直接传参都能正确赋值 ==========
        self.min_connections = int(kwargs.pop('min_connections', min_connections))
        self.max_connections = int(kwargs.pop('max_connections', max_connections))
        self.application_name = kwargs.get('application_name', f'PyPgClient-{database}')
        
        # 加载配置
        self.config = DBConfig(config_file) if config_file else DBConfig()
            
        # 合并其他连接参数，优先级：直接传参 > kwargs > config > 默认
        self.connection_params = {
            'host': host,
            'port': port,
            'database': database,
            'user': user,
            'password': password,
            'min_connections': self.min_connections,
            'max_connections': self.max_connections,
            'application_name': self.application_name,
            'client_encoding': 'utf8',  # 强制使用 UTF-8 编码
            'options': '-c client_encoding=utf8'  # 额外的编码设置
        }
        # 合并kwargs（如有重复，kwargs优先）
        self.connection_params.update(kwargs)
        # 再次覆盖，确保优先级
        self.connection_params['min_connections'] = self.min_connections
        self.connection_params['max_connections'] = self.max_connections
        self.connection_params['application_name'] = self.application_name

        # 初始化连接和事务状态（在创建连接池之前）
        self.conn = None
        self.in_transaction = False
        
        # ========== 修复：在创建连接池之前先检查并创建数据库 ==========
        try:
            # 先尝试检查并创建数据库（如果不存在）
            self._create_database_if_not_exists(database)
        except Exception as db_create_e:
            logger.warning(f"预检查数据库时出现问题，继续尝试连接: {str(db_create_e)}", color=Colors.WARNING)

        # 创建连接池
        try:
            self.pool = get_connection_pool(**self.connection_params)
            # 同步属性，确保与实际连接池一致
            self.min_connections = getattr(self.pool, 'min_connections', self.min_connections)
            self.max_connections = getattr(self.pool, 'max_connections', self.max_connections)
            logger.info(f"成功初始化数据库连接: {database}@{host}:{port}", color=Colors.INFO)
        except Exception as e:
            # 检查是否是数据库不存在的错误
            # UTF-8 解码错误通常表示 PostgreSQL 返回了包含非 UTF-8 字符的错误消息
            # 这在数据库不存在时经常发生
            error_str = str(e).lower()

            # 检查异常链中是否包含 UnicodeDecodeError
            def has_unicode_decode_error(exception):
                """递归检查异常链中是否包含 UnicodeDecodeError"""
                if isinstance(exception, UnicodeDecodeError):
                    return True
                if hasattr(exception, '__cause__') and exception.__cause__:
                    return has_unicode_decode_error(exception.__cause__)
                if hasattr(exception, '__context__') and exception.__context__:
                    return has_unicode_decode_error(exception.__context__)
                return False

            is_db_not_exist_error = (
                'utf-8' in error_str and 'decode' in error_str or  # UTF-8 解码错误
                'database' in error_str and ('does not exist' in error_str or 'not exist' in error_str) or  # 明确的数据库不存在错误
                isinstance(e, UnicodeDecodeError) or  # 直接的 Unicode 解码错误
                has_unicode_decode_error(e)  # 异常链中包含 Unicode 解码错误
            )

            if is_db_not_exist_error:
                logger.warning(f"数据库 '{database}' 可能不存在（检测到编码错误或数据库不存在错误），尝试自动创建...", color=Colors.WARNING)
                try:
                    self._create_database_if_not_exists(database)
                    # 重新尝试创建连接池
                    self.pool = get_connection_pool(**self.connection_params)
                    self.min_connections = getattr(self.pool, 'min_connections', self.min_connections)
                    self.max_connections = getattr(self.pool, 'max_connections', self.max_connections)
                    logger.info(f"成功创建数据库并初始化连接: {database}@{host}:{port}", color=Colors.INFO)
                except Exception as create_e:
                    logger.error(f"自动创建数据库失败: {str(create_e)}", color=Colors.ERROR)
                    logger.error(f"原始连接错误: {str(e)}", color=Colors.ERROR)
                    traceback.print_exc()
                    raise e  # 抛出原始错误
            else:
                logger.error(f"初始化数据库连接池失败: {str(e)}", color=Colors.ERROR)
                traceback.print_exc()  # 打印详细异常堆栈
                raise e
        
        # 将实例保存到静态字典
        self.__class__._instances[key] = self
        
        # 初始化完成
        logger.debug(f"PostgreSQLClient实例初始化完成: {database}@{host}:{port}", color=Colors.DEBUG)
        
    def __del__(self):
        """析构函数，确保释放资源"""
        self.close()
        
    def __enter__(self):
        """上下文管理器入口，开始事务"""
        self.begin()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，提交或回滚事务"""
        if exc_type is not None:
            # 发生异常，回滚事务
            self.rollback()
            return False  # 重新抛出异常
        else:
            # 没有异常，提交事务
            self.commit()
            return True
            
    def _ensure_connection(self):
        """
        确保数据库连接有效，如果连接不存在或已关闭则从连接池获取新连接。
        内部方法。
        """
        try:
            if self.conn is None or self.conn.closed:
                logger.debug("数据库连接不存在或已关闭，从连接池获取新连接...", color=Colors.DEBUG)
                self.conn = self._get_connection()
        except Exception as e:  # 捕获更广泛的异常，以防连接状态检查出错
            logger.error(f"检查数据库连接状态失败: {e}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            
            # 如果当前连接存在且有问题，先尝试归还给连接池（即使可能会失败）
            if self.conn is not None:
                try:
                    self.pool.return_connection(
                        self.conn, close=True)  # close=True 表示关闭这个有问题的连接
                except Exception:
                    traceback.print_exc()  # 打印详细异常堆栈
                    pass

            # 尝试获取新连接
            self.conn = self._get_connection()
            
    def _get_connection(self):
        """
        从连接池获取连接

        Returns:
            数据库连接对象

        Raises:
            ConnectionError: 如果获取连接失败
        """
        try:
            # 如果已有连接且有效，直接返回
            if self.conn and not self.conn.closed:
                return self.conn

            # 检查连接池是否存在，如果不存在则重新创建
            if not hasattr(self, 'pool') or self.pool is None:
                logger.warning(f"连接池不存在，重新创建连接池: {self.database}@{self.host}:{self.port}")
                self.pool = get_connection_pool(**self.connection_params)

            # 从连接池获取新连接
            self.conn = self.pool.get_connection()
            return self.conn

        except Exception as e:
            logger.error(f"获取数据库连接失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            raise ConnectionError(f"获取数据库连接失败: {str(e)}") from e
            
    def _release_connection(self, close: bool = False):
        """
        归还连接到连接池
        
        Args:
            close: 是否关闭连接
            
        Raises:
            ConnectionError: 如果归还连接失败
        """
        try:
            if self.conn:
                # 如果在事务中且要关闭连接，先回滚事务
                if close and self.in_transaction:
                    try:
                        self.conn.rollback()
                        self.in_transaction = False
                    except Exception as e:
                        logger.warning(f"回滚事务失败: {str(e)}", color=Colors.WARNING)
                        traceback.print_exc()  # 打印详细异常堆栈
                        
                # 归还连接到连接池
                self.pool.return_connection(self.conn, close)
                self.conn = None
                
        except Exception as e:
            logger.error(f"归还数据库连接失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            raise ConnectionError(f"归还数据库连接失败: {str(e)}") from e
            
    def execute_query(self, sql: str, params: Any = None, fetch: bool = True) -> Optional[List[Tuple]]:
        """
        执行SQL查询或数据操作。

        功能：
            - 支持SELECT、INSERT、UPDATE、DELETE等SQL语句的执行。
            - 自动区分是否需要fetch结果。
            - 自动处理事务提交/回滚。
            - 统一异常处理和日志记录。

        Args:
            sql (str): SQL语句。
            params (Any, optional): 查询参数（元组、列表或字典）。
            fetch (bool, optional): 是否获取结果，默认True。

        Returns:
            Optional[List[Tuple]]: 
            - 当fetch=True且是查询语句时，返回查询结果列表。每一行是一个元组，元组中的元素对应查询列的值。
            - 当fetch=False或是非查询语句（如INSERT/UPDATE/DELETE）时，返回None。
              在这种情况下，如果需要知道影响的行数，可以使用cursor.rowcount属性，但此方法不直接返回。

        Raises:
            ExecutionError: 执行失败时抛出，包含原始错误信息、SQL语句和参数。

        示例::
            # 查询
            rows = client.execute_query("SELECT * FROM users WHERE id = %s", (1,))
            if rows:
                for row in rows:
                    print(f"ID: {row[0]}, Name: {row[1]}")
                
            # 插入（不返回结果）
            client.execute_query("INSERT INTO users (name) VALUES (%s)", ("张三",), fetch=False)
            
            # 使用字典参数（仅支持某些数据库驱动）
            client.execute_query("SELECT * FROM users WHERE id = %(id)s", {"id": 1})
        """
        conn = None
        cursor = None
        start_time = time.time()
        
        # 检查是否是不需要获取结果的语句
        sql_command = sql.strip().upper().rstrip(';') # 移除末尾分号以便正确匹配
        is_non_fetch_sql = (
            sql_command.startswith(('CREATE', 'ALTER', 'DROP', 'TRUNCATE', 'INSERT', 'UPDATE', 'DELETE', 'COMMENT ON', 'VACUUM')) or
            sql_command in ('COMMIT', 'ROLLBACK', 'BEGIN', 'START TRANSACTION') or
            'CREATE' in sql_command or 'ALTER' in sql_command or 'DROP' in sql_command # 保持原有逻辑，尽管CREATE/ALTER/DROP已在前缀中覆盖
        )
        
        if is_non_fetch_sql:
            fetch = False  # 对于非查询语句，不获取结果
        
        try:
            # 获取连接
            conn = self._get_connection()
            
            # 创建游标
            cursor = conn.cursor()
            
            # 记录查询
            logger.debug(f"执行SQL: {sql}", color=Colors.DEBUG)
            if params:
                logger.debug(f"参数: {params}", color=Colors.DEBUG)
                
            # 执行查询
            cursor.execute(sql, params)
            
            # 如果需要获取结果且不是非查询语句
            if fetch and not is_non_fetch_sql:
                results = cursor.fetchall()
                logger.debug(f"查询返回 {len(results)} 行结果", color=Colors.DEBUG)
                return results
            else:
                affected_rows = cursor.rowcount
                logger.debug(f"查询影响了 {affected_rows} 行", color=Colors.DEBUG)
                
                # 如果不在事务中，自动提交
                if not self.in_transaction:
                    conn.commit()
                    
                return None
                
        except Exception as e:
            # 如果不在事务中，自动回滚
            if conn and not self.in_transaction:
                try:
                    conn.rollback()
                except Exception:
                    traceback.print_exc()  # 打印详细异常堆栈
                    pass
                    
            logger.error(f"执行SQL查询失败: {str(e)}", color=Colors.ERROR)
            logger.error(f"SQL: {sql}", color=Colors.ERROR)
            if params:
                logger.error(f"参数: {params}", color=Colors.ERROR)
            
            traceback.print_exc()  # 打印详细异常堆栈
            raise ExecutionError(f"执行SQL查询失败: {str(e)}", sql=sql, params=params) from e
            
        finally:
            # 关闭游标
            if cursor:
                cursor.close()
                
            # 记录执行时间
            elapsed = time.time() - start_time
            logger.debug(f"SQL执行耗时: {elapsed:.6f}秒", color=Colors.DEBUG)
            
            # 如果不在事务中，归还连接
            if not self.in_transaction and conn:
                self._release_connection()
                
    def begin(self):
        """
        开始数据库事务。

        功能：
            - 显式开启事务，后续操作需手动commit/rollback。
            - 支持上下文管理器自动调用。

        Raises:
            ExecutionError: 开始事务失败时抛出。

        示例::
            client.begin()
            try:
                client.execute_query(...)
                client.commit()
            except:
                client.rollback()
        """
        if self.in_transaction:
            logger.warning("已经在事务中，忽略begin()调用")
            return
            
        try:
            conn = self._get_connection()
            self.in_transaction = True
            logger.debug("开始事务")
        except Exception as e:
            logger.error(f"开始事务失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            raise ExecutionError(f"开始事务失败: {str(e)}") from e
            
    def commit(self):
        """
        提交当前事务。

        功能：
            - 显式提交事务。
            - 提交后自动归还连接。

        Raises:
            ExecutionError: 提交失败时抛出。

        示例::
            client.begin()
            client.execute_query(...)
            client.commit()
        """
        if not self.in_transaction:
            logger.warning("不在事务中，忽略commit()调用")
            return
            
        try:
            conn = self.conn
            if conn and not conn.closed:
                conn.commit()
                logger.debug("事务已提交")
            self.in_transaction = False
            
            # 提交后归还连接
            self._release_connection()
            
        except Exception as e:
            logger.error(f"提交事务失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            raise ExecutionError(f"提交事务失败: {str(e)}") from e
            
    def rollback(self):
        """
        回滚当前事务。

        功能：
            - 显式回滚事务。
            - 回滚后自动归还连接。

        Raises:
            ExecutionError: 回滚失败时抛出。

        示例::
            client.begin()
            client.execute_query(...)
            client.rollback()
        """
        if not self.in_transaction:
            logger.warning("不在事务中，忽略rollback()调用")
            return
            
        try:
            conn = self.conn
            if conn and not conn.closed:
                conn.rollback()
                logger.debug("事务已回滚")
            self.in_transaction = False
            
            # 回滚后归还连接
            self._release_connection()
            
        except Exception as e:
            logger.error(f"回滚事务失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
            raise ExecutionError(f"回滚事务失败: {str(e)}") from e
            
    def close(self):
        """
        关闭数据库连接。

        功能：
            - 归还当前连接到连接池。
            - 不关闭连接池，保持连接池可用于其他客户端实例。
            - 建议在程序结束时调用。

        Returns:
            None

        示例::
            client.close()
        """
        try:
            # 归还当前连接
            if self.conn:
                try:
                    self._release_connection(close=True)
                except Exception as e:
                    logger.warning(f"关闭时归还连接失败: {str(e)}", color=Colors.WARNING)
                    traceback.print_exc()  # 打印详细异常堆栈
                    
            # 注意：不再关闭连接池！连接池由全局管理器管理，应该保持可用
            # 这样其他PostgreSQLClient实例仍然可以使用同一个连接池
            if hasattr(self, 'pool') and self.pool:
                logger.debug(f"保持连接池开放以供其他客户端使用: {self.database}@{self.host}:{self.port}")
                # 只清除当前客户端对连接池的引用，不销毁连接池本身
                self.pool = None
                    
        except Exception as e:
            logger.error(f"关闭客户端失败: {str(e)}", color=Colors.ERROR)
            traceback.print_exc()  # 打印详细异常堆栈
        
    def _table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名，可以是schema.table格式
            
        Returns:
            表是否存在
        """
        # 处理可能包含schema的表名
        schema_name = 'public'
        pure_table_name = table_name
        
        if '.' in table_name:
            parts = table_name.split('.', 1)
            schema_name = parts[0]
            pure_table_name = parts[1]
            
        sql = """
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s
        )
        """
        result = self.execute_query(sql, (schema_name, pure_table_name))
        exists = result[0][0] if result else False
        if not exists:
            # ========== 辅助调试：打印当前schema下所有表名，辅助定位大小写问题 ==========
            try:
                all_tables = self.execute_query(
                    f"SELECT table_name FROM information_schema.tables WHERE table_schema = '{schema_name}'", fetch=True)
                logger.warning(f"[DEBUG] 当前schema下所有表名: {[row[0] for row in all_tables]}", color=Colors.WARNING)
            except Exception as e:
                logger.warning(f"[DEBUG] 查询所有表名失败: {e}", color=Colors.WARNING)
                traceback.print_exc()  # 打印详细异常堆栈
        return exists
        
    def _get_timestamp(self) -> float:
        """
        获取当前时间戳

        Returns:
            时间戳浮点数
        """
        return time.time()

    def _create_database_if_not_exists(self, database_name: str):
        """
        检查数据库是否存在，如果不存在则创建

        Args:
            database_name: 要创建的数据库名称

        Raises:
            ConnectionError: 如果连接到默认数据库失败
            ExecutionError: 如果创建数据库失败
        """
        import psycopg2

        # 创建连接到默认数据库 postgres 的参数
        default_params = self.connection_params.copy()
        default_params['database'] = 'postgres'  # 连接到默认数据库

        # 移除连接池相关参数
        default_params.pop('min_connections', None)
        default_params.pop('max_connections', None)
        default_params.pop('application_name', None)

        conn = None
        cursor = None

        try:
            logger.info(f"连接到默认数据库 postgres 检查数据库 '{database_name}' 是否存在...", color=Colors.INFO)

            # ========== 修复UTF-8编码问题：设置环境变量 ==========
            import os
            old_pgclientencoding = os.environ.get('PGCLIENTENCODING')
            os.environ['PGCLIENTENCODING'] = 'UTF8'

            try:
                # 连接到默认数据库
                conn = psycopg2.connect(**default_params)
                cursor = conn.cursor()
            finally:
                # 恢复环境变量
                if old_pgclientencoding is not None:
                    os.environ['PGCLIENTENCODING'] = old_pgclientencoding
                elif 'PGCLIENTENCODING' in os.environ:
                    del os.environ['PGCLIENTENCODING']

            # 检查数据库是否存在
            check_sql = "SELECT 1 FROM pg_database WHERE datname = %s"
            cursor.execute(check_sql, (database_name,))
            exists = cursor.fetchone() is not None

            if exists:
                logger.info(f"数据库 '{database_name}' 已存在", color=Colors.INFO)
                return

            # 创建数据库需要重新连接并设置 autocommit
            logger.info(f"数据库 '{database_name}' 不存在，正在创建...", color=Colors.INFO)

            # 关闭当前连接
            cursor.close()
            conn.close()

            # 重新连接并设置 autocommit
            conn = psycopg2.connect(**default_params)
            conn.autocommit = True  # 设置自动提交，CREATE DATABASE 需要在自动提交模式下执行
            cursor = conn.cursor()

            # 注意：CREATE DATABASE 语句不能使用参数化查询，需要直接拼接
            # 为了安全，我们需要验证数据库名称只包含安全字符
            if not database_name.replace('_', '').replace('-', '').isalnum():
                raise ValueError(f"数据库名称 '{database_name}' 包含不安全字符")

            create_sql = f'CREATE DATABASE "{database_name}" WITH ENCODING = \'UTF8\' LC_COLLATE = \'C\' LC_CTYPE = \'C\' TEMPLATE = template0'
            cursor.execute(create_sql)

            logger.info(f"成功创建数据库 '{database_name}'", color=Colors.SUCCESS)

        except psycopg2.Error as e:
            error_msg = f"创建数据库 '{database_name}' 失败: {str(e)}"
            logger.error(error_msg, color=Colors.ERROR)
            raise ExecutionError(error_msg) from e
        except Exception as e:
            error_msg = f"连接默认数据库或创建数据库时发生错误: {str(e)}"
            logger.error(error_msg, color=Colors.ERROR)
            raise ConnectionError(error_msg) from e
        finally:
            # 清理资源
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def _parse_condition_json(self, condition_str):
        """
        解析条件字符串，生成 WHERE 子句。
        参数:
            condition_str (str): 条件字符串，例如 "name == '张三' and age > 20"。
        返回:
            str: WHERE 子句的字符串，例如 "(name = '张三' AND age > 20)"
        """
        try:
            return parse_condition(condition_str)
        except ValueError as e:
            # ===================== 关键修复 =====================
            # 直接抛出 ValueError，不再包装为 ConditionParseError，保证 fetch_data 能捕获到 ValueError
            raise ValueError(str(e))
    
    def _adapt_value_for_db(self, value):
        """
        将Python数据类型转换为PostgreSQL可接受的数据类型。

        参数:
            value: 任何Python数据类型

        返回:
            转换后的值，适合PostgreSQL使用
        """
        return adapt_value_for_db(value)
    
    # 数据操作方法 - 从各个模块中整合
    
    # 从 DataOperations1 整合的方法
    def filter_insert_data_by_schema(self, insert_data, table_schema):
        """
        检查插入数据的JSON字段是否存在于表结构定义中，自动过滤无效字段。

        功能：
            - 支持单条或批量插入数据的字段校验。
            - 返回过滤后的数据、被移除字段、处理信息。

        Args:
            insert_data (dict or list): 要插入的数据（单条或多条）。
            table_schema (dict): 表结构的JSON描述。

        Returns:
            dict: 过滤结果，包含filtered_data、removed_fields、success、message等字段。

        示例::
            schema = {"columns": {"name": {"type": "VARCHAR"}, "age": {"type": "INT"}}}
            data = {"name": "张三", "age": 20, "foo": 1}
            result = client.filter_insert_data_by_schema(data, schema)
        """
        return DataOperations1.filter_insert_data_by_schema(self, insert_data, table_schema)
    
    def insert_data(self, table_name, data_json) -> Dict[str, Union[bool, int, str, float, List]]:
        """
        向表中插入数据，数据以JSON格式描述。

        功能：
            - 支持单条、多条、带rows字段的多种JSON格式插入。
            - 自动校验表结构、字段合法性。
            - 自动处理事务、异常、日志。

        Args:
            table_name (str): 目标表名。
            data_json (dict, list, or str): 插入数据，支持字典、列表或JSON字符串。
                - 字典格式: {"name": "张三", "age": 20} (单行数据)
                - 列表格式: [{"name": "张三", "age": 20}, {"name": "李四", "age": 30}] (多行数据)
                - 带rows字段: {"rows": [{"name": "张三", "age": 20}, {"name": "李四", "age": 30}]}
                - JSON字符串: 上述任意格式的JSON字符串

        Returns:
            Dict[str, Union[bool, int, str, float, List]]: 插入操作的结果字典，包含以下字段:
                成功时:
                {
                    "success": bool,  # 操作是否成功，总是True
                    "message": str,  # 成功消息
                    "inserted_count": int,  # 实际插入的行数
                    "expected_count": int,  # 预期插入的行数
                    "transaction_status": str,  # 事务状态："committed", "in_transaction"等
                    "table_name": str,  # 目标表名
                    "timestamp": float,  # 操作时间戳
                    "execution_time_ms": int  # 执行时间(毫秒)
                }
                
                失败时:
                {
                    "success": bool,  # 操作是否成功，总是False
                    "error": str,  # 错误信息
                    "error_type": str,  # 错误类型，如"DatabaseError", "UniqueViolation"等
                    "error_code": str,  # 数据库错误代码（如果有）
                    "transaction_status": str,  # 事务状态，如"rolled_back_due_to_db_error"
                    "table_name": str,  # 目标表名
                    "inserted_count": int,  # 通常为0
                    "expected_count": int,  # 预期插入的行数
                    "timestamp": float,  # 操作时间戳
                    "execution_time_ms": int  # 执行时间(毫秒)
                }

        Raises:
            ExecutionError: 插入操作失败时可能抛出，包含详细错误信息、错误代码和原始SQL。

        示例::
            # 单行插入
            data = {"name": "张三", "age": 20}
            result = client.insert_data("users", data)
            if result["success"]:
                print(f"成功插入 {result['inserted_count']} 条记录")
            else:
                print(f"插入失败: {result.get('error')}")
            
            # 多行插入
            data = [
                {"name": "张三", "age": 20},
                {"name": "李四", "age": 30}
            ]
            result = client.insert_data("users", data)
        """
        return DataOperations1.insert_data(self, table_name, data_json)
    
    # 从 DataOperations2 整合的方法
    def update_data(self, table_name=None, condition_str=None, data_json=None, batch_updates=None) -> Union[Dict[str, Any], Dict[str, Union[bool, int, List[Dict[str, Any]], str, float]]]:
        """
        更新表中数据，支持单条和批量更新。

        功能：
            - 支持条件字符串描述的单条更新。
            - 支持批量更新（batch_updates参数）。
            - 自动校验表结构、字段合法性。
            - 自动处理事务、异常、日志。

        Args:
            table_name (str): 表名。
            condition_str (str, optional): 更新条件字符串。
            data_json (dict or str, optional): 更新数据。
            batch_updates (list, optional): 批量更新数据列表。

        Returns:
            Union[Dict[str, Any], Dict[str, Union[bool, int, List[Dict[str, Any]], str, float]]]:
            1. 单条更新时返回字典，成功时包含以下字段:
               {
                   "success": bool,  # 操作是否成功
                   "updated_count": int,  # 更新的行数
                   "updated_data": Dict[str, Any],  # 更新后的数据
                   "removed_fields": List[str],  # 被过滤掉的无效字段列表
                   "transaction_status": str,  # 事务状态："committed"或"in_transaction"
                   "table_name": str,  # 表名
                   "condition": str,  # 更新条件
                   "timestamp": float,  # 操作时间戳
                   "execution_time_ms": int  # 执行时间(毫秒)
               }
               失败时包含以下字段:
               {
                   "success": false,
                   "error": str,  # 错误信息
                   "error_type": str,  # 错误类型，如"ConditionError", "TableNotExist", "SchemaError"等
                   "transaction_status": str,  # 事务状态："not_started"或"rolled_back"
                   "table_name": str,
                   "condition": str,
                   "timestamp": float,
                   "execution_time_ms": int
               }
               
            2. 批量更新时返回字典，包含以下字段:
               {
                   "success": bool,  # 总体成功状态
                   "total_updates": int,  # 总更新操作数
                   "successful_updates": int,  # 成功更新操作数
                   "failed_updates": int,  # 失败更新操作数
                   "results": List[Dict[str, Any]],  # 每个更新操作的结果
                   "transaction_status": str,  # 事务状态
                   "table_name": str,  # 表名
                   "timestamp": float,  # 操作时间戳
                   "execution_time_ms": int  # 执行时间(毫秒)
               }
               
               每个更新操作的结果包含:
               {
                   "index": int,  # 在batch_updates中的索引
                   "condition": str,  # 更新条件
                   "success": bool,  # 操作是否成功
                   "updated_count": int,  # 更新的行数 (成功时)
                   "error": str,  # 错误信息 (失败时)
                   "error_type": str,  # 错误类型 (失败时)
                   "execution_time_ms": int  # 此次更新执行时间(毫秒)
               }

        Raises:
            ExecutionError: 执行更新时可能抛出的异常，包含错误信息、SQL和参数。
            ValueError: 条件解析错误时抛出。

        示例::
            # 单条更新
            result = client.update_data("users", "id == 1", {"name": "李四"})
            if result["success"]:
                print(f"成功更新 {result['updated_count']} 行数据")
            
            # 批量更新
            batch = [
                ["id == 1", {"name": "A"}], 
                ["id == 2", {"name": "B"}]
            ]
            result = client.update_data("users", batch_updates=batch)
            print(f"总共 {result['total_updates']} 条更新，成功 {result['successful_updates']} 条")
        """
        return DataOperations2.update_data(self, table_name, condition_str, data_json, batch_updates)
    
    def delete_data(self, table_name, condition_str) -> Dict[str, Any]:
        """
        删除表中数据，条件使用字符串描述。

        功能：
            - 支持复杂条件字符串。
            - 自动校验表存在性、条件合法性。
            - 自动处理事务、异常、日志。

        Args:
            table_name (str): 表名。
            condition_str (str): 删除条件字符串。

        Returns:
            Dict[str, Any]: 删除操作的结果字典，包含以下字段:
                成功时:
                {
                    "success": bool,  # 操作是否成功，总是True
                    "deleted_count": int,  # 删除的行数
                    "condition": str,  # 删除条件
                    "transaction_status": str,  # 事务状态："committed"或"in_transaction"
                    "table_name": str,  # 表名
                    "timestamp": float,  # 操作时间戳
                    "execution_time_ms": int  # 执行时间(毫秒)
                }
                
                失败时:
                {
                    "success": bool,  # 操作是否成功，总是False
                    "error": str,  # 错误信息
                    "error_type": str,  # 错误类型，如"ConditionError", "TableNotExist"等
                    "transaction_status": str,  # 事务状态："not_started"或"rolled_back"
                    "table_name": str,  # 表名
                    "condition": str,  # 删除条件
                    "timestamp": float,  # 操作时间戳
                    "execution_time_ms": int  # 执行时间(毫秒)
                }

        Raises:
            ExecutionError: 执行删除操作失败时可能抛出。
            ValueError: 条件解析错误时抛出。

        示例::
            result = client.delete_data("users", "age < 18")
            if result["success"]:
                print(f"成功删除 {result['deleted_count']} 行数据")
            else:
                print(f"删除失败: {result.get('error')}")
        """
        return DataOperations2.delete_data(self, table_name, condition_str)
    
    # 从 DataOperationsFetch 整合的方法
    def fetch_data(self, table_name, condition_str=None, columns="*", order_by=None, limit=None, offset=None, batch_conditions=None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        查询表中数据，支持条件、列、排序、分页、批量查询。

        功能：
            - 支持单条件和批量条件查询。
            - 支持列名、排序、limit、offset参数。
            - 自动处理JSON字段反序列化。
            - 自动处理异常、日志。

        Args:
            table_name (str): 表名。
            condition_str (str, optional): 查询条件字符串，如"age > 18 and name like '%张%'"。
            columns (str or list, optional): 查询列，默认"*"。可以是列名列表或逗号分隔的列名字符串。
            order_by (str or list, optional): 排序字段，如"name ASC"或["name ASC", "age DESC"]。
            limit (int, optional): 限制返回行数。
            offset (int, optional): 偏移量，用于分页查询。
            batch_conditions (list, optional): 批量查询条件列表，如["age > 18", "name == '张三'"]。

        Returns:
            Union[Dict[str, Any], List[Dict[str, Any]]]: 
            1. 单条件查询时返回字典，结构如下:
               {
                   "success": bool,  # 查询是否成功
                   "data": List[Dict[str, Any]],  # 查询结果行列表，每行为一个字典，键为列名
                   "count": int,  # 结果行数
                   "message": str,  # 成功/错误信息
                   "table_name": str,  # 查询的表名
                   "condition": str,  # 使用的查询条件
                   "columns": List[str],  # 查询的列名列表
                   "timestamp": float,  # 操作时间戳
                   "execution_time_ms": int  # 执行时间(毫秒)
               }
               失败时会添加以下字段:
               {
                   "error": str,  # 详细错误信息
                   "error_type": str,  # 错误类型(如"ConditionError", "TableNotExist"等)
               }
               
            2. 批量查询时返回查询结果列表，每项对应一个条件的查询结果，结构同上。
               List[Dict[str, Any]]

        Raises:
            ValueError: 表不存在或条件解析错误时抛出。
            ExecutionError: SQL执行错误时抛出。

        示例::
            # 单条件查询
            result = client.fetch_data("users", "age > 18", columns=["id", "name"])
            # 结果访问
            if result["success"]:
                for row in result["data"]:
                    print(f"ID: {row['id']}, Name: {row['name']}")
            
            # 批量查询
            results = client.fetch_data("users", batch_conditions=["age > 18", "name == '张三'"])
            # 结果访问
            for query_result in results:
                if query_result["success"]:
                    print(f"条件 '{query_result['condition']}' 匹配到 {query_result['count']} 条记录")
        """
        return DataOperationsFetch.fetch_data(self, table_name, condition_str, columns, order_by, limit, offset, batch_conditions)
    
    # 从 DBOperations 整合的方法
    def create_table(self, table_name, table_schema_json) -> Dict[str, Any]:
        """
        根据JSON模式创建表，支持丰富的PostgreSQL字段属性和约束。

        功能：
            - 支持新旧两种JSON表结构格式。
            - 自动创建枚举类型、主键、外键、索引、注释等。
            - 自动处理异常、日志。

        Args:
            table_name (str): 表名。
            table_schema_json (dict or str): 表结构JSON对象或字符串。

        Returns:
            Dict[str, Any]: 创建表操作的结果字典，包含以下字段:
                成功时:
                {
                    "success": bool,  # 操作是否成功，总是True
                    "message": str,  # 成功消息
                    "table_name": str,  # 创建的表名
                    "execution_time_ms": int,  # 执行时间(毫秒)
                    "sql": str,  # 执行的建表SQL语句
                    "schema": Dict[str, Any]  # 使用的表结构
                }
                
                失败时:
                {
                    "success": bool,  # 操作是否成功，总是False
                    "error": str,  # 错误信息
                    "error_type": str,  # 错误类型，如"SchemaError", "SQLError"等
                    "table_name": str,  # 表名
                    "execution_time_ms": int  # 执行时间(毫秒)
                }

        Raises:
            ExecutionError: 执行建表SQL失败时可能抛出。
            ValueError: JSON解析失败或表结构无效时抛出。

        示例::
            schema = {
                "columns": {
                    "id": {"type": "SERIAL", "primary_key": True}, 
                    "name": {"type": "VARCHAR(50)", "nullable": False},
                    "age": {"type": "INT", "default": 18},
                    "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}
                },
                "indexes": [
                    {"name": "idx_name", "columns": ["name"]}
                ]
            }
            result = client.create_table("users", schema)
            if result["success"]:
                print(f"表创建成功: {result['message']}")
            else:
                print(f"表创建失败: {result.get('error')}")
        """
        return DBOperations.create_table(self, table_name, table_schema_json)
    
    def add_column(self, table_name, column_json):
        """
        向现有表中增加列，列信息使用JSON格式描述。

        功能：
            - 支持所有PostgreSQL字段属性。
            - 自动处理主键、唯一、外键、注释等。
            - 自动处理异常、日志。

        Args:
            table_name (str): 表名。
            column_json (dict or str): 列信息JSON对象或字符串。

        Returns:
            dict: 操作结果，包含success、message、error等字段。

        示例::
            col = {"name": "age", "type": "INT", "nullable": False}
            client.add_column("users", col)
        """
        return DBOperations.add_column(self, table_name, column_json)
    
    def drop_table(self, table_name, force=False):
        """
        删除表。

        功能：
            - 支持普通和强制（VACUUM+CASCADE）删除。
            - 自动处理事务、异常、日志。

        Args:
            table_name (str): 要删除的表名。
            force (bool, optional): 是否强制删除，默认False。

        Returns:
            dict: 操作结果，包含success、message、error等字段。

        示例::
            client.drop_table("users")
            client.drop_table("users", force=True)
        """
        return DBOperations.drop_table(self, table_name, force)
    
    def execute_sql_script(self, sql_script_path):
        """
        执行SQL脚本文件。

        功能：
            - 批量执行SQL文件中的所有语句。
            - 自动处理异常、日志。

        Args:
            sql_script_path (str): SQL脚本文件路径。

        Returns:
            None

        示例::
            client.execute_sql_script("init_db.sql")
        """
        return DBOperations.execute_sql_script(self, sql_script_path)
    
    def get_all_columns(self, table_name):
        """
        获取表的所有列名，并对每个列名进行安全引号处理。

        功能：
            - 查询表的所有列名，返回带双引号的逗号分隔字符串。
            - 自动处理大小写、特殊字符。
            - 若表不存在，抛出异常。

        Args:
            table_name (str): 表名。

        Returns:
            str: 所有列名的逗号分隔字符串（带双引号）。

        Raises:
            ValueError: 表不存在时抛出。

        示例::
            cols = client.get_all_columns("users")
            # 返回 '"id", "name", "age"'
        """
        # 检查表是否存在，避免查询不存在的表
        if not self._table_exists(table_name):
            logger.warning(f"尝试获取列信息的表 '{table_name}' 不存在或无法访问。", color=Colors.WARNING)
            raise ValueError(f"表 '{table_name}' 不存在")

        # information_schema.columns.table_name 通常是未引用的名称
        # 我们需要根据实际表名来查询，PostgreSQL中表名和列名是大小写敏感的（除非全小写且无特殊字符）
        # SQL注入风险：table_name直接用于字符串格式化。_table_exists应已验证。
        # psycopg2 通常期望标识符在SQL字符串中按SQL标准引用，或者不引用（此时大小写和关键字规则适用）
        # 为了安全和一致性，我们查询时不对table_name进行额外引用，依赖于PostgreSQL的标准行为
        # 但在拼接返回的列名时，我们会引用它们。
        
        sql = "SELECT column_name FROM information_schema.columns WHERE table_catalog = %s AND table_schema = 'public' AND table_name = %s ORDER BY ordinal_position"
        
        # 通常情况下，table_catalog 是当前数据库名，table_schema 是 'public'
        # table_name 应该是用户提供的原始名称，psycopg2 会处理其作为参数的传递
        
        # 获取当前数据库名称 (如果需要精确匹配 catalog)
        # current_db_sql = "SELECT current_database();"
        # db_name_result = self.execute_query(current_db_sql, fetch=True)
        # current_db = db_name_result[0][0] if db_name_result else self.database # Fallback to configured DB name

        # 使用参数化查询以避免SQL注入风险
        # table_name 本身不应包含引号或特殊字符，如果包含，则其创建和引用方式需非常小心
        # 这里假设 table_name 是一个标准的、未经外部引号处理的表名
        results = self.execute_query(sql, (self.database, table_name), fetch=True)
        
        if results:
            # 对每个列名进行处理：替换内部双引号为两个双引号，然后用双引号包裹
            processed_columns = []
            for row in results:
                column_name = str(row[0])
                escaped_column_name = column_name.replace('"', '""')
                processed_columns.append(f'"{escaped_column_name}"')
            return ", ".join(processed_columns)
        else:
            # 如果表存在但没有列（理论上不太可能对于普通表），或查询因权限等原因未返回列
            logger.warning(f"无法获取表 '{table_name}' 的列信息，或表没有列。", color=Colors.WARNING)
            return "*"
