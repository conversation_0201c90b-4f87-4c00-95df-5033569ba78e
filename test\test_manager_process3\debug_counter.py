#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试计数器问题
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess

# 导入工作函数
import importlib.util
spec = importlib.util.spec_from_file_location("worker_functions", "test/test_manager_process3/worker_functions.py")
worker_functions = importlib.util.module_from_spec(spec)
spec.loader.exec_module(worker_functions)
data_sharing_worker = worker_functions.data_sharing_worker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def counter_worker(shared_data_manager, item, *args, **kwargs):
    """
    计数器测试工作函数
    
    Args:
        shared_data_manager: 共享数据管理器
        item: 要处理的数据项
        *args, **kwargs: 额外参数
        
    Returns:
        处理结果
    """
    import os
    pid = os.getpid()
    
    print(f"[PID {pid}] 开始处理 {item}")
    
    # 使用锁保证计数器原子性
    lock = shared_data_manager.get_lock()
    with lock:
        counter = shared_data_manager.get_value("counter", 0)
        print(f"[PID {pid}] 当前计数器: {counter}, 处理项: {item}")
        shared_data_manager.add_value("counter", counter + 1)
        new_counter = shared_data_manager.get_value("counter", 0)
        print(f"[PID {pid}] 更新后计数器: {new_counter}")
    
    # 添加到处理列表
    shared_data_manager.append_to_list("processed_items", item)
    
    print(f"[PID {pid}] 完成处理 {item}")
    return f"processed_{item}"


def test_counter():
    """测试计数器功能"""
    print("=" * 80)
    print("调试计数器问题")
    print("=" * 80)
    
    # 创建测试数据
    test_data = [f"shared_item_{i}" for i in range(10)]
    print(f"测试数据: {test_data}")
    
    # 创建 ManagedMultiProcess 实例（使用实际的 data_sharing_worker）
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=data_sharing_worker,
        num_processes=3,
        max_queue_size=20
    )
    
    try:
        # 启动处理器并等待完成
        print("启动多进程管理器...")
        results = manager.run()
        
        # 等待所有任务完成
        print("等待任务完成...")
        manager.wait_all(timeout=15)
        
        # 获取最终结果
        print("获取最终结果...")
        results = manager.get_results()
        
        print(f"最终结果: {results}")
        
        # 检查计数器
        if "counter" in results:
            counter_value = results["counter"]
            expected_count = len(test_data)
            print(f"计数器值: {counter_value}")
            print(f"期望值: {expected_count}")
            print(f"计数器正确: {counter_value == expected_count}")
        else:
            print("错误: 结果中没有计数器")
        
        # 检查处理项
        if "processed_items" in results:
            processed_items = results["processed_items"]
            print(f"处理项数量: {len(processed_items)}")
            print(f"处理项: {processed_items}")
            print(f"处理项正确: {len(processed_items) == len(test_data)}")
        else:
            print("错误: 结果中没有处理项列表")
        
        # 检查错误
        if "errors" in results and results["errors"]:
            print(f"发现错误: {results['errors']}")
        else:
            print("没有错误")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理
        try:
            manager.stop_all(immediate=True)
        except:
            pass


if __name__ == "__main__":
    test_counter()
