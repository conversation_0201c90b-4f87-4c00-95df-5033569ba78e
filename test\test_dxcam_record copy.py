import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from global_tools.window_operation.dxcam_core import DXCamCore
import time
import cv2

def test_basic_recording():
    """测试基本录制功能"""
    print("测试基本录制功能 - 录制5秒")
    dx = DXCamCore(monitor_index=0)
    output_file = os.path.join(os.path.dirname(__file__), "test_recording.mp4")
    
    # 录制5秒
    dx.record_screen(
        output_file=output_file,
        fps=30,
        duration=5,
        codec='mp4v',
        quality=95,
        show_preview=True
    )
    
    # 验证文件是否存在
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # 转换为MB
        print(f"录制成功: {output_file} (大小: {file_size:.2f} MB)")
        
        # # 尝试打开视频检查帧数
        # cap = cv2.VideoCapture(output_file)
        # if cap.isOpened():
        #     frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        #     fps = cap.get(cv2.CAP_PROP_FPS)
        #     duration = frame_count / fps if fps > 0 else 0
        #     print(f"视频信息 - 帧数: {frame_count}, FPS: {fps:.2f}, 时长: {duration:.2f}秒")
        #     cap.release()
        # else:
        #     print("无法打开录制的视频文件进行验证")
    else:
        print(f"录制失败: 文件 {output_file} 不存在")

def test_advanced_recording():
    """测试高级录制功能"""
    print("\n测试高级录制功能 - 录制特定帧数")
    dx = DXCamCore(monitor_index=0)
    output_file = os.path.join(os.path.dirname(__file__), "test_recording_frames.mp4")
    
    # 录制100帧
    dx.record_screen(
        output_file=output_file,
        fps=30,
        max_frames=100,
        codec='mp4v',
        buffer_size=50,
        skip_duplicate_frames=True
    )
    
    # 验证文件是否存在
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # 转换为MB
        print(f"录制成功: {output_file} (大小: {file_size:.2f} MB)")
        
        # 尝试打开视频检查帧数
        cap = cv2.VideoCapture(output_file)
        if cap.isOpened():
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            print(f"视频信息 - 帧数: {frame_count}, FPS: {fps:.2f}")
            cap.release()
        else:
            print("无法打开录制的视频文件进行验证")
    else:
        print(f"录制失败: 文件 {output_file} 不存在")

def test_region_recording():
    """测试区域录制功能"""
    print("\n测试区域录制功能 - 录制屏幕特定区域")
    
    # 获取主显示器信息
    temp_dx = DXCamCore(monitor_index=0)
    monitor_info = temp_dx.get_current_monitor_info()
    temp_dx.release()
    
    if not monitor_info:
        print("无法获取显示器信息，跳过区域录制测试")
        return
        
    # 计算屏幕中心区域 (中间的1/4区域)
    width = monitor_info["width"]
    height = monitor_info["height"]
    left = monitor_info["left"] + width // 4
    top = monitor_info["top"] + height // 4
    right = left + width // 2
    bottom = top + height // 2
    
    region = (left, top, right, bottom)
    print(f"录制区域: {region}")
    
    # 创建区域捕获器
    dx = DXCamCore(monitor_index=0, region=region)
    output_file = os.path.join(os.path.dirname(__file__), "test_recording_region.mp4")
    
    # 录制3秒
    dx.record_screen(
        output_file=output_file,
        fps=30,
        duration=3,
        show_preview=True
    )
    
    # 验证文件是否存在
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # 转换为MB
        print(f"区域录制成功: {output_file} (大小: {file_size:.2f} MB)")
        
        # 尝试打开视频检查尺寸
        cap = cv2.VideoCapture(output_file)
        if cap.isOpened():
            video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            print(f"视频尺寸: {video_width}x{video_height} (应为 {width//2}x{height//2})")
            cap.release()
        else:
            print("无法打开录制的视频文件进行验证")
    else:
        print(f"录制失败: 文件 {output_file} 不存在")

if __name__ == "__main__":
    print("DXCamCore 屏幕录制功能测试")
    print("=" * 50)
    
    try:
        test_basic_recording()
        # test_advanced_recording()
        # test_region_recording()
        print("\n所有测试完成!")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    
    print("\n按任意键退出...")
    input()