# -*- coding: utf-8 -*-
"""
增强进程模块入口。
导出 EnhancedProcess, EnhancedProcessWithSharedData 和 ResultContainer。
"""
from global_tools.utils import Logger, ClassInstanceManager, Colors, LogLevel

ClassInstanceManager.create_instance(Logger, "EnhancedProcessLogger")
logger: Logger = ClassInstanceManager.get_instance("EnhancedProcessLogger")
logger.set_instance_level(LogLevel.OFF)

from .process import EnhancedProcess, ProcessLogger

__all__ = [
    "EnhancedProcess",
    "ProcessLogger",
]
