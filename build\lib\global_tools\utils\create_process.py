#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ManagedMultiProcess - 多进程管理工具

该模块提供了一个用于管理多进程任务的类，支持任务分发、结果收集、状态管理和错误处理。
主要用于并行处理大量任务，同时提供状态监控和结果集中管理功能。
"""

import os
import sys
import threading
import time
import logging
import traceback
import multiprocessing
from multiprocessing import Pool, Manager, Process, JoinableQueue, Lock, Event
from queue import Empty  # 导入 Empty 异常
from typing import Any, Dict, Callable, Iterable, List, Optional, Tuple, Union, cast

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    force=True,
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# =============== 主类实现 ===============


class ManagedMultiProcess:
    """
    多进程管理类，使用 multiprocessing 实现多进程并行处理。

    特点:
    - 自动分发任务和收集结果
    - 支持异常处理和状态监控
    - 完善的错误处理和日志记录
    - 支持进程生命周期管理

    数据处理能力:
    - 支持安全地管理多进程共享数据
    - 提供键值对、列表和集合等数据结构的统一操作接口
    - 保证多进程环境下数据读写的线程安全

    共享数据操作方法使用指南:

    1. 共享数据结构:
       - 字典类型(Dict): 用于存储键值对数据，如 {"key1": value1, "key2": value2}
       - 集合类型(Set): 用于存储不重复元素，内部实现为列表
       - 列表类型(List): 用于存储有序元素

    2. 主要操作方法:
       - add_to_shared_dict: 添加/更新字典中的单个键值对
       - update_shared_dict: 批量添加/更新字典中的多个键值对
       - get_from_shared_dict: 从字典中安全地获取值
       - add_to_shared_set: 添加元素到集合(确保元素唯一)

    3. 线程安全考虑:
       - 所有写操作必须使用锁(lock)确保线程安全
       - 读操作可选择是否使用锁(取决于并发读写情况)
       - 批量操作比多次单个操作更高效

    4. 性能优化建议:
       - 尽量减少锁的获取次数
       - 对频繁访问的数据考虑缓存
       - 选择合适的数据结构(字典用于映射关系，集合用于唯一性检查)

    详细使用指南:
    ----------------
    ManagedMultiProcess 提供一种简单的方式来并行处理大量任务。只需定义一个回调函数来处理单个任务，
    指定任务数据和进程数量，剩下的工作（进程创建、任务分发、结果收集、错误处理等）将由类自动完成。

    重要说明：run() 方法不会自动等待所有任务完成，如需等待，请手动调用 wait_all() 方法。

    基本使用流程:
    1. 创建 ManagedMultiProcess 实例，提供任务列表和回调函数
    2. 调用 run() 方法启动处理（不会等待完成）
    3. 可选：调用 wait_all() 方法等待所有任务完成
    4. 获取处理结果进行后续操作

    完整使用示例:
    ```python
    import multiprocessing
    from global_tools.utils.create_process import ManagedMultiProcess

    def process_item(shared_data, item, lock, *args):
        # 计算结果
        result = item * 2

        # 安全地存储结果
        with lock:
            key = f"result_{item}"
            shared_data[key] = result

            # 也可以使用辅助方法存储
            if "processed" not in shared_data:
                shared_data["processed"] = []
            shared_data["processed"].append(item)

        # 使用提供的静态方法更新共享数据
        ManagedMultiProcess.add_to_shared_set(shared_data, "unique_items", item, lock)

        return result

    def main():
        # 准备任务数据
        tasks = list(range(10))  # 10个任务

        # 创建并运行多进程管理器
        processor = ManagedMultiProcess(
            input_data=tasks,
            callback_func=process_item,
            num_processes=4  # 使用4个进程
        )

        # 启动任务处理（不等待完成）
        initial_results = processor.run()

        # 可以在这里做一些其他工作，任务会在后台处理
        print(f"任务已启动，初始状态: {processor.get_status()}")

        # 如果需要等待所有任务完成
        processor.wait_all(timeout=30)  # 等待最多30秒

        # 获取完整结果
        results = processor.get_results()

        # 处理结果
        print(f"处理结果: {results}")
        print(f"已处理项: {results.get('processed', [])}")
        print(f"唯一项集合: {results.get('unique_items', [])}")

    if __name__ == "__main__":
        # 防止递归启动进程
        multiprocessing.freeze_support()
        main()
    ```

    错误处理策略:
    ----------------
    1. 单个任务错误处理:
       - 每个任务在单独的进程中执行，一个任务的错误不会影响其他任务
       - 任务错误被捕获并记录到共享数据字典中的 "errors" 列表
       - 错误信息包含任务内容、错误消息和进程ID，便于调试

    2. 进程错误处理:
       - 进程级别的错误被记录到进程状态字典
       - 如果进程发生错误，状态会被标记为 'error'
       - 即使部分进程失败，管理器仍会尽可能收集可用结果

    3. 异常恢复与结果获取:
       - run() 方法会捕获所有异常，确保能够返回已处理的结果
       - 即使出现错误，共享数据也会被保存并返回
       - 可以检查结果中的 "errors" 列表来识别和处理失败的任务

    4. 超时控制:
       - wait_all() 和 stop_all() 方法有超时机制，防止无限等待
       - 超时后会强制结束进程，确保资源被释放

    高级应用:
    ----------------
    1. 实时状态监控:
       ```python
       import threading
       import time

       def monitor_status(processor):
           while True:
               status = processor.get_status()
               print(f"当前状态: 总任务数={status['total_tasks']}, 成功={status['success']}, 失败={status['errors']}")

               # 检查是否所有任务都已完成
               if status['pending'] == 0:
                   break

               time.sleep(1)  # 每秒更新一次状态

       # 创建处理器
       processor = ManagedMultiProcess(tasks, process_task, num_processes=4)

       # 启动监控线程
       monitor_thread = threading.Thread(target=monitor_status, args=(processor,))
       monitor_thread.daemon = True
       monitor_thread.start()

       # 启动处理器（不等待完成）
       processor.run()

       # 可以在这里执行其他工作...

       # 如果需要等待所有任务和监控完成
       monitor_thread.join(timeout=60)  # 最多等待60秒
       processor.wait_all(timeout=30)   # 等待处理器完成

       # 获取最终结果
       final_results = processor.get_results()
       ```

    2. 异步任务处理:
       ```python
       import threading
       import time

       def background_processing():
           # 创建处理器
           processor = ManagedMultiProcess(large_tasks, process_task, num_processes=4)

           # 启动任务但不等待
           processor.run()

           # 执行其他工作
           print("任务已在后台启动，继续执行其他工作...")

           # 定期检查状态
           while True:
               status = processor.get_status()
               print(f"进度: {len(large_tasks) - status['pending']}/{len(large_tasks)}")

               if status['pending'] == 0:
                   break

               time.sleep(2)

           # 所有任务完成后获取结果
           final_results = processor.get_results()
           print(f"所有任务已完成，结果数量: {len(final_results)}")

           # 记得释放资源
           processor.stop_all()

           return final_results

       # 在后台线程中执行处理
       bg_thread = threading.Thread(target=background_processing)
       bg_thread.start()

       # 主线程继续执行其他工作
       print("主线程继续执行...")

       # 如果需要等待后台处理完成
       bg_thread.join()
       ```

    3. 处理大量数据:
       ```python
       # 对于大量任务，可以批量处理并使用更少的进程
       large_tasks = list(range(10000))

       # 使用系统CPU核心数作为进程数量的参考
       import os
       cpu_count = os.cpu_count() or 4
       num_processes = max(1, cpu_count - 1)  # 预留一个核心给系统

       processor = ManagedMultiProcess(large_tasks, process_task, num_processes=num_processes)
       results = processor.run()
       ```

    4. 自定义参数传递:
       ```python
       # 创建配置字典
       config = {
           "timeout": 30,
           "retry_count": 3,
           "log_level": "INFO"
       }

       # 传递给回调函数
       def process_with_config(shared_data, item, lock, config):
           timeout = config.get("timeout", 10)
           # 使用配置参数处理任务...

       # 创建处理器，传递配置
       processor = ManagedMultiProcess(tasks, process_with_config, num_processes=4, config)
       ```

    5. 进程资源和内存管理:
       - 对于内存密集型任务，减少进程数量以避免内存不足
       - 考虑任务分块，分批次处理大量数据
       - 使用 stop_all() 手动管理资源释放

    6. 错误重试机制示例:
       ```python
       def process_with_retry(shared_data, item, lock, max_retries=3):
           retries = 0
           while retries < max_retries:
               try:
                   # 处理任务...
                   return result
               except Exception as e:
                   retries += 1
                   if retries >= max_retries:
                       # 所有重试都失败，记录错误
                       with lock:
                           if "failed_items" not in shared_data:
                               shared_data["failed_items"] = []
                           shared_data["failed_items"].append({"item": item, "error": str(e)})
                       raise  # 重新抛出异常，让框架捕获
                   else:
                       # 等待短暂时间后重试
                       time.sleep(0.5)
       ```
    """

    def __init__(
        self,
        input_data: Iterable[Any],
        callback_func: Callable,
        num_processes: int = 3,
        *custom_args,
    ):
        """
        初始化多进程管理器

        Args:
            input_data: 可迭代的任务数据
            callback_func: 用户定义的进程工作函数，必须接受以下参数：
                           shared_data, task_element, lock, *custom_args
            num_processes: 启动的进程数量，默认为3
            *custom_args: 传递给 callback_func 的额外参数

        Raises:
            ValueError: 如果参数无效（如进程数量<=0或回调函数不可调用）
        """
        logger.info("开始初始化 ManagedMultiProcess...")

        # 参数验证
        if not callable(callback_func):
            error_msg = "callback_func 必须是可调用的函数"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if num_processes <= 0:
            error_msg = f"num_processes 必须大于0，当前值: {num_processes}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 存储参数
        self.input_data = list(input_data)
        self.num_processes = (
            min(num_processes, len(self.input_data)) if self.input_data else 1
        )
        self.user_callback = callback_func
        self.extra_args = custom_args

        # 错误和结果计数（用于状态跟踪）
        self.success_count = 0
        self.error_count = 0

        # 创建共享资源
        self.manager = Manager()
        self.shared_data_proxy = self.manager.dict()
        self.task_queue = JoinableQueue()
        self.process_lock = self.manager.Lock()
        self.stop_event = Event()
        self.process_status_dict = self.manager.dict()
        self.worker_processes = []

        # 初始化共享数据结构
        # 列表类型
        self.shared_data_proxy["dict_value"] = (
            self.manager.list()
        )  # 用于存储计算结果的列表
        self.shared_data_proxy["list_value"] = (
            self.manager.list()
        )  # 用于存储原始输入的列表
        self.shared_data_proxy["set_value"] = (
            self.manager.list()
        )  # 用于存储不重复元素的集合（用列表模拟）
        self.shared_data_proxy["processed"] = self.manager.list()  # 用于记录已处理的项
        self.shared_data_proxy["errors"] = self.manager.list()  # 用于记录错误信息

        # 字典类型 - 用于键值对数据
        self.shared_data_proxy["square"] = (
            self.manager.dict()
        )  # 用于存储项与其平方值的映射
        self.shared_data_proxy["value"] = (
            self.manager.dict()
        )  # 用于存储项与其原始值的映射

        # 将输入数据放入队列
        for item in self.input_data:
            self.task_queue.put(item)

        # 如果是单进程模式或输入数据较少，不创建工作进程
        if self.num_processes == 1 or len(self.input_data) <= 1:
            logger.info("使用单进程模式，暂不创建工作进程。")
            return

        # 创建并启动工作进程
        for i in range(self.num_processes):
            p = Process(
                target=self._worker_loop,
                args=(
                    self.task_queue,
                    self.shared_data_proxy,
                    self.process_lock,
                    self.stop_event,
                    self.process_status_dict,
                    self.user_callback,
                    self.extra_args,
                ),
            )
            self.worker_processes.append(p)
            p.daemon = True
            p.start()
            logger.info(f"已启动进程 PID: {p.pid}")

        logger.info(
            f"ManagedMultiProcess 初始化完成，已启动 {len(self.worker_processes)} 个进程。"
        )

    def __del__(self):
        """析构函数，确保资源被释放"""
        try:
            self.stop_all()

            # 尝试关闭 manager
            if hasattr(self, "manager"):
                self.manager.shutdown()
        except Exception as e:
            logger.error(f"析构时发生错误: {str(e)}")

    @staticmethod
    def _worker_loop(
        queue, shared_data, lock, stop_flag, status_dict, callback, custom_init_args
    ):
        """
        工作进程的主循环

        Args:
            queue: 任务队列
            shared_data: 共享数据字典
            lock: 进程锁
            stop_flag: 停止事件
            status_dict: 进程状态字典
            callback: 用户回调函数
            custom_init_args: 自定义参数
        """
        pid = os.getpid()
        status_dict[pid] = "running"
        logger.info(f"进程 {pid} 开始运行。")

        # 为了更快地响应停止信号，减少了队列获取任务的超时时间
        queue_timeout = 0.2  # 从1秒减少到0.2秒

        while not stop_flag.is_set():
            try:
                # 从队列获取任务，使用更短的超时以便更频繁地检查停止标志
                try:
                    task_element = queue.get(timeout=queue_timeout)
                except Empty:  # 使用导入的 Empty 异常
                    # 队列暂时为空，继续循环检查停止标志
                    continue
                except Exception as e:
                    # 获取任务时发生意外错误
                    logger.error(f"进程 {pid} 从队列获取任务失败: {str(e)}\n{traceback.format_exc()}")
                    status_dict[pid] = "error"
                    break

                # 检查是否正在停止，避免开始处理新任务
                if stop_flag.is_set():
                    # 将任务放回队列，便于其他进程处理
                    try:
                        queue.put(task_element)
                    except BaseException:
                        pass
                    queue.task_done()  # 标记当前任务未完成但我们不再处理
                    break

                # 处理任务
                try:
                    logger.info(f"进程 {pid} 开始处理任务: {task_element}")
                    callback(shared_data, task_element, lock, *custom_init_args)
                    logger.info(f"进程 {pid} 完成处理任务: {task_element}")
                except Exception as e:
                    # 回调函数执行出错
                    logger.error(f"进程 {pid} 执行回调函数时出错: {str(e)}\n{traceback.format_exc()}")
                    with lock:
                        if "errors" not in shared_data:
                            shared_data["errors"] = []
                        shared_data["errors"].append({
                            "item": task_element,
                            "error": str(e),
                            "pid": pid
                        })
                    status_dict[pid] = "error"
                finally:
                    # 标记任务完成
                    queue.task_done()

                    # 再次检查停止标志，如果设置了则立即退出循环
                    if stop_flag.is_set():
                        break
            except Exception as e:
                # 捕获循环中的任何其他异常
                logger.error(f"进程 {pid} 处理循环中发生意外错误: {str(e)}\n{traceback.format_exc()}")
                status_dict[pid] = "error"
                break

        # 循环结束处理
        if stop_flag.is_set():
            logger.info(f"进程 {pid} 收到停止信号，准备退出。")
            status_dict[pid] = "stopping"
        elif status_dict.get(pid) != "error":
            logger.info(f"进程 {pid} 处理完所有分配的任务，正常退出。")
            status_dict[pid] = "stopped"
        else:
            logger.info(f"进程 {pid} 因错误退出。")

        logger.info(f"进程 {pid} 退出。")

    def stop_all(self, immediate=False, force_timeout=1.0):
        """
        停止所有工作进程

        Args:
            immediate: 是否立即停止，True表示立即强制终止进程，False表示尝试优雅停止
            force_timeout: 优雅停止尝试的最大等待时间（秒），仅在immediate=False时有效
        """
        if not hasattr(self, "stop_event") or not hasattr(self, "worker_processes"):
            return

        logger.info("请求停止所有工作进程...")
        # 设置停止标志，通知所有进程应该停止
        self.stop_event.set()
        logger.info("停止信号已发送。")

        # 如果需要立即强制终止
        if immediate:
            logger.warning("启用立即终止模式，强制终止所有进程")
            for p in self.worker_processes:
                if p.is_alive():
                    logger.warning(f"立即终止进程 {p.pid}")
                    p.terminate()
            return

        # 添加超时终止机制
        start_time = time.time()
        max_wait_time = force_timeout  # 使用指定的超时时间

        # 等待一段短时间，让进程有机会自行退出
        for p in self.worker_processes:
            if p.is_alive():
                # 减少每个进程的join超时，加快检查速度
                p.join(timeout=0.1)  # 从0.5秒减少到0.1秒

        # 检查是否有进程仍在运行，如果有则强制终止
        for p in self.worker_processes:
            if p.is_alive():
                elapsed = time.time() - start_time
                if elapsed > max_wait_time:
                    logger.warning(f"进程 {p.pid} 未能在规定时间 {max_wait_time}秒 内退出，强制终止")
                    p.terminate()
                    # 给进程一点时间来响应终止信号
                    time.sleep(0.05)

    def wait_all(self, timeout=30):
        """
        等待所有进程完成

        Args:
            timeout: 最大等待时间（秒），防止无限等待
        """
        if not self.worker_processes:
            return

        logger.info("等待所有任务处理完成...")

        # 添加超时机制，防止卡死
        try:
            # 使用 queue 的 join 方法时需要注意超时
            wait_start = time.time()
            while not self.task_queue.empty():
                if time.time() - wait_start > timeout:
                    logger.warning(f"任务队列等待超时（{timeout}秒），强制继续")
                    break
                time.sleep(0.1)

            # 尝试 join 队列，但加入超时
            remaining = max(1, timeout - int(time.time() - wait_start))
            for _ in range(remaining * 10):  # 分成小段检查，每0.1秒检查一次
                if self.task_queue.empty():
                    break
                time.sleep(0.1)

            logger.info("所有任务已处理完毕或超时。等待所有工作进程退出...")

            # 逐个等待进程退出，加入超时
            for p in self.worker_processes:
                p.join(timeout=2)  # 最多等待2秒
                if p.is_alive():
                    logger.warning(f"进程 {p.pid} 未能在规定时间内退出")

            logger.info("所有工作进程已退出或超时。")
        except Exception as e:
            logger.error(f"等待任务完成时出错: {str(e)}\n{traceback.format_exc()}")
            self.stop_all()  # 出错时尝试停止所有进程

    def get_status(self):
        """
        获取处理状态信息

        Returns:
            Dict: 包含状态信息的字典
        """
        logger.info("查询进程状态...")
        status = {
            "total_tasks": len(self.input_data),
            "success": self.success_count,
            "errors": self.error_count,
            "pending": len(self.input_data) - self.success_count - self.error_count,
        }

        # 如果有工作进程，添加进程状态
        if self.worker_processes:
            try:
                current_status = dict(self.process_status_dict)
                process_info = {}

                for p in self.worker_processes:
                    process_info[p.pid] = {
                        "status": current_status.get(p.pid, "unknown"),
                        "is_alive": p.is_alive(),
                    }

                status["processes"] = process_info
            except Exception as e:
                logger.error(f"获取进程状态时出错: {str(e)}")

        return status

    def get_results(self):
        """
        获取处理结果

        Returns:
            Dict: 包含处理结果的字典
        """
        logger.info("获取共享数据结果...")
        try:
            result_copy = self._convert_manager_proxy(self.shared_data_proxy)
            return result_copy
        except Exception as e:
            logger.error(f"获取或转换结果时出错: {str(e)}\n{traceback.format_exc()}")
            return {}

    def _convert_manager_proxy(self, proxy_obj):
        """
        将 Manager 代理对象转换为普通 Python 对象

        Args:
            proxy_obj: Manager 返回的代理对象

        Returns:
            转换后的普通 Python 对象
        """
        try:
            if isinstance(proxy_obj, dict) or hasattr(proxy_obj, "items"):
                # 处理字典类型
                normal_dict = {}
                for key, value in proxy_obj.items():
                    normal_dict[key] = self._convert_manager_proxy(value)
                return normal_dict
            elif (
                isinstance(proxy_obj, list)
                or hasattr(proxy_obj, "__iter__")
                and not isinstance(proxy_obj, (str, bytes, dict))
            ):
                # 处理列表类型
                normal_list = []
                for item in proxy_obj:
                    normal_list.append(self._convert_manager_proxy(item))
                return normal_list
            else:
                # 其他基本类型直接返回
                return proxy_obj
        except Exception as e:
            logger.error(f"转换代理对象时出错: {str(e)}\n{traceback.format_exc()}")
            # 返回一个安全的默认值
            if isinstance(proxy_obj, dict) or hasattr(proxy_obj, "items"):
                return {}
            elif (
                isinstance(proxy_obj, list)
                or hasattr(proxy_obj, "__iter__")
                and not isinstance(proxy_obj, (str, bytes, dict))
            ):
                return []
            else:
                return proxy_obj

    def run(self):
        """
        运行所有任务并返回结果

        这个方法不会自动等待所有进程完成，如果需要等待，请手动调用 wait_all() 方法。

        Returns:
            Dict: 包含处理结果的字典，如果进程仍在运行，结果可能不完整
        """
        logger.info(f"开始处理 {len(self.input_data)} 个任务...")

        # 如果没有任务，直接返回空结果
        if not self.input_data:
            logger.info("没有任务需要处理，返回空结果。")
            return {}

        # 单进程模式: 直接在主进程中处理
        if not self.worker_processes:
            logger.info("使用单进程模式处理任务...")
            lock = multiprocessing.RLock()

            # 创建管理器和共享数据
            with Manager() as manager:
                shared_dict = manager.dict()

                # 初始化共享数据结构
                # 列表类型
                shared_dict["dict_value"] = manager.list()  # 用于存储计算结果的列表
                shared_dict["list_value"] = manager.list()  # 用于存储原始输入的列表
                shared_dict["set_value"] = (
                    manager.list()
                )  # 用于存储不重复元素的集合（用列表模拟）
                shared_dict["processed"] = manager.list()  # 用于记录已处理的项
                shared_dict["errors"] = manager.list()  # 用于记录错误信息

                # 字典类型 - 用于键值对数据
                shared_dict["square"] = manager.dict()  # 用于存储项与其平方值的映射
                shared_dict["value"] = manager.dict()  # 用于存储项与其原始值的映射

                # 处理所有任务
                for item in self.input_data:
                    try:
                        logger.info(f"开始处理任务: {item}")
                        self.user_callback(shared_dict, item, lock, *self.extra_args)
                        logger.info(f"完成处理任务: {item}")
                        self.success_count += 1
                    except Exception as e:
                        error_msg = f"处理任务 {item} 时出错: {str(e)}"
                        logger.error(f"{error_msg}\n{traceback.format_exc()}")

                        # 记录错误
                        if "errors" not in shared_dict:
                            shared_dict["errors"] = manager.list()
                        shared_dict["errors"].append({"item": item, "error": str(e)})
                        self.error_count += 1

                # 转换结果为普通Python对象
                result = self._convert_manager_proxy(shared_dict)

            logger.info(
                f"任务处理完成。成功: {self.success_count}, 失败: {self.error_count}"
            )
            return result

        # 多进程模式: 开始处理但不等待完成
        logger.info("使用多进程模式处理任务，任务已提交但不自动等待完成...")
        logger.info("如需等待任务完成，请手动调用 wait_all() 方法")

        try:
            # 注意：不再自动等待所有任务完成
            # 直接获取当前结果，可能不完整
            results = self.get_results()

            # 计算当前的成功和失败数量（可能不准确，因为进程可能仍在运行）
            error_list = results.get("errors", [])
            if error_list:
                self.error_count = len(error_list)
            processed_items = sum(1 for k in results.keys() if k.startswith("result_"))
            self.success_count = processed_items

            logger.info(
                f"当前处理状态：成功: {self.success_count}, 失败: {self.error_count}, 总任务数: {len(self.input_data)}"
            )
            return results
        except Exception as e:
            logger.error(
                f"获取多进程任务结果时出错: {str(e)}\n{traceback.format_exc()}"
            )
            # 不自动停止进程，除非发生错误
            self.stop_all()
            return self.get_results()

    @staticmethod
    def add_to_shared_set(shared_data, key, value, lock=None):
        """
        将值添加到共享数据字典中指定键的集合(实现为列表)，确保集合中不存在重复元素。

        详细说明:
        ----------
        此方法用于向共享数据中的集合添加元素，如果集合不存在则创建新集合。
        集合内部实现为列表，但保证元素唯一性，适用于需要维护唯一元素集合的场景。

        参数:
        ----------
        shared_data: dict
            多进程共享数据字典，通常由 multiprocessing.Manager().dict() 创建
        key: str
            共享数据字典中的键名，对应的值将被视为集合(列表)
        value: Any
            要添加到集合的元素，可以是任何类型，但建议使用可哈希类型
        lock: multiprocessing.Lock, 可选
            用于确保线程安全的锁对象，在多进程环境中必须提供

        返回:
        ----------
        bool
            如果元素成功添加返回 True，如果元素已存在返回 False

        使用示例:
        ----------
        ```python
        # 创建共享数据和锁
        manager = multiprocessing.Manager()
        shared_data = manager.dict()
        lock = manager.Lock()

        # 添加元素到集合
        result1 = ManagedMultiProcess.add_to_shared_set(shared_data, "unique_ids", 12345, lock)
        # result1 = True (新元素添加成功)

        result2 = ManagedMultiProcess.add_to_shared_set(shared_data, "unique_ids", 12345, lock)
        # result2 = False (元素已存在)

        # 共享数据现在包含: {"unique_ids": [12345]}
        ```

        线程安全:
        ----------
        - 多进程环境中必须提供有效的 lock 参数
        - 如果 lock 为 None，则假定在单线程环境中调用
        - 一次只获取一次锁，尽量减少锁持有时间

        注意事项:
        ----------
        - 元素比较使用 Python 的 in 操作符，依赖元素的 __eq__ 方法
        - 对于复杂对象，确保其正确实现了 __eq__ 方法
        - 如需批量添加多个元素，考虑使用循环多次调用此方法
        - 元素添加的顺序会保留在内部列表中
        """
        if lock:
            with lock:
                return ManagedMultiProcess._add_to_shared_set_impl(
                    shared_data, key, value
                )
        else:
            return ManagedMultiProcess._add_to_shared_set_impl(shared_data, key, value)

    @staticmethod
    def _add_to_shared_set_impl(shared_data, key, value):
        """内部实现：添加元素到共享集合"""
        # 确保集合存在
        if key not in shared_data:
            shared_data[key] = []

        # 只有在元素不存在时才添加，保证唯一性
        current_set = shared_data[key]
        if value not in current_set:
            current_set.append(value)
            return True
        return False

    @staticmethod
    def add_to_shared_dict(shared_data, key, value, lock=None):
        """
        向共享数据字典中添加或更新键值对。

        详细说明:
        ----------
        此方法用于在多进程环境中安全地向共享数据字典添加或更新键值对。
        如果 key 已存在，将覆盖原有值；如果不存在，将创建新条目。

        参数:
        ----------
        shared_data: dict
            多进程共享数据字典，通常由 multiprocessing.Manager().dict() 创建
        key: str
            要添加/更新的键名
        value: Any
            要存储的值，可以是任何类型，包括数字、字符串、列表、字典等
        lock: multiprocessing.Lock, 可选
            用于确保线程安全的锁对象，在多进程环境中必须提供

        返回:
        ----------
        Any
            返回添加/更新前的旧值，如果键不存在则返回 None

        使用示例:
        ----------
        ```python
        # 创建共享数据和锁
        manager = multiprocessing.Manager()
        shared_data = manager.dict()
        lock = manager.Lock()

        # 添加新键值对
        old_value = ManagedMultiProcess.add_to_shared_dict(shared_data, "status", "running", lock)
        # old_value = None (键不存在)

        # 更新现有键值对
        old_value = ManagedMultiProcess.add_to_shared_dict(shared_data, "status", "completed", lock)
        # old_value = "running" (返回原值)

        # 共享数据现在包含: {"status": "completed"}
        ```

        线程安全:
        ----------
        - 多进程环境中必须提供有效的 lock 参数
        - 如果 lock 为 None，则假定在单线程环境中调用
        - 一次获取一次锁，尽量减少锁持有时间

        注意事项:
        ----------
        - 此方法适用于简单的键值对操作，每次只能操作一个键值对
        - 对于需要批量更新多个键值对的情况，请使用 update_shared_dict 方法
        - 对于复杂值类型(如字典或列表)，注意多进程环境下的数据一致性问题
        - 在存储大型对象时，考虑序列化/反序列化的开销
        """
        if lock:
            with lock:
                return ManagedMultiProcess._add_to_shared_dict_impl(
                    shared_data, key, value
                )
        else:
            return ManagedMultiProcess._add_to_shared_dict_impl(shared_data, key, value)

    @staticmethod
    def _add_to_shared_dict_impl(shared_data, key, value):
        """内部实现：添加或更新共享字典中的键值对"""
        # 检查是否是新键
        is_new_key = key not in shared_data

        # 添加或更新键值对
        shared_data[key] = value

        return is_new_key

    @staticmethod
    def get_from_shared_dict(shared_data, key, default=None, lock=None):
        """
        从共享数据字典中安全地获取指定键的值。

        详细说明:
        ----------
        此方法用于在多进程环境中安全地从共享字典获取值，支持提供默认值以处理键不存在的情况。
        类似于 Python 字典的 get() 方法，但增加了线程安全保障。

        参数:
        ----------
        shared_data: dict
            多进程共享数据字典，通常由 multiprocessing.Manager().dict() 创建
        key: str
            要获取值的键名
        default: Any, 可选
            如果键不存在，返回的默认值，默认为 None
        lock: multiprocessing.Lock, 可选
            用于确保线程安全的锁对象，在多进程环境中提供可确保读取的一致性

        返回:
        ----------
        Any
            如果键存在则返回对应的值，否则返回 default 值

        使用示例:
        ----------
        ```python
        # 创建共享数据和锁
        manager = multiprocessing.Manager()
        shared_data = manager.dict({"counter": 5, "status": "running"})
        lock = manager.Lock()

        # 获取存在的键的值
        counter = ManagedMultiProcess.get_from_shared_dict(shared_data, "counter", 0, lock)
        # counter = 5

        # 获取不存在的键的值，使用默认值
        result = ManagedMultiProcess.get_from_shared_dict(shared_data, "result", "not found", lock)
        # result = "not found"
        ```

        线程安全:
        ----------
        - 多进程环境中建议提供有效的 lock 参数，尤其是当同时存在读写操作时
        - 如果 lock 为 None，则以非线程安全方式读取，适用于只读场景
        - 对于只读操作，可以考虑不使用锁以提高性能

        注意事项:
        ----------
        - 此方法适用于简单的键值对读取，每次只能读取一个键的值
        - 对于需要读取多个键值的场景，考虑多次调用此方法
        - 确保 default 值的类型与预期的值类型一致，避免类型错误
        - 在并发环境中，如果不使用锁，可能读取到不一致的数据
        """
        # 如果提供了锁，使用锁保护读取操作
        if lock:
            with lock:
                if key not in shared_data:
                    return default
                return shared_data[key]
        else:
            # 非锁保护读取
            if key not in shared_data:
                return default
            return shared_data[key]

    @staticmethod
    def update_shared_dict(shared_data, update_dict, lock=None):
        """
        批量更新共享数据字典中的多个键值对。

        详细说明:
        ----------
        此方法用于在多进程环境中安全地向共享数据字典批量添加或更新多个键值对。
        通过一次锁操作更新多个键值对，比多次调用 add_to_shared_dict 更高效。

        参数:
        ----------
        shared_data: dict
            多进程共享数据字典，通常由 multiprocessing.Manager().dict() 创建
        update_dict: dict
            包含要更新的键值对的字典，格式为 {key1: value1, key2: value2, ...}
        lock: multiprocessing.Lock, 可选
            用于确保线程安全的锁对象，在多进程环境中必须提供

        返回:
        ----------
        dict
            包含所有被更新的键及其更新前的旧值的字典，如果键不存在则旧值为 None

        使用示例:
        ----------
        ```python
        # 创建共享数据和锁
        manager = multiprocessing.Manager()
        shared_data = manager.dict({"count": 10})
        lock = manager.Lock()

        # 批量更新多个键值对
        old_values = ManagedMultiProcess.update_shared_dict(
            shared_data,
            {"count": 20, "status": "active", "timestamp": 1632145600},
            lock
        )
        # old_values = {"count": 10, "status": None, "timestamp": None}

        # 共享数据现在包含: {"count": 20, "status": "active", "timestamp": 1632145600}
        ```

        线程安全:
        ----------
        - 多进程环境中必须提供有效的 lock 参数
        - 如果 lock 为 None，则假定在单线程环境中调用
        - 以一次锁操作更新所有键值对，减少锁争用

        注意事项:
        ----------
        - 此方法适用于需要原子性更新多个键值对的场景
        - 与 Python 字典的 update() 方法类似，但提供线程安全保障和返回旧值
        - 返回的旧值字典可用于回溯或撤销更新
        - 如果 update_dict 为空，则不执行任何操作，返回空字典
        - 对于大量键值对的更新，此方法比多次调用 add_to_shared_dict 更高效
        """
        if lock:
            with lock:
                return ManagedMultiProcess._update_shared_dict_impl(
                    shared_data, update_dict
                )
        else:
            return ManagedMultiProcess._update_shared_dict_impl(
                shared_data, update_dict
            )

    @staticmethod
    def _update_shared_dict_impl(shared_data, update_dict):
        """内部实现：批量更新共享字典中的键值对"""
        # 记录新添加的键的数量
        new_keys = 0

        # 批量更新键值对
        for key, value in update_dict.items():
            # 检查是否是新键
            if key not in shared_data:
                new_keys += 1

            # 添加或更新键值对
            shared_data[key] = value

        return {
            key: shared_data[key] for key in update_dict.keys() if key in shared_data
        }


# =============== 测试用回调函数 ===============


def basic_test_callback(shared_data, item, lock, *args):
    """基本回调函数：计算平方并返回结果"""
    with lock:
        # 保持原来的行为，将结果存储为键值对
        key_result = f"result_{item}"
        if key_result not in shared_data:
            shared_data[key_result] = item * item

    # 使用新增的方法将项和其平方值添加到共享字典中
    # 先创建或获取 square 字典
    square_dict = ManagedMultiProcess.get_from_shared_dict(
        shared_data, "square", {}, lock
    )
    # 然后将键值对添加到 shared_data 中
    square_dict[item] = item * item
    ManagedMultiProcess.add_to_shared_dict(shared_data, "square", square_dict, lock)


def error_test_callback(shared_data, item, lock, *args):
    """可能抛出异常的回调函数"""
    if item == "error":
        raise ValueError("测试错误：预期的错误")

    with lock:
        # 保持原来的行为，将结果存储为键值对
        key_result = f"result_{item}"
        if key_result not in shared_data:
            shared_data[key_result] = item * 2

    # 使用新增的方法将项和其值添加到共享字典中
    # 先创建或获取 value 字典
    value_dict = ManagedMultiProcess.get_from_shared_dict(
        shared_data, "value", {}, lock
    )
    # 然后将键值对添加到 value_dict 中
    value_dict[item] = item
    ManagedMultiProcess.add_to_shared_dict(shared_data, "value", value_dict, lock)


def resource_test_callback(shared_data, item, lock, *args):
    """使用共享资源的回调函数"""
    with lock:
        # 处理 dict_value
        if "dict_value" not in shared_data:
            shared_data["dict_value"] = []
        shared_data["dict_value"].append(item * 2)

        # 处理 list_value
        if "list_value" not in shared_data:
            shared_data["list_value"] = []
        shared_data["list_value"].append(item)

    # 处理 set_value（使用专用方法确保元素唯一性）
    ManagedMultiProcess.add_to_shared_set(shared_data, "set_value", item, lock)


def edge_test_callback(shared_data, item, lock, *args):
    """处理边界条件的回调函数"""
    # 使用相同的静态方法处理 processed 列表，如果需要唯一性
    ManagedMultiProcess.add_to_shared_set(shared_data, "processed", item, lock)


# =============== 测试函数 ===============


def run_tests():
    """运行所有测试用例"""
    logger.info("=" * 50)
    logger.info("开始运行测试用例")
    logger.info("=" * 50)

    # 测试基本功能
    logger.info("\n" + "=" * 30)
    logger.info("测试基本功能")
    logger.info("=" * 30)

    test_data = list(range(5))
    mp = ManagedMultiProcess(test_data, basic_test_callback, num_processes=2)

    try:
        # 启动任务但不等待完成
        mp.run()

        # 手动等待任务完成
        mp.wait_all(timeout=30)

        # 获取结果
        results = mp.get_results()

        # 验证结果
        success = True
        expected_squares = {f"result_{i}": i * i for i in test_data}

        # 验证单个结果
        for key, expected_value in expected_squares.items():
            if not isinstance(results, dict) or key not in results:
                logger.error(f"测试失败: {key} 不在结果中。")
                success = False
            elif results[key] != expected_value:
                logger.error(
                    f"测试失败: {key} 的值不正确。期望 {expected_value}，实际 {results.get(key)}"
                )
                success = False

        if success:
            logger.info("基本功能测试通过！")
        else:
            logger.error("基本功能测试失败！")

    except Exception as e:
        logger.error(f"基本功能测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    # 测试错误处理
    logger.info("\n" + "=" * 30)
    logger.info("测试错误处理")
    logger.info("=" * 30)

    test_data = list(range(3)) + ["error"] + list(range(3, 5))
    mp = ManagedMultiProcess(test_data, error_test_callback, num_processes=2)

    try:
        # 启动任务但不等待完成
        mp.run()

        # 手动等待任务完成
        mp.wait_all(timeout=30)

        # 获取结果
        results = mp.get_results()

        # 验证错误处理
        error_list = results.get("errors", [])
        if not error_list:
            logger.error("错误处理测试失败：未检测到错误")
        else:
            logger.info("错误处理测试通过：成功检测到错误")

        # 验证部分结果仍然正确
        success_keys = [k for k in results.keys() if k.startswith("result_")]
        success_count = len(success_keys)
        if success_count < 3:
            logger.error(f"错误处理测试失败：处理数量过少，仅处理了 {success_count} 项")
        else:
            logger.info(f"错误处理测试部分通过：成功处理了 {success_count} 项")

    except Exception as e:
        logger.error(f"错误处理测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    # 测试资源管理
    logger.info("\n" + "=" * 30)
    logger.info("测试资源管理")
    logger.info("=" * 30)

    test_data = list(range(5))
    mp = ManagedMultiProcess(test_data, resource_test_callback, num_processes=2)

    try:
        # 启动任务但不等待完成
        mp.run()

        # 手动等待任务完成
        mp.wait_all(timeout=30)

        # 获取结果
        results = mp.get_results()

        logger.info(f"资源管理测试结果: {results}")

        success = True

        # 验证字典数据
        dict_values = set(results.get("dict_value", []))
        expected_dict_values = {i * 2 for i in test_data}
        if dict_values != expected_dict_values:
            logger.error(
                f"资源管理测试失败：字典数据不匹配，期望: {expected_dict_values}，实际: {dict_values}"
            )
            success = False

        # 验证列表数据
        list_values = set(results.get("list_value", []))
        expected_list_values = set(test_data)
        if list_values != expected_list_values:
            logger.error(
                f"资源管理测试失败：列表数据不匹配，期望: {expected_list_values}，实际: {list_values}"
            )
            success = False

        # 验证集合数据
        set_values = set(results.get("set_value", []))
        expected_set_values = set(test_data)
        if set_values != expected_set_values:
            logger.error(
                f"资源管理测试失败：集合数据不匹配，期望: {expected_set_values}，实际: {set_values}"
            )
            success = False

        if success:
            logger.info("资源管理测试通过！")
        else:
            logger.error("资源管理测试失败！")

    except Exception as e:
        logger.error(f"资源管理测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    # 测试边界条件
    logger.info("\n" + "=" * 30)
    logger.info("测试边界条件")
    logger.info("=" * 30)

    # 测试空输入
    logger.info("测试空输入...")
    try:
        mp = ManagedMultiProcess([], edge_test_callback, num_processes=2)
        results = mp.run()
        if results.get("processed", []):
            logger.error("边界条件测试失败：空输入测试未通过")
        else:
            logger.info("空输入测试通过")
    except Exception as e:
        logger.error(f"空输入测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    # 测试单进程
    logger.info("测试单进程...")
    try:
        test_data = list(range(3))
        mp = ManagedMultiProcess(test_data, edge_test_callback, num_processes=1)

        # 单进程模式会自动完成所有任务，不需要手动等待
        results = mp.run()

        logger.info(f"单进程测试结果: {results}")

        processed_items = set(results.get("processed", []))
        if processed_items != set(test_data):
            logger.error(
                f"边界条件测试失败：单进程测试未通过，期望: {set(test_data)}，实际: {processed_items}"
            )
        else:
            logger.info("单进程测试通过")
    except Exception as e:
        logger.error(f"单进程测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    # 测试状态信息
    logger.info("\n" + "=" * 30)
    logger.info("测试状态信息")
    logger.info("=" * 30)

    try:
        test_data = list(range(5))
        mp = ManagedMultiProcess(test_data, basic_test_callback, num_processes=2)

        # 获取初始状态
        initial_status = mp.get_status()
        logger.info(f"初始状态: {initial_status}")

        # 启动处理器（不等待完成）
        mp.run()

        # 获取中间状态
        middle_status = mp.get_status()
        logger.info(f"任务提交后状态: {middle_status}")

        # 手动等待任务完成
        mp.wait_all(timeout=30)

        # 获取结果以更新成功/失败计数
        results = mp.get_results()

        # 手动更新成功计数
        success_count = sum(1 for k in results.keys() if k.startswith("result_"))
        mp.success_count = success_count
        mp.error_count = len(results.get("errors", []))

        # 获取最终状态
        final_status = mp.get_status()
        logger.info(f"最终状态: {final_status}")

        # 检查成功处理的任务数量是否与输入数据数量一致
        if final_status["success"] == len(test_data) and final_status["errors"] == 0:
            logger.info("状态信息测试通过！")
        else:
            logger.error(
                f"状态信息测试失败！成功: {final_status['success']}, 预期: {len(test_data)}"
            )

        # 确保清理资源
        mp.stop_all()
    except Exception as e:
        logger.error(f"状态信息测试出错: {str(e)}")
        logger.error(traceback.format_exc())

    logger.info("\n" + "=" * 50)
    logger.info("所有测试完成！")
    logger.info("=" * 50)


def target_func(shared_data, item, lock, *args):
    """目标函数：计算平方并返回结果"""
    for i in range(10):
        print( item )
        time.sleep(0.5)


def main():
    """主函数"""
    print("测试1: 普通停止模式")
    mp = ManagedMultiProcess([1, 2, 3, 4, 5], target_func, num_processes=3)
    mp.run()

    def thread_func():
        time.sleep(1.5)
        print("使用普通停止模式")
        mp.stop_all(immediate=False, force_timeout=1.0)  # 使用较低的超时值

    t = threading.Thread(target=thread_func)
    t.start()
    mp.wait_all()
    t.join()  # 确保线程完成
    print("普通停止模式完成")

    # 等待一段时间，确保前一个测试完全清理
    time.sleep(1)

    print("\n" + "=" * 50)
    print("测试2: 立即停止模式")

    mp2 = ManagedMultiProcess([6, 7, 8, 9, 10], target_func, num_processes=3)
    mp2.run()

    def thread_func2():
        time.sleep(1.5)
        print("使用立即停止模式")
        mp2.stop_all(immediate=True)  # 使用立即停止模式

    t2 = threading.Thread(target=thread_func2)
    t2.start()
    mp2.wait_all()
    t2.join()  # 确保线程完成
    print("立即停止模式完成")


# 主函数，确保多进程代码位于 if __name__ == '__main__': 之下
if __name__ == "__main__":
    # 防止递归启动进程（在Windows平台上特别重要）
    multiprocessing.freeze_support()
    main()
