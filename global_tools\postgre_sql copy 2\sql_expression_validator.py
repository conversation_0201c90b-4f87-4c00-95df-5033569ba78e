#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用SQL表达式验证器

提供健壮的、可扩展的SQL表达式验证功能，支持各种复杂的SQL语法场景。
采用四层验证架构，确保验证的准确性和完整性。

作者: PostgreSQL客户端修复团队
版本: 1.0.0
"""

import re
import logging
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum


class ValidationLevel(Enum):
    """验证严格程度枚举"""
    STRICT = "strict"      # 严格模式：最严格的验证
    NORMAL = "normal"      # 标准模式：平衡的验证
    LOOSE = "loose"        # 宽松模式：最宽松的验证


class ErrorType(Enum):
    """错误类型枚举"""
    SYNTAX_ERROR = "syntax_error"           # 语法错误
    OPERATOR_ERROR = "operator_error"       # 运算符错误
    STRUCTURE_ERROR = "structure_error"     # 结构错误
    SEMANTIC_ERROR = "semantic_error"       # 语义错误
    SINGLE_FIELD = "single_field"          # 单字段（非表达式）
    EMPTY_EXPRESSION = "empty_expression"   # 空表达式


@dataclass
class ValidationResult:
    """
    SQL验证结果类
    
    提供详细的验证结果信息，包括错误诊断和改进建议。
    """
    is_valid: bool = False
    error_type: Optional[ErrorType] = None
    error_message: str = ""
    suggestions: List[str] = field(default_factory=list)
    validated_components: Dict[str, bool] = field(default_factory=dict)
    confidence_score: float = 0.0
    validation_layers: Dict[str, bool] = field(default_factory=dict)
    debug_info: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'is_valid': self.is_valid,
            'error_type': self.error_type.value if self.error_type else None,
            'error_message': self.error_message,
            'suggestions': self.suggestions,
            'validated_components': self.validated_components,
            'confidence_score': self.confidence_score,
            'validation_layers': self.validation_layers,
            'debug_info': self.debug_info
        }


class ValidationConfig:
    """
    验证配置类
    
    提供灵活的验证规则配置，支持自定义运算符和验证级别。
    """
    
    def __init__(self, strictness: ValidationLevel = ValidationLevel.NORMAL):
        self.strictness = strictness
        self.supported_operators = self._init_operators()
        self.sql_keywords = self._init_keywords()
        self.custom_patterns: List[str] = []
        self.logger = logging.getLogger(__name__)
    
    def _init_operators(self) -> Dict[str, List[str]]:
        """初始化支持的运算符模式"""
        return {
            'comparison': [
                r'\s*=\s*', r'\s*!=\s*', r'\s*<>\s*', 
                r'\s*>\s*', r'\s*<\s*', r'\s*>=\s*', r'\s*<=\s*'
            ],
            'logical': [
                r'\s+AND\s+', r'\s+OR\s+', r'\s+NOT\s+'
            ],
            'null_check': [
                r'\s+IS\s+NULL\s*', r'\s+IS\s+NOT\s+NULL\s*',
                r'\s+IS\s+TRUE\s*', r'\s+IS\s+FALSE\s*',
                r'\s+IS\s+NOT\s+TRUE\s*', r'\s+IS\s+NOT\s+FALSE\s*'
            ],
            'pattern_matching': [
                r'\s+LIKE\s+', r'\s+ILIKE\s+', r'\s+REGEXP\s+',
                r'\s+SIMILAR\s+TO\s+', r'\s+~\s*', r'\s+~\*\s*',
                r'\s+!~\s*', r'\s+!~\*\s*'
            ],
            'range_and_set': [
                r'\s+BETWEEN\s+.*?\s+AND\s+', r'\s+IN\s*\([^)]*\)',
                r'\s+NOT\s+IN\s*\([^)]*\)', r'\s+ANY\s*\([^)]*\)',
                r'\s+ALL\s*\([^)]*\)', r'\s+SOME\s*\([^)]*\)'
            ],
            'existence': [
                r'\s+EXISTS\s*\([^)]*\)', r'\s+NOT\s+EXISTS\s*\([^)]*\)'
            ],
            'json_operators': [
                r'\s*->\s*', r'\s*->>\s*', r'\s*#>\s*', r'\s*#>>\s*',
                r'\s*@>\s*', r'\s*<@\s*', r'\s*\?\s*', r'\s*\?\|\s*', r'\s*\?&\s*'
            ],
            'array_operators': [
                r'\s*\|\|\s*', r'\s*&&\s*', r'\s*@>\s*', r'\s*<@\s*'
            ]
        }
    
    def _init_keywords(self) -> Set[str]:
        """初始化SQL关键字集合"""
        return {
            'and', 'or', 'not', 'is', 'null', 'true', 'false',
            'like', 'ilike', 'regexp', 'similar', 'to',
            'between', 'in', 'any', 'all', 'some',
            'exists', 'case', 'when', 'then', 'else', 'end',
            'cast', 'extract', 'substring', 'position',
            'distinct', 'order', 'by', 'group', 'having',
            'limit', 'offset', 'union', 'intersect', 'except'
        }
    
    def add_custom_operator(self, pattern: str, category: str = 'custom'):
        """添加自定义运算符支持"""
        if category not in self.supported_operators:
            self.supported_operators[category] = []
        self.supported_operators[category].append(pattern)
        self.logger.info(f"添加自定义运算符: {pattern} (类别: {category})")
    
    def set_validation_strictness(self, level: ValidationLevel):
        """设置验证严格程度"""
        self.strictness = level
        self.logger.info(f"设置验证严格程度: {level.value}")


class UniversalSQLValidator:
    """
    通用SQL表达式验证器
    
    提供四层验证架构，确保SQL表达式的正确性和安全性：
    1. 基础语法检查：括号匹配、引号匹配、字符合法性
    2. 运算符验证：识别和验证所有SQL运算符
    3. 结构分析：分析表达式的逻辑结构和完整性
    4. 语义检查：验证表达式的语义一致性
    """
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        self.config = config or ValidationConfig()
        self.logger = logging.getLogger(__name__)
        self._validation_cache: Dict[str, ValidationResult] = {}
    
    def validate(self, sql_expr: str, use_cache: bool = True) -> ValidationResult:
        """
        验证SQL表达式
        
        Args:
            sql_expr (str): 要验证的SQL表达式
            use_cache (bool): 是否使用缓存
            
        Returns:
            ValidationResult: 详细的验证结果
        """
        if not sql_expr or not isinstance(sql_expr, str):
            return ValidationResult(
                is_valid=False,
                error_type=ErrorType.EMPTY_EXPRESSION,
                error_message="SQL表达式为空或类型无效",
                confidence_score=1.0
            )
        
        # 缓存检查
        cache_key = f"{sql_expr}_{self.config.strictness.value}"
        if use_cache and cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        # 使用简化验证器进行验证
        result = self._perform_simple_validation(sql_expr)
        
        # 缓存结果
        if use_cache:
            self._validation_cache[cache_key] = result
        
        return result
    
    def _perform_simple_validation(self, sql_expr: str) -> ValidationResult:
        """执行简化验证"""
        from .simple_sql_validator import is_valid_sql_expression, validate_sql_expression_detailed
        
        # 使用简化验证器
        simple_result = validate_sql_expression_detailed(sql_expr)
        
        # 转换为ValidationResult格式
        result = ValidationResult(
            is_valid=simple_result['is_valid'],
            error_type=ErrorType.SINGLE_FIELD if simple_result['error_type'] == 'single_field' else ErrorType.OPERATOR_ERROR,
            error_message=simple_result['error_message'],
            suggestions=simple_result['suggestions'],
            confidence_score=simple_result['confidence_score']
        )
        
        return result
    
    def clear_cache(self):
        """清空验证缓存"""
        self._validation_cache.clear()
        self.logger.info("验证缓存已清空")
