#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试 PROCESS_STOPPED_WITH_DATA 事件
"""

import sys
import os
import time
import unittest
import threading

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager


def test_worker(shared_data_manager, item, *args, **kwargs):
    """测试工作函数"""
    import time
    time.sleep(0.3)  # 模拟一些工作
    
    # 使用锁保证原子性
    lock = shared_data_manager.get_lock()
    with lock:
        counter = shared_data_manager.get_value("counter", 0)
        shared_data_manager.add_value("counter", counter + 1)
    
    shared_data_manager.append_to_list("processed_items", item)
    return f"processed_{item}"


class TestStoppedWithDataEvent(unittest.TestCase):
    """专门测试 PROCESS_STOPPED_WITH_DATA 事件"""
    
    def setUp(self):
        """设置测试"""
        self.events_triggered = []
        self.event_lock = threading.Lock()
        self.manager = None
    
    def tearDown(self):
        """清理测试"""
        if self.manager:
            try:
                self.manager.stop_all(immediate=True)
            except:
                pass
    
    def on_event(self, mp_instance, *args, **kwargs):
        """事件回调函数"""
        event_name = getattr(self, '_current_event_name', 'UNKNOWN')
        with self.event_lock:
            self.events_triggered.append(event_name)
        print(f"🎉 事件触发: {event_name}")
    
    def register_event(self, manager, event_name):
        """注册单个事件"""
        self._current_event_name = event_name
        manager.listen_event(event_name, self.on_event)
    
    def wait_for_event(self, event_name, timeout=10.0):
        """等待特定事件触发"""
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            with self.event_lock:
                if event_name in self.events_triggered:
                    return True
            time.sleep(0.1)
        return False
    
    def test_stopped_with_data_event_basic(self):
        """基础 PROCESS_STOPPED_WITH_DATA 事件测试"""
        print("=" * 80)
        print("基础 PROCESS_STOPPED_WITH_DATA 事件测试")
        print("=" * 80)
        
        # 创建测试数据
        test_data = [f"shared_item_{i}" for i in range(5)]
        print(f"测试数据: {test_data}")
        
        # 创建 ManagedMultiProcess 实例
        self.manager = ManagedMultiProcess(
            input_data=test_data,
            callback_func=test_worker,
            num_processes=2,
            max_queue_size=10
        )
        
        # 注册我们关心的事件
        events_to_monitor = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
        ]
        
        for event in events_to_monitor:
            # 为每个事件创建单独的回调
            def make_callback(event_name):
                def callback(mp_instance, *args, **kwargs):
                    with self.event_lock:
                        self.events_triggered.append(event_name)
                    print(f"🎉 事件触发: {event_name}")
                return callback
            
            self.manager.listen_event(event, make_callback(event))
        
        print("\n启动多进程处理...")
        self.manager.run()
        
        print("等待一段时间让任务开始...")
        time.sleep(1.5)  # 让一些任务开始执行
        
        print("调用 stop_all()...")
        self.manager.stop_all(immediate=False)
        
        print("等待 PROCESS_STOPPED 事件...")
        stopped_triggered = self.wait_for_event(ProcessEventManager.PROCESS_STOPPED, timeout=10.0)
        self.assertTrue(stopped_triggered, "PROCESS_STOPPED 事件应该被触发")
        print("✅ PROCESS_STOPPED 事件已触发")
        
        print("等待 PROCESS_STOPPED_WITH_DATA 事件...")
        stopped_with_data_triggered = self.wait_for_event(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, timeout=10.0)
        
        # 显示调试信息
        print(f"\n调试信息:")
        print(f"触发的事件: {self.events_triggered}")
        
        # 获取最终结果
        try:
            results = self.manager.get_results()
            print(f"最终结果: {results}")
        except Exception as e:
            print(f"获取结果时出错: {e}")
        
        # 验证事件
        self.assertTrue(stopped_with_data_triggered, "PROCESS_STOPPED_WITH_DATA 事件应该被触发")
        print("✅ PROCESS_STOPPED_WITH_DATA 事件已触发")
    
    def test_stopped_with_data_event_with_delay(self):
        """带延迟的 PROCESS_STOPPED_WITH_DATA 事件测试"""
        print("\n" + "=" * 80)
        print("带延迟的 PROCESS_STOPPED_WITH_DATA 事件测试")
        print("=" * 80)
        
        # 创建测试数据
        test_data = [f"item_{i}" for i in range(3)]
        print(f"测试数据: {test_data}")
        
        # 创建 ManagedMultiProcess 实例
        self.manager = ManagedMultiProcess(
            input_data=test_data,
            callback_func=test_worker,
            num_processes=2,
            max_queue_size=10
        )
        
        # 重置事件列表
        with self.event_lock:
            self.events_triggered.clear()
        
        # 只监控我们关心的事件
        def on_stopped_with_data(mp_instance, *args, **kwargs):
            with self.event_lock:
                self.events_triggered.append(ProcessEventManager.PROCESS_STOPPED_WITH_DATA)
            print(f"🎉 PROCESS_STOPPED_WITH_DATA 事件触发!")
        
        self.manager.listen_event(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, on_stopped_with_data)
        
        print("\n启动多进程处理...")
        self.manager.run()
        
        print("等待任务完成...")
        time.sleep(2.0)  # 让任务完成
        
        print("调用 stop_all()...")
        self.manager.stop_all(immediate=False)
        
        print("等待 PROCESS_STOPPED_WITH_DATA 事件...")
        # 给更多时间等待事件
        stopped_with_data_triggered = self.wait_for_event(ProcessEventManager.PROCESS_STOPPED_WITH_DATA, timeout=15.0)
        
        print(f"事件触发状态: {stopped_with_data_triggered}")
        print(f"触发的事件: {self.events_triggered}")
        
        self.assertTrue(stopped_with_data_triggered, "PROCESS_STOPPED_WITH_DATA 事件应该被触发")
        print("✅ 带延迟的 PROCESS_STOPPED_WITH_DATA 事件测试通过")


def main():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    main()
