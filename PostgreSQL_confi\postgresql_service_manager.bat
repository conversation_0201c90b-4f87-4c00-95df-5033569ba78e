@echo off
chcp 936 >nul
setlocal enabledelayedexpansion

:: PostgreSQL 服务管理脚本
:: 作用：提供简单的方式管理PostgreSQL服务
:: 服务名：PostgreSQL_17
:: 数据目录：H:\PostgreSQL_data
:: 端口：9001

title PostgreSQL 服务管理工具

:menu
cls
echo ===========================================================
echo                PostgreSQL 服务管理工具
echo ===========================================================
echo.
echo  [1] 查看 PostgreSQL 服务状态
echo  [2] 启动 PostgreSQL 服务
echo  [3] 停止 PostgreSQL 服务
echo  [4] 重启 PostgreSQL 服务
echo  [5] 检查 PostgreSQL 端口监听状态
echo  [6] 查看 PostgreSQL 配置信息
echo  [7] 设置 PostgreSQL 开机自动启动
echo  [8] 设置 PostgreSQL 手动启动
echo  [9] 查看 PostgreSQL 日志
echo  [0] 退出
echo.
echo ===========================================================
echo.

set /p choice=请输入选项编号:

if "%choice%"=="1" goto :status
if "%choice%"=="2" goto :start
if "%choice%"=="3" goto :stop
if "%choice%"=="4" goto :restart
if "%choice%"=="5" goto :check_port
if "%choice%"=="6" goto :config
if "%choice%"=="7" goto :auto_start
if "%choice%"=="8" goto :manual_start
if "%choice%"=="9" goto :view_log
if "%choice%"=="0" goto :exit

echo 输入无效，请重新选择...
ping 127.0.0.1 -n 2 >nul
goto :menu

:status
cls
echo 正在查询 PostgreSQL 服务状态...
echo.
sc query PostgreSQL_17
echo.
echo PostgreSQL 进程状态:
tasklist | findstr postgres
echo.
echo PostgreSQL 端口状态:
netstat -ano | findstr :9001
echo.
pause
goto :menu

:start
cls
echo 正在启动 PostgreSQL 服务...
echo.
sc start PostgreSQL_17
echo.
ping 127.0.0.1 -n 3 >nul
sc query PostgreSQL_17 | findstr STATE
echo.
pause
goto :menu

:stop
cls
echo 正在停止 PostgreSQL 服务...
echo.
sc stop PostgreSQL_17
echo.
ping 127.0.0.1 -n 3 >nul
sc query PostgreSQL_17 | findstr STATE
echo.
pause
goto :menu

:restart
cls
echo 正在重启 PostgreSQL 服务...
echo.
sc stop PostgreSQL_17
ping 127.0.0.1 -n 3 >nul
sc start PostgreSQL_17
echo.
ping 127.0.0.1 -n 3 >nul
sc query PostgreSQL_17 | findstr STATE
echo.
pause
goto :menu

:check_port
cls
echo 正在检查 PostgreSQL 端口监听状态...
echo.
netstat -ano | findstr :9001
echo.
if %errorlevel% NEQ 0 (
    echo 端口 9001 未被监听，PostgreSQL 可能未正常运行！
) else (
    echo 端口 9001 正在被监听，PostgreSQL 运行正常！
)
echo.
pause
goto :menu

:config
cls
echo PostgreSQL 配置信息:
echo.
echo 服务名称: PostgreSQL_17
echo 安装目录: C:\Program Files\PostgreSQL\17
echo 数据目录: H:\PostgreSQL_data
echo 监听端口: 9001
echo 配置文件: H:\PostgreSQL_data\postgresql.conf
echo 访问控制: H:\PostgreSQL_data\pg_hba.conf
echo.
sc qc PostgreSQL_17
echo.
pause
goto :menu

:auto_start
cls
echo 正在设置 PostgreSQL 为开机自动启动...
echo.
sc config PostgreSQL_17 start= auto
echo.
ping 127.0.0.1 -n 2 >nul
sc qc PostgreSQL_17 | findstr START_TYPE
echo.
pause
goto :menu

:manual_start
cls
echo 正在设置 PostgreSQL 为手动启动...
echo.
sc config PostgreSQL_17 start= demand
echo.
ping 127.0.0.1 -n 2 >nul
sc qc PostgreSQL_17 | findstr START_TYPE
echo.
pause
goto :menu

:view_log
cls
echo PostgreSQL 日志文件:
echo.
dir /b "H:\PostgreSQL_data\log"
echo.
set /p logfile=请输入要查看的日志文件名(直接回车查看最近日志):

if "%logfile%"=="" (
    for /f "tokens=*" %%a in ('dir /b /o-d "H:\PostgreSQL_data\log\postgresql-*.log" 2^>nul') do (
        set "latest_log=%%a"
        goto :show_latest_log
    )
    
    :show_latest_log
    if defined latest_log (
        echo 显示最新日志: !latest_log!
        echo.
        type "H:\PostgreSQL_data\log\!latest_log!"
    ) else (
        echo 未找到日志文件
    )
) else (
    if exist "H:\PostgreSQL_data\log\%logfile%" (
        type "H:\PostgreSQL_data\log\%logfile%"
    ) else (
        echo 日志文件不存在: %logfile%
    )
)
echo.
pause
goto :menu

:exit
cls
echo 正在退出 PostgreSQL 服务管理工具...
ping 127.0.0.1 -n 2 >nul
exit /b

endlocal 