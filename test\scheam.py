"""
此文件定义了用于存储 YOLO 模型推理结果的 PostgreSQL 数据库表结构。

每个表结构都以 Python 字典的形式定义，包含了列、约束和索引等信息，
可以被用于动态生成 SQL CREATE TABLE 语句或被 ORM (对象关系映射) 使用。
"""

# 存储YOLO模型推理出的对象检测结果
DETECTION_RESULTS_TABLE = {
    "table_name": "detection_results",
    "table_comment": "存储YOLO模型推理出的对象检测结果（简化版，边界框和分割数据以JSONB格式存储）",
    "columns": {
        "detection_id": {
            "type": "SERIAL",
            "primary_key": True,
            "comment": "检测结果的唯一标识符，自增主键"
        },
        "image_id": {
            "type": "VARCHAR(500)",
            "nullable": False,
            "comment": "图像的唯一标识符，例如UUID或自定义ID字符串"
        },
        "image_path": {
            "type": "VARCHAR(500)",
            "nullable": False,
            "comment": "图像的完整路径"
        },
        "file_hash": {
            "type": "VARCHAR(64)",
            "nullable": True,
            "comment": "图像文件的SHA256哈希值，用于文件完整性验证"
        },
        "image_width": {
            "type": "INTEGER",
            "nullable": False,
            "comment": "图像宽度(像素)"
        },
        "image_height": {
            "type": "INTEGER",
            "nullable": False,
            "comment": "图像高度(像素)"
        },
        "inference_count": {
            "type": "INTEGER",
            "nullable": False,
            "default": 0,
            "comment": "记录此检测结果产生时，其源图像已被推理的总次数"
        },
        "data_source": {
            "type": "VARCHAR(50)",
            "nullable": False,
            "comment": "标识采集数据的来源或项目类型，例如 '亚基矿', '铋矿'"
        },
        "model_output_type": {
            "type": "VARCHAR(50)",
            "nullable": False,
            "comment": "标识该条记录的模型输出类型，例如 'OBB', 'Seg', 'Detection'"
        },
        "model_md5": {
            "type": "VARCHAR(32)",
            "nullable": True,
            "comment": "用于推理的模型文件的MD5哈希值"
        },
        "training_type": {
            "type": "JSONB",
            "nullable": True,
            "comment": "存储训练类型，例如 {'type': '亚基矿', 'mode': 'A', 'version': 'v1.2'}"
        },
        "obb_data": {
            "type": "JSONB",
            "nullable": True,
            "comment": '存储OBB（旋转边界框）数据，例如 {"x_center": 0.5, "y_center": 0.5, "width": 0.1, "height": 0.2, "angle": 45.0}'
        },
        "segmentation_data": {
            "type": "JSONB",
            "nullable": True,
            "comment": '存储实例分割的多边形坐标点，例如 [[x1, y1, x2, y2, ...]]'
        },
        "created_at": {
            "type": "TIMESTAMP WITH TIME ZONE",
            "default": "CURRENT_TIMESTAMP",
            "nullable": False,
            "comment": "检测结果创建时间"
        },
        "is_appended_annotation": {
            "type": "BOOLEAN",
            "nullable": False,
            "default": False,
            "comment": "标识当前数据条目是否为后续追加的标注数据。True表示是追加的标注数据，False表示是原始标注数据"
        }
    },
    "constraints": [],
    # 索引 (Indexes) 是数据库中用于加速查询速度的特殊数据结构。
    # 它们通过创建指向表中数据的快速访问路径来实现，类似于书的目录。
    # 虽然索引会占用额外的存储空间并可能略微减慢写入操作的速度，
    # 但对于频繁执行的读取查询，其性能提升是巨大的。
    "indexes": [
        {
            "columns": ["image_id"],
            "name": "idx_detection_image_id",
            "comment": "加速根据图像唯一标识符查找所有相关检测结果的查询"
        },
        {
            "columns": ["inference_count"],
            "name": "idx_detection_inference_count",
            "comment": "加速根据图像被推理的次数进行筛选的查询"
        },
        {
            "columns": ["data_source"],
            "name": "idx_detection_data_source",
            "comment": "加速根据数据来源（项目类型）筛选检测结果的查询"
        },
        {
            "columns": ["model_output_type"],
            "name": "idx_detection_model_output_type",
            "comment": "加速根据模型输出类型进行筛选的查询"
        },
        {
            "columns": ["training_type"],
            "name": "idx_detection_training_type",
            "using": "GIN",
            "comment": "GIN索引，用于高效查询JSONB格式的训练类型数据"
        },
        {
            "columns": ["obb_data"], 
            "name": "idx_detection_obb_data", 
            "using": "GIN",
            "comment": "GIN索引，专门用于高效查询JSONB字段内部的键值对。例如，快速查找所有角度为45度的OBB数据"
        },
        {
            "columns": ["segmentation_data"], 
            "name": "idx_detection_segmentation_data", 
            "using": "GIN",
            "comment": "GIN索引，用于高效查询JSONB格式的分割数据"
        }
    ]
}
