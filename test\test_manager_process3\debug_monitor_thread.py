#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试自动监控线程的行为
"""

import sys
import os
import time
import threading

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager


def simple_worker(shared_data_manager, item, *args, **kwargs):
    """简单的工作函数"""
    import time
    time.sleep(0.2)  # 很短的工作时间
    shared_data_manager.append_to_list("results", f"processed_{item}")
    return f"result_{item}"


def test_monitor_thread_behavior():
    """测试自动监控线程的行为"""
    print("=" * 80)
    print("调试自动监控线程的行为")
    print("=" * 80)
    
    # 创建测试数据
    test_data = [f"item_{i}" for i in range(3)]
    print(f"测试数据: {test_data}")
    
    # 事件触发记录
    events_triggered = []
    event_lock = threading.Lock()
    
    def on_event(event_name):
        def callback(mp_instance, *args, **kwargs):
            with event_lock:
                events_triggered.append(event_name)
            print(f"🎉 事件触发: {event_name}")
        return callback
    
    # 创建 ManagedMultiProcess 实例
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=simple_worker,
        num_processes=2,
        max_queue_size=10
    )
    
    try:
        # 注册所有事件
        all_events = [
            ProcessEventManager.PROCESS_CREATED,
            ProcessEventManager.PROCESS_COMPLETED,
            ProcessEventManager.PROCESS_COMPLETED_WITH_DATA,
            ProcessEventManager.PROCESS_STOPPED,
            ProcessEventManager.PROCESS_STOPPED_WITH_DATA,
        ]
        
        for event in all_events:
            manager.listen_event(event, on_event(event))
        
        print("\n启动多进程处理...")
        manager.run()
        
        print("等待任务完成...")
        time.sleep(3.0)  # 让所有任务完成
        
        print(f"任务完成后的事件: {events_triggered}")
        
        # 检查自动监控线程状态
        if hasattr(manager, '_ManagedMultiProcess__auto_event_monitor_thread'):
            monitor_thread = manager._ManagedMultiProcess__auto_event_monitor_thread
            if monitor_thread:
                print(f"自动监控线程状态: alive={monitor_thread.is_alive()}")
            else:
                print("自动监控线程为 None")
        else:
            print("没有找到自动监控线程属性")
        
        print("\n现在调用 stop_all()...")
        manager.stop_all(immediate=False)
        
        print("等待 stop 相关事件...")
        time.sleep(3.0)
        
        print(f"最终事件列表: {events_triggered}")
        
        # 检查是否触发了 PROCESS_STOPPED_WITH_DATA
        if ProcessEventManager.PROCESS_STOPPED_WITH_DATA in events_triggered:
            print("✅ PROCESS_STOPPED_WITH_DATA 事件已触发")
        else:
            print("❌ PROCESS_STOPPED_WITH_DATA 事件未触发")
            
            # 分析可能的原因
            print("\n可能的原因分析:")
            if ProcessEventManager.PROCESS_COMPLETED_WITH_DATA in events_triggered:
                print("- PROCESS_COMPLETED_WITH_DATA 已触发，监控线程可能已退出")
            if ProcessEventManager.PROCESS_STOPPED not in events_triggered:
                print("- PROCESS_STOPPED 事件也未触发")
            
            # 检查进程状态
            if hasattr(manager, 'worker_processes') and manager.worker_processes:
                alive_count = sum(1 for p in manager.worker_processes if p.is_alive())
                print(f"- 当前活跃进程数: {alive_count}")
        
        # 获取最终结果
        try:
            results = manager.get_results()
            print(f"\n最终结果: {results}")
        except Exception as e:
            print(f"获取结果时出错: {e}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            manager.stop_all(immediate=True)
        except:
            pass


if __name__ == "__main__":
    test_monitor_thread_behavior()
