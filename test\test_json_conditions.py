#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库客户端JSON条件查询功能测试

测试 yolo_obb 表中JSON字段（training_type、obb_data、segmentation_data）的复杂查询功能。
验证 fetch_data 方法对PostgreSQL JSON/JSONB运算符的支持。

测试覆盖：
1. JSON字段存在性检查
2. JSON属性值查询
3. JSON数组元素查询
4. 复杂JSON路径查询
5. 复杂条件组合
6. 边界情况处理
"""

import sys
import os
import json
import unittest
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from global_tools.postgre_sql import PostgreSQLClient


class TestJSONConditions(unittest.TestCase):
    """JSON条件查询测试类"""

    def setUp(self):
        """每个测试方法前的初始化 - 建立数据库连接"""
        self.client = PostgreSQLClient(
            host="localhost",
            port=5432,
            database="wow_data",
            user="postgres",
            password="123456",
            min_connections=1,
            max_connections=5
        )

    def tearDown(self):
        """每个测试方法后的清理 - 关闭数据库连接"""
        if hasattr(self, 'client'):
            self.client.close()
    
    def _execute_and_validate(self, condition: str, description: str, 
                             expect_success: bool = True, 
                             min_records: int = 0,
                             max_records: Optional[int] = None) -> Dict[str, Any]:
        """
        执行查询并验证结果
        
        Args:
            condition: SQL条件字符串
            description: 测试描述
            expect_success: 是否期望查询成功
            min_records: 最少记录数
            max_records: 最多记录数
            
        Returns:
            查询结果字典
        """
        print(f"\n测试: {description}")
        print(f"条件: {condition}")
        
        result = self.client.fetch_data(
            table_name="yolo_obb",
            condition_str=condition
        )
        
        # 验证基本结构
        self.assertIsInstance(result, dict, "返回结果应该是字典")
        self.assertIn('success', result, "结果应包含success字段")
        
        if expect_success:
            self.assertTrue(result['success'], f"查询应该成功: {result.get('error', '')}")
            self.assertIn('data', result, "成功结果应包含data字段")
            self.assertIn('count', result, "成功结果应包含count字段")
            
            record_count = result['count']
            print(f"   ✅ 查询成功，返回 {record_count} 条记录")
            
            # 验证记录数范围
            self.assertGreaterEqual(record_count, min_records, 
                                  f"记录数应不少于 {min_records}")
            if max_records is not None:
                self.assertLessEqual(record_count, max_records, 
                                   f"记录数应不超过 {max_records}")
        else:
            self.assertFalse(result['success'], "查询应该失败")
            print(f"   ✅ 查询按预期失败: {result.get('error', '')}")
        
        return result


class TestJSONExistenceConditions(TestJSONConditions):
    """JSON字段存在性检查测试"""
    
    def test_training_type_not_null(self):
        """测试 training_type 字段非空查询"""
        result = self._execute_and_validate(
            condition="training_type is not null",
            description="training_type 字段存在性检查",
            min_records=1
        )
        
        # 验证返回的记录确实包含 training_type 数据
        if result['data']:
            first_record = result['data'][0]
            self.assertIsNotNone(first_record.get('training_type'), 
                               "返回的记录应包含非空的training_type")
    
    def test_obb_data_not_null(self):
        """测试 obb_data 字段非空查询"""
        result = self._execute_and_validate(
            condition="obb_data is not null",
            description="obb_data 字段存在性检查",
            min_records=1
        )
        
        # 验证返回的记录确实包含 obb_data 数据
        if result['data']:
            first_record = result['data'][0]
            self.assertIsNotNone(first_record.get('obb_data'), 
                               "返回的记录应包含非空的obb_data")
    
    def test_segmentation_data_null(self):
        """测试 segmentation_data 字段为空查询"""
        result = self._execute_and_validate(
            condition="segmentation_data is null",
            description="segmentation_data 字段为空检查",
            min_records=1
        )
        
        # 验证返回的记录确实 segmentation_data 为空
        if result['data']:
            first_record = result['data'][0]
            self.assertIsNone(first_record.get('segmentation_data'), 
                            "返回的记录的segmentation_data应为空")


class TestJSONPropertyQueries(TestJSONConditions):
    """JSON属性值查询测试"""
    
    def test_training_type_property_query(self):
        """测试 training_type JSON属性查询"""
        result = self._execute_and_validate(
            condition="training_type->>'train' = 'true'",
            description="查询 training_type.train = 'true' 的记录",
            min_records=1
        )
        
        # 验证返回的记录确实符合条件
        if result['data']:
            first_record = result['data'][0]
            training_type = first_record.get('training_type')
            self.assertIsInstance(training_type, dict, "training_type应该是字典")
            self.assertEqual(training_type.get('train'), 'true', 
                           "training_type.train应该等于'true'")
    
    def test_training_type_pretrain_query(self):
        """测试 training_type pretrain_train 属性查询"""
        result = self._execute_and_validate(
            condition="training_type->>'pretrain_train' = 'true'",
            description="查询 training_type.pretrain_train = 'true' 的记录",
            min_records=1
        )
        
        # 验证返回的记录确实符合条件
        if result['data']:
            first_record = result['data'][0]
            training_type = first_record.get('training_type')
            self.assertIsInstance(training_type, dict, "training_type应该是字典")
            self.assertEqual(training_type.get('pretrain_train'), 'true', 
                           "training_type.pretrain_train应该等于'true'")
    
    def test_json_property_contains_check(self):
        """测试 JSON属性包含检查"""
        result = self._execute_and_validate(
            condition="training_type ? 'train'",
            description="检查 training_type 是否包含 'train' 键",
            min_records=1
        )


class TestJSONArrayQueries(TestJSONConditions):
    """JSON数组查询测试"""
    
    def test_obb_data_array_contains(self):
        """测试 obb_data 数组包含查询"""
        result = self._execute_and_validate(
            condition="obb_data @> '[{\"label\": \"亚基矿\"}]'",
            description="查询 obb_data 包含 label='亚基矿' 的记录",
            min_records=1
        )
        
        # 验证返回的记录确实包含指定标签
        if result['data']:
            first_record = result['data'][0]
            obb_data = first_record.get('obb_data')
            self.assertIsInstance(obb_data, list, "obb_data应该是列表")
            
            # 检查是否包含指定标签
            has_target_label = any(
                item.get('label') == '亚基矿' 
                for item in obb_data 
                if isinstance(item, dict)
            )
            self.assertTrue(has_target_label, "obb_data应包含label='亚基矿'的元素")
    
    def test_obb_data_score_query(self):
        """测试 obb_data 分数查询"""
        result = self._execute_and_validate(
            condition="obb_data->>0->>'score' > '0.9'",
            description="查询第一个obb_data元素的score > 0.9的记录",
            min_records=0  # 可能没有符合条件的记录
        )
    
    def test_obb_data_array_length(self):
        """测试 obb_data 数组长度查询"""
        result = self._execute_and_validate(
            condition="jsonb_array_length(obb_data) >= 2",
            description="查询 obb_data 数组长度 >= 2 的记录",
            min_records=1
        )


class TestComplexJSONConditions(TestJSONConditions):
    """复杂JSON条件组合测试"""
    
    def test_json_and_traditional_conditions(self):
        """测试 JSON条件与传统SQL条件组合"""
        result = self._execute_and_validate(
            condition="training_type->>'train' = 'true' and detection_id > 50",
            description="JSON条件与传统条件的AND组合",
            min_records=1
        )
    
    def test_multiple_json_conditions(self):
        """测试多个JSON条件组合"""
        result = self._execute_and_validate(
            condition="training_type->>'train' = 'true' and obb_data is not null",
            description="多个JSON条件的AND组合",
            min_records=1
        )
    
    def test_json_or_conditions(self):
        """测试JSON条件的OR组合"""
        result = self._execute_and_validate(
            condition="training_type->>'train' = 'true' or segmentation_data is not null",
            description="JSON条件的OR组合",
            min_records=1
        )
    
    def test_nested_json_conditions(self):
        """测试嵌套JSON条件"""
        result = self._execute_and_validate(
            condition="(training_type->>'train' = 'true' and obb_data @> '[{\"label\": \"亚基矿\"}]') or detection_id < 60",
            description="嵌套的复杂JSON条件组合",
            min_records=1
        )


class TestJSONEdgeCases(TestJSONConditions):
    """JSON边界情况测试"""
    
    def test_invalid_json_path(self):
        """测试无效的JSON路径"""
        result = self._execute_and_validate(
            condition="training_type->>'nonexistent_key' = 'value'",
            description="查询不存在的JSON键",
            expect_success=True,
            min_records=0  # 应该返回0条记录
        )
    
    def test_json_type_conversion(self):
        """测试JSON类型转换"""
        result = self._execute_and_validate(
            condition="(obb_data->>0->>'score')::float > 0.9",
            description="JSON值类型转换为float进行比较",
            min_records=0  # 可能没有符合条件的记录
        )
    
    def test_json_null_handling(self):
        """测试JSON NULL值处理"""
        result = self._execute_and_validate(
            condition="training_type->>'nonexistent' is null",
            description="检查不存在的JSON键是否为null",
            min_records=1
        )
    
    def test_complex_json_path(self):
        """测试复杂JSON路径查询"""
        result = self._execute_and_validate(
            condition="obb_data #> '{0,points,0,0}' is not null",
            description="使用JSON路径运算符查询嵌套数据",
            min_records=1
        )


class TestJSONPerformance(TestJSONConditions):
    """JSON查询性能测试"""
    
    def test_json_query_performance(self):
        """测试JSON查询性能"""
        import time
        
        conditions = [
            "training_type->>'train' = 'true'",
            "obb_data @> '[{\"label\": \"亚基矿\"}]'",
            "jsonb_array_length(obb_data) >= 2"
        ]
        
        for condition in conditions:
            start_time = time.time()
            
            result = self._execute_and_validate(
                condition=condition,
                description=f"性能测试: {condition}",
                min_records=0
            )
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            
            print(f"   执行时间: {execution_time:.2f} ms")
            
            # 验证执行时间合理（应该在1秒内完成）
            self.assertLess(execution_time, 1000, 
                          f"JSON查询执行时间应少于1秒，实际: {execution_time:.2f}ms")


def run_json_tests():
    """运行所有JSON条件测试"""
    print("=" * 80)
    print("PostgreSQL JSON条件查询功能测试")
    print("=" * 80)
    
    # 创建测试套件
    test_classes = [
        TestJSONExistenceConditions,
        TestJSONPropertyQueries, 
        TestJSONArrayQueries,
        TestComplexJSONConditions,
        TestJSONEdgeCases,
        TestJSONPerformance
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果总结
    print("\n" + "=" * 80)
    print("JSON条件查询测试结果总结")
    print("=" * 80)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success_count = total_tests - failures - errors
    
    print(f"总测试数: {total_tests}")
    print(f"成功: {success_count}")
    print(f"失败: {failures}")
    print(f"错误: {errors}")
    print(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if success_count == total_tests:
        print(f"\n🎉 所有JSON条件查询测试通过！PostgreSQL客户端JSON功能完全正常！")
        return True
    else:
        print(f"\n⚠️  部分测试未通过，需要进一步优化")
        return False


if __name__ == "__main__":
    success = run_json_tests()
    sys.exit(0 if success else 1)
