#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试共享数据管理器传递问题
"""

import sys
import os
import time
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from global_tools.utils.manager_process3.managed_multi_process import ManagedMultiProcess

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def debug_worker(shared_data_manager, item, *args, **kwargs):
    """
    调试工作函数，用于检查 shared_data_manager 的类型

    Args:
        shared_data_manager: 共享数据管理器（应该是代理对象）
        item: 要处理的数据项
        *args, **kwargs: 额外参数

    Returns:
        处理结果
    """
    import sys

    # 强制输出到标准输出
    sys.stdout.write(f"DEBUG: shared_data_manager 类型: {type(shared_data_manager)}\n")
    sys.stdout.write(f"DEBUG: shared_data_manager 值: {shared_data_manager}\n")
    sys.stdout.write(f"DEBUG: item 类型: {type(item)}\n")
    sys.stdout.write(f"DEBUG: item 值: {item}\n")
    sys.stdout.flush()

    # 尝试调用方法
    try:
        if hasattr(shared_data_manager, 'get_value'):
            result = shared_data_manager.get_value("test", "default")
            sys.stdout.write(f"DEBUG: get_value 调用成功: {result}\n")

            # 测试设置值
            shared_data_manager.add_value("test", f"value_from_{item}")
            new_result = shared_data_manager.get_value("test", "default")
            sys.stdout.write(f"DEBUG: 设置后 get_value 结果: {new_result}\n")
        else:
            sys.stdout.write(f"DEBUG: shared_data_manager 没有 get_value 方法\n")
            sys.stdout.write(f"DEBUG: 可用方法: {dir(shared_data_manager)}\n")
    except Exception as e:
        sys.stdout.write(f"DEBUG: 调用 get_value 失败: {e}\n")

    sys.stdout.flush()
    return f"debug_{item}"


def test_shared_data_manager():
    """测试共享数据管理器的传递"""
    print("=" * 80)
    print("调试共享数据管理器传递问题")
    print("=" * 80)
    
    # 创建测试数据
    test_data = ["item1", "item2", "item3"]
    
    # 创建 ManagedMultiProcess 实例（强制多进程模式）
    manager = ManagedMultiProcess(
        input_data=test_data,
        callback_func=debug_worker,
        num_processes=3,  # 使用3个进程确保是多进程模式
        max_queue_size=10
    )
    
    try:
        # 启动处理器并等待完成
        print("启动多进程管理器...")
        results = manager.run()

        # 等待所有任务完成
        print("等待任务完成...")
        manager.wait_all(timeout=10)

        # 获取最终结果
        print("获取最终结果...")
        results = manager.get_results()
        
        print(f"处理结果: {results}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理
        try:
            manager.stop_all(immediate=True)
        except:
            pass


if __name__ == "__main__":
    test_shared_data_manager()
