import multiprocessing
from multiprocessing.managers import DictProxy, ListProxy, AcquirerProxy
from typing import Any, Dict, List, Set, Optional
import logging
from multiprocessing.managers import BaseManager


class SharedDataManager:
    """
    共享数据管理器（简化版）
    ----------------------
    提供多进程安全的共享 dict、list、set 以及锁对象。
    适用于多进程间共享和同步数据。

    用法示例：
    --------
    >>> manager = SharedDataManager()
    >>> lock = manager.get_lock()
    >>> with lock:
    ...     manager.set('key', 'value')
    ...     manager.append_to_list('mylist', 123)
    ...     manager.add_to_set('myset', 'abc')
    >>> with lock:
    ...     print(manager.get('key'))
    ...     print(manager.get_list('mylist'))
    ...     print(manager.get_set('myset'))
    >>> manager.shutdown()
    """

    def __init__(self):
        self._manager = multiprocessing.Manager()
        self._dict = self._manager.dict()
        self._lock = self._manager.Lock()
        self._lists = self._manager.dict()  # key -> ListProxy
        self._sets = self._manager.dict()   # key -> ListProxy (set 用 list 存储)

    def get_lock(self) -> AcquirerProxy:
        """获取多进程锁对象"""
        return self._lock

    def set(self, key: str, value: Any) -> None:
        """设置共享字典的键值对"""
        self._dict[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """获取共享字典的值"""
        return self._dict.get(key, default)

    def get_all(self) -> Dict[str, Any]:
        """获取共享字典的所有数据（普通 dict）"""
        return dict(self._dict)

    def append_to_list(self, key: str, value: Any) -> None:
        """向指定 key 的共享列表追加元素"""
        if key not in self._lists:
            self._lists[key] = self._manager.list()
        self._lists[key].append(value)

    def get_list(self, key: str, default: Optional[List[Any]] = None) -> Optional[List[Any]]:
        """获取指定 key 的共享列表（普通 list）"""
        if key not in self._lists:
            return default
        return list(self._lists[key])

    def add_to_set(self, key: str, value: Any) -> None:
        """向指定 key 的共享集合添加元素（内部用 list 存储，自动去重）"""
        if key not in self._sets:
            self._sets[key] = self._manager.list()
        if value not in self._sets[key]:
            self._sets[key].append(value)

    def get_set(self, key: str, default: Optional[Set[Any]] = None) -> Optional[Set[Any]]:
        """获取指定 key 的共享集合（普通 set）"""
        if key not in self._sets:
            return default
        return set(self._sets[key])

    def shutdown(self):
        """关闭管理器，释放资源（进程结束时调用）"""
        try:
            self._dict.clear()
            self._lists.clear()
            self._sets.clear()
            self._manager.shutdown()
        except Exception:
            pass


"""
使用说明：
---------
1. 创建实例：manager = SharedDataManager()
2. 获取锁：lock = manager.get_lock()
3. 在 with lock: 代码块中安全地读写共享数据。
4. 支持 set/get、append_to_list/get_list、add_to_set/get_set。
5. 用完后调用 manager.shutdown() 释放资源。

详细注释见各方法说明。
"""

# =====================
# DataManagerManager 实现
# =====================


class DataManagerManager(BaseManager):
    """
    DataManagerManager
    -----------------
    用于注册和管理 SharedDataManager 的自定义 Manager 类。
    支持本地/远程多进程共享数据。

    用法示例：
    --------
    >>> from global_tools.utils.enhanced_process.shared_data import DataManagerManager
    >>> manager = DataManagerManager(address=("127.0.0.1", 50000), authkey=b"abc")
    >>> manager.start()
    >>> shared_data = manager.SharedDataManager()
    >>> # 现在 shared_data 可在多个进程/主机间共享
    >>> with shared_data.get_lock():
    ...     shared_data.set('key', 'value')
    ...     shared_data.append_to_list('mylist', 123)
    >>> manager.shutdown()
    """

    def __init__(self, *args, **kwargs):
        # 默认参数
        filtered_kwargs = {}
        filtered_kwargs['authkey'] = kwargs.get('authkey', b'enhanced_process')
        filtered_kwargs['address'] = kwargs.get('address', ('127.0.0.1', 0))
        super().__init__(*args, **filtered_kwargs)
        # 注册 SharedDataManager
        self.register('SharedDataManager', SharedDataManager)

    def start(self):
        """启动管理器进程"""
        return super().start()

    def shutdown(self):
        """关闭管理器进程"""
        try:
            super().shutdown()
        except Exception as e:
            logging.warning(f"DataManagerManager shutdown error: {e}")


"""
使用说明：
---------
1. 创建 DataManagerManager 实例，可指定 address/authkey。
2. 调用 start() 启动进程。
3. 通过 manager.SharedDataManager() 获取共享数据实例。
4. 在多个进程/主机间共享数据。
5. 用完后调用 manager.shutdown() 释放资源。

详细注释见各方法说明。
"""
