# Global Tools 工具包文档

## 目录

1. [简介](#简介)
2. [安装](#安装)
3. [模块结构](#模块结构)
4. [核心模块](#核心模块)
    - [Colors 颜色工具](#colors-颜色工具)
    - [Logger 日志工具](#logger-日志工具)
    - [Singleton 单例装饰器](#singleton-单例装饰器)
    - [Window Operation 窗口操作](#window-operation-窗口操作模块)
        - [简介与特性](#简介)
        - [安装依赖](#安装依赖)
        - [核心类和函数](#核心类和函数)
            - [capture_window_handle](#1-capture_window_handle-函数)
            - [GetSpecifiedWindowHandle](#2-getspecifiedwindowhandle-类)
            - [WindowHandleManager](#3-windowhandlemanager-类)
            - [WindowCapture](#4-windowcapture-类)
        - [异常处理](#异常处理)
        - [注意事项](#注意事项)
5. [使用示例](#使用示例)
6. [VSCode 调试配置](#vscode-调试配置)
7. [注意事项](#注意事项)

## 简介

Global Tools 是一个功能强大的 Python 工具包，提供了一系列实用工具类和函数，包括颜色管理、日志记录和单例模式实现等功能。该工具包设计简洁，易于使用，同时具有高度的可扩展性。

## 安装

```bash
pip install global-tools
```

## 模块结构

```
global_tools/
├── __init__.py          # 包初始化文件
└── utils/               # 工具模块目录
    ├── __init__.py      # 工具模块初始化文件
    ├── colors.py        # 颜色工具类
    └── helper.py        # 辅助工具类（日志、单例等）
```

## 核心模块

### Colors 颜色工具

`Colors` 类提供了一组预定义的 16 进制颜色常量，可用于日志输出和其他需要颜色的场景。

#### 主要特性

-   提供多种颜色系列（基础色、暖色系、冷色系、自然色系等）
-   支持 16 进制颜色值
-   包含特殊用途颜色（成功、错误、警告等）
-   提供颜色查询和打印功能

#### 可用颜色分类

1. 基础颜色

    - BLACK: #000000 (黑色)
    - WHITE: #FFFFFF (白色)
    - RED: #FF0000 (红色)
    - GREEN: #00FF00 (绿色)
    - BLUE: #0000FF (蓝色)
    - 等...

2. 暖色系

    - ORANGE: #FFA500 (橙色)
    - CORAL: #FF7F50 (珊瑚色)
    - TOMATO: #FF6347 (番茄红)
    - 等...

3. 冷色系

    - TEAL: #008080 (青蓝色)
    - TURQUOISE: #40E0D0 (绿宝石色)
    - SKYBLUE: #87CEEB (天蓝色)
    - 等...

4. 特殊用途
    - SUCCESS: #28A745 (成功提示色)
    - ERROR: #DC3545 (错误提示色)
    - WARNING: #FFC107 (警告提示色)
    - INFO: #17A2B8 (信息提示色)

#### 方法说明

1. `get_all_colors()`

    ```python
    @classmethod
    def get_all_colors(cls) -> dict:
        """
        获取所有颜色常量及其对应的颜色值

        Returns:
            dict: 颜色名称和颜色值的字典
        """
    ```

2. `print_all_colors()`
    ```python
    @classmethod
    def print_all_colors(cls):
        """
        打印所有可用的颜色名称及其对应的颜色值
        """
    ```

#### 使用示例

```python
from global_tools import Colors

# 使用预定义颜色
print(f"这是红色文本，使用颜色值：{Colors.RED}")

# 获取所有颜色
all_colors = Colors.get_all_colors()
print(f"可用颜色总数：{len(all_colors)}")

# 打印所有颜色
Colors.print_all_colors()
```

### Logger 日志工具

`Logger` 类提供了一个功能强大的日志记录系统，支持控制台彩色输出和文件记录。

#### 主要特性

1. 支持彩色输出
2. 记录文件名和行号
3. 记录时间戳
4. 支持多种日志级别
5. 支持日志文件写入
6. 支持日志缓存
7. 支持日志文件大小限制
8. 支持日志文件数量限制
9. 支持自定义消息颜色

#### 初始化参数

```python
def __init__(
    self,
    cache_size: int = 100,          # 日志缓存大小
    enable_file_logging: bool = True,# 是否启用文件日志
    log_dir: str = "logs",          # 日志目录
    max_file_size_mb: float = 100.0,# 单个日志文件最大大小(MB)
    max_file_count: int = 10        # 最大日志文件数量
)
```

#### 日志级别

-   DEBUG: 调试信息（紫色）
-   INFO: 一般信息（蓝色）
-   WARNING: 警告信息（黄色）
-   ERROR: 错误信息（红色）
-   CRITICAL: 严重错误（亮红色）

#### 主要方法

1. `debug(message: str, color=None, truncate_length: int = 100)`

    ```python
    def debug(self, message: str, color=None, truncate_length: int = 100):
        """
        记录调试级别的日志

        Args:
            message: 日志消息
            color: 消息颜色（可选，支持16进制颜色值或 colorama 颜色）
            truncate_length: 控制台显示的消息最大长度
        """
    ```

2. `info(message: str, color=None, truncate_length: int = 100)`
3. `warning(message: str, color=None, truncate_length: int = 100)`
4. `error(message: str, color=None, truncate_length: int = 100)`
5. `critical(message: str, color=None, truncate_length: int = 100)`
6. `flush()`：强制将缓存中的日志写入文件

#### 使用示例

```python
from global_tools import Logger, Colors

# 创建日志记录器
logger = Logger(
    cache_size=50,           # 50条日志写入一次文件
    enable_file_logging=True,# 启用文件日志
    log_dir="my_logs",      # 自定义日志目录
    max_file_size_mb=50.0,  # 单个日志文件最大50MB
    max_file_count=5        # 最多保留5个日志文件
)

# 记录不同级别的日志
logger.debug("这是一条调试信息")
logger.info("这是一条普通信息", color=Colors.BLUE)
logger.warning("这是一条警告信息", color="#FFA500")
logger.error("这是一条错误信息")
logger.critical("这是一条严重错误信息")

# 使用上下文管理器自动刷新缓存
with logger:
    logger.info("这条日志会在上下文退出时自动写入文件")
```

### Singleton 单例装饰器

`Singleton` 装饰器提供了一个改进的单例模式实现。

#### 主要特性

1. 每个类使用独立的锁确保线程安全
2. 使用弱引用字典避免内存泄漏
3. 支持多个类的单例管理
4. 提供实例管理和查询功能

#### 类方法

1. `clear_instance(target_cls)`

    ```python
    @classmethod
    def clear_instance(cls, target_cls):
        """
        清除指定类的单例实例

        Args:
            target_cls: 目标类
        """
    ```

2. `clear_all_instances()`

    ```python
    @classmethod
    def clear_all_instances(cls):
        """
        清除所有类的单例实例
        """
    ```

3. `get_instance(target_cls)`

    ```python
    @classmethod
    def get_instance(cls, target_cls):
        """
        获取指定类的单例实例

        Args:
            target_cls: 目标类

        Returns:
            目标类的实例，如果不存在则返回 None
        """
    ```

4. `has_instance(target_cls)`

    ```python
    @classmethod
    def has_instance(cls, target_cls) -> bool:
        """
        检查指定类是否有单例实例

        Args:
            target_cls: 目标类

        Returns:
            bool: 是否存在实例
        """
    ```

5. `get_instance_info()`

    ```python
    def get_instance_info(self):
        """
        获取实例信息

        Returns:
            dict: 包含实例信息的字典
        """
    ```

#### 使用示例

```python
from global_tools import Singleton

# 创建单例类
@Singleton
class MyClass:
    def __init__(self, value):
        self.value = value

# 创建实例
instance1 = MyClass(1)
instance2 = MyClass(2)  # 返回相同的实例

# 检查实例
print(instance1 is instance2)  # True
print(MyClass.get_instance_info())

# 清除实例
Singleton.clear_instance(MyClass)

# 检查实例是否存在
print(Singleton.has_instance(MyClass))  # False
```

## 使用示例

### 综合示例

```python
from global_tools import Logger, Colors, Singleton

# 创建单例日志记录器
@Singleton
class MyLogger(Logger):
    def __init__(self):
        super().__init__(
            cache_size=50,
            log_dir="app_logs"
        )

# 使用日志记录器
logger = MyLogger()

# 记录不同颜色的日志
logger.info("启动应用程序", color=Colors.SUCCESS)
logger.debug("调试信息", color=Colors.CYAN)
logger.warning("警告信息", color=Colors.ORANGE)

# 确保日志写入文件
logger.flush()
```

## VSCode 调试配置

### launch.json 配置说明

VSCode 的 `launch.json` 文件用于配置 Python 程序的运行和调试选项。以下是详细的配置说明：

#### 基础配置结构

```json
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Python: 当前文件",
			"type": "debugpy",
			"request": "launch",
			"program": "${file}",
			"console": "integratedTerminal",
			"justMyCode": true
		}
	]
}
```

#### 配置项说明

1. **通用参数**

    - `version`: 配置文件版本号
    - `configurations`: 调试配置数组

2. **每个配置项的核心参数**

    - `name`: 配置名称，显示在调试下拉菜单中
    - `type`: 调试器类型，Python 使用 "debugpy"
    - `request`: 请求类型
        - `launch`: 启动新程序
        - `attach`: 附加到已运行的程序
    - `program`: 要运行的程序文件路径
    - `console`: 终端类型
        - `integratedTerminal`: VSCode 集成终端
        - `externalTerminal`: 外部终端
        - `internalConsole`: 调试控制台

3. **常用变量**
    - `${file}`: 当前打开的文件
    - `${workspaceFolder}`: 工作区根目录
    - `${fileDirname}`: 当前文件所在目录
    - `${fileBasename}`: 当前文件名
    - `${fileBasenameNoExtension}`: 不带扩展名的文件名

#### 常用配置示例

1. **调试当前文件**

```json
{
	"name": "Python: 当前文件",
	"type": "debugpy",
	"request": "launch",
	"program": "${file}",
	"console": "integratedTerminal",
	"justMyCode": true
}
```

2. **调试指定文件**

```json
{
	"name": "Python: 指定文件",
	"type": "debugpy",
	"request": "launch",
	"program": "${workspaceFolder}/test.py",
	"console": "integratedTerminal",
	"justMyCode": true
}
```

3. **带参数调试**

```json
{
	"name": "Python: 带参数调试",
	"type": "debugpy",
	"request": "launch",
	"program": "${file}",
	"console": "integratedTerminal",
	"args": ["arg1", "arg2"],
	"env": {
		"PYTHONPATH": "${workspaceFolder}"
	}
}
```

4. **远程调试**

```json
{
	"name": "Python: 远程调试",
	"type": "debugpy",
	"request": "attach",
	"connect": {
		"host": "localhost",
		"port": 5678
	}
}
```

5. **其他常用配置参数**

```json
{
	"purpose": ["debug-in-terminal"], // 调试目的
	"python": "${command:python.interpreterPath}", // Python解释器路径
	"cwd": "${workspaceFolder}", // 程序运行的工作目录
	"stopOnEntry": true, // 是否在程序入口处停止
	"showReturnValue": true, // 是否显示函数返回值
	"redirectOutput": true, // 是否重定向输出
	"django": true, // Django 调试支持
	"jinja": true, // Jinja 模板调试支持
	"sudo": true, // 是否使用 sudo 运行
	"pyramid": true, // Pyramid 框架调试支持
	"subProcess": true // 是否启用子进程调试
}
```

6. **调试控制台选项**

    - `console`: "integratedTerminal" - 使用 VSCode 集成终端
    - `console`: "externalTerminal" - 使用外部终端
    - `console`: "internalConsole" - 使用 VSCode 调试控制台

#### 高级配置选项

1. **Python 解释器路径配置**

```json
{
	"name": "Python: 当前文件",
	"type": "debugpy",
	"request": "launch",
	"program": "${file}",
	"python": [
		// 方式1：使用 VSCode Python 扩展自动检测的解释器
		"${command:python.interpreterPath}",

		// 方式2：使用系统环境变量中的 Python
		"python",

		// 方式3：指定具体的 Python 解释器路径
		"D:\\Anaconda3\\envs\\myenv\\python.exe",

		// 方式4：使用相对于工作区的 Python 虚拟环境
		"${workspaceFolder}\\venv\\Scripts\\python.exe"
	],
	"console": "integratedTerminal"
}
```

**解释器路径说明：**

1. **自动检测方式** (推荐)

    - 使用 `${command:python.interpreterPath}`
    - 会自动使用 VSCode Python 扩展选择的解释器
    - 可以通过 VSCode 命令面板中的 "Python: Select Interpreter" 切换

2. **系统环境变量方式**

    - 直接使用 `"python"`
    - 使用系统 PATH 中的默认 Python

3. **绝对路径方式**

    - 使用完整的解释器路径
    - 例如：`"D:\\Anaconda3\\envs\\myenv\\python.exe"`
    - 注意 Windows 系统需要使用双反斜杠

4. **相对路径方式**
    - 使用 `${workspaceFolder}` 相对路径
    - 例如：`"${workspaceFolder}\\venv\\Scripts\\python.exe"`
    - 适用于项目特定的虚拟环境

**快速切换解释器：**

1. 使用快捷键 `Ctrl+Shift+P`
2. 输入 "Python: Select Interpreter"
3. 选择需要的 Python 环境

**注意事项：**

-   建议使用 `${command:python.interpreterPath}` 方式，方便环境切换
-   路径中的反斜杠在 Windows 系统需要转义（使用双反斜杠）
-   可以在 VSCode 状态栏查看当前选择的 Python 解释器
-   虚拟环境路径因操作系统而异：
    -   Windows: `venv\\Scripts\\python.exe`
    -   Linux/Mac: `venv/bin/python`

2. **环境变量配置**

```json
{
	"env": {
		"PYTHONPATH": "${workspaceFolder}",
		"DJANGO_SETTINGS_MODULE": "myproject.settings",
		"DEBUG": "1"
	}
}
```

3. **Django 调试配置**

```json
{
	"name": "Django",
	"type": "debugpy",
	"request": "launch",
	"program": "${workspaceFolder}/manage.py",
	"args": ["runserver"],
	"django": true,
	"python": "${command:python.interpreterPath}"
}
```

4. **Flask 调试配置**

```json
{
	"name": "Flask",
	"type": "debugpy",
	"request": "launch",
	"module": "flask",
	"env": {
		"FLASK_APP": "app.py",
		"FLASK_DEBUG": "1"
	},
	"args": ["run", "--no-debugger"],
	"python": "${command:python.interpreterPath}"
}
```

#### 调试功能使用说明

1. **断点操作**

    - 设置断点：点击行号左侧或按 F9
    - 条件断点：右键断点 -> 编辑断点
    - 日志断点：右键断点 -> 编辑断点 -> 记录消息

2. **调试控制**

    - F5: 继续执行
    - F10: 单步跳过
    - F11: 单步调试
    - Shift+F11: 单步跳出
    - Ctrl+Shift+F5: 重启调试

3. **变量监视**
    - 使用调试侧边栏的变量窗口
    - 添加监视表达式
    - 鼠标悬停查看变量值

#### 注意事项

1. 文件路径

    - 使用工作区相对路径
    - 注意路径分隔符的跨平台兼容性

2. 环境变量

    - 调试环境变量不影响全局环境
    - 可以使用 `.env` 文件管理环境变量

3. 性能考虑

    - `justMyCode`: true 可以提高调试性能
    - 避免在生产环境保留调试配置

4. 安全性
    - 远程调试时注意网络安全
    - 不要在版本控制中保存敏感的环境变量

## 注意事项

1. 颜色显示

    - 颜色显示效果取决于终端的支持情况
    - Windows 系统需要安装 colorama 库

2. 日志记录

    - 大量日志时建议增加缓存大小
    - 定期清理日志文件
    - 注意日志文件权限设置

3. 单例模式
    - 注意在多线程环境下的使用
    - 合理使用清理方法避免内存泄漏
    - 不要在单例类中保存大量状态

## Window Operation 窗口操作模块

### 简介

Window Operation 是一个功能强大的 Windows 窗口操作工具模块，提供了一系列用于窗口句柄获取、窗口截图和窗口操作的功能。该模块设计灵活，支持同步和异步操作，适用于自动化测试、窗口监控等场景。

### 主要特性

1. 窗口句柄管理

    - 支持通过类名和标题查找窗口
    - 支持正则表达式匹配
    - 提供句柄缓存机制
    - 支持窗口状态监控

2. 窗口截图功能

    - 高性能窗口截图
    - 支持区域截图
    - 支持图像预处理（灰度、二值化）
    - 提供异步截图接口

3. 窗口操作
    - 窗口位置和大小调整
    - 窗口标题修改
    - 窗口置前
    - 窗口状态查询

### 安装依赖

```bash
pip install win32gui numpy opencv-python pillow
```

### 模块结构

```
window_operation/
├── __init__.py          # 模块入口
├── __operation.py       # 核心实现
└── helper.py            # 辅助函数和异常类
```

### 核心类和函数

#### 1. capture_window_handle 函数

用于高性能窗口截图的函数。

```python
def capture_window_handle(
    hwnd: int,
    region: list = None,
    save: bool = False,
    save_path: str = "screenshots",
    preview: bool = False,
    grayscale: bool = False,
    binary: bool = False
) -> dict:
    """
    对指定窗口句柄进行截图

    参数:
        hwnd (int): 窗口句柄
        region (list, optional): 截图区域 [x1, y1, x2, y2]
        save (bool, optional): 是否保存图片
        save_path (str, optional): 保存路径
        preview (bool, optional): 是否预览
        grayscale (bool, optional): 是否转换为灰度图
        binary (bool, optional): 是否二值化（需要先转换为灰度图）

    返回:
        dict: {
            "status": "success" 或 "error",
            "message": 错误信息（如果有）,
            "image": 图像数组,
            "path": 保存路径（如果保存）
        }
    """
```

使用示例：

```python
from global_tools.window_operation import capture_window_handle

# 获取窗口句柄（示例）
hwnd = win32gui.FindWindow(None, "记事本")

# 基本截图
result = capture_window_handle(hwnd)
if result["status"] == "success":
    image = result["image"]

# 保存截图
result = capture_window_handle(
    hwnd,
    save=True,
    save_path="screenshot.png",
    grayscale=True
)
```

#### 2. GetSpecifiedWindowHandle 类

用于获取指定窗口句柄的单例类，支持正则表达式匹配。

```python
@Singleton
class GetSpecifiedWindowHandle:
    """
    根据窗口类名和标题获取窗口句柄的类

    特性:
    - 单例模式
    - 支持正则表达式匹配
    - 句柄缓存机制
    - 自动刷新
    """

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化窗口句柄获取器

        参数:
            class_name (str, optional): 窗口类名，支持正则表达式
            title (str, optional): 窗口标题，支持正则表达式
            timeout (float, optional): 查找超时时间（秒）
        """

    @property
    def handle(self) -> Optional[int]:
        """获取当前缓存的窗口句柄"""

    @property
    def is_valid(self) -> bool:
        """检查当前句柄是否有效"""

    def refresh(self) -> Optional[int]:
        """强制刷新窗口句柄"""

    def get_window_info(self) -> Optional[Dict[str, Any]]:
        """
        获取窗口详细信息

        返回:
            Dict 包含以下信息：
            - handle: 窗口句柄
            - class_name: 窗口类名
            - title: 窗口标题
            - rect: 窗口位置和大小
            - is_visible: 是否可见
            - is_enabled: 是否启用
            - parent: 父窗口句柄
        """

    def wait_for_valid(self, timeout: float = None) -> bool:
        """
        等待直到获取到有效的窗口句柄或超时

        参数:
            timeout (float, optional): 超时时间（秒）

        返回:
            bool: 是否成功获取到有效句柄
        """
```

使用示例：

```python
from global_tools.window_operation import GetSpecifiedWindowHandle

# 创建窗口句柄获取器
finder = GetSpecifiedWindowHandle(title="记事本")

# 获取句柄
if finder.is_valid:
    handle = finder.handle

# 获取窗口信息
window_info = finder.get_window_info()
if window_info:
    print(f"窗口标题: {window_info['title']}")
    print(f"窗口位置: {window_info['rect']}")

# 等待窗口出现
if finder.wait_for_valid(timeout=10.0):
    print("窗口已找到")
```

#### 3. WindowHandleManager 类

提供全面的窗口操作管理功能。

```python
class WindowHandleManager:
    """
    窗口操作管理器
    提供完整的窗口操作功能集
    """

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化窗口管理器

        参数:
            class_name (str, optional): 窗口类名，支持正则表达式
            title (str, optional): 窗口标题，支持正则表达式
            timeout (float, optional): 操作超时时间（秒）
        """

    @property
    def handle(self) -> Optional[int]:
        """获取窗口句柄"""

    @property
    def is_alive(self) -> bool:
        """检查窗口监控线程是否活动"""

    def is_valid_window(self) -> bool:
        """检查窗口是否有效"""

    def get_window_info(self) -> Dict[str, Any]:
        """获取窗口详细信息"""

    def set_foreground(self) -> bool:
        """将窗口置于前台"""

    def get_window_text(self) -> str:
        """获取窗口标题文本"""

    def set_window_text(self, text: str) -> bool:
        """设置窗口标题文本"""

    def get_window_rect(self) -> Dict[str, Any]:
        """
        获取窗口位置和大小

        返回:
            Dict 包含以下信息：
            - left: 左边界
            - top: 上边界
            - right: 右边界
            - bottom: 下边界
            - width: 宽度
            - height: 高度
        """

    def set_window_pos(self, x: int, y: int, width: int, height: int,
                      flags: int = win32con.SWP_SHOWWINDOW) -> bool:
        """
        设置窗口位置和大小

        参数:
            x (int): 左边界坐标
            y (int): 上边界坐标
            width (int): 宽度
            height (int): 高度
            flags (int, optional): 窗口位置标志
        """
```

使用示例：

```python
from global_tools.window_operation import WindowHandleManager

# 创建窗口管理器
manager = WindowHandleManager(title="记事本")

# 基本窗口操作
if manager.is_valid_window():
    # 获取窗口信息
    info = manager.get_window_info()
    print(f"窗口标题: {info['title']}")

    # 修改窗口标题
    manager.set_window_text("新标题")

    # 调整窗口位置和大小
    manager.set_window_pos(100, 100, 800, 600)

    # 窗口置前
    manager.set_foreground()
```

#### 4. WindowCapture 类

提供高级窗口截图功能，支持异步操作。

```python
class WindowCapture:
    """
    窗口截图类
    支持同步和异步截图操作
    """

    def __init__(self, class_name: str = None, title: str = None, timeout: float = 5.0):
        """
        初始化截图器

        参数:
            class_name (str, optional): 窗口类名，支持正则表达式
            title (str, optional): 窗口标题，支持正则表达式
            timeout (float, optional): 操作超时时间（秒）
        """

    def capture(self, width: int, height: int, save_path: str = None,
                filename: str = None, file_type: str = "png") -> np.ndarray:
        """
        同步截图

        参数:
            width (int): 截图宽度
            height (int): 截图高度
            save_path (str, optional): 保存路径
            filename (str, optional): 文件名
            file_type (str, optional): 文件类型

        返回:
            np.ndarray: 图像数组
        """

    def capture_async(self, width: int, height: int, save_path: str = None,
                     filename: str = None, file_type: str = "png") -> asyncio.Future:
        """
        异步截图

        参数同 capture 方法

        返回:
            asyncio.Future: 异步结果对象
        """

    def preview_capture(self, width: int, height: int, title: str = None,
                       dpi: int = 100, window_size: tuple = None,
                       block: bool = True, timeout: float = None) -> bool:
        """
        预览截图

        参数:
            width (int): 截图宽度
            height (int): 截图高度
            title (str, optional): 预览窗口标题
            dpi (int, optional): 显示DPI
            window_size (tuple, optional): 预览窗口大小
            block (bool, optional): 是否阻塞等待
            timeout (float, optional): 预览超时时间

        返回:
            bool: 预览是否成功
        """
```

使用示例：

```python
from global_tools.window_operation import WindowCapture
import asyncio

# 创建截图器
capturer = WindowCapture(title="记事本")

# 同步截图
image = capturer.capture(800, 600, save_path="screenshot.png")

# 异步截图
async def capture_async():
    future = capturer.capture_async(800, 600)
    image = await future
    return image

# 预览截图
capturer.preview_capture(800, 600, title="预览", timeout=5.0)
```

### 异常处理

模块定义了以下异常类：

1. `WindowCaptureException`: 截图异常基类
2. `WindowCaptureError`: 具体截图错误
3. `WindowHandleException`: 窗口操作异常基类
4. `WindowNotFoundError`: 窗口未找到异常
5. `WindowOperationError`: 窗口操作失败异常

使用示例：

```python
from global_tools.window_operation import (
    WindowHandleManager,
    WindowNotFoundError,
    WindowOperationError
)

try:
    manager = WindowHandleManager(title="不存在的窗口")
    manager.set_foreground()
except WindowNotFoundError:
    print("窗口未找到")
except WindowOperationError as e:
    print(f"操作失败: {str(e)}")
```

### 注意事项

1. 窗口操作

    - 确保目标窗口存在且可访问
    - 注意处理窗口状态变化
    - 合理设置超时时间

2. 截图功能

    - 考虑图像大小和内存使用
    - 异步操作时注意异常处理
    - 预览功能可能占用较多资源

3. 性能优化

    - 合理使用句柄缓存
    - 避免频繁刷新窗口信息
    - 大量截图时考虑使用异步接口

4. 兼容性
    - 仅支持 Windows 系统
    - 部分功能可能需要管理员权限
    - 注意不同 Windows 版本的差异
