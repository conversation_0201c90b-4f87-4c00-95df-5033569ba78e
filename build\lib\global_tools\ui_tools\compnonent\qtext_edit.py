import os
import json
import threading
import traceback
from collections import deque
from threading import Lock
from typing import Union, List, Optional, Dict, Any, TYPE_CHECKING

from PyQt5.QtWidgets import QTextEdit, QApplication
from PyQt5.QtCore import QObject, QEvent, QTimer, pyqtSignal, pyqtSlot, QThread
from PyQt5.QtGui import QFont, QTextCursor
from PyQt5 import sip, QtCore, QtGui

from global_tools.utils.helper import Logger, LogLevel
from global_tools.utils import ClassInstanceManager

from global_tools.ui_tools import LogOutput



class _TextEditSaveWorker( QObject ):
    """
    一个在后台线程中执行 QTextEdit 内容保存操作的工作类。

    这个类被设计为在单独的 QThread 中运行，以避免文件 I/O 操作阻塞主GUI线程。
    它接收保存请求，读取现有的配置文件，更新数据，然后将数据写回文件。

    信号:
        log_message_signal (pyqtSignal(str, str)):
            发射日志消息和颜色。
        log_multi_style_message_signal (pyqtSignal(list)):
            发射多样式日志消息。
        save_finished_signal (pyqtSignal(bool, str)):
            在保存操作完成后发射，包含成功状态和消息。
    """
    log_message_signal = pyqtSignal( str, str )
    log_multi_style_message_signal = pyqtSignal( list )
    save_finished_signal = pyqtSignal( bool, str )

    def __init__( self, parent: Optional[ QObject ] = None, logger: Optional[ Logger ] = None ):
        super().__init__( parent )
        self.logger = logger if logger else Logger()
        self.logger.debug( "后台保存工作者已创建" )

    @pyqtSlot( list, str, str )
    def perform_save( self, data_to_save: list, config_file_path: str, config_path: str ) -> None:
        """
        执行保存操作。

        读取JSON配置文件，更新或删除指定的键值对，然后写回文件。

        Args:
            data_to_save (list):
                一个元组列表，每个元组包含 (widget_key, text_content)。
                如果 text_content 为 None，则表示从配置中删除该键。
            config_file_path (str):
                配置文件的完整路径。
            config_path (str):
                配置文件所在的目录路径。
        """
        try:
            # 确保目录存在
            if not os.path.exists( config_path ):
                os.makedirs( config_path, exist_ok = True )
                self.logger.info( f"配置目录不存在，已创建: {config_path}" )

            # 读取现有配置
            saved_data = {}
            if os.path.exists( config_file_path ):
                try:
                    with open( config_file_path, 'r', encoding = 'utf-8' ) as f:
                        content = f.read()
                        if content.strip():
                            saved_data = json.loads( content )
                        if not isinstance( saved_data, dict ):
                            self.logger.warning( "配置文件内容不是有效的字典，将重置为空字典。" )
                            saved_data = {}
                except ( json.JSONDecodeError, IOError ) as e:
                    self.logger.error( f"读取或解析配置文件失败: {e}" )
                    self.log_message_signal.emit( f"配置文件损坏，将创建新的配置: {e}", "orange" )
                    saved_data = {}

            # 更新数据
            update_count = 0
            delete_count = 0
            for key, value in data_to_save:
                if value is None:
                    if key in saved_data:
                        del saved_data[ key ]
                        delete_count += 1
                else:
                    saved_data[ key ] = value
                    update_count += 1

            # 写回文件
            with open( config_file_path, 'w', encoding = 'utf-8' ) as f:
                json.dump( saved_data, f, ensure_ascii = False, indent = 4 )

            # 发送成功信号和日志
            log_message_parts = [
                ( "输入记忆: ", {
                    "color": "green",
                    "bold": True
                } ),
                ( "配置已更新 (", {
                    "color": "black"
                } ),
                ( f"新增/修改: {update_count}", {
                    "color": "blue"
                } ),
                ( ", ", {
                    "color": "black"
                } ),
                ( f"删除: {delete_count}", {
                    "color": "red"
                } ),
                ( ")", {
                    "color": "black"
                } ),
            ]
            self.log_multi_style_message_signal.emit( log_message_parts )
            self.save_finished_signal.emit( True, f"成功保存了 {len(data_to_save)} 项内容。" )

        except Exception as e:
            error_message = f"保存QTextEdit内容时发生严重错误: {e}"
            self.logger.error( error_message )
            self.logger.debug( traceback.format_exc() )
            self.save_finished_signal.emit( False, error_message )



class TextEditMemory( QObject ):
    """
    实现 QTextEdit 控件输入记忆功能。

    该类监控一个或多个 QTextEdit 控件，当控件失去焦点且内容发生改变时，
    会自动将其文本内容通过异步方式保存到 JSON 配置文件中。
    下次程序启动时，会自动从配置文件加载并恢复这些文本。

    主要特性:
    - 支持单个或多个 QTextEdit 控件。
    - 配置文件路径和名称可自定义。
    - 使用防抖机制（debouncing）避免频繁写入文件，提高性能。
    - 异步保存，完全不阻塞UI主线程。
    - 通过 LogOutput 实例输出用户操作日志，提供良好的用户反馈。
    - 详细的内部开发日志记录，便于调试。
    - 支持在运行时动态添加和移除被管理的控件。

    Args:
        text_edits (Union[QTextEdit, List[QTextEdit]]):
            一个或多个 QTextEdit 控件对象。
        log_output (LogOutput):
            LogOutput 类的实例，用于在UI上显示日志。
        config_filename (str, optional):
            配置文件的名称。默认为 "textEdit_memory.json"。
        config_path (str, optional):
            配置文件存放的目录。默认为 "GUI/config"。
        debounce_interval_ms (int, optional):
            防抖延迟时间（毫秒）。延迟时间内连续的修改只会触发一次保存。默认为 500。
        parent (QObject, optional):
            Qt父对象。默认为 None。
        logger (Logger, optional):
            日志记录器实例。如果为 None，将使用全局日志记录器。

    使用示例:
    --------------------------------------------------------------------------
    import sys
    import logging
    from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QTextEdit

    # 假设这些是你项目中的工具
    # from global_tools.ui_tools.compnonent.helper import LogOutput
    # from global_tools.utils.helper import Logger

    class LogOutput: # 示例替代
        def __init__(self, text_widget):
            self.text_widget = text_widget
        def append(self, msg, color="black"):
            # 简单的实现，实际应处理颜色
            self.text_widget.append(msg)
        def append_multi_style(self, parts):
            html = ""
            for text, styles in parts:
                color = styles.get("color", "black")
                weight = "bold" if styles.get("bold") else "normal"
                html += f'<span style="color:{color}; font-weight:{weight};">{text}</span>'
            self.text_widget.insertHtml(html)
            self.text_widget.append("") # 换行

    class MyWindow(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("QTextEdit Memory Test")
            self.setGeometry(100, 100, 500, 400)
            layout = QVBoxLayout(self)

            # 创建日志显示控件
            self.log_display = QTextEdit()
            self.log_display.setReadOnly(True)
            # from global_tools.ui_tools.compnonent.helper import LogOutput
            self.log_output = LogOutput(self.log_display)

            # 创建输入框
            self.textEdit1 = QTextEdit()
            self.textEdit1.setObjectName("notesField")
            self.textEdit1.setPlaceholderText("写下你的笔记...")

            self.textEdit2 = QTextEdit()
            self.textEdit2.setObjectName("descriptionField")
            self.textEdit2.setPlaceholderText("输入详细描述...")

            layout.addWidget(self.textEdit1)
            layout.addWidget(self.textEdit2)
            layout.addWidget(self.log_display)

            # 为窗口设置 objectName，作为控件父级的标识
            self.setObjectName("MyTestWindow")

            # 实例化 TextEditMemory
            self.memory_handler = TextEditMemory(
                text_edits=[self.textEdit1, self.textEdit2],
                log_output=self.log_output,
                config_filename="my_app_text_edits.json"
            )

        def closeEvent(self, event):
            # 在窗口关闭前，确保所有待处理的更改都已保存
            self.memory_handler.cleanup()
            super().closeEvent(event)

    if __name__ == "__main__":
        logging.basicConfig(level=logging.DEBUG,
                            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        app = QApplication(sys.argv)
        window = MyWindow()
        window.show()
        sys.exit(app.exec_())
    --------------------------------------------------------------------------
    """
    _request_save_on_worker_signal = pyqtSignal( list, str, str )

    def __init__(
        self,
        text_edits: Union[ QTextEdit, List[ QTextEdit ] ],
        log_output: LogOutput,
        config_filename: str = "textEdit_memory.json",
        config_path: str = "GUI/config",
        debounce_interval_ms: int = 150,
        parent: Optional[ QObject ] = None,
        logger: Optional[ Logger ] = None
    ):
        super().__init__( parent )

        self.logger = logger if logger else Logger()
        self.logger.set_instance_level( LogLevel.DEBUG )
        self.logger.debug( "初始化 TextEditMemory" )

        self._text_edits = text_edits if isinstance( text_edits, list ) else [ text_edits ]
        self._log_output = log_output

        # 根据传入的路径类型（绝对或相对）来确定最终的配置路径
        if os.path.isabs( config_path ):
            # 传入的是一个绝对路径，此为默认行为
            self._config_path = config_path
        else:
            # 传入的是一个相对路径，获取当前 Python 的工作目录然后进行拼接成绝对路径
            self.logger.warning( f"提供的 config_path ('{config_path}') 是一个相对路径。 "
                                 f"它将被基于当前工作目录 '{os.getcwd()}' 解析为绝对路径。" )
            self.logger.warning( "为了确保配置文件位置的长期稳定性，强烈建议在实例化 TextEditMemory 时提供一个绝对路径。" )
            # 严格按照指示：获取当前工作目录，然后进行拼接，不使用 os.path.abspath
            current_dir = os.getcwd()
            joined_path = os.path.join( current_dir, config_path )
            # 规范化路径以处理 '..' 等情况，确保路径正确
            self._config_path = os.path.normpath( joined_path )

        self._config_file_path = os.path.join( self._config_path, config_filename )
        self.logger.info( f"TextEditMemory 配置文件绝对路径: {self._config_file_path}" )

        self._data_to_save_queue = deque()
        self._save_lock = Lock()
        self._is_saving_flag = False

        self._save_timer = QTimer( self )
        self._save_timer.setSingleShot( True )
        self._save_timer.timeout.connect( self._process_save_queue )
        self._debounce_interval_ms = debounce_interval_ms

        self._initial_texts = {}
        self._pending_save_data = {}

        self._save_qthread = QThread( self )
        self._save_worker = _TextEditSaveWorker( logger = self.logger )
        self._save_worker.moveToThread( self._save_qthread )

        self._request_save_on_worker_signal.connect( self._save_worker.perform_save )
        self._save_worker.log_message_signal.connect( self._handle_worker_log )
        self._save_worker.log_multi_style_message_signal.connect( self._handle_worker_log_multi_style )
        self._save_worker.save_finished_signal.connect( self._handle_save_finished )

        self._save_qthread.start()
        self.logger.debug( "保存工作线程已启动" )

        self._ensure_config_dir_exists()
        self._connect_signals()
        self._load_config_and_populate()

    def _ensure_config_dir_exists( self ) -> None:
        """确保配置文件目录存在。"""
        try:
            os.makedirs( self._config_path, exist_ok = True )
            self.logger.info( f"确保配置目录存在: {self._config_path}" )
        except Exception as e:
            self.logger.error( f"创建配置目录失败: {e}" )
            self._log_output.append( f"创建配置目录失败: {e}", color = "red" )
            self.logger.debug( traceback.format_exc() )

    def _connect_signals( self ) -> None:
        """为所有输入框连接必要的信号和事件过滤器。"""
        for text_edit in self._text_edits:
            if not isinstance( text_edit, QTextEdit ):
                self.logger.warning( f"忽略非QTextEdit对象: {text_edit}" )
                continue
            if sip.isdeleted( text_edit ):
                self.logger.warning( f"忽略已被销毁的Qt对象: {text_edit}" )
                continue
            try:
                text_edit.installEventFilter( self )
                widget_key = self._get_widget_key( text_edit )
                if widget_key:
                    self.logger.debug( f"已为输入框 '{widget_key}' 安装事件过滤器" )
                else:
                    self.logger.warning( f"输入框缺少有效的对象名: {text_edit}" )
            except Exception as e:
                self.logger.error( f"连接输入框信号失败: {e}" )
                self._log_output.append( f"连接输入框信号失败: {e}", color = "red" )
                self.logger.debug( traceback.format_exc() )

    def eventFilter( self, watched_object: QObject, event: QEvent ) -> bool:
        """
        事件过滤器，用于捕获QTextEdit的焦点获取、失去以及按键事件。
        - Enter键：失去焦点并触发保存（实现为清除焦点）。
        - Shift+Enter键组合：正常换行行为。
        
        Args:
            watched_object (QObject): 被监视的对象
            event (QEvent): 发生的事件
            
        Returns:
            bool: True表示事件已被处理，False表示事件需要继续传递
        """
        if (
            isinstance( watched_object, QTextEdit ) and watched_object in self._text_edits
            and not sip.isdeleted( watched_object )
        ):
            # 处理键盘事件 - 明确检查KeyPress事件类型和QKeyEvent类型
            if event.type() == QtCore.QEvent.Type.KeyPress and isinstance( event, QtGui.QKeyEvent ):
                # 检查是否是Enter或Return键
                if event.key() in ( QtCore.Qt.Key.Key_Return, QtCore.Qt.Key.Key_Enter ):
                    # 更改判断逻辑：只有在修饰键 *完全* 是 Shift 时，才认为是 Shift+Enter。
                    # 这能更好地处理某些环境下，即使只按 Enter 也会附带额外修饰键标志的问题。
                    if event.modifiers() == QtCore.Qt.KeyboardModifier.ShiftModifier:
                        # 精确匹配 Shift+Enter，允许默认的换行行为
                        self.logger.debug( f"检测到精确的Shift+Enter组合，允许默认的换行行为" )
                        return False
                    else:
                        # 其他所有情况（包括单独的Enter或带有其他修饰键的Enter）都触发保存
                        self.logger.debug( f"检测到Enter键（或非Shift+Enter组合），清除焦点以触发保存" )
                        watched_object.clearFocus()
                        # 返回True表示事件已被处理，阻止默认的换行行为
                        return True

            # 处理焦点事件
            elif event.type() == QtCore.QEvent.Type.FocusIn:
                self._handle_focus_in( watched_object )
            elif event.type() == QtCore.QEvent.Type.FocusOut:
                self._handle_focus_out( watched_object )

        # 对于所有其他事件，传递给父类处理
        return super().eventFilter( watched_object, event )

    def _handle_focus_in( self, text_edit_widget: QTextEdit ) -> None:
        """
        处理输入框获取焦点事件。
        """
        if sip.isdeleted( text_edit_widget ):
            return
        widget_key = self._get_widget_key( text_edit_widget )
        if widget_key:
            current_text = text_edit_widget.toPlainText()
            self._initial_texts[ widget_key ] = current_text
            self.logger.debug( f"输入框 '{widget_key}' 获取焦点，初始文本已记录。" )

    def _handle_focus_out( self, text_edit_widget: QTextEdit ) -> None:
        """
        处理输入框失去焦点事件。
        """
        if sip.isdeleted( text_edit_widget ):
            return

        widget_key = self._get_widget_key( text_edit_widget )
        if not widget_key:
            return

        current_text = text_edit_widget.toPlainText()
        initial_text = self._initial_texts.get( widget_key )

        if current_text == initial_text:
            self.logger.debug( f"输入框 '{widget_key}' 内容未变化，跳过保存。" )
            return

        self.logger.info( f"输入框 '{widget_key}' 内容已变化，准备保存。" )

        if not current_text.strip():
            self._pending_save_data[ widget_key ] = None
            self.logger.info( f"输入框 '{widget_key}' 内容已清空，将从配置中删除。" )
        else:
            self._pending_save_data[ widget_key ] = current_text

        self._save_timer.start( self._debounce_interval_ms )

    def _get_widget_key( self, text_edit_widget: QTextEdit ) -> Optional[ str ]:
        """
        为输入框生成唯一的键名，格式为 "父容器名称_输入框名称"。
        """
        if not text_edit_widget or sip.isdeleted( text_edit_widget ) or not hasattr( text_edit_widget, 'objectName' ):
            return None

        widget_name = text_edit_widget.objectName()
        if not widget_name:
            self.logger.warning( f"输入框没有设置objectName: {text_edit_widget}" )
            return None

        try:
            parent_widget = text_edit_widget.parent()
            parent_name = parent_widget.objectName(
            ) if parent_widget and hasattr( parent_widget, 'objectName' ) and parent_widget.objectName() else None

            return f"{parent_name}_{widget_name}" if parent_name else widget_name
        except Exception as e:
            self.logger.error( f"获取widget_key时发生错误: {e}" )
            self.logger.debug( traceback.format_exc() )
            return None

    def _load_config_and_populate( self ) -> None:
        """加载配置文件并填充输入框。"""
        if not os.path.exists( self._config_file_path ):
            self.logger.info( f"配置文件不存在: {self._config_file_path}, 将在首次保存时创建。" )
            self._log_output.append( "输入记忆配置文件不存在，将在首次使用时创建。", color = "orange" )
            return

        try:
            with open( self._config_file_path, 'r', encoding = 'utf-8' ) as f:
                content = f.read()
                if not content.strip():
                    return
                saved_data = json.loads( content )

            if not isinstance( saved_data, dict ):
                return

            applied_count = 0
            for text_edit in self._text_edits:
                if sip.isdeleted( text_edit ):
                    continue

                widget_key = self._get_widget_key( text_edit )
                if not widget_key:
                    continue

                if widget_key in saved_data and saved_data[ widget_key ] is not None:
                    text_edit.setPlainText( saved_data[ widget_key ] )
                    applied_count += 1
                    self.logger.debug( f"已恢复输入框 '{widget_key}' 的文本。" )

            if applied_count > 0:
                self._log_output.append_multi_style( [ ( "输入记忆: ", {
                    "color": "green",
                    "bold": True
                } ), ( f"已恢复 {applied_count} 个文本框的内容", {
                    "color": "black"
                } ) ] )

        except Exception as e:
            self.logger.error( f"加载或应用配置文件时出错: {e}" )
            self._log_output.append( f"加载输入记忆失败: {e}", color = "red" )
            self.logger.debug( traceback.format_exc() )

    def _process_save_queue( self ) -> None:
        """处理因防抖定时器触发的保存队列。"""
        if not self._pending_save_data:
            return

        for key, value in self._pending_save_data.items():
            self._data_to_save_queue.append( ( key, value ) )
        self._pending_save_data.clear()

        if self._is_saving_flag:
            return

        if not self._data_to_save_queue:
            return

        self._is_saving_flag = True
        data_to_save = list( self._data_to_save_queue )
        self._data_to_save_queue.clear()

        self._request_save_on_worker_signal.emit( data_to_save, self._config_file_path, self._config_path )
        self.logger.info( f"已向工作线程发出保存 {len(data_to_save)} 个文本框内容的请求。" )

    @pyqtSlot( bool, str )
    def _handle_save_finished( self, success: bool, message: str ) -> None:
        """处理保存完成的回调。"""
        self._is_saving_flag = False
        if not success:
            self.logger.error( f"保存失败: {message}" )
            self._log_output.append( f"保存输入内容失败: {message}", color = "red" )

        if self._pending_save_data:
            self._process_save_queue()

    @pyqtSlot( str, str )
    def _handle_worker_log( self, message: str, color_name: str ) -> None:
        """处理来自工作线程的单样式日志消息。"""
        self._log_output.append( message, color = color_name )

    @pyqtSlot( list )
    def _handle_worker_log_multi_style( self, parts: list ) -> None:
        """处理来自工作线程的多样式日志消息。"""
        self._log_output.append_multi_style( parts )

    def add_text_edit( self, text_edit: QTextEdit ) -> bool:
        """
        动态添加一个新的QTextEdit控件到管理列表。
        """
        if not isinstance( text_edit, QTextEdit ) or sip.isdeleted( text_edit ):
            return False
        if text_edit in self._text_edits:
            return False

        widget_key = self._get_widget_key( text_edit )
        if not widget_key:
            return False

        self._text_edits.append( text_edit )
        text_edit.installEventFilter( self )

        # 尝试从配置加载内容
        # ... (与_load_config_and_populate中类似的逻辑)

        self.logger.info( f"已添加文本框 '{widget_key}' 到管理列表。" )
        return True

    def remove_text_edit( self, text_edit: QTextEdit ) -> bool:
        """
        从管理列表中移除一个QTextEdit控件。
        """
        if sip.isdeleted( text_edit ) or text_edit not in self._text_edits:
            return False

        widget_key = self._get_widget_key( text_edit )

        try:
            text_edit.removeEventFilter( self )
        except Exception:
            pass

        self._text_edits.remove( text_edit )

        if widget_key:
            self._initial_texts.pop( widget_key, None )
            self._pending_save_data.pop( widget_key, None )

        self.logger.info( f"已移除文本框 '{widget_key}' 从管理列表。" )
        return True

    def cleanup( self ) -> None:
        """
        清理资源，应在应用程序退出前调用。
        """
        self.logger.debug( "开始清理 TextEditMemory 资源" )
        if self._save_timer.isActive():
            self._save_timer.stop()
        if self._pending_save_data:
            self._process_save_queue()

        if self._save_qthread.isRunning():
            self._save_qthread.quit()
            if not self._save_qthread.wait( 5000 ):
                self._save_qthread.terminate()
                self._save_qthread.wait()

        for text_edit in list( self._text_edits ):
            if text_edit and not sip.isdeleted( text_edit ):
                try:
                    text_edit.removeEventFilter( self )
                except Exception:
                    pass

        self._text_edits.clear()
        self._initial_texts.clear()
        self._pending_save_data.clear()
        self._data_to_save_queue.clear()
        self.logger.info( "TextEditMemory 资源已清理完毕。" )

    def __del__( self ):
        """析构函数，确保资源被释放。"""
        try:
            self.cleanup()
        except Exception:
            pass


# =================================================================================
# TextEditManager - QTextEdit 批量管理类（单例模式）
# =================================================================================

class TextEditManager(QObject):
    """
    Qt多行文本编辑框(QTextEdit)的批量管理类（单例模式）

    该类提供了对多个QTextEdit控件的统一管理功能：
    1. 接收一组QTextEdit控件，使用objectName作为key进行管理
    2. 提供统一的接口操作所有QTextEdit，包括：
       - 文本内容管理（设置、获取、清空、追加等）
       - 样式管理（字体、颜色、背景、边框等）
       - 状态管理（启用/禁用、只读、可见性等）
       - 格式管理（HTML、纯文本、富文本等）
       - 事件处理（焦点获取/失去、文本变化、滚动等）
    3. 每个方法都接受一个额外的name参数，用于指定要操作的特定QTextEdit
    4. 信号发射时会附带QTextEdit的名称，方便识别信号来源

    ===== 单例模式说明 =====
    该类实现了线程安全的单例模式：
    - 首次创建时需要传入QTextEdit控件参数
    - 后续调用可以不传参数，直接返回已创建的实例
    - 支持重置单例实例，重新创建新的管理器
    - 在多线程环境下保证线程安全

    ===== 线程安全说明 =====
    所有UI操作都通过内部信号槽机制实现，确保：
    - 公有方法可以在任何线程中安全调用
    - 实际的UI更新始终在主GUI线程中执行
    - 避免跨线程操作UI导致的程序崩溃

    ===== 使用示例 =====

    1. 创建和初始化（单例模式）:
       ```python
       # 创建多个QTextEdit
       notes_edit = QTextEdit()
       notes_edit.setObjectName("notes")

       description_edit = QTextEdit()
       description_edit.setObjectName("description")

       log_edit = QTextEdit()
       log_edit.setObjectName("log")

       # 方式1：直接创建管理器（首次创建）
       manager = TextEditManager(notes_edit, description_edit, log_edit)

       # 方式2：使用推荐的类方法创建（首次创建）
       manager = TextEditManager.get_instance(notes_edit, description_edit, log_edit)

       # 方式3：后续获取已创建的实例（无需参数）
       manager = TextEditManager.get_instance()
       # 或者
       manager = TextEditManager()

       # 也可以通过列表添加
       # edits = [notes_edit, description_edit, log_edit]
       # manager = TextEditManager.get_instance(*edits)
       ```

    2. 文本内容操作:
       ```python
       # 设置文本内容
       manager.set_text("notes", "这是一些重要的笔记内容")
       manager.set_html("description", "<h1>标题</h1><p>这是HTML格式的描述</p>")

       # 获取文本内容
       notes_text = manager.get_text("notes")
       description_html = manager.get_html("description")

       # 追加文本
       manager.append_text("log", "新的日志条目\\n")
       manager.append_html("log", "<p style='color:red;'>错误信息</p>")

       # 清空文本
       manager.clear("notes")
       ```

    3. 样式和格式管理:
       ```python
       # 设置字体
       manager.set_font("notes", "Arial", 12, bold=True)

       # 设置样式表
       manager.set_style_sheet("description", "background-color: #f0f0f0; border: 1px solid #ccc;")

       # 设置只读状态
       manager.set_read_only("log", True)

       # 设置启用状态
       manager.set_enabled("notes", False)
       ```

    4. 批量操作:
       ```python
       # 批量设置文本
       manager.set_text_batch({
           "notes": "笔记内容",
           "description": "描述内容",
           "log": "日志内容"
       })

       # 批量清空
       manager.clear_all()

       # 批量设置只读
       manager.set_read_only_all(True)
       ```

    5. 信号连接:
       ```python
       # 连接信号
       def on_text_changed(name, text):
           print(f"文本框 '{name}' 的内容变为: {text[:50]}...")

       manager.text_changed.connect(on_text_changed)

       def on_focus_changed(name, has_focus):
           status = "获得" if has_focus else "失去"
           print(f"文本框 '{name}' {status}焦点")

       manager.focus_changed.connect(on_focus_changed)
       ```

    6. 获取原始QTextEdit对象:
       ```python
       # 获取原始QTextEdit对象
       notes_edit = manager.get_text_edit("notes")
       if notes_edit:
           # 直接使用QTextEdit的原生方法
           notes_edit.moveCursor(QTextCursor.End)
       ```

    7. 单例模式特殊用法:
        ```python
        # 检查实例是否已创建
        if TextEditManager.is_instance_created():
            manager = TextEditManager.get_instance()
        else:
            # 首次创建
            manager = TextEditManager.get_instance(edit1, edit2)

        # 重置单例实例（在需要重新初始化时）
        TextEditManager.reset_instance()
        new_manager = TextEditManager.get_instance(new_edit1, new_edit2)
        ```
    """

    # =================================================================================
    # 单例模式相关类变量
    # =================================================================================
    _instance = None  # 单例实例
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _initialized = False  # 初始化标志
    _cached_args = None  # 缓存的构造参数
    _cached_kwargs = None  # 缓存的关键字参数

    # 定义信号，每个信号都包含name参数用于标识控件
    text_changed = pyqtSignal(str, str)  # name, text
    html_changed = pyqtSignal(str, str)  # name, html
    focus_changed = pyqtSignal(str, bool)  # name, has_focus
    cursor_position_changed = pyqtSignal(str, int)  # name, position
    selection_changed = pyqtSignal(str, str)  # name, selected_text
    enabled_changed = pyqtSignal(str, bool)  # name, enabled
    read_only_changed = pyqtSignal(str, bool)  # name, read_only
    visibility_changed = pyqtSignal(str, bool)  # name, visible

    # =================================================================================
    # Internal Signals for Thread-Safe UI Updates
    # ---------------------------------------------------------------------------------
    # 以下信号用于在内部实现线程安全的UI更新。
    # 公共方法（如 set_text）会发射这些信号，而不是直接修改QTextEdit控件。
    # 信号会被连接到相应的私有槽函数（如 __on_set_text），
    # Qt的事件循环机制会确保这些槽函数总是在主GUI线程中执行，从而避免跨线程操作UI导致的崩溃。
    # =================================================================================
    __set_text_signal = pyqtSignal(str, str)  # name, text
    __set_html_signal = pyqtSignal(str, str)  # name, html
    __append_text_signal = pyqtSignal(str, str)  # name, text
    __append_html_signal = pyqtSignal(str, str)  # name, html
    __clear_signal = pyqtSignal(str)  # name
    __set_font_signal = pyqtSignal(str, object)  # name, font_dict
    __set_style_sheet_signal = pyqtSignal(str, str)  # name, style_sheet
    __set_enabled_signal = pyqtSignal(str, bool)  # name, enabled
    __set_read_only_signal = pyqtSignal(str, bool)  # name, read_only
    __set_visible_signal = pyqtSignal(str, bool)  # name, visible

    def __new__(cls, *text_edits: Union[QTextEdit, List[QTextEdit]], parent: Optional[QObject] = None):
        """
        单例模式的 __new__ 方法

        实现线程安全的单例模式：
        - 如果实例不存在，创建新实例
        - 如果实例已存在且有效，直接返回现有实例
        - 如果实例已存在但无效（被删除），重新创建实例

        Args:
            *text_edits: QTextEdit控件参数（首次创建时需要）
            parent: 父对象（可选）

        Returns:
            TextEditManager: 单例实例
        """
        # 双重检查锁定模式，确保线程安全
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # 创建新实例
                    cls._instance = super(TextEditManager, cls).__new__(cls)
                    cls._initialized = False
                    # 缓存参数供后续使用
                    cls._cached_args = text_edits
                    cls._cached_kwargs = {'parent': parent}
        else:
            # 检查现有实例是否仍然有效
            try:
                if sip.isdeleted(cls._instance):
                    # 实例已被删除，重新创建
                    with cls._lock:
                        if sip.isdeleted(cls._instance):
                            cls._instance = super(TextEditManager, cls).__new__(cls)
                            cls._initialized = False
                            # 如果没有传入新参数，使用缓存的参数
                            if not text_edits and cls._cached_args:
                                cls._cached_args = cls._cached_args
                                cls._cached_kwargs = cls._cached_kwargs
                            else:
                                cls._cached_args = text_edits
                                cls._cached_kwargs = {'parent': parent}
            except RuntimeError:
                # 处理可能的运行时错误
                with cls._lock:
                    cls._instance = super(TextEditManager, cls).__new__(cls)
                    cls._initialized = False
                    cls._cached_args = text_edits
                    cls._cached_kwargs = {'parent': parent}

        return cls._instance

    def __init__(self, *text_edits: Union[QTextEdit, List[QTextEdit]], parent: Optional[QObject] = None):
        """
        初始化TextEditManager（单例模式）

        Args:
            *text_edits: 一个或多个QTextEdit实例，或包含QTextEdit实例的列表
                        首次创建时必须提供，后续调用可以为空（使用缓存的参数）
            parent: 父QObject对象，默认为None

        注意：
            - 如果实例已经初始化过，此方法会直接返回，不会重复初始化
            - 如果没有传入参数且有缓存参数，会使用缓存的参数进行初始化
            - 可以通过 reset_instance() 方法重置单例实例
        """
        # 检查是否已经初始化过
        if self.__class__._initialized:
            return

        # 如果没有传入参数，尝试使用缓存的参数
        if not text_edits and self.__class__._cached_args:
            text_edits = self.__class__._cached_args
            if self.__class__._cached_kwargs and 'parent' in self.__class__._cached_kwargs:
                parent = self.__class__._cached_kwargs['parent']

        # 如果仍然没有参数，记录警告但继续初始化
        if not text_edits:
            # 延迟获取logger，避免循环依赖
            try:
                logger = ClassInstanceManager.get_instance(key="ui_logger")
                if logger:
                    logger.warning("TextEditManager 初始化时未提供 QTextEdit 控件，将创建空的管理器")
            except:
                pass  # 如果获取logger失败，忽略警告

        super().__init__(parent)

        # -------------------------------------------------
        # 连接内部信号到槽函数，以实现线程安全的UI更新
        # -------------------------------------------------
        self.__set_text_signal.connect(self.__on_set_text)
        self.__set_html_signal.connect(self.__on_set_html)
        self.__append_text_signal.connect(self.__on_append_text)
        self.__append_html_signal.connect(self.__on_append_html)
        self.__clear_signal.connect(self.__on_clear)
        self.__set_font_signal.connect(self.__on_set_font)
        self.__set_style_sheet_signal.connect(self.__on_set_style_sheet)
        self.__set_enabled_signal.connect(self.__on_set_enabled)
        self.__set_read_only_signal.connect(self.__on_set_read_only)
        self.__set_visible_signal.connect(self.__on_set_visible)

        # 内部存储和记录
        self.__text_edit_controls: Dict[str, QTextEdit] = {}  # 存储每个QTextEdit控件
        self.__logger: Logger = ClassInstanceManager.get_instance(key="ui_logger")  # type: ignore
        self.__logger.debug("初始化 TextEditManager...")

        # 处理传入的QTextEdit控件
        for item in text_edits:
            if isinstance(item, QTextEdit):
                self.__add_text_edit(item)
            elif isinstance(item, list):
                for text_edit in item:
                    if isinstance(text_edit, QTextEdit):
                        self.__add_text_edit(text_edit)
                    else:
                        self.__logger.warning(f"列表中的项目不是QTextEdit类型: {type(text_edit)}")
            else:
                self.__logger.warning(f"传入的参数不是QTextEdit或QTextEdit列表: {type(item)}")

        self.__logger.debug(f"TextEditManager初始化完成，共管理{len(self.__text_edit_controls)}个文本框")

        # 标记为已初始化
        self.__class__._initialized = True

    def __add_text_edit(self, text_edit: QTextEdit) -> None:
        """
        添加一个QTextEdit到管理器中

        Args:
            text_edit: 要添加的QTextEdit实例
        """
        # 检查QTextEdit是否已被删除
        if sip.isdeleted(text_edit):
            self.__logger.warning(f"尝试添加一个已被删除的QTextEdit，已跳过")
            return

        # 获取控件名称
        name = text_edit.objectName()
        if not name:
            self.__logger.warning(f"QTextEdit没有设置objectName，将使用默认名称")
            name = f"text_edit_{len(self.__text_edit_controls)}"
            text_edit.setObjectName(name)

        # 检查是否已存在同名控件
        if name in self.__text_edit_controls:
            self.__logger.warning(f"已存在名为'{name}'的QTextEdit，将覆盖原有控件")

        try:
            # 添加到管理字典
            self.__text_edit_controls[name] = text_edit

            # 连接信号
            self.__connect_text_edit_signals(name, text_edit)

            self.__logger.debug(f"成功添加QTextEdit: {name}")

        except Exception as e:
            self.__logger.error(f"添加QTextEdit '{name}' 时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __connect_text_edit_signals(self, name: str, text_edit: QTextEdit) -> None:
        """
        连接QTextEdit的信号到TextEditManager的信号

        Args:
            name: QTextEdit的名称
            text_edit: QTextEdit实例
        """
        try:
            # 连接文本变化信号
            text_edit.textChanged.connect(lambda: self.text_changed.emit(name, text_edit.toPlainText()))

            # 连接光标位置变化信号
            text_edit.cursorPositionChanged.connect(
                lambda: self.cursor_position_changed.emit(name, text_edit.textCursor().position())
            )

            # 连接选择变化信号
            text_edit.selectionChanged.connect(
                lambda: self.selection_changed.emit(name, text_edit.textCursor().selectedText())
            )

            self.__logger.debug(f"成功连接QTextEdit '{name}' 的信号")

        except Exception as e:
            self.__logger.error(f"连接QTextEdit '{name}' 信号时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __get_control(self, name: str) -> Optional[QTextEdit]:
        """
        获取指定名称的QTextEdit实例

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            QTextEdit实例，如果未找到或已被删除则返回None
        """
        if name not in self.__text_edit_controls:
            self.__logger.warning(f"未找到名为'{name}'的QTextEdit")
            return None

        text_edit = self.__text_edit_controls[name]

        # 检查控件是否已被删除
        try:
            if sip.isdeleted(text_edit):
                self.__logger.warning(f"QTextEdit '{name}' 已被删除，从管理器中移除")
                del self.__text_edit_controls[name]
                return None
        except Exception as e:
            self.__logger.error(f"检查QTextEdit '{name}' 状态时出错: {str(e)}")
            return None

        return text_edit

    # =================================================================================
    # 私有槽函数 - 线程安全的UI更新
    # =================================================================================

    def __on_set_text(self, name: str, text: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置文本。
        此方法由 __set_text_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setPlainText(text)
            self.__logger.debug(f"设置QTextEdit '{name}' 的文本内容")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 文本时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_html(self, name: str, html: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置HTML内容。
        此方法由 __set_html_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setHtml(html)
            self.__logger.debug(f"设置QTextEdit '{name}' 的HTML内容")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' HTML时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_append_text(self, name: str, text: str) -> None:
        """
        (私有槽) 在GUI线程中安全地追加文本。
        此方法由 __append_text_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.append(text)
            self.__logger.debug(f"向QTextEdit '{name}' 追加文本内容")
        except Exception as e:
            self.__logger.error(f"向QTextEdit '{name}' 追加文本时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_append_html(self, name: str, html: str) -> None:
        """
        (私有槽) 在GUI线程中安全地追加HTML内容。
        此方法由 __append_html_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.insertHtml(html)
            self.__logger.debug(f"向QTextEdit '{name}' 追加HTML内容")
        except Exception as e:
            self.__logger.error(f"向QTextEdit '{name}' 追加HTML时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_clear(self, name: str) -> None:
        """
        (私有槽) 在GUI线程中安全地清空文本。
        此方法由 __clear_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.clear()
            self.__logger.debug(f"清空QTextEdit '{name}' 的内容")
        except Exception as e:
            self.__logger.error(f"清空QTextEdit '{name}' 时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_font(self, name: str, font_dict: dict) -> None:
        """
        (私有槽) 在GUI线程中安全地设置字体。
        此方法由 __set_font_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 创建字体对象
            font = QFont()
            if 'family' in font_dict:
                font.setFamily(font_dict['family'])
            if 'size' in font_dict:
                font.setPointSize(font_dict['size'])
            if 'bold' in font_dict:
                font.setBold(font_dict['bold'])
            if 'italic' in font_dict:
                font.setItalic(font_dict['italic'])

            # 执行实际的UI更新
            text_edit.setFont(font)
            self.__logger.debug(f"设置QTextEdit '{name}' 的字体")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 字体时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_style_sheet(self, name: str, style_sheet: str) -> None:
        """
        (私有槽) 在GUI线程中安全地设置样式表。
        此方法由 __set_style_sheet_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setStyleSheet(style_sheet)
            self.__logger.debug(f"设置QTextEdit '{name}' 的样式表")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 样式表时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_enabled(self, name: str, enabled: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置启用状态。
        此方法由 __set_enabled_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setEnabled(enabled)
            self.enabled_changed.emit(name, enabled)
            self.__logger.debug(f"设置QTextEdit '{name}' 的启用状态为: {enabled}")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 启用状态时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_read_only(self, name: str, read_only: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置只读状态。
        此方法由 __set_read_only_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setReadOnly(read_only)
            self.read_only_changed.emit(name, read_only)
            self.__logger.debug(f"设置QTextEdit '{name}' 的只读状态为: {read_only}")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 只读状态时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    def __on_set_visible(self, name: str, visible: bool) -> None:
        """
        (私有槽) 在GUI线程中安全地设置可见性。
        此方法由 __set_visible_signal 信号触发。
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return
        try:
            # 执行实际的UI更新
            text_edit.setVisible(visible)
            self.visibility_changed.emit(name, visible)
            self.__logger.debug(f"设置QTextEdit '{name}' 的可见性为: {visible}")
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 可见性时出错: {str(e)}")
            self.__logger.debug(traceback.format_exc())

    # =================================================================================
    # 单例模式管理方法
    # =================================================================================

    @classmethod
    def get_instance(cls, *text_edits: Union[QTextEdit, List[QTextEdit]], parent: Optional[QObject] = None) -> 'TextEditManager':
        """
        获取TextEditManager的单例实例

        Args:
            *text_edits: QTextEdit控件参数（首次创建时需要）
            parent: 父对象（可选）

        Returns:
            TextEditManager: 单例实例

        使用示例:
            ```python
            # 首次创建
            manager = TextEditManager.get_instance(edit1, edit2, edit3)

            # 后续获取
            manager = TextEditManager.get_instance()
            ```
        """
        return cls(*text_edits, parent=parent)

    @classmethod
    def is_instance_created(cls) -> bool:
        """
        检查单例实例是否已经创建

        Returns:
            bool: 如果实例已创建且有效返回True，否则返回False

        使用示例:
            ```python
            if TextEditManager.is_instance_created():
                manager = TextEditManager.get_instance()
            else:
                manager = TextEditManager.get_instance(edit1, edit2)
            ```
        """
        if cls._instance is None:
            return False

        try:
            # 检查实例是否仍然有效
            return not sip.isdeleted(cls._instance)
        except RuntimeError:
            return False

    @classmethod
    def reset_instance(cls) -> None:
        """
        重置单例实例，下次调用get_instance时将创建新实例

        注意：这会清除当前实例的所有状态和管理的控件

        使用示例:
            ```python
            # 重置实例
            TextEditManager.reset_instance()

            # 创建新实例
            new_manager = TextEditManager.get_instance(new_edit1, new_edit2)
            ```
        """
        with cls._lock:
            if cls._instance is not None:
                try:
                    # 尝试清理现有实例
                    if not sip.isdeleted(cls._instance):
                        cls._instance.cleanup()
                except:
                    pass  # 忽略清理过程中的错误

            cls._instance = None
            cls._initialized = False
            cls._cached_args = None
            cls._cached_kwargs = None

    @classmethod
    def get_cached_args(cls) -> tuple:
        """
        获取缓存的构造参数

        Returns:
            tuple: (args, kwargs) 缓存的参数

        使用示例:
            ```python
            args, kwargs = TextEditManager.get_cached_args()
            print(f"缓存了 {len(args)} 个控件参数")
            ```
        """
        return cls._cached_args or (), cls._cached_kwargs or {}

    # =================================================================================
    # 控件管理方法
    # =================================================================================

    def add_text_edit(self, text_edit: QTextEdit) -> bool:
        """
        添加一个QTextEdit控件到管理器

        Args:
            text_edit: 要添加的QTextEdit实例

        Returns:
            bool: 添加成功返回True，失败返回False

        使用示例:
            ```python
            new_edit = QTextEdit()
            new_edit.setObjectName("new_notes")

            success = manager.add_text_edit(new_edit)
            if success:
                print("控件添加成功")
            ```
        """
        try:
            self.__add_text_edit(text_edit)
            return True
        except Exception as e:
            self.__logger.error(f"添加QTextEdit时出错: {str(e)}")
            return False

    def remove_text_edit(self, name: str) -> bool:
        """
        从管理器中移除指定的QTextEdit控件

        Args:
            name: 要移除的QTextEdit的名称（objectName）

        Returns:
            bool: 移除成功返回True，失败返回False

        使用示例:
            ```python
            success = manager.remove_text_edit("notes")
            if success:
                print("控件移除成功")
            ```
        """
        try:
            if name in self.__text_edit_controls:
                text_edit = self.__text_edit_controls[name]

                # 断开信号连接
                try:
                    text_edit.textChanged.disconnect()
                    text_edit.cursorPositionChanged.disconnect()
                    text_edit.selectionChanged.disconnect()
                except:
                    pass  # 忽略断开连接时的错误

                # 从字典中移除
                del self.__text_edit_controls[name]

                self.__logger.debug(f"成功移除QTextEdit: {name}")
                return True
            else:
                self.__logger.warning(f"未找到名为'{name}'的QTextEdit")
                return False

        except Exception as e:
            self.__logger.error(f"移除QTextEdit '{name}' 时出错: {str(e)}")
            return False

    def get_text_edit(self, name: str) -> Optional[QTextEdit]:
        """
        获取指定名称的QTextEdit实例

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            QTextEdit: QTextEdit实例，如果未找到则返回None

        使用示例:
            ```python
            notes_edit = manager.get_text_edit("notes")
            if notes_edit:
                # 直接使用QTextEdit的原生方法
                notes_edit.moveCursor(QTextCursor.End)
            ```
        """
        return self.__get_control(name)

    def get_all_names(self) -> List[str]:
        """
        获取所有管理的QTextEdit控件名称

        Returns:
            List[str]: 所有控件名称的列表

        使用示例:
            ```python
            names = manager.get_all_names()
            print(f"管理的控件: {', '.join(names)}")
            ```
        """
        # 清理已删除的控件
        self.__cleanup_deleted_controls()
        return list(self.__text_edit_controls.keys())

    def get_control_count(self) -> int:
        """
        获取当前管理的QTextEdit控件数量

        Returns:
            int: 控件数量

        使用示例:
            ```python
            count = manager.get_control_count()
            print(f"当前管理 {count} 个文本框")
            ```
        """
        # 清理已删除的控件
        self.__cleanup_deleted_controls()
        return len(self.__text_edit_controls)

    def has_text_edit(self, name: str) -> bool:
        """
        检查是否存在指定名称的QTextEdit控件

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 存在返回True，不存在返回False

        使用示例:
            ```python
            if manager.has_text_edit("notes"):
                print("notes文本框存在")
            ```
        """
        return self.__get_control(name) is not None

    def __cleanup_deleted_controls(self) -> None:
        """
        清理已被删除的控件引用
        """
        deleted_names = []
        for name, text_edit in self.__text_edit_controls.items():
            try:
                if sip.isdeleted(text_edit):
                    deleted_names.append(name)
            except:
                deleted_names.append(name)

        for name in deleted_names:
            del self.__text_edit_controls[name]
            self.__logger.debug(f"清理已删除的QTextEdit: {name}")

    def cleanup(self) -> None:
        """
        清理资源，断开所有信号连接

        使用示例:
            ```python
            manager.cleanup()
            ```
        """
        try:
            # 断开所有控件的信号连接
            for name, text_edit in self.__text_edit_controls.items():
                try:
                    if not sip.isdeleted(text_edit):
                        text_edit.textChanged.disconnect()
                        text_edit.cursorPositionChanged.disconnect()
                        text_edit.selectionChanged.disconnect()
                except:
                    pass

            # 清空控件字典
            self.__text_edit_controls.clear()

            self.__logger.debug("TextEditManager 资源清理完成")

        except Exception as e:
            self.__logger.error(f"清理TextEditManager资源时出错: {str(e)}")

    def __del__(self):
        """析构函数，确保资源被释放"""
        try:
            self.cleanup()
        except:
            pass

    # =================================================================================
    # 文本内容操作方法
    # =================================================================================

    def set_text(self, name: str, text: str) -> bool:
        """
        设置指定QTextEdit的纯文本内容

        Args:
            name: QTextEdit的名称（objectName）
            text: 要设置的文本内容

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 设置笔记内容
            success = manager.set_text("notes", "这是一些重要的笔记内容")
            if success:
                print("文本设置成功")
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_text_signal.emit(name, text)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 文本时出错: {str(e)}")
            return False

    def set_html(self, name: str, html: str) -> bool:
        """
        设置指定QTextEdit的HTML内容

        Args:
            name: QTextEdit的名称（objectName）
            html: 要设置的HTML内容

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 设置HTML格式的描述
            html_content = "<h1>标题</h1><p>这是<b>粗体</b>文本</p>"
            success = manager.set_html("description", html_content)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_html_signal.emit(name, html)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' HTML时出错: {str(e)}")
            return False

    def get_text(self, name: str) -> Optional[str]:
        """
        获取指定QTextEdit的纯文本内容

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            str: 文本内容，如果控件不存在则返回None

        使用示例:
            ```python
            # 获取笔记内容
            notes_content = manager.get_text("notes")
            if notes_content:
                print(f"笔记内容: {notes_content}")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            return text_edit.toPlainText()
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' 文本时出错: {str(e)}")
            return None

    def get_html(self, name: str) -> Optional[str]:
        """
        获取指定QTextEdit的HTML内容

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            str: HTML内容，如果控件不存在则返回None

        使用示例:
            ```python
            # 获取HTML格式的描述
            html_content = manager.get_html("description")
            if html_content:
                print(f"HTML内容: {html_content}")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            return text_edit.toHtml()
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' HTML时出错: {str(e)}")
            return None

    def append_text(self, name: str, text: str) -> bool:
        """
        向指定QTextEdit追加纯文本内容

        Args:
            name: QTextEdit的名称（objectName）
            text: 要追加的文本内容

        Returns:
            bool: 追加成功返回True，失败返回False

        使用示例:
            ```python
            # 向日志追加新条目
            success = manager.append_text("log", "\\n[INFO] 新的日志条目")
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__append_text_signal.emit(name, text)
            return True
        except Exception as e:
            self.__logger.error(f"向QTextEdit '{name}' 追加文本时出错: {str(e)}")
            return False

    def append_html(self, name: str, html: str) -> bool:
        """
        向指定QTextEdit追加HTML内容

        Args:
            name: QTextEdit的名称（objectName）
            html: 要追加的HTML内容

        Returns:
            bool: 追加成功返回True，失败返回False

        使用示例:
            ```python
            # 向日志追加HTML格式的错误信息
            error_html = "<p style='color:red;'>[ERROR] 发生错误</p>"
            success = manager.append_html("log", error_html)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__append_html_signal.emit(name, html)
            return True
        except Exception as e:
            self.__logger.error(f"向QTextEdit '{name}' 追加HTML时出错: {str(e)}")
            return False

    def clear(self, name: str) -> bool:
        """
        清空指定QTextEdit的内容

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 清空成功返回True，失败返回False

        使用示例:
            ```python
            # 清空笔记内容
            success = manager.clear("notes")
            if success:
                print("内容已清空")
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__clear_signal.emit(name)
            return True
        except Exception as e:
            self.__logger.error(f"清空QTextEdit '{name}' 时出错: {str(e)}")
            return False

    def is_empty(self, name: str) -> Optional[bool]:
        """
        检查指定QTextEdit是否为空

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 为空返回True，不为空返回False，控件不存在返回None

        使用示例:
            ```python
            if manager.is_empty("notes"):
                print("笔记为空")
            ```
        """
        text = self.get_text(name)
        if text is None:
            return None
        return len(text.strip()) == 0

    def get_text_length(self, name: str) -> Optional[int]:
        """
        获取指定QTextEdit的文本长度

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            int: 文本长度，控件不存在返回None

        使用示例:
            ```python
            length = manager.get_text_length("notes")
            if length is not None:
                print(f"笔记长度: {length} 字符")
            ```
        """
        text = self.get_text(name)
        if text is None:
            return None
        return len(text)

    # =================================================================================
    # 批量操作方法
    # =================================================================================

    def set_text_batch(self, text_dict: Dict[str, str]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的文本内容

        Args:
            text_dict: 字典，键为控件名称，值为要设置的文本内容

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置文本
            results = manager.set_text_batch({
                "notes": "笔记内容",
                "description": "描述内容",
                "log": "日志内容"
            })

            for name, success in results.items():
                print(f"{name}: {'成功' if success else '失败'}")
            ```
        """
        results = {}
        for name, text in text_dict.items():
            results[name] = self.set_text(name, text)
        return results

    def set_html_batch(self, html_dict: Dict[str, str]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的HTML内容

        Args:
            html_dict: 字典，键为控件名称，值为要设置的HTML内容

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置HTML
            results = manager.set_html_batch({
                "description": "<h1>标题</h1><p>内容</p>",
                "help": "<b>帮助信息</b>"
            })
            ```
        """
        results = {}
        for name, html in html_dict.items():
            results[name] = self.set_html(name, html)
        return results

    def get_text_batch(self, names: List[str]) -> Dict[str, Optional[str]]:
        """
        批量获取多个QTextEdit的文本内容

        Args:
            names: 控件名称列表

        Returns:
            Dict[str, Optional[str]]: 字典，键为控件名称，值为文本内容（失败时为None）

        使用示例:
            ```python
            # 批量获取文本
            texts = manager.get_text_batch(["notes", "description", "log"])
            for name, text in texts.items():
                if text is not None:
                    print(f"{name}: {text[:50]}...")
            ```
        """
        results = {}
        for name in names:
            results[name] = self.get_text(name)
        return results

    def get_all_texts(self) -> Dict[str, Optional[str]]:
        """
        获取所有管理的QTextEdit的文本内容

        Returns:
            Dict[str, Optional[str]]: 字典，键为控件名称，值为文本内容

        使用示例:
            ```python
            # 获取所有文本内容
            all_texts = manager.get_all_texts()
            for name, text in all_texts.items():
                if text:
                    print(f"{name}: {len(text)} 字符")
            ```
        """
        names = self.get_all_names()
        return self.get_text_batch(names)

    def clear_batch(self, names: List[str]) -> Dict[str, bool]:
        """
        批量清空多个QTextEdit的内容

        Args:
            names: 要清空的控件名称列表

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量清空指定控件
            results = manager.clear_batch(["notes", "description"])
            ```
        """
        results = {}
        for name in names:
            results[name] = self.clear(name)
        return results

    def clear_all(self) -> Dict[str, bool]:
        """
        清空所有管理的QTextEdit的内容

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 清空所有文本框
            results = manager.clear_all()
            success_count = sum(1 for success in results.values() if success)
            print(f"成功清空 {success_count} 个文本框")
            ```
        """
        names = self.get_all_names()
        return self.clear_batch(names)

    def append_text_batch(self, text_dict: Dict[str, str]) -> Dict[str, bool]:
        """
        批量向多个QTextEdit追加文本内容

        Args:
            text_dict: 字典，键为控件名称，值为要追加的文本内容

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量追加文本
            results = manager.append_text_batch({
                "log": "\\n[INFO] 新日志条目",
                "notes": "\\n补充说明"
            })
            ```
        """
        results = {}
        for name, text in text_dict.items():
            results[name] = self.append_text(name, text)
        return results

    # =================================================================================
    # 样式和属性管理方法
    # =================================================================================

    def set_font(self, name: str, family: str = None, size: int = None, bold: bool = None, italic: bool = None) -> bool:
        """
        设置指定QTextEdit的字体

        Args:
            name: QTextEdit的名称（objectName）
            family: 字体族名称（如 "Arial", "宋体"）
            size: 字体大小（点数）
            bold: 是否粗体
            italic: 是否斜体

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 设置字体
            success = manager.set_font("notes", family="Arial", size=12, bold=True)

            # 只设置字体大小
            manager.set_font("description", size=14)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            font_dict = {}
            if family is not None:
                font_dict['family'] = family
            if size is not None:
                font_dict['size'] = size
            if bold is not None:
                font_dict['bold'] = bold
            if italic is not None:
                font_dict['italic'] = italic

            if font_dict:  # 只有在有参数时才发射信号
                self.__set_font_signal.emit(name, font_dict)
                return True
            else:
                self.__logger.warning(f"设置QTextEdit '{name}' 字体时未提供任何参数")
                return False

        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 字体时出错: {str(e)}")
            return False

    def set_style_sheet(self, name: str, style_sheet: str) -> bool:
        """
        设置指定QTextEdit的样式表

        Args:
            name: QTextEdit的名称（objectName）
            style_sheet: CSS样式表字符串

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 设置样式表
            style = '''
                QTextEdit {
                    background-color: #f0f0f0;
                    border: 2px solid #ccc;
                    border-radius: 5px;
                    padding: 5px;
                }
            '''
            success = manager.set_style_sheet("notes", style)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_style_sheet_signal.emit(name, style_sheet)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 样式表时出错: {str(e)}")
            return False

    def set_enabled(self, name: str, enabled: bool) -> bool:
        """
        设置指定QTextEdit的启用状态

        Args:
            name: QTextEdit的名称（objectName）
            enabled: 是否启用

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 禁用文本框
            manager.set_enabled("notes", False)

            # 启用文本框
            manager.set_enabled("notes", True)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_enabled_signal.emit(name, enabled)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 启用状态时出错: {str(e)}")
            return False

    def set_read_only(self, name: str, read_only: bool) -> bool:
        """
        设置指定QTextEdit的只读状态

        Args:
            name: QTextEdit的名称（objectName）
            read_only: 是否只读

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 设置为只读
            manager.set_read_only("log", True)

            # 设置为可编辑
            manager.set_read_only("notes", False)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_read_only_signal.emit(name, read_only)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 只读状态时出错: {str(e)}")
            return False

    def set_visible(self, name: str, visible: bool) -> bool:
        """
        设置指定QTextEdit的可见性

        Args:
            name: QTextEdit的名称（objectName）
            visible: 是否可见

        Returns:
            bool: 设置成功返回True，失败返回False

        使用示例:
            ```python
            # 隐藏文本框
            manager.set_visible("notes", False)

            # 显示文本框
            manager.set_visible("notes", True)
            ```
        """
        if not self.has_text_edit(name):
            return False

        try:
            self.__set_visible_signal.emit(name, visible)
            return True
        except Exception as e:
            self.__logger.error(f"设置QTextEdit '{name}' 可见性时出错: {str(e)}")
            return False

    def get_enabled(self, name: str) -> Optional[bool]:
        """
        获取指定QTextEdit的启用状态

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 启用状态，控件不存在返回None

        使用示例:
            ```python
            if manager.get_enabled("notes"):
                print("文本框已启用")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            return text_edit.isEnabled()
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' 启用状态时出错: {str(e)}")
            return None

    def get_read_only(self, name: str) -> Optional[bool]:
        """
        获取指定QTextEdit的只读状态

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 只读状态，控件不存在返回None

        使用示例:
            ```python
            if manager.get_read_only("log"):
                print("日志文本框是只读的")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            return text_edit.isReadOnly()
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' 只读状态时出错: {str(e)}")
            return None

    def get_visible(self, name: str) -> Optional[bool]:
        """
        获取指定QTextEdit的可见性

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            bool: 可见性，控件不存在返回None

        使用示例:
            ```python
            if manager.get_visible("notes"):
                print("文本框是可见的")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            return text_edit.isVisible()
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' 可见性时出错: {str(e)}")
            return None

    # =================================================================================
    # 批量样式和属性管理方法
    # =================================================================================

    def set_enabled_batch(self, enabled_dict: Dict[str, bool]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的启用状态

        Args:
            enabled_dict: 字典，键为控件名称，值为启用状态

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置启用状态
            results = manager.set_enabled_batch({
                "notes": True,
                "description": False,
                "log": True
            })
            ```
        """
        results = {}
        for name, enabled in enabled_dict.items():
            results[name] = self.set_enabled(name, enabled)
        return results

    def set_enabled_all(self, enabled: bool) -> Dict[str, bool]:
        """
        设置所有管理的QTextEdit的启用状态

        Args:
            enabled: 启用状态

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 禁用所有文本框
            results = manager.set_enabled_all(False)
            ```
        """
        names = self.get_all_names()
        enabled_dict = {name: enabled for name in names}
        return self.set_enabled_batch(enabled_dict)

    def set_read_only_batch(self, read_only_dict: Dict[str, bool]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的只读状态

        Args:
            read_only_dict: 字典，键为控件名称，值为只读状态

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置只读状态
            results = manager.set_read_only_batch({
                "log": True,      # 日志设为只读
                "notes": False,   # 笔记可编辑
                "description": False
            })
            ```
        """
        results = {}
        for name, read_only in read_only_dict.items():
            results[name] = self.set_read_only(name, read_only)
        return results

    def set_read_only_all(self, read_only: bool) -> Dict[str, bool]:
        """
        设置所有管理的QTextEdit的只读状态

        Args:
            read_only: 只读状态

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 设置所有文本框为只读
            results = manager.set_read_only_all(True)
            ```
        """
        names = self.get_all_names()
        read_only_dict = {name: read_only for name in names}
        return self.set_read_only_batch(read_only_dict)

    def set_visible_batch(self, visible_dict: Dict[str, bool]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的可见性

        Args:
            visible_dict: 字典，键为控件名称，值为可见性

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置可见性
            results = manager.set_visible_batch({
                "notes": True,
                "description": False,
                "log": True
            })
            ```
        """
        results = {}
        for name, visible in visible_dict.items():
            results[name] = self.set_visible(name, visible)
        return results

    def set_visible_all(self, visible: bool) -> Dict[str, bool]:
        """
        设置所有管理的QTextEdit的可见性

        Args:
            visible: 可见性

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 隐藏所有文本框
            results = manager.set_visible_all(False)
            ```
        """
        names = self.get_all_names()
        visible_dict = {name: visible for name in names}
        return self.set_visible_batch(visible_dict)

    def set_style_sheet_batch(self, style_dict: Dict[str, str]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的样式表

        Args:
            style_dict: 字典，键为控件名称，值为样式表字符串

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置样式
            styles = {
                "notes": "background-color: #f0f0f0; border: 1px solid #ccc;",
                "log": "background-color: #000; color: #0f0; font-family: monospace;"
            }
            results = manager.set_style_sheet_batch(styles)
            ```
        """
        results = {}
        for name, style_sheet in style_dict.items():
            results[name] = self.set_style_sheet(name, style_sheet)
        return results

    def set_font_batch(self, font_dict: Dict[str, Dict[str, Any]]) -> Dict[str, bool]:
        """
        批量设置多个QTextEdit的字体

        Args:
            font_dict: 字典，键为控件名称，值为字体参数字典

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为操作是否成功

        使用示例:
            ```python
            # 批量设置字体
            fonts = {
                "notes": {"family": "Arial", "size": 12, "bold": True},
                "log": {"family": "Consolas", "size": 10},
                "description": {"size": 14, "italic": True}
            }
            results = manager.set_font_batch(fonts)
            ```
        """
        results = {}
        for name, font_params in font_dict.items():
            results[name] = self.set_font(
                name,
                family=font_params.get('family'),
                size=font_params.get('size'),
                bold=font_params.get('bold'),
                italic=font_params.get('italic')
            )
        return results

    # =================================================================================
    # 状态查询和验证功能
    # =================================================================================

    def get_all_states(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有管理的QTextEdit的状态信息

        Returns:
            Dict[str, Dict[str, Any]]: 字典，键为控件名称，值为状态信息字典

        使用示例:
            ```python
            # 获取所有控件状态
            states = manager.get_all_states()
            for name, state in states.items():
                print(f"{name}: 启用={state['enabled']}, 只读={state['read_only']}")
            ```
        """
        results = {}
        names = self.get_all_names()

        for name in names:
            text_edit = self.__get_control(name)
            if text_edit:
                try:
                    results[name] = {
                        'enabled': text_edit.isEnabled(),
                        'read_only': text_edit.isReadOnly(),
                        'visible': text_edit.isVisible(),
                        'has_focus': text_edit.hasFocus(),
                        'text_length': len(text_edit.toPlainText()),
                        'is_empty': len(text_edit.toPlainText().strip()) == 0,
                        'cursor_position': text_edit.textCursor().position(),
                        'has_selection': text_edit.textCursor().hasSelection()
                    }
                except Exception as e:
                    self.__logger.error(f"获取QTextEdit '{name}' 状态时出错: {str(e)}")
                    results[name] = None

        return results

    def validate_controls(self) -> Dict[str, bool]:
        """
        验证所有管理的QTextEdit控件是否仍然有效

        Returns:
            Dict[str, bool]: 字典，键为控件名称，值为是否有效

        使用示例:
            ```python
            # 验证控件有效性
            validation = manager.validate_controls()
            invalid_controls = [name for name, valid in validation.items() if not valid]
            if invalid_controls:
                print(f"无效控件: {', '.join(invalid_controls)}")
            ```
        """
        results = {}
        for name in list(self.__text_edit_controls.keys()):
            text_edit = self.__text_edit_controls[name]
            try:
                # 检查控件是否被删除
                if sip.isdeleted(text_edit):
                    results[name] = False
                    # 从管理字典中移除无效控件
                    del self.__text_edit_controls[name]
                else:
                    results[name] = True
            except Exception:
                results[name] = False
                # 从管理字典中移除无效控件
                if name in self.__text_edit_controls:
                    del self.__text_edit_controls[name]

        return results

    def get_control_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定QTextEdit控件的详细信息

        Args:
            name: QTextEdit的名称（objectName）

        Returns:
            Dict[str, Any]: 控件信息字典，控件不存在返回None

        使用示例:
            ```python
            # 获取控件详细信息
            info = manager.get_control_info("notes")
            if info:
                print(f"控件类型: {info['type']}")
                print(f"对象名称: {info['object_name']}")
                print(f"文本长度: {info['text_length']}")
            ```
        """
        text_edit = self.__get_control(name)
        if not text_edit:
            return None

        try:
            cursor = text_edit.textCursor()
            return {
                'type': type(text_edit).__name__,
                'object_name': text_edit.objectName(),
                'enabled': text_edit.isEnabled(),
                'read_only': text_edit.isReadOnly(),
                'visible': text_edit.isVisible(),
                'has_focus': text_edit.hasFocus(),
                'text_length': len(text_edit.toPlainText()),
                'html_length': len(text_edit.toHtml()),
                'is_empty': len(text_edit.toPlainText().strip()) == 0,
                'cursor_position': cursor.position(),
                'has_selection': cursor.hasSelection(),
                'selected_text': cursor.selectedText(),
                'line_count': text_edit.document().lineCount(),
                'accepts_rich_text': text_edit.acceptRichText(),
                'font_family': text_edit.font().family(),
                'font_size': text_edit.font().pointSize(),
                'style_sheet': text_edit.styleSheet()
            }
        except Exception as e:
            self.__logger.error(f"获取QTextEdit '{name}' 信息时出错: {str(e)}")
            return None

    def find_controls_by_state(self, **criteria) -> List[str]:
        """
        根据状态条件查找控件

        Args:
            **criteria: 状态条件，支持的键：
                       enabled, read_only, visible, has_focus, is_empty

        Returns:
            List[str]: 符合条件的控件名称列表

        使用示例:
            ```python
            # 查找所有启用且非只读的控件
            editable_controls = manager.find_controls_by_state(enabled=True, read_only=False)

            # 查找所有空的控件
            empty_controls = manager.find_controls_by_state(is_empty=True)

            # 查找所有可见且有焦点的控件
            focused_controls = manager.find_controls_by_state(visible=True, has_focus=True)
            ```
        """
        matching_controls = []
        states = self.get_all_states()

        for name, state in states.items():
            if state is None:
                continue

            match = True
            for key, value in criteria.items():
                if key in state and state[key] != value:
                    match = False
                    break

            if match:
                matching_controls.append(name)

        return matching_controls

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取管理器的统计信息

        Returns:
            Dict[str, Any]: 统计信息字典

        使用示例:
            ```python
            # 获取统计信息
            stats = manager.get_statistics()
            print(f"总控件数: {stats['total_controls']}")
            print(f"启用控件数: {stats['enabled_controls']}")
            print(f"只读控件数: {stats['read_only_controls']}")
            print(f"空控件数: {stats['empty_controls']}")
            ```
        """
        states = self.get_all_states()
        valid_states = {name: state for name, state in states.items() if state is not None}

        total_controls = len(valid_states)
        enabled_controls = sum(1 for state in valid_states.values() if state.get('enabled', False))
        read_only_controls = sum(1 for state in valid_states.values() if state.get('read_only', False))
        visible_controls = sum(1 for state in valid_states.values() if state.get('visible', False))
        empty_controls = sum(1 for state in valid_states.values() if state.get('is_empty', False))
        focused_controls = sum(1 for state in valid_states.values() if state.get('has_focus', False))

        total_text_length = sum(state.get('text_length', 0) for state in valid_states.values())

        return {
            'total_controls': total_controls,
            'enabled_controls': enabled_controls,
            'disabled_controls': total_controls - enabled_controls,
            'read_only_controls': read_only_controls,
            'editable_controls': total_controls - read_only_controls,
            'visible_controls': visible_controls,
            'hidden_controls': total_controls - visible_controls,
            'empty_controls': empty_controls,
            'non_empty_controls': total_controls - empty_controls,
            'focused_controls': focused_controls,
            'total_text_length': total_text_length,
            'average_text_length': total_text_length / total_controls if total_controls > 0 else 0
        }
