"""
常用16进制颜色值常量

此模块提供了一组预定义的16进制颜色常量，可用于日志输出和其他需要颜色的场景。
颜色值采用16进制格式，可以直接用于 Logger 类的颜色参数。

使用示例:
    >>> from tools.colors import Colors
    >>> logger.info("这是一条蓝色的信息", Colors.BLUE)
    >>> logger.warning("这是一条金色的警告", Colors.GOLD)
"""


class Colors:
    # 基础颜色
    BLACK = "#000000"          # 黑色
    WHITE = "#FFFFFF"          # 白色
    RED = "#FF0000"           # 红色
    GREEN = "#00FF00"         # 绿色
    BLUE = "#0000FF"          # 蓝色
    YELLOW = "#FFFF00"        # 黄色
    PURPLE = "#800080"        # 紫色
    CYAN = "#00FFFF"          # 青色
    MAGENTA = "#FF00FF"       # 品红
    GRAY = "#808080"          # 灰色

    # 暖色系
    ORANGE = "#FFA500"        # 橙色
    CORAL = "#FF7F50"         # 珊瑚色
    TOMATO = "#FF6347"        # 番茄红
    GOLD = "#FFD700"          # 金色
    PINK = "#FFC0CB"          # 粉色
    SALMON = "#FA8072"        # 鲑鱼色
    PEACH = "#FFE5B4"         # 桃色

    # 冷色系
    TEAL = "#008080"          # 青蓝色
    TURQUOISE = "#40E0D0"     # 绿宝石色
    SKYBLUE = "#87CEEB"       # 天蓝色
    INDIGO = "#4B0082"        # 靛蓝色
    NAVY = "#000080"          # 海军蓝
    AZURE = "#F0FFFF"         # 天蓝色

    # 自然色系
    BROWN = "#A52A2A"         # 棕色
    BEIGE = "#F5F5DC"         # 米色
    IVORY = "#FFFFF0"         # 象牙色
    KHAKI = "#F0E68C"         # 卡其色
    OLIVE = "#808000"         # 橄榄色
    MAROON = "#800000"        # 栗色
    FOREST_GREEN = "#228B22"   # 森林绿

    # 柔和色系
    LAVENDER = "#E6E6FA"      # 薰衣草色
    MINT = "#98FF98"          # 薄荷色
    PEACH_PUFF = "#FFDAB9"    # 粉扑桃色
    THISTLE = "#D8BFD8"       # 蓟色
    POWDER_BLUE = "#B0E0E6"   # 粉蓝色
    MISTY_ROSE = "#FFE4E1"    # 浅玫瑰色

    # 深色系
    DARK_RED = "#8B0000"      # 深红色
    DARK_GREEN = "#006400"    # 深绿色
    DARK_BLUE = "#00008B"     # 深蓝色
    DARK_CYAN = "#008B8B"     # 深青色
    DARK_MAGENTA = "#8B008B"  # 深品红
    DARK_ORANGE = "#FF8C00"   # 深橙色
    DARK_VIOLET = "#9400D3"   # 深紫罗兰

    # 亮色系
    LIGHT_PINK = "#FFB6C1"    # 亮粉色
    LIGHT_BLUE = "#ADD8E6"    # 亮蓝色
    LIGHT_GREEN = "#90EE90"   # 亮绿色
    LIGHT_YELLOW = "#FFFFE0"  # 亮黄色
    LIGHT_CYAN = "#E0FFFF"    # 亮青色
    LIGHT_GRAY = "#D3D3D3"    # 亮灰色

    # 特殊用途
    SUCCESS = "#28A745"       # 成功提示色
    ERROR = "#DC3545"         # 错误提示色
    WARNING = "#FFC107"       # 警告提示色
    INFO = "#17A2B8"          # 信息提示色
    PRIMARY = "#007BFF"       # 主要提示色
    SECONDARY = "#6C757D"     # 次要提示色

    # 调试颜色系列
    DEBUG = "#7B68EE"         # 调试基础色（中等紫罗兰蓝）
    DEBUG_HIGHLIGHT = "#FF69B4"  # 调试高亮色（热粉红）
    DEBUG_BACKGROUND = "#F0F8FF"  # 调试背景色（爱丽丝蓝）
    DEBUG_TRACE = "#20B2AA"    # 调试跟踪色（浅海洋绿）
    DEBUG_VARIABLE = "#FF7F50"  # 调试变量色（珊瑚色）
    DEBUG_FUNCTION = "#6A5ACD"  # 调试函数色（板岩蓝）
    DEBUG_TIMESTAMP = "#708090"  # 调试时间戳色（板岩灰）
    DEBUG_ASSERTION = "#FF4500"  # 调试断言色（橙红色）
    DEBUG_MEMORY = "#2E8B57"    # 调试内存信息色（海洋绿）
    DEBUG_NETWORK = "#4682B4"   # 调试网络信息色（钢蓝）
    DEBUG_IO = "#9932CC"        # 调试输入输出色（暗兰花紫）
    DEBUG_PERFORMANCE = "#DAA520"  # 调试性能信息色（金菊黄）
    VERBOSE = "#708090"         # 详细日志色（板岩灰）

    @classmethod
    def get_all_colors(cls) -> dict:
        """
        获取所有颜色常量及其对应的颜色值

        Returns:
            dict: 颜色名称和颜色值的字典
        """
        return {
            name: value for name, value in vars(cls).items()
            if isinstance(value, str) and value.startswith('#')
        }

    @classmethod
    def print_all_colors(cls):
        """
        打印所有可用的颜色名称及其对应的颜色值
        """
        colors = cls.get_all_colors()
        print("可用的颜色:")
        for name, value in colors.items():
            print(f"{name:15} = {value}")






