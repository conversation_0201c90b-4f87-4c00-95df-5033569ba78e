import json
import traceback
import logging

from global_tools.utils import Colors

class DBOperations:
    """
    数据库操作类，包含表操作相关的方法
    """
    
    @staticmethod
    def create_table(client, table_name, table_schema_json):
        """
        根据JSON模式创建表，支持丰富的PostgreSQL字段属性和约束
        :param client: PostgreSQLClient实例
        :param table_name: 表名
        :param table_schema_json: 表模式的JSON对象或JSON字符串，格式如下：
        {
                "columns": {
                        "字段名1": {"type": "数据类型", "primary_key": true/false, "nullable": true/false,
                                        "default": "默认值", "comment": "注释", "unique": true/false,
                                        "check": "CHECK条件", "references": "引用表.列", "on_delete": "CASCADE/SET NULL/...",
                                        "on_update": "CASCADE/...", "collate": "排序规则", "identity": true/false,
                                        "enum_values": ["值1", "值2", ...],  # 如果是枚举类型，定义可选值
                                        "create_type": true/false  # 是否需要创建枚举类型
                                        },
                        "字段名2": {"type": "数据类型", ...},
                        ...
                },
                "constraints": [  # 表级约束（可选）
                        {"type": "PRIMARY KEY", "columns": ["列1", "列2"]},
                        {"type": "FOREIGN KEY", "columns": ["列1"], "references": "引用表(引用列)", "on_delete": "CASCADE"},
                        {"type": "UNIQUE", "columns": ["列1", "列2"]},
                        {"type": "CHECK", "condition": "CHECK条件"}
                ],
                "indexes": [  # 索引定义（可选）
                        {"columns": ["列1", "列2"], "unique": true/false, "name": "索引名称", "method": "BTREE/GIN/..."},
                        ...
                ],
                "table_comment": "表注释"  # 表注释（可选）
        }

        也支持简化格式（向后兼容）：
        {
                "字段名1": {"type": "数据类型", "primary_key": true/false, ...},
                "字段名2": {"type": "数据类型", ...},
                ...
        }

        :return: 成功返回True，失败返回False
        """
        try:
            # 检查表是否已存在
            safe_table_name = table_name.replace('"', '""')
            if client._table_exists(table_name):
                client.logger.warning(f"表 '{table_name}' 已存在，不再创建。")
                return {"success": False, "error": f"表 '{table_name}' 已存在，创建失败。", "table_name": table_name, "reason": "table_already_exists"}

            # 如果输入是字符串，尝试解析成JSON对象
            if isinstance(table_schema_json, str):
                try:
                    table_schema_json = json.loads(table_schema_json)
                except json.JSONDecodeError as e:
                    client.logger.error(f"JSON解析错误: {e}")
                    return {"success": False, "error": f"JSON解析错误: {e}", "table_name": table_name}

            # 检查并处理新旧两种格式
            if "columns" in table_schema_json:
                # 新格式: {"columns": {...}, "constraints": [...], ...}
                columns_dict = table_schema_json.get("columns", {})
                constraints_list = table_schema_json.get("constraints", [])
                indexes_list = table_schema_json.get("indexes", [])
                table_comment = table_schema_json.get("table_comment", "")
            else:
                # 旧格式: {"字段名1": {...}, "字段名2": {...}, ...}
                columns_dict = table_schema_json
                constraints_list = []
                indexes_list = []
                table_comment = ""

            # 首先创建所需的枚举类型
            for column_name, column_def in columns_dict.items():
                if column_def.get("create_type", False) and "enum_values" in column_def:
                    enum_type_name = column_def["type"]
                    enum_values = column_def["enum_values"]

                    # 检查枚举类型是否已存在
                    check_type_sql = """
                        SELECT typname FROM pg_type 
                        WHERE typname = %s AND typnamespace = 
                            (SELECT oid FROM pg_namespace WHERE nspname = 'public')
                    """
                    if not client.execute_query(check_type_sql, (enum_type_name,), fetch=True):
                        # 创建枚举类型
                        enum_values_str = ", ".join(
                            f"'{val}'" for val in enum_values)
                        create_type_sql = f"CREATE TYPE {enum_type_name} AS ENUM ({enum_values_str})"
                        client.execute_query(create_type_sql)
                        client.logger.info(f"创建枚举类型 '{enum_type_name}' 成功")

            # 构建创建表的SQL语句
            column_definitions = []
            table_constraints = []
            comments = []  # 存储列注释，稍后单独执行
            indexes = []  # 存储索引创建语句，稍后单独执行

            # 处理列定义
            for column_name, column_def in columns_dict.items():
                safe_column_name = column_name.replace('"', '""')
                column_type = column_def.get("type", "VARCHAR(255)")

                # 基本属性
                attrs = []

                # 处理NULL约束
                if not column_def.get("nullable", True):
                    attrs.append("NOT NULL")

                # 处理DEFAULT值
                if "default" in column_def:
                    default_value = column_def.get("default")
                    attrs.append(f"DEFAULT {default_value}")

                # 处理单列主键
                if column_def.get("primary_key", False):
                    attrs.append("PRIMARY KEY")

                # 处理唯一约束
                if column_def.get("unique", False):
                    attrs.append("UNIQUE")

                # 处理CHECK约束
                if "check" in column_def:
                    check_condition = column_def.get("check")
                    attrs.append(f"CHECK ({check_condition})")

                # 处理排序规则
                if "collate" in column_def:
                    collate = column_def.get("collate")
                    attrs.append(f"COLLATE {collate}")

                # 处理IDENTITY/SERIAL (自增)
                if column_def.get("identity", False):
                    if "int" in column_type.lower():
                        attrs.append("GENERATED ALWAYS AS IDENTITY")

                # 处理单列外键引用
                if "references" in column_def:
                    ref_table_column = column_def.get("references")
                    ref_sql = f"REFERENCES {ref_table_column}"

                    # 处理ON DELETE
                    if "on_delete" in column_def:
                        ref_sql += f" ON DELETE {column_def.get('on_delete')}"

                    # 处理ON UPDATE
                    if "on_update" in column_def:
                        ref_sql += f" ON UPDATE {column_def.get('on_update')}"

                    attrs.append(ref_sql)

                # 组合列定义
                attrs_str = " ".join(attrs) if attrs else ""
                column_def_str = f'"{safe_column_name}" {column_type} {attrs_str}'.strip()
                column_definitions.append(column_def_str)

                # 处理列注释
                if "comment" in column_def:
                    comment = column_def.get("comment", "")
                    # 使用 $$ 作为引号，避免单引号转义问题
                    comments.append(
                        f'COMMENT ON COLUMN "{safe_table_name}"."{safe_column_name}" IS $${comment}$$;')

            # 处理表级约束
            for constraint in constraints_list:
                constraint_type = constraint.get("type", "").upper()

                if constraint_type == "PRIMARY KEY":
                    columns = constraint.get("columns", [])
                    if columns:
                        safe_cols = []
                        for c in columns:
                            safe_c = c.replace('"', '""')
                            safe_cols.append(f'"{safe_c}"')
                        columns_str = ", ".join(safe_cols)
                        table_constraints.append(
                            f"PRIMARY KEY ({columns_str})")

                elif constraint_type == "FOREIGN KEY":
                    columns = constraint.get("columns", [])
                    references = constraint.get("references", "")

                    if columns and references:
                        safe_cols = []
                        for c in columns:
                            safe_c = c.replace('"', '""')
                            safe_cols.append(f'"{safe_c}"')
                        columns_str = ", ".join(safe_cols)
                        # references 可能是 table(col)
                        if '(' in references and ')' in references:
                            ref_table, ref_col = references.split('(')
                            ref_col = ref_col.strip(')')
                            ref_table = ref_table.strip().replace('"', '""')
                            ref_col = ref_col.strip().replace('"', '""')
                            ref_str = f'"{ref_table}"("{ref_col}")'
                        else:
                            ref_str = references
                        constraint_str = f"FOREIGN KEY ({columns_str}) REFERENCES {ref_str}"

                        # 处理ON DELETE
                        if "on_delete" in constraint:
                            constraint_str += f" ON DELETE {constraint.get('on_delete')}"

                        # 处理ON UPDATE
                        if "on_update" in constraint:
                            constraint_str += f" ON UPDATE {constraint.get('on_update')}"

                        table_constraints.append(constraint_str)

                elif constraint_type == "UNIQUE":
                    columns = constraint.get("columns", [])
                    if columns:
                        safe_cols = []
                        for c in columns:
                            safe_c = c.replace('"', '""')
                            safe_cols.append(f'"{safe_c}"')
                        columns_str = ", ".join(safe_cols)
                        table_constraints.append(f"UNIQUE ({columns_str})")

                elif constraint_type == "CHECK":
                    condition = constraint.get("condition", "")
                    if condition:
                        table_constraints.append(f"CHECK ({condition})")

            # 将列定义和表级约束合并
            all_definitions = column_definitions + table_constraints
            columns_str = ", ".join(all_definitions)
            create_table_sql = f'CREATE TABLE "{safe_table_name}" ({columns_str})'

            # 执行创建表操作
            client.execute_query(create_table_sql)
            client.logger.info(f"表 '{table_name}' 创建成功。")

            # 如果有表注释，添加表注释
            if table_comment:
                table_comment_sql = f'COMMENT ON TABLE "{safe_table_name}" IS $${table_comment}$$;'
                client.execute_query(table_comment_sql)
                client.logger.info(f"已为表 '{table_name}' 添加表注释。")

            # 如果有列注释，逐个执行列注释语句
            if comments:
                for comment_sql in comments:
                    client.execute_query(comment_sql)
                client.logger.info(f"已为表 '{table_name}' 的列添加注释。")

            # 创建索引
            for index in indexes_list:
                columns = index.get("columns", [])
                if not columns:
                    continue

                index_name = index.get(
                    "name", f"idx_{safe_table_name}_{'_'.join(columns)}")
                unique = "UNIQUE " if index.get("unique", False) else ""
                method = index.get("method", "BTREE")
                safe_cols = []
                for c in columns:
                    safe_c = c.replace('"', '""')
                    safe_cols.append(f'"{safe_c}"')
                columns_str = ", ".join(safe_cols)

                # 添加 IF NOT EXISTS 防止重复创建索引
                create_index_sql = f'CREATE {unique}INDEX IF NOT EXISTS {index_name} ON "{safe_table_name}" USING {method} ({columns_str})'
                try:
                    client.execute_query(create_index_sql)
                    client.logger.info(
                        f"已为表 '{table_name}' 创建索引 '{index_name}'。")
                except Exception as e:
                    client.logger.warning(f"创建索引 '{index_name}' 失败: {str(e)}")
                    continue # 即使索引失败，表创建本身可能仍然是成功的

            return {"success": True, "message": f"表 '{table_name}' 创建成功。", "table_name": table_name}
        except Exception as e:
            client.logger.error(f"创建表 '{table_name}' 失败: {str(e)}")
            client.logger.error(f"创建表 '{table_name}' 失败，详细堆栈信息:", exc_info=True)
            return {"success": False, "error": f"创建表 '{table_name}' 失败: {str(e)}", "table_name": table_name}

    @staticmethod
    def add_column(client, table_name, column_json):
        """
        向现有表中增加列，列的信息使用 JSON 格式描述。
        支持与create_table相同的完整字段属性集。

        参数:
            client: PostgreSQLClient实例
            table_name (str): 表名。
            column_json (str或dict): 描述列信息的 JSON 字符串或字典对象。
            格式示例:
            {
                "name": "new_column",
                "type": "VARCHAR(255)",
                "nullable": false,
                "default": "默认值",
                "comment": "列注释",
                "unique": true,
                "check": "列值 > 0",
                "references": "other_table(other_column)",
                "on_delete": "CASCADE",
                "on_update": "SET NULL",
                "collate": "排序规则",
                "identity": true
            }

        返回:
            dict: 操作结果，包含以下字段：
                - success (bool): 操作是否成功
                - message/error: 成功/错误信息
                - table_name: 表名
                - column_name: 列名
                - timestamp: 操作时间戳
        """
        start_time = client._get_timestamp()

        try:
            # 解析JSON数据(如果是字符串)
            if isinstance(column_json, str):
                try:
                    column_info = json.loads(column_json)
                except json.JSONDecodeError as e:
                    client.logger.error(f"JSON 解析错误: {e}, 列信息: {column_json}")
                    return {
                        "success": False,
                        "error": f"无效的 JSON 格式: {e}",
                        "table_name": table_name,
                        "timestamp": client._get_timestamp()
                    }
            else:
                column_info = column_json  # 直接使用字典对象

            # 验证必要字段
            column_name = column_info.get("name")
            column_type = column_info.get("type")

            if not column_name or not column_type:
                error_msg = "列信息必须包含 'name' 和 'type' 字段。"
                client.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "table_name": table_name,
                    "timestamp": client._get_timestamp()
                }

            # 验证表是否存在
            if not client._table_exists(table_name):
                error_msg = f"表 '{table_name}' 不存在"
                client.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "timestamp": client._get_timestamp()
                }

            # 检查列是否已存在
            existing_columns = [c.strip('"') for c in client.get_all_columns(table_name).split(", ")]
            if column_name in existing_columns:
                return {
                    "success": False,
                    "message": f"列 '{column_name}' 已存在于表 '{table_name}' 中。",
                    "table_name": table_name,
                    "column_name": column_name,
                    "timestamp": client._get_timestamp()
                }

            # 构建属性列表
            attrs = []

            # 处理NULL约束
            if not column_info.get("nullable", True):
                attrs.append("NOT NULL")

            # 处理DEFAULT值
            if "default" in column_info:
                default_value = column_info.get("default")
                # 修复点：只加一层单引号，避免重复
                if isinstance(default_value, str) and not default_value.upper() in [
                        "CURRENT_TIMESTAMP", "NULL", "TRUE", "FALSE"]:
                    # 如果不是以单引号包裹，则加单引号
                    if not (default_value.startswith("'") and default_value.endswith("'")):
                        attrs.append(f"DEFAULT '{default_value}'")
                    else:
                        attrs.append(f"DEFAULT {default_value}")
                else:
                    attrs.append(f"DEFAULT {default_value}")

            # 处理唯一约束
            if column_info.get("unique", False):
                attrs.append("UNIQUE")

            # 处理CHECK约束
            if "check" in column_info:
                check_condition = column_info.get("check")
                attrs.append(f"CHECK ({check_condition})")

            # 处理排序规则
            if "collate" in column_info:
                collate = column_info.get("collate")
                attrs.append(f"COLLATE {collate}")

            # 处理IDENTITY/SERIAL (自增)
            if column_info.get("identity", False):
                if "int" in column_type.lower():
                    attrs.append("GENERATED ALWAYS AS IDENTITY")

            # 处理外键引用
            if "references" in column_info:
                ref_table_column = column_info.get("references")
                ref_sql = f"REFERENCES {ref_table_column}"

                # 处理ON DELETE
                if "on_delete" in column_info:
                    ref_sql += f" ON DELETE {column_info.get('on_delete')}"

                # 处理ON UPDATE
                if "on_update" in column_info:
                    ref_sql += f" ON UPDATE {column_info.get('on_update')}"

                attrs.append(ref_sql)

            # 构建 ALTER TABLE 语句
            attrs_str = " ".join(attrs) if attrs else ""
            safe_table_name = table_name.replace('"', '""')
            safe_column_name = column_name.replace('"', '""')
            alter_sql = f'ALTER TABLE "{safe_table_name}" ADD COLUMN "{safe_column_name}" {column_type} {attrs_str}'.strip()

            # 执行ALTER TABLE语句
            client.execute_query(alter_sql)
            client.logger.info(f"成功向表 '{table_name}' 添加列 '{column_name}'")

            # 处理列注释
            if "comment" in column_info:
                comment = column_info.get("comment", "")
                comment_sql = f'COMMENT ON COLUMN "{safe_table_name}"."{safe_column_name}" IS $${comment}$$;'
                try:
                    client.execute_query(comment_sql)
                    client.logger.info(f"成功为列 '{column_name}' 添加注释")
                except Exception as ce:
                    client.logger.warning(f"添加列注释失败: {ce}，但列已成功创建")

            # 处理主键（如果指定）- 需要单独处理，因为ALTER TABLE ADD COLUMN不能直接添加PRIMARY KEY约束
            if column_info.get("primary_key", False):
                try:
                    pk_sql = f'ALTER TABLE "{safe_table_name}" ADD PRIMARY KEY ("{safe_column_name}")'
                    client.execute_query(pk_sql)
                    client.logger.info(f"成功将列 '{column_name}' 设置为主键")
                except Exception as pke:
                    client.logger.warning(f"设置主键失败: {pke}，但列已成功创建")

            return {
                "success": True,
                "message": f"成功向表 '{table_name}' 添加列 '{column_name}'",
                "table_name": table_name,
                "column_name": column_name,
                "timestamp": client._get_timestamp(),
                "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
            }

        except Exception as e:
            client.logger.error(f"向表 '{table_name}' 添加列失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "timestamp": client._get_timestamp(),
                "execution_time_ms": int((client._get_timestamp() - start_time) * 1000)
            }
            
    @staticmethod
    def drop_table(client, table_name, force=False):
        """
        删除表。

        参数:
            client: PostgreSQLClient实例
            table_name (str): 要删除的表名。
            force (bool): 是否强制删除，处理不可见元组问题，默认为False。
                          设置为True时，会尝试执行VACUUM并使用CASCADE模式删除。
        
        返回:
            bool: 操作是否成功
        """
        try:
            # 首先尝试回滚任何未完成的事务
            client.execute_query("ROLLBACK;")
            
            if force:
                # 强制模式下，先尝试清理表
                try:
                    # VACUUM FULL需要在事务之外执行，或者连接处于autocommit模式
                    conn = client._get_connection() # 获取当前连接
                    original_autocommit = conn.autocommit
                    conn.autocommit = True # 临时设置为autocommit
                    try:
                        safe_table_name = table_name.replace('"', '""')
                        client.execute_query(f'VACUUM FULL "{safe_table_name}";')
                        client.logger.info(f"表 '{table_name}' VACUUM FULL 完成。")
                    finally:
                        conn.autocommit = original_autocommit # 恢复之前的autocommit状态
                except Exception as vacuum_e:
                    client.logger.warning(f"执行VACUUM FULL失败，继续尝试删除: {str(vacuum_e)}")
                
                # 使用CASCADE强制删除表及其依赖
                sql = f'DROP TABLE IF EXISTS "{safe_table_name}" CASCADE;'
            else:
                # 普通模式
                safe_table_name = table_name.replace('"', '""')
                sql = f'DROP TABLE IF EXISTS "{safe_table_name}";'
            
            # 在新事务中执行删除
            client.execute_query("BEGIN;")
            client.execute_query(sql)
            client.execute_query("COMMIT;")
            
            client.logger.info(f"表 '{table_name}' 删除成功。")
            return {"success": True, "message": f"表 '{table_name}' 删除成功。"}
        except Exception as e:
            client.logger.error(f"删除表 '{table_name}' 失败: {str(e)}")
            try:
                client.execute_query("ROLLBACK;")  # 确保回滚
            except Exception as rb_e:
                client.logger.error(f"尝试回滚删除表操作失败: {rb_e}")
            return {"success": False, "error": f"删除表 '{table_name}' 失败: {str(e)}"}

    @staticmethod
    def execute_sql_script(client, sql_script_path):
        """
        执行 SQL 脚本文件。

        参数:
            client: PostgreSQLClient实例
            sql_script_path (str): SQL 脚本文件路径。
        """
        try:
            with open(sql_script_path, "r", encoding="utf-8") as f:  # 指定 UTF-8 编码
                sql_script = f.read()
            # execute_query 可以执行多条 SQL 语句吗？需要测试，或者改为逐条执行
            client.execute_query(sql_script)
            client.logger.info(f"成功执行 SQL 脚本: {sql_script_path}")
        except FileNotFoundError:
            client.logger.error(f"SQL 脚本文件未找到: {sql_script_path}")
            raise
        except Exception as e:
            client.logger.error(f"执行 SQL 脚本 '{sql_script_path}' 失败: {e}")
            raise 