#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单独测试 PROCESS_COMPLETED_WITH_DATA 事件
"""

import sys
import os
import unittest

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from test_events_basic import TestBasicEvents
from global_tools.utils.manager_process3.process_event_manager import ProcessEventManager


class TestSingleEvent(TestBasicEvents):
    """单独测试 PROCESS_COMPLETED_WITH_DATA 事件"""
    
    def test_only_process_completed_with_data_event(self):
        """
        单独测试 PROCESS_COMPLETED_WITH_DATA 事件
        
        验证：
        1. 事件在所有工作进程退出且数据通知队列为空后触发
        2. 事件在 PROCESS_COMPLETED 之后触发
        3. 共享数据已正确处理
        """
        print("=" * 80)
        print("单独测试 PROCESS_COMPLETED_WITH_DATA 事件")
        print("=" * 80)
        
        self.logger.info("开始测试 PROCESS_COMPLETED_WITH_DATA 事件")
        
        # 创建数据共享测试场景
        scenario = self.mock_factory.create_test_scenario("data_sharing")
        data = self.mock_factory.create_data_for_scenario(scenario)
        worker = self.mock_factory.create_worker_for_scenario(scenario)
        
        print(f"测试场景: {scenario.name}")
        print(f"数据项数量: {len(data)}")
        print(f"工作进程数: {scenario.num_processes}")
        print(f"预期持续时间: {scenario.expected_duration}s")
        
        # 创建 ManagedMultiProcess 实例
        mp = self.create_mp_instance(data, worker, scenario.num_processes)
        
        # 注册相关事件处理器
        self.register_all_events(mp)
        
        # 添加事件回调函数来处理进程完成
        event_triggered = False
        event_data = None

        def on_process_completed_with_data(mp_instance, *args, **kwargs):
            nonlocal event_triggered, event_data
            event_triggered = True
            event_data = {'mp_instance': mp_instance, 'args': args, 'kwargs': kwargs}
            print("🎉 PROCESS_COMPLETED_WITH_DATA 事件被触发！")
            print(f"   事件数据: mp_instance={type(mp_instance).__name__}, args={args}, kwargs={kwargs}")

        # 注册事件回调
        mp.listen_event(ProcessEventManager.PROCESS_COMPLETED_WITH_DATA, on_process_completed_with_data)

        print("\n启动多进程处理...")
        # 启动处理
        mp.run()

        print("等待事件自动触发（真正的事件驱动机制）...")
        # 现在事件会由自动监控线程触发，无需调用 wait_all()
        import time
        start_time = time.time()
        timeout = scenario.expected_duration + 10.0

        # 等待事件被自动触发
        while not event_triggered and (time.time() - start_time) < timeout:
            time.sleep(0.1)  # 短暂休眠，让自动监控线程有机会检测和触发事件

        if not event_triggered:
            # 如果事件未触发，检查进程状态以便调试
            if hasattr(mp, 'worker_processes') and mp.worker_processes:
                alive_count = sum(1 for p in mp.worker_processes if p.is_alive())
                print(f"调试信息：当前活跃进程数: {alive_count}")
            self.fail(f"PROCESS_COMPLETED_WITH_DATA 事件在 {timeout}s 内未被自动触发")

        print("\n验证事件触发...")
        # 验证事件已被触发
        self.assertTrue(event_triggered, "PROCESS_COMPLETED_WITH_DATA 事件应该被触发")
        print("✅ PROCESS_COMPLETED_WITH_DATA 事件已触发")

        # 验证事件数据
        self.assertIsNotNone(event_data, "事件数据不应为空")
        self.assertIs(event_data['mp_instance'], mp, "事件数据应包含正确的 mp_instance")
        print("✅ 事件数据正确")
        
        print("\n验证共享数据...")
        # 验证共享数据已正确处理
        results = mp.get_results()
        
        # 检查计数器
        self.assertIn("counter", results, "应该包含共享计数器")
        counter_value = results["counter"]
        expected_count = len(data)
        self.assertEqual(counter_value, expected_count, "共享计数器应等于数据项数量")
        print(f"✅ 计数器正确: {counter_value}/{expected_count}")
        
        # 检查处理项列表
        self.assertIn("processed_items", results, "应该包含处理项列表")
        processed_items = results["processed_items"]
        processed_count = len(processed_items)
        self.assertEqual(processed_count, len(data), "处理项数量应等于输入数据数量")
        print(f"✅ 处理项数量正确: {processed_count}/{len(data)}")
        
        # 显示详细结果
        print(f"\n详细结果:")
        print(f"  输入数据: {data}")
        print(f"  处理项: {processed_items}")
        print(f"  计数器: {counter_value}")
        
        # 检查错误
        if "errors" in results and results["errors"]:
            print(f"  错误: {results['errors']}")
        else:
            print("  ✅ 无错误")
        
        self.logger.info("PROCESS_COMPLETED_WITH_DATA 事件测试完成")
        print("\n🎉 PROCESS_COMPLETED_WITH_DATA 事件测试完全通过！")


def main():
    """运行单个事件测试"""
    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(TestSingleEvent('test_only_process_completed_with_data_event'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回结果
    return result.wasSuccessful()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
