["global_tools/utils/event.py::test_emit_return_values", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_backward_compatibility", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_data_queue_functionality", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_enhanced_process_creation", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_enhanced_process_start_and_join", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_error_handling", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_multiple_processes", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_parameter_passing", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_process_logger", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_process_termination", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_proxy_disabled_by_default", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_shared_data_access", "test_proxy_manager.py::TestCrossProcessCommunication::test_child_process_calls_main_process"]