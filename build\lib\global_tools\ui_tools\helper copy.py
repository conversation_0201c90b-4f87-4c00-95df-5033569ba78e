import traceback
from typing import Callable, Dict, List
from PyQt5.QtWidgets import (
    QApplication,
    QMainWindow,
    QLineEdit,
    QWidget,
    QShortcut,
    QProgressBar,
    QGroupBox,
    QRadioButton,
    QPushButton,
    QPlainTextEdit,
    QButtonGroup,
    QCheckBox,
    QFormLayout,
    QCheckBox,
    QProgressBar,
    QLabel,
    QGridLayout,
    QLayout,
    QScrollArea,
    QVBoxLayout,
    QTextEdit,
    QGraphicsOpacityEffect,
)
from PyQt5.QtGui import (
    QIntValidator,
    QDoubleValidator,
    QRegExpValidator,
    QValidator,
    QColor,
    QFont,
    QTextCursor,
)
from PyQt5.QtCore import (
    QRegExp,
    QThread,
    pyqtSignal,
    QObject,
    Qt,
    QTimer,
    QEvent,
    QTime,
)
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5 import sip
from PyQt5.QtWidgets import *
import re
from typing import *
import logging
from functools import partial
# logging.basicConfig(
#     level=logging.FATAL,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d:%(funcName)s - %(message)s'
# )
# logging.disable(logging.CRITICAL)


class WorkerThread(QThread):
    progress_signal = pyqtSignal(int)  # 用于更新进度条
    label_signal = pyqtSignal(str)  # 用于更新标签

    def __init__(self):
        super().__init__()
        self.progress_value = 0
        self.label_text = ""


class ManagerWorkerThread:

    def __init__(self, *args: List[QProgressBar | QLabel]):
        self.__progress_bar_list = args

        self.__dict = {}

        self.__key()

    def __key(self):
        for item in self.__progress_bar_list:
            ids = hash(item)
            if ids not in self.__dict:
                self.__dict[ids] = WorkerThread()

    def set_progress_signal(self, progress_bar: QProgressBar,
                            callback: Callable):
        ids = hash(progress_bar)
        self.__dict[ids].progress_signal.connect(callback)

    def set_label_signal(self, label: QLabel, callback: Callable):
        ids = hash(label)
        self.__dict[ids].label_signal.connect(callback)

    def progress_signal_emit(self, progress_bar: QProgressBar, *args):
        ids = hash(progress_bar)
        self.__dict[ids].progress_signal.emit(*args)

    def label_signal_emit(self, label: QLabel, *args):
        ids = hash(label)
        self.__dict[ids].label_signal.emit(*args)


class LineEditControl(QObject):
    """
    Qt文本输入框(QLineEdit)的增强控制类

    该类封装了QLineEdit，提供了更丰富的功能：
    1. 多种类型的输入验证（整数、浮点数、长度、正则表达式等）
    2. 自动样式管理（正常样式、错误样式、禁用样式）
    3. 事件处理（焦点获取/失去、文本变化、回车按下）
    4. 回调函数支持（文本变化回调、失去焦点回调）
    5. 延迟过滤功能（防止频繁触发回调）
    6. 信号槽机制支持，使用SignalSlotManager进行统一管理

    ===== 类方法分类 =====

    一、信号相关方法：
       1. 信号属性（用作属性访问，支持connect/disconnect/emit操作）:
          - text_changed: 当文本变化时触发
          - validation_changed: 当验证状态变化时触发
          - focus_in: 当获取焦点时触发
          - focus_out: 当失去焦点时触发
          - return_pressed: 当按下回车键时触发
          - enabled_changed: 当启用状态变化时触发
          - style_changed: 当样式变化时触发
          - read_only_changed: 当只读状态变化时触发
          - echo_mode_changed: 当回显模式变化时触发

       2. 信号管理方法:
          - connect_signal(): 连接信号到槽函数
          - disconnect_signal(): 断开信号和槽函数的连接
          - emit_signal(): 发射信号
          - _connect_signal()/_disconnect_signal()/_emit_signal(): 内部实现方法
          - _get_signal_property(): 获取信号属性对象

    二、输入验证方法：
       - set_input_validator(): 设置输入验证器，支持多种类型
       - set_input_mask(): 设置输入掩码，限制输入格式
       - set_max_length(): 设置最大输入长度
       - __validate_input(): 内部方法，验证输入内容

    三、样式管理方法：
       - set_custom_style(): 设置自定义样式
       - __set_normal_style()/__set_error_style(): 内部方法，设置正常/错误样式

    四、文本操作方法：
       - get_text(): 获取输入框文本
       - set_text(): 设置输入框文本
       - clear(): 清空输入框
       - set_placeholder(): 设置占位符文本

    五、状态控制方法：
       - set_enabled()/enable()/disable(): 设置输入框启用/禁用状态
       - set_read_only(): 设置输入框只读状态
       - set_echo_mode(): 设置输入框回显模式

    六、回调函数管理方法：
       - set_callback(): 设置文本变化回调函数
       - set_focus_out_callback(): 设置失去焦点回调函数
       - set_auto_clear(): 设置获取焦点时是否自动清空
       - set_delay_time(): 设置延迟触发时间

    七、内部事件处理方法：
       - __focus_in_event(): 处理获取焦点事件
       - __focus_out_event(): 处理失去焦点事件
       - __text_changed(): 处理文本变化事件
       - __return_pressed(): 处理回车键按下事件
       - __do_filter(): 执行延迟过滤操作

    ===== 使用场景示例 =====

    1. 基本使用:
       ```python
       # 创建输入框控制实例
       line_edit = QLineEdit()
       control = LineEditControl(line_edit)

       # 获取和设置文本
       current_text = control.get_text()
       control.set_text("新文本")

       # 清空输入框
       control.clear()

       # 设置占位符
       control.set_placeholder("请输入...")
       ```

    2. 输入验证:
       ```python
       # 整数验证（0-100）
       control.set_input_validator('int', min_value=0, max_value=100)

       # 浮点数验证（0-1，两位小数）
       control.set_input_validator('float', min_value=0, max_value=1, decimals=2)

       # 长度验证（6-20个字符）
       control.set_input_validator('length', min_length=6, max_length=20)

       # 正则表达式验证（仅字母和数字）
       control.set_input_validator('regexp', pattern='^[a-zA-Z0-9]*$')

       # 邮箱验证
       control.set_input_validator('email')

       # IP地址验证
       control.set_input_validator('ipaddress')
       ```

    3. 回调函数使用:
       ```python
       # 文本变化回调
       def on_text_changed(text):
           print(f"文本已更改为: {text}")
       control.set_callback(on_text_changed)

       # 失去焦点回调
       def on_focus_lost(text):
           print(f"输入完成: {text}")
       control.set_focus_out_callback(on_focus_lost)

       # 设置延迟触发时间（毫秒）
       control.set_delay_time(500)
       ```

    4. 信号槽使用:
       ```python
       # 使用属性方式连接信号
       control.text_changed.connect(lambda text: print(f"文本变化: {text}"))
       control.validation_changed.connect(lambda valid: print(f"验证状态: {valid}"))
       control.return_pressed.connect(lambda text: print(f"按下回车: {text}"))

       # 使用方法方式连接信号
       control.connect_signal("text_changed", lambda text: print(f"文本变化: {text}"))

       # 手动发射信号
       control.text_changed.emit("手动触发")
       # 或者
       control.emit_signal("text_changed", "手动触发")
       ```

    5. 样式管理:
       ```python
       # 设置自定义样式
       control.set_custom_style(
           normal="border: 1px solid blue; border-radius: 5px; padding: 5px;",
           focus="border: 2px solid darkblue; border-radius: 5px; padding: 5px;",
           error="border: 2px solid red; background: #ffe0e0; border-radius: 5px; padding: 5px;",
           disabled="border: 1px solid #ccc; background: #f0f0f0; color: #888; border-radius: 5px; padding: 5px;"
       )
       ```

    6. 特殊功能:
       ```python
       # 设置输入掩码（电话号码格式）
       control.set_input_mask("999-999-9999")

       # 设置最大长度
       control.set_max_length(20)

       # 禁用获取焦点时自动清空功能
       control.set_auto_clear(False)

       # 设置只读状态
       control.set_read_only(True)

       # 设置密码模式
       from PyQt5.QtWidgets import QLineEdit
       control.set_echo_mode(QLineEdit.Password)
       ```

    使用示例:
    ```python
    # 创建一个QLineEdit并应用LineEditControl
    from PyQt5.QtWidgets import QLineEdit

    line_edit = QLineEdit()
    control = LineEditControl(line_edit)

    # 设置输入验证器（限制输入为0-100的整数）
    control.set_input_validator('int', min_value=0, max_value=100)

    # 设置占位符文本
    control.set_placeholder("请输入0-100的整数")

    # 设置文本变化回调函数
    def on_text_changed(text):
        print(f"文本已变更为: {text}")
    control.set_callback(on_text_changed)

    # 使用信号槽机制连接文本变化信号
    control.text_changed.connect(lambda text: print(f"信号：文本已变更为: {text}"))

    # 设置失去焦点回调函数
    def on_focus_lost(text):
        print(f"输入完成: {text}")
    control.set_focus_out_callback(on_focus_lost)

    # 禁用自动清空功能（默认为启用）
    control.set_auto_clear(False)

    # 设置自定义样式
    control.set_custom_style(normal="border: 1px solid blue;",
                             focus="border: 2px solid darkblue;",
                             error="border: 2px solid red; background: #ffe0e0;")
    ```
    """

    # 定义原始信号属性以保持向后兼容
    @property
    def text_changed(self):
        return self._get_signal_property("text_changed")

    @property
    def validation_changed(self):
        return self._get_signal_property("validation_changed")

    @property
    def focus_in(self):
        return self._get_signal_property("focus_in")

    @property
    def focus_out(self):
        return self._get_signal_property("focus_out")

    @property
    def return_pressed(self):
        return self._get_signal_property("return_pressed")

    @property
    def enabled_changed(self):
        return self._get_signal_property("enabled_changed")

    @property
    def style_changed(self):
        return self._get_signal_property("style_changed")

    @property
    def read_only_changed(self):
        return self._get_signal_property("read_only_changed")

    @property
    def echo_mode_changed(self):
        return self._get_signal_property("echo_mode_changed")

    def __init__(self, line_edit: QLineEdit):
        """输入框控制类初始化

        Args:
            line_edit: QLineEdit对象，要增强控制的输入框
        """
        super(LineEditControl, self).__init__()  # 初始化QObject父类

        self.line_edit = line_edit
        self.__callback = None
        self.__validator = None
        self.__auto_clear_enabled = True  # 控制获取焦点时是否自动清空
        self.__focus_out_callback = None  # 失去焦点回调函数
        self.__ignore_text_change = False  # 文本改变忽略标志
        self.__timer = QTimer()  # 防抖定时器，避免频繁触发回调
        self.__timer.setSingleShot(True)
        self.__timer.timeout.connect(self.__do_filter)

        # 创建信号槽管理器
        self.__signal_manager = SignalSlotManager()

        # 注册所有信号
        self.__signal_manager.register_signal("text_changed",
                                              SignalType.STRING)
        self.__signal_manager.register_signal("validation_changed",
                                              SignalType.BOOL)
        self.__signal_manager.register_signal("focus_in",
                                              SignalType.OBJECT)  # 不带参数的信号
        self.__signal_manager.register_signal("focus_out", SignalType.STRING)
        self.__signal_manager.register_signal("return_pressed",
                                              SignalType.STRING)
        self.__signal_manager.register_signal("enabled_changed",
                                              SignalType.BOOL)
        self.__signal_manager.register_signal("style_changed",
                                              SignalType.STRING)
        self.__signal_manager.register_signal("read_only_changed",
                                              SignalType.BOOL)
        self.__signal_manager.register_signal("echo_mode_changed",
                                              SignalType.INT)

        # 创建向后兼容的信号属性
        self.__compat_signals = {}

        # 样式配置
        self.__normal_style = """
            QLineEdit {
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QLineEdit:focus {
                border: 2px solid #000;
            }
            QLineEdit:disabled {
                background-color: #f0f0f0;
                color: #888;
            }
        """
        self.__error_style = """
            QLineEdit {
                border: 2px solid red;
                border-radius: 5px;
                padding: 5px;
            }
        """
        self.__disabled_style = """
            QLineEdit:disabled {
                background-color: #f0f0f0;
                color: #888;
                border: 2px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
        """

        self.__setup_connections()
        self.__setup_style()

    def _get_signal_property(self, signal_name):
        """获取向后兼容的信号属性

        这是一个受保护的方法，用于内部实现信号属性，不应直接调用。

        Args:
            signal_name: 信号名称

        Returns:
            信号模拟器对象
        """
        if signal_name not in self.__compat_signals:

            class SignalEmulator:

                def __init__(self, manager, signal_name):
                    self.manager = manager
                    self.signal_name = signal_name

                def connect(self, slot):
                    self.manager.connect_slot(self.signal_name, slot)

                def disconnect(self, slot):
                    self.manager.disconnect_slot(self.signal_name, slot)

                def emit(self, *args):
                    self.manager.emit_signal(self.signal_name, *args)

            self.__compat_signals[signal_name] = SignalEmulator(
                self.__signal_manager, signal_name)

        return self.__compat_signals[signal_name]

    def _connect_signal(self, signal_name: str, slot_func: Callable):
        """连接信号到槽函数

        这是一个受保护的方法，建议通过信号属性调用connect方法来连接信号。

        Args:
            signal_name: 信号名称
            slot_func: 槽函数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.__signal_manager.connect_slot(signal_name, slot_func)
        return self

    def _disconnect_signal(self, signal_name: str, slot_func: Callable):
        """断开信号和槽函数的连接

        这是一个受保护的方法，建议通过信号属性调用disconnect方法来断开连接。

        Args:
            signal_name: 信号名称
            slot_func: 槽函数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.__signal_manager.disconnect_slot(signal_name, slot_func)
        return self

    def _emit_signal(self, signal_name: str, *args):
        """发射信号

        这是一个受保护的方法，通常由类内部方法调用，不应直接使用。
        如需手动发射信号，可以通过信号属性调用emit方法。

        Args:
            signal_name: 信号名称
            *args: 信号参数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.__signal_manager.emit_signal(signal_name, *args)
        return self

    def __setup_connections(self):
        """设置信号连接，将各种事件与处理函数关联起来"""
        # 获取焦点时清除内容（如果启用了自动清空）
        self.line_edit.focusInEvent = self.__focus_in_event
        # 失去焦点时触发回调
        self.line_edit.focusOutEvent = self.__focus_out_event
        # 文本改变时触发回调和验证
        self.line_edit.textChanged.connect(self.__text_changed)
        # 按下回车时触发回调和取消焦点
        self.line_edit.returnPressed.connect(self.__return_pressed)

    def __setup_style(self):
        """设置默认样式"""
        self.line_edit.setStyleSheet(self.__normal_style)
        self._emit_signal("style_changed", self.__normal_style)

    # 为了保持向后兼容，保留这三个方法作为公共API的别名
    def connect_signal(self, signal_name: str, slot_func: Callable):
        """连接信号到槽函数（向后兼容方法）

        建议使用信号属性的connect方法，例如：control.text_changed.connect(my_slot)

        Args:
            signal_name: 信号名称，可用的信号有：
                - "text_changed": 文本变化信号
                - "validation_changed": 验证状态变化信号
                - "focus_in": 获取焦点信号
                - "focus_out": 失去焦点信号
                - "return_pressed": 回车键按下信号
                - "enabled_changed": 启用状态变化信号
                - "style_changed": 样式变化信号
                - "read_only_changed": 只读状态变化信号
                - "echo_mode_changed": 回显模式变化信号
            slot_func: 槽函数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        return self._connect_signal(signal_name, slot_func)

    def disconnect_signal(self, signal_name: str, slot_func: Callable):
        """断开信号和槽函数的连接（向后兼容方法）

        建议使用信号属性的disconnect方法，例如：control.text_changed.disconnect(my_slot)

        Args:
            signal_name: 信号名称
            slot_func: 槽函数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        return self._disconnect_signal(signal_name, slot_func)

    def emit_signal(self, signal_name: str, *args):
        """发射信号（向后兼容方法）

        建议使用信号属性的emit方法，例如：control.text_changed.emit("新文本")

        Args:
            signal_name: 信号名称
            *args: 信号参数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        return self._emit_signal(signal_name, *args)

    def set_input_validator(self, validator_type: str, **kwargs):
        """设置输入验证器，用于限制和验证用户输入内容

        Args:
            validator_type (str): 验证器类型，可选值：
                - 'int': 整数验证器，限制输入为整数
                - 'float': 浮点数验证器，限制输入为浮点数
                - 'length': 文本长度验证器，限制文本长度范围
                - 'regexp': 正则表达式验证器，使用正则表达式验证输入
                - 'range': 范围验证器，根据输入类型自动选择整数或浮点数验证器
                - 'email': 电子邮件地址验证器
                - 'ipaddress': IP地址验证器

            **kwargs: 验证器的配置参数，根据验证器类型不同而不同：
                整数验证器(int)和范围验证器(range)：
                    - min_value (int): 最小值，默认为-2147483648
                    - max_value (int): 最大值，默认为2147483647

                浮点数验证器(float)：
                    - min_value (float): 最小值，默认为-2147483648
                    - max_value (float): 最大值，默认为2147483647
                    - decimals (int): 小数位数，默认为2

                长度验证器(length)：
                    - min_length (int): 最小长度，默认为0
                    - max_length (int): 最大长度，默认为32767

                正则表达式验证器(regexp)：
                    - pattern (str): 正则表达式模式，默认为空字符串

        Returns:
            self: 返回自身实例，支持链式调用

        示例:
            # 设置整数验证器（范围0-100）
            control.set_input_validator('int', min_value=0, max_value=100)

            # 设置浮点数验证器（范围0-1，保留2位小数）
            control.set_input_validator('float', min_value=0, max_value=1, decimals=2)

            # 设置长度验证器（限制长度为6-20）
            control.set_input_validator('length', min_length=6, max_length=20)

            # 设置正则表达式验证器（只允许字母和数字）
            control.set_input_validator('regexp', pattern='^[a-zA-Z0-9]*$')

            # 设置电子邮件验证器
            control.set_input_validator('email')
        """
        # 1. 整数验证器：限制输入为整数，可设置最小值和最大值
        if validator_type == "int":
            # 获取最小值和最大值，使用32位整数的范围作为默认值
            min_val = kwargs.get("min_value", -2147483648)
            max_val = kwargs.get("max_value", 2147483647)
            # 创建整数验证器实例
            self.__validator = QIntValidator(min_val, max_val)

        # 2. 浮点数验证器：限制输入为浮点数，可设置最小值、最大值和小数位数
        elif validator_type == "float":
            # 获取最小值和最大值
            min_val = kwargs.get("min_value", -2147483648)
            max_val = kwargs.get("max_value", 2147483647)
            # 获取小数位数，默认为2位
            decimals = kwargs.get("decimals", 2)
            # 创建浮点数验证器实例
            self.__validator = QDoubleValidator(min_val, max_val, decimals)
            # 设置数字表示法为标准表示法（不使用科学计数法）
            self.__validator.setNotation(QDoubleValidator.StandardNotation)

        # 3. 长度验证器：使用正则表达式限制文本的长度范围
        elif validator_type == "length":
            # 获取最小长度和最大长度
            min_len = kwargs.get("min_length", 0)
            max_len = kwargs.get("max_length", 32767)
            # 构造正则表达式模式：^表示开始，$表示结束，{m,n}表示长度范围
            pattern = f"^.{{{min_len},{max_len}}}$"
            # 创建正则表达式验证器实例
            self.__validator = QRegExpValidator(QRegExp(pattern))

        # 4. 正则表达式验证器：使用自定义正则表达式验证输入
        elif validator_type == "regexp":
            # 获取正则表达式模式
            pattern = kwargs.get("pattern", "")
            # 创建正则表达式验证器实例
            self.__validator = QRegExpValidator(QRegExp(pattern))

        # 5. 范围验证器：根据输入的类型自动选择整数或浮点数验证器
        elif validator_type == "range":
            # 获取最小值和最大值
            min_val = kwargs.get("min_value", -2147483648)
            max_val = kwargs.get("max_value", 2147483647)
            # 判断最小值和最大值是否都是整数
            if isinstance(min_val, int) and isinstance(max_val, int):
                # 如果是整数，使用整数验证器
                self.__validator = QIntValidator(min_val, max_val)
            else:
                # 如果是浮点数，使用浮点数验证器，默认保留2位小数
                self.__validator = QDoubleValidator(min_val, max_val, 2)
                # 设置标准表示法
                self.__validator.setNotation(QDoubleValidator.StandardNotation)

        # 6. 电子邮件验证器：使用正则表达式验证电子邮件格式
        elif validator_type == "email":
            # 基本的电子邮件格式正则表达式
            pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            self.__validator = QRegExpValidator(QRegExp(pattern))

        # 7. IP地址验证器：使用正则表达式验证IP地址格式
        elif validator_type == "ipaddress":
            # IPv4地址格式正则表达式
            pattern = r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
            self.__validator = QRegExpValidator(QRegExp(pattern))

        # 8. 如果成功创建了验证器，则设置到输入框
        if self.__validator:
            self.line_edit.setValidator(self.__validator)
            # 验证当前文本
            self.__validate_input(self.get_text())

        return self  # 返回self以支持链式调用

    def __validate_input(self, text: str) -> bool:
        """验证输入内容

        Args:
            text: 输入的文本

        Returns:
            bool: 验证是否通过，True表示通过，False表示未通过
        """
        if not text:  # 空文本视为有效
            self.__set_normal_style()
            self._emit_signal("validation_changed", True)
            return True

        if self.__validator:
            state = self.__validator.validate(text, 0)[0]
            if state == QValidator.Acceptable:
                self.__set_normal_style()
                self._emit_signal("validation_changed", True)
                return True
            else:
                self.__set_error_style()
                self._emit_signal("validation_changed", False)
                return False

        self._emit_signal("validation_changed", True)
        return True

    def __set_normal_style(self):
        """设置正常样式"""
        self.line_edit.setStyleSheet(self.__normal_style)
        self._emit_signal("style_changed", self.__normal_style)

    def __set_error_style(self):
        """设置错误样式"""
        self.line_edit.setStyleSheet(self.__error_style)
        self._emit_signal("style_changed", self.__error_style)

    def set_input_mask(self, mask: str):
        """设置输入掩码，限制输入的格式

        Args:
            mask: 输入掩码字符串，详见Qt文档
                常见的掩码字符：
                - 9: 表示必须输入一个数字（0-9）
                - A: 表示必须输入一个字母（A-Z, a-z）
                - N: 表示必须输入一个字母或数字
                - X: 表示任何字符都可以输入
                - D: 表示必须输入一个数字（1-9）
                - #: 表示可以输入一个数字或加减号

        示例:
            # 设置电话号码掩码
            control.set_input_mask("999-999-9999")

            # 设置日期掩码
            control.set_input_mask("9999/99/99")

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setInputMask(mask)
        return self

    def set_max_length(self, length: int):
        """设置最大输入长度，限制输入的字符数

        Args:
            length: 最大字符数

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setMaxLength(length)
        return self

    def set_callback(self, callback):
        """设置文本变化时的回调函数

        Args:
            callback: 回调函数，接收当前文本作为参数

        Returns:
            self: 返回自身实例，支持链式调用

        示例:
            def on_text_changed(text):
                print(f"文本已变更为: {text}")

            control.set_callback(on_text_changed)
        """
        self.__callback = callback
        return self

    def get_text(self) -> str:
        """获取输入框文本

        Returns:
            str: 当前输入框中的文本
        """
        return self.line_edit.text()

    def set_text(self, text: str):
        """设置输入框文本

        Args:
            text: 要设置的文本

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setText(text)
        self.__validate_input(text)  # 验证设置的文本
        # 手动发射文本变化信号，因为这可能不会通过__text_changed触发
        self._emit_signal("text_changed", text)
        return self

    def clear(self):
        """清空输入框

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.clear()
        self.__set_normal_style()
        self._emit_signal("text_changed", "")
        return self

    def set_placeholder(self, text: str):
        """设置占位符文本，当输入框为空时显示的提示文本

        Args:
            text: 占位符文本

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setPlaceholderText(text)
        return self

    def set_enabled(self, enabled: bool):
        """设置输入框是否可用

        Args:
            enabled: True表示启用，False表示禁用

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setEnabled(enabled)
        self._emit_signal("enabled_changed", enabled)
        if not enabled:
            self.__set_normal_style()
        return self

    def __focus_in_event(self, event):
        """获取焦点时的事件处理"""
        # 如果启用了自动清空，则清空输入框
        if self.__auto_clear_enabled:
            self.clear()
        self._emit_signal("focus_in")
        QLineEdit.focusInEvent(self.line_edit, event)

    def __text_changed(self, text: str):
        """文本改变时的事件处理"""
        # 如果是点击选项导致的文本改变，则直接返回，不做任何处理
        if self.__ignore_text_change:
            return

        # 发射文本变化信号
        self._emit_signal("text_changed", text)

        # 重置定时器，延迟200毫秒后执行过滤
        self.__timer.stop()
        self.__timer.start(200)

    def __return_pressed(self):
        """按下回车键时的事件处理"""
        text = self.get_text()
        if self.__validate_input(text):
            self._emit_signal("return_pressed", text)
            if self.__callback:
                self.__callback(text)
            # 取消焦点
            self.line_edit.clearFocus()

    def enable(self):
        """启用输入框

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setEnabled(True)
        self._emit_signal("enabled_changed", True)
        self.__set_normal_style()
        return self

    def disable(self):
        """禁用输入框

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setEnabled(False)
        self._emit_signal("enabled_changed", False)
        self.line_edit.setStyleSheet(self.__disabled_style)
        self._emit_signal("style_changed", self.__disabled_style)
        return self

    def set_focus_out_callback(self, callback):
        """设置失去焦点时的回调函数

        Args:
            callback: 回调函数，接收当前文本作为参数

        Returns:
            self: 返回自身实例，支持链式调用

        示例:
            def on_focus_lost(text):
                print(f"输入完成: {text}")

            control.set_focus_out_callback(on_focus_lost)
        """
        self.__focus_out_callback = callback
        return self

    def __focus_out_event(self, event):
        """失去焦点时的事件处理"""
        text = self.get_text()
        valid = self.__validate_input(text)

        # 发射失去焦点信号
        self._emit_signal("focus_out", text)

        # 如果设置了回调函数，则执行
        if self.__focus_out_callback and valid:  # 验证通过才触发回调
            self.__focus_out_callback(text)

        # 调用父类的focusOutEvent
        QLineEdit.focusOutEvent(self.line_edit, event)

    def __do_filter(self):
        """执行过滤操作"""
        text = self.get_text()
        if self.__validate_input(text):
            if self.__callback:
                self.__callback(text)

    def set_auto_clear(self, enabled: bool):
        """设置是否启用自动清空功能

        启用自动清空功能后，当输入框获取焦点时会自动清空内容。

        Args:
            enabled: True表示启用，False表示禁用

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.__auto_clear_enabled = enabled
        return self

    def set_custom_style(self,
                         normal=None,
                         focus=None,
                         error=None,
                         disabled=None):
        """设置自定义样式

        Args:
            normal: 正常状态的样式，如果为None则使用默认样式
            focus: 获取焦点时的样式，如果为None则使用默认样式
            error: 输入错误时的样式，如果为None则使用默认样式
            disabled: 禁用状态的样式，如果为None则使用默认样式

        Returns:
            self: 返回自身实例，支持链式调用

        示例:
            # 设置蓝色边框样式
            control.set_custom_style(
                normal="border: 1px solid blue; border-radius: 5px; padding: 5px;",
                focus="border: 2px solid darkblue; border-radius: 5px; padding: 5px;",
                error="border: 2px solid red; background: #ffe0e0; border-radius: 5px; padding: 5px;",
                disabled="border: 1px solid #ccc; background: #f0f0f0; color: #888; border-radius: 5px; padding: 5px;"
            )
        """
        # 更新样式
        if normal:
            self.__normal_style = """
                QLineEdit {
                    %s
                }
                QLineEdit:focus {
                    %s
                }
                QLineEdit:disabled {
                    %s
                }
            """ % (
                normal,
                focus or "border: 2px solid #000;",
                disabled or "background-color: #f0f0f0; color: #888;",
            )

        if error:
            self.__error_style = ("""
                QLineEdit {
                    %s
                }
            """ % error)

        if disabled:
            self.__disabled_style = ("""
                QLineEdit:disabled {
                    %s
                }
            """ % disabled)

        # 应用当前状态的样式
        current_style = None
        if not self.line_edit.isEnabled():
            self.line_edit.setStyleSheet(self.__disabled_style)
            current_style = self.__disabled_style
        elif self.__validate_input(self.get_text()):
            self.__set_normal_style()
            current_style = self.__normal_style
        else:
            self.__set_error_style()
            current_style = self.__error_style

        if current_style:
            self._emit_signal("style_changed", current_style)

        return self

    def set_delay_time(self, milliseconds: int):
        """设置文本变化后触发回调的延迟时间

        Args:
            milliseconds: 延迟时间，单位为毫秒

        Returns:
            self: 返回自身实例，支持链式调用
        """
        if milliseconds > 0:
            self.__timer.setInterval(milliseconds)
        return self

    def set_read_only(self, read_only: bool):
        """设置输入框是否为只读模式

        Args:
            read_only: True表示只读，False表示可编辑

        Returns:
            self: 返回自身实例，支持链式调用
        """
        self.line_edit.setReadOnly(read_only)
        self._emit_signal("read_only_changed", read_only)
        return self

    def set_echo_mode(self, mode: "QLineEdit.EchoMode"):
        """设置输入框的回显模式

        Args:
            mode: 回显模式，可选值：
                - QLineEdit.Normal: 正常显示输入内容
                - QLineEdit.NoEcho: 不显示任何内容
                - QLineEdit.Password: 显示密码掩码字符代替输入内容
                - QLineEdit.PasswordEchoOnEdit: 编辑时显示输入内容，否则显示掩码

        Returns:
            self: 返回自身实例，支持链式调用

        示例:
            from PyQt5.QtWidgets import QLineEdit

            # 设置密码模式
            control.set_echo_mode(QLineEdit.Password)
        """
        self.line_edit.setEchoMode(mode)
        self._emit_signal("echo_mode_changed", mode)
        return self


class CheckBoxManager(QObject):
    """管理多个容器中的复选框，符合 PyQt5 信号槽规范

    CheckBoxManager 提供了统一管理多个容器中复选框的功能，包括查找、操作和监控复选框状态。
    它符合 PyQt5 的信号槽机制，可以通过信号监听复选框状态变化。

    使用示例:
    ---------

    1. 创建管理器:
    ```python
    # 从布局中查找所有复选框
    layout = QVBoxLayout()
    manager = CheckBoxManager(layout)

    # 也可以从多个容器中查找
    widget1 = QWidget()
    widget2 = QGroupBox()
    manager = CheckBoxManager(widget1, widget2)
    ```

    2. 获取复选框:
    ```python
    # 获取所有复选框
    all_boxes = manager.get_all_checkboxes()

    # 获取所有选中的复选框
    checked_boxes = manager.get_checked_boxes()

    # 获取所有未选中的复选框
    unchecked_boxes = manager.get_unchecked_boxes()

    # 获取未选中且文本为"选项1"的复选框
    unchecked_option1 = manager.get_unchecked_boxes(filter_text="选项1", inverse=False)

    # 获取未选中且文本不在列表中的复选框
    filtered = manager.get_unchecked_boxes(filter_text=["选项1", "选项2"], inverse=True)
    ```

    3. 获取复选框文本:
    ```python
    # 获取所有选中复选框的文本
    checked_texts = manager.get_checked_texts()
    print(f"选中项: {', '.join(checked_texts)}")
    ```

    4. 通过文本或对象名查找复选框:
    ```python
    # 通过文本查找
    checkbox = manager.get_checkbox_by_text("选项1")
    if checkbox:
        checkbox.setChecked(True)

    # 通过对象名查找
    checkbox = manager.get_checkbox_by_object_name("option1_checkbox")
    if checkbox:
        checkbox.setChecked(True)
    ```

    5. 设置复选框状态:
    ```python
    # 通过文本设置状态
    success = manager.set_checked_by_text("选项1", checked=True)
    if not success:
        print("未找到复选框")

    # 设置所有复选框状态
    manager.set_all_checked(checked=True)  # 全选
    manager.set_all_checked(checked=False) # 全不选
    ```

    6. 使用回调处理复选框点击:
    ```python
    def on_checkbox_clicked(checkbox, checked):
        print(f"复选框 {checkbox.text()} 状态变为: {checked}")

    # 为所有复选框设置点击回调
    manager.set_click_callback(on_checkbox_clicked)
    ```

    7. 使用信号槽监听状态变化:
    ```python
    def on_state_changed(checkbox, checked):
        print(f"复选框 {checkbox.text()} 状态变为: {checked}")

    # 连接信号
    manager.checkbox_checked.connect(on_state_changed)
    ```

    8. 设置互斥选择模式(类似单选按钮):
    ```python
    # 所有复选框互斥
    manager.set_exclusive_selection()

    # 指定文本的复选框互斥
    manager.set_exclusive_selection(filter_text="选项\\d+")  # 正则匹配"选项"后跟数字

    # 指定多个文本模式互斥
    manager.set_exclusive_selection(filter_text=["选项[A-C]", "测试.*"])

    # 添加状态变化回调
    def on_exclusive_changed(checkbox, checked):
        if checked:
            print(f"选中了: {checkbox.text()}")

    manager.set_exclusive_selection(callback=on_exclusive_changed)

    # 使用信号监听互斥选择变化
    manager.exclusive_selection_changed.connect(on_exclusive_changed)
    ```
    """

    # 定义信号
    checkbox_checked = pyqtSignal(QCheckBox, bool)  # 复选框选中状态改变信号
    exclusive_selection_changed = pyqtSignal(QCheckBox, bool)  # 互斥选择状态改变信号

    def __init__(self, *containers, parent=None):
        """初始化复选框管理器

        Args:
            *containers: 要管理的容器列表，支持任意QWidget或QLayout类型
            parent: 父QObject对象，默认为None
        """
        super().__init__(parent)
        self.__checkboxes = []
        self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__logger.debug("初始化复选框管理器")

        # 递归查找每个容器中的复选框
        for container in containers:
            self.__find_checkboxes(container)

        self.__logger.debug(f"找到 {len(self.__checkboxes)} 个复选框")

    def __find_checkboxes(self, container):
        """递归查找所有复选框

        Args:
            container: QWidget或QLayout类型的容器
        """
        self.__logger.debug(f"在容器 {container} 中查找复选框")

        # 如果是布局类型
        if isinstance(container, QLayout):
            for i in range(container.count()):
                item = container.itemAt(i)
                if item:
                    # 递归处理子布局
                    if item.layout():
                        self.__find_checkboxes(item.layout())
                    # 处理部件
                    if item.widget():
                        self.__find_checkboxes(item.widget())

        # 如果是部件类型
        elif isinstance(container, QWidget):
            # 如果是复选框，直接添加
            if isinstance(container, QCheckBox):
                self.__logger.debug(f"找到复选框: {container.text() or container.objectName()}")
                self.__checkboxes.append(container)
            # 处理子部件
            for child in container.children():
                if isinstance(child, (QWidget, QLayout)):
                    self.__find_checkboxes(child)

    def get_all_checkboxes(self) -> List[QCheckBox]:
        """获取所有复选框

        Returns:
            所有复选框列表
        """
        self.__logger.debug(f"获取所有复选框，共 {len(self.__checkboxes)} 个")
        return self.__checkboxes

    def get_checked_boxes(self) -> List[QCheckBox]:
        """获取所有选中的复选框

        Returns:
            选中的复选框列表
        """
        checked_boxes = [box for box in self.__checkboxes if box.isChecked()]
        self.__logger.debug(f"获取选中的复选框，共 {len(checked_boxes)} 个")
        return checked_boxes

    def get_unchecked_boxes(self, filter_text: Optional[Union[str, List[str]]] = None, inverse: bool = True) -> List[QCheckBox]:
        """获取所有未选中的复选框

        Args:
            filter_text: 用于过滤复选框的文本，可以是单个字符串或字符串列表。
                        如果提供，只返回文本匹配的复选框
            inverse: 是否反选过滤结果，默认为True。
                    当为True时，返回不匹配filter_text的复选框

        Returns:
            未选中的复选框列表
        """
        unchecked = [box for box in self.__checkboxes if not box.isChecked()]
        self.__logger.debug(f"获取未选中的复选框，初始数量: {len(unchecked)}")

        if filter_text is not None:
            if isinstance(filter_text, str):
                self.__logger.debug(f"使用文本过滤: {filter_text}, 反选: {inverse}")
                result = [box for box in unchecked if (box.text() == filter_text) != inverse]
            elif isinstance(filter_text, list):
                self.__logger.debug(f"使用文本列表过滤，列表长度: {len(filter_text)}, 反选: {inverse}")
                result = [box for box in unchecked if (box.text() in filter_text) != inverse]
            self.__logger.debug(f"过滤后的结果数量: {len(result)}")
            return result

        self.__logger.debug(f"不使用过滤，返回所有未选中复选框: {len(unchecked)}")
        return unchecked

    def get_checked_texts(self) -> List[str]:
        """获取所有选中复选框的文本

        Returns:
            选中复选框的文本列表
        """
        texts = [box.text() for box in self.__checkboxes if box.isChecked()]
        self.__logger.debug(f"获取选中复选框的文本，共 {len(texts)} 个")
        return texts

    def set_checked_by_text(self, text: str, checked: bool = True) -> bool:
        """通过文本设置复选框状态

        Args:
            text: 要设置的复选框文本
            checked: 是否选中,默认True

        Returns:
            bool: 是否成功设置（找到匹配的复选框）
        """
        self.__logger.debug(f"尝试设置文本为 '{text}' 的复选框为 {checked}")
        for box in self.__checkboxes:
            if box.text() == text:
                box.setChecked(checked)
                self.__logger.debug("设置成功")
                # 发出信号
                self.checkbox_checked.emit(box, checked)
                return True

        self.__logger.debug(f"未找到文本为 '{text}' 的复选框")
        return False

    def set_all_checked(self, checked: bool = True) -> None:
        """设置所有复选框的状态

        Args:
            checked: 是否选中,默认True
        """
        self.__logger.debug(f"设置所有复选框的状态为 {checked}")
        for box in self.__checkboxes:
            box.setChecked(checked)
            # 发出信号
            self.checkbox_checked.emit(box, checked)

    def get_checkbox_by_text(self, text: str) -> Optional[QCheckBox]:
        """通过文本查找复选框

        Args:
            text: 要查找的复选框文本

        Returns:
            QCheckBox | None: 找到的复选框对象，如果未找到则返回None
        """
        self.__logger.debug(f"通过文本 '{text}' 查找复选框")
        for box in self.__checkboxes:
            if box.text() == text:
                self.__logger.debug("找到复选框")
                return box

        self.__logger.debug("未找到复选框")
        return None

    def get_checkbox_by_object_name(self, object_name: str) -> Optional[QCheckBox]:
        """通过 ObjectName 查找复选框

        Args:
            object_name: 要查找的复选框 ObjectName

        Returns:
            QCheckBox | None: 找到的复选框对象，如果未找到则返回None
        """
        self.__logger.debug(f"通过对象名 '{object_name}' 查找复选框")
        for box in self.__checkboxes:
            if box.objectName() == object_name:
                self.__logger.debug("找到复选框")
                return box

        self.__logger.debug("未找到复选框")
        return None

    def set_click_callback(self, callback: Callable[[QCheckBox, bool], None]) -> None:
        """为所有复选框添加点击事件处理函数

        Args:
            callback: 回调函数，接收两个参数：
                     - checkbox: QCheckBox对象
                     - checked: bool类型，表示复选框是否被选中
                     函数签名应为: def callback(checkbox: QCheckBox, checked: bool) -> None

        Example:
            ```python
            def on_checkbox_clicked(checkbox: QCheckBox, checked: bool):
                print(f"复选框 {checkbox.text()} 被{'选中' if checked else '取消选中'}")

            manager = CheckBoxManager(container)
            manager.set_click_callback(on_checkbox_clicked)
            ```
        """
        self.__logger.debug("为所有复选框设置点击回调")

        # 首先断开之前可能存在的连接
        self.__disconnect_all_checkbox_signals()

        # 设置新的回调，使用内部方法包装回调
        for checkbox in self.__checkboxes:
            checkbox.clicked.connect(
                lambda state, cb=checkbox: self.__on_checkbox_clicked_wrapper(cb, state, callback)
            )

    def __on_checkbox_clicked_wrapper(self, checkbox: QCheckBox, state: bool, callback: Callable[[QCheckBox, bool], None]) -> None:
        """包装复选框点击事件，发出信号并调用回调

        Args:
            checkbox: 被点击的复选框
            state: 复选框状态
            callback: 用户提供的回调函数
        """
        self.__logger.debug(f"复选框 '{checkbox.text() or checkbox.objectName()}' 状态变为 {state}")

        # 发出信号
        self.checkbox_checked.emit(checkbox, state)

        # 调用用户回调
        callback(checkbox, state)

    def __disconnect_all_checkbox_signals(self) -> None:
        """断开所有复选框的信号连接"""
        self.__logger.debug("断开所有复选框的信号连接")
        for checkbox in self.__checkboxes:
            try:
                checkbox.clicked.disconnect()
                self.__logger.debug(f"断开复选框 '{checkbox.text() or checkbox.objectName()}' 的信号")
            except TypeError:
                # 忽略没有连接的情况
                self.__logger.debug(f"复选框 '{checkbox.text() or checkbox.objectName()}' 没有信号连接")
                pass

    def __get_filtered_checkboxes(self, filter_text: Optional[Union[str, List[str]]]) -> List[QCheckBox]:
        """根据过滤文本获取目标复选框

        Args:
            filter_text: 过滤文本，可以是字符串或字符串列表，支持正则表达式

        Returns:
            符合条件的复选框列表
        """
        target_boxes = self.__checkboxes

        if filter_text is None:
            self.__logger.debug("没有提供过滤条件，返回所有复选框")
            return target_boxes

        if isinstance(filter_text, str):
            self.__logger.debug(f"使用字符串过滤: {filter_text}")
            try:
                pattern = re.compile(filter_text)
                target_boxes = [box for box in self.__checkboxes if pattern.match(box.text())]
                self.__logger.debug(f"正则匹配成功，匹配到 {len(target_boxes)} 个复选框")
            except re.error:
                # 如果正则表达式无效，退回到精确匹配
                self.__logger.debug(f"正则表达式无效，使用精确匹配")
                target_boxes = [box for box in self.__checkboxes if box.text() == filter_text]
                self.__logger.debug(f"精确匹配到 {len(target_boxes)} 个复选框")

        elif isinstance(filter_text, list):
            self.__logger.debug(f"使用文本列表过滤，列表长度: {len(filter_text)}")
            target_boxes = []
            for pattern_text in filter_text:
                try:
                    pattern = re.compile(pattern_text)
                    matches = [box for box in self.__checkboxes if pattern.match(box.text())]
                    self.__logger.debug(f"正则 '{pattern_text}' 匹配到 {len(matches)} 个复选框")
                    target_boxes.extend(matches)
                except re.error:
                    # 如果正则表达式无效，退回到精确匹配
                    matches = [box for box in self.__checkboxes if box.text() == pattern_text]
                    self.__logger.debug(f"精确匹配 '{pattern_text}' 匹配到 {len(matches)} 个复选框")
                    target_boxes.extend(matches)

        return target_boxes

    def __on_exclusive_checkbox_clicked(self, current_checkbox: QCheckBox, checked: bool,
                                        filtered_boxes: List[QCheckBox],
                                        callback: Optional[Callable[[QCheckBox, bool], None]]) -> None:
        """处理互斥选择模式下的复选框点击事件

        Args:
            current_checkbox: 当前被点击的复选框
            checked: 复选框状态
            filtered_boxes: 过滤后的复选框列表（互斥组）
            callback: 用户提供的回调函数
        """
        self.__logger.debug(f"互斥模式: 复选框 '{current_checkbox.text()}' 状态变为 {checked}")

        if checked:
            # 取消其他复选框的选中状态
            for box in filtered_boxes:
                if box != current_checkbox and box.isChecked():
                    self.__logger.debug(f"取消选中复选框: {box.text()}")
                    box.setChecked(False)

                    # 发出信号
                    self.exclusive_selection_changed.emit(box, False)

                    # 调用用户回调
                    if callback:
                        callback(box, False)

        # 为当前复选框发出信号
        self.exclusive_selection_changed.emit(current_checkbox, checked)

        # 调用用户回调
        if callback:
            callback(current_checkbox, checked)

    def set_exclusive_selection(self, filter_text: Optional[Union[str, List[str]]] = None,
                                callback: Optional[Callable[[QCheckBox, bool], None]] = None) -> None:
        """设置复选框为单选模式（互斥选择）

        当一个复选框被选中时，其他复选框会自动取消选中。类似于 QRadioButton 的行为，
        但保留了 QCheckBox 的外观和可以完全取消选择的特性。

        Args:
            filter_text (str | list[str], optional):
                - 用于指定需要互斥选择的复选框范围
                - 可以是单个字符串或字符串列表
                - 如果为 None，则对所有复选框生效
                - 如果提供字符串，支持正则表达式匹配复选框文本
                - 如果提供字符串列表，列表中的每个字符串都支持正则表达式

            callback (Callable[[QCheckBox, bool], None], optional):
                - 选择状态改变时的回调函数
                - 函数签名：def callback(checkbox: QCheckBox, checked: bool) -> None
                - checkbox: 发生改变的复选框对象
                - checked: 是否被选中
                - 如果为 None，则不调用回调

        Example:
            ```python
            # 创建复选框管理器
            manager = CheckBoxManager(container)

            # 对所有复选框启用单选模式
            manager.set_exclusive_selection()

            # 使用正则表达式匹配复选框
            manager.set_exclusive_selection(filter_text=r"选项\\d+")  # 匹配"选项"后跟数字

            # 使用正则表达式列表
            manager.set_exclusive_selection(filter_text=[
                r"选项[A-C]",   # 匹配"选项A"到"选项C"
                r"测试.*"       # 匹配以"测试"开头的所有选项
            ])

            # 添加状态改变回调
            def on_selection_changed(checkbox: QCheckBox, checked: bool):
                print(f"选择改变：{checkbox.text()} -> {checked}")

            manager.set_exclusive_selection(callback=on_selection_changed)

            # 使用信号处理状态变化
            manager.exclusive_selection_changed.connect(on_selection_changed)
            ```
        注意事项:
            1. 此功能会覆盖之前通过 set_click_callback 设置的回调函数
            2. 如果需要同时处理多个回调，请在提供的 callback 中实现
            3. 启用此功能后，仍然可以通过代码调用 setChecked 改变选中状态
            4. 此功能不会影响复选框的外观，它们仍然显示为复选框而不是单选按钮
            5. 用户可以通过再次点击选中的复选框来取消选择
            6. filter_text 参数可以用来创建多个互斥组
            7. 正则表达式匹配使用 re.match，因此默认从字符串开始匹配
            8. 如果正则表达式无效，将被视为普通字符串处理
        """
        self.__logger.debug(f"设置互斥选择模式，过滤条件：{filter_text}")

        # 获取目标复选框列表
        target_boxes = self.__get_filtered_checkboxes(filter_text)
        self.__logger.debug(f"目标复选框数量: {len(target_boxes)}")

        # 断开所有目标复选框的现有连接
        for checkbox in target_boxes:
            try:
                checkbox.clicked.disconnect()
                self.__logger.debug(f"断开复选框 '{checkbox.text()}' 的信号")
            except TypeError:
                # 忽略没有连接的情况
                pass

        # 添加新的点击事件处理
        for checkbox in target_boxes:
            checkbox.clicked.connect(
                lambda state, cb=checkbox: self.__on_exclusive_checkbox_clicked(
                    cb, state, target_boxes, callback
                )
            )
            self.__logger.debug(f"为复选框 '{checkbox.text()}' 添加互斥选择处理")


class SignalType:
    """信号类型枚举"""

    STRING = "str"
    INT = "int"
    FLOAT = "float"
    BOOL = "bool"
    LIST = "list"
    DICT = "dict"
    OBJECT = "object"


class SignalContainer(QObject):
    """信号容器类"""

    str_signal = pyqtSignal(str)
    int_signal = pyqtSignal(int)
    float_signal = pyqtSignal(float)
    bool_signal = pyqtSignal(bool)
    list_signal = pyqtSignal(list)
    dict_signal = pyqtSignal(dict)
    object_signal = pyqtSignal(object)

    def __init__(self):
        super().__init__()
        self._signal_map = {
            SignalType.STRING: self.str_signal,
            SignalType.INT: self.int_signal,
            SignalType.FLOAT: self.float_signal,
            SignalType.BOOL: self.bool_signal,
            SignalType.LIST: self.list_signal,
            SignalType.DICT: self.dict_signal,
            SignalType.OBJECT: self.object_signal,
        }

    def get_signal(self, signal_type: str) -> pyqtSignal:
        """获取对应类型的信号实例"""
        return self._signal_map.get(signal_type)  # type: ignore


class SignalSlotManager:
    """
    信号槽管理器，提供一个通用的信号槽管理机制。
    支持动态注册信号、连接槽函数、发送信号等功能。

    主要特点：
        1. 支持多种信号类型（str、int、float、bool、list、dict、object）
        2. 支持动态注册和管理信号
        3. 支持多对多的信号槽连接
        4. 支持带参数的信号发送
        5. 支持异常处理和日志记录
        6. 线程安全

    完整使用示例:
        ```python
        from typing import Dict
        from PyQt5.QtCore import QObject

        # 创建信号管理器
        class MyWindow(QObject):
            def __init__(self):
                super().__init__()
                self.signal_manager = SignalSlotManager()

                # 注册信号
                self.signal_manager.register_signal("message", SignalType.STRING)
                self.signal_manager.register_signal("data_update", SignalType.DICT)
                self.signal_manager.register_signal("progress", SignalType.INT)

                # 连接槽函数
                self.signal_manager.connect_slot("message", self.on_message)
                self.signal_manager.connect_slot("data_update", self.on_data_update)
                self.signal_manager.connect_slot("progress", self.on_progress)

            def on_message(self, text: str):
                print(f"收到消息: {text}")

            def on_data_update(self, data: Dict):
                print(f"数据更新: {data}")

            def on_progress(self, value: int):
                print(f"进度更新: {value}%")

            def process_task(self):
                # 发送各种信号
                self.signal_manager.emit_signal("message", "开始处理任务...")

                # 发送进度信号
                for i in range(0, 101, 20):
                    self.signal_manager.emit_signal("progress", i)

                # 发送数据更新信号
                self.signal_manager.emit_signal("data_update", {
                    "status": "completed",
                    "result": "success",
                    "count": 100
                })

                # 发送完成消息
                self.signal_manager.emit_signal("message", "任务处理完成！")

            def __del__(self):
                # 清理所有连接
                self.signal_manager.clear_all_connections()

        # 使用示例
        if __name__ == "__main__":
            window = MyWindow()
            window.process_task()
        ```

    输出结果:
        ```
        收到消息: 开始处理任务...
        进度更新: 0%
        进度更新: 20%
        进度更新: 40%
        进度更新: 60%
        进度更新: 80%
        进度更新: 100%
        数据更新: {'status': 'completed', 'result': 'success', 'count': 100}
        收到消息: 任务处理完成！
        ```
    """

    def __init__(self):
        super().__init__()
        # 存储所有注册的信号
        self._signals: Dict[str, Dict[str, Any]] = {}
        # 存储所有注册的槽函数
        self._slots: Dict[str, List[Callable]] = {}
        # 存储信号槽连接状态
        self._connections: Dict[str, List[bool]] = {}
        # 配置日志
        self._logger = self._setup_logger()
        # 创建信号容器
        self._signal_container = SignalContainer()

    def _setup_logger(self) -> logging.Logger:
        """配置日志记录器"""
        logger = logging.getLogger("SignalSlotManager")
        logger.setLevel(logging.DEBUG)
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger

    def register_signal(self, signal_name: str, signal_type: str) -> bool:
        """
        注册新的信号。在使用信号之前必须先调用此方法进行注册。

        Args:
            signal_name: 信号名称，用于标识不同的信号，建议使用有意义的名称
            signal_type: 信号类型，必须是 SignalType 中定义的类型之一
                - SignalType.STRING: 字符串类型
                - SignalType.INT: 整数类型
                - SignalType.FLOAT: 浮点数类型
                - SignalType.BOOL: 布尔类型
                - SignalType.LIST: 列表类型
                - SignalType.DICT: 字典类型
                - SignalType.OBJECT: 对象类型

        Returns:
            bool: 注册是否成功
                - True: 注册成功
                - False: 注册失败（可能是信号已存在或类型无效）

        示例:
            ```python
            # 注册一个字符串类型的信号
            manager.register_signal("message", SignalType.STRING)

            # 注册一个字典类型的信号
            manager.register_signal("data_update", SignalType.DICT)
            ```

        注意:
            1. 信号名称在同一个管理器中必须唯一
            2. 注册失败会在日志中记录详细错误信息
            3. 可以使用 get_signal_info 方法查看已注册的信号
        """
        try:
            if signal_name not in self._signals:
                signal = self._signal_container.get_signal(signal_type)
                if signal is not None:
                    self._signals[signal_name] = {
                        "type": signal_type,
                        "instance": signal,
                    }
                    self._slots[signal_name] = []
                    self._connections[signal_name] = []
                    self._logger.info(
                        f"Signal '{signal_name}' registered successfully")
                    return True
            return False
        except Exception as e:
            self._logger.error(f"Error registering signal: {str(e)}")
            self._logger.debug(traceback.format_exc())
            return False

    def connect_slot(self, signal_name: str, slot_func: Callable) -> bool:
        """
        连接槽函数到信号。一个信号可以连接多个槽函数，当信号发出时，所有连接的槽函数都会被调用。

        Args:
            signal_name: 信号名称，必须是已经注册的信号
            slot_func: 槽函数，必须是一个可调用对象（函数或方法），参数类型必须与信号类型匹配

        Returns:
            bool: 连接是否成功
                - True: 连接成功
                - False: 连接失败（可能是信号不存在或参数类型不匹配）

        示例:
            ```python
            # 定义一个简单的槽函数
            def on_message(text: str):
                print(f"收到消息: {text}")

            # 连接到字符串类型的信号
            manager.connect_slot("message", on_message)

            # 定义一个带有多个参数的槽函数
            def on_data_update(data: Dict):
                print(f"数据更新: {data}")

            # 连接到字典类型的信号
            manager.connect_slot("data_update", on_data_update)
            ```

        注意:
            1. 槽函数的参数类型必须与信号类型匹配
            2. 一个槽函数可以连接到多个不同的信号
            3. 同一个槽函数可以多次连接到同一个信号
            4. 连接失败会在日志中记录详细错误信息
        """
        try:
            if signal_name in self._signals:
                signal_instance = self._signals[signal_name]["instance"]
                signal_instance.connect(slot_func)
                self._slots[signal_name].append(slot_func)
                self._connections[signal_name].append(True)
                self._logger.info(
                    f"Slot connected to signal '{signal_name}' successfully")
                return True
            self._logger.warning(f"Signal '{signal_name}' not found")
            return False
        except Exception as e:
            self._logger.error(f"Error connecting slot: {str(e)}")
            self._logger.debug(traceback.format_exc())
            return False

    def disconnect_slot(self, signal_name: str, slot_func: Callable) -> bool:
        """
        断开信号和槽函数的连接。

        Args:
            signal_name: 信号名称，必须是已经注册的信号
            slot_func: 槽函数，必须是之前已经连接到该信号的函数

        Returns:
            bool: 断开连接是否成功
                - True: 断开连接成功
                - False: 断开连接失败（可能是信号不存在或槽函数未连接）

        示例:
            ```python
            # 断开特定的槽函数连接
            manager.disconnect_slot("message", on_message)

            # 在对象销毁时断开所有连接
            def __del__(self):
                self.signal_manager.disconnect_slot("data_update", self.on_data_update)
            ```

        注意:
            1. 只有之前已经连接的槽函数才能断开连接
            2. 断开连接后，该槽函数将不再接收信号
            3. 建议在不需要接收信号时主动断开连接
            4. 断开连接失败会在日志中记录详细错误信息
        """
        try:
            if signal_name in self._signals:
                if slot_func in self._slots[signal_name]:
                    signal_instance = self._signals[signal_name]["instance"]
                    signal_instance.disconnect(slot_func)
                    idx = self._slots[signal_name].index(slot_func)
                    self._slots[signal_name].pop(idx)
                    self._connections[signal_name].pop(idx)
                    self._logger.info(
                        f"Slot disconnected from signal '{signal_name}' successfully"
                    )
                    return True
            return False
        except Exception as e:
            self._logger.error(f"Error disconnecting slot: {str(e)}")
            self._logger.debug(traceback.format_exc())
            return False

    def emit_signal(self, signal_name: str, *args, **kwargs) -> bool:
        """
        发送信号。当信号发出时，所有连接的槽函数都会被调用。

        Args:
            signal_name: 信号名称，必须是已经注册的信号
            *args: 位置参数，将传递给槽函数
            **kwargs: 关键字参数，将传递给槽函数

        Returns:
            bool: 发送是否成功
                - True: 发送成功
                - False: 发送失败（可能是信号不存在或参数类型不匹配）

        示例:
            ```python
            # 发送字符串类型的信号
            manager.emit_signal("message", "Hello, World!")

            # 发送字典类型的信号
            manager.emit_signal("data_update", {
                "name": "test",
                "value": 123,
                "status": True
            })

            # 发送带有关键字参数的信号
            manager.emit_signal("status_update",
                status="running",
                progress=75,
                message="Processing..."
            )
            ```

        注意:
            1. 参数类型必须与信号类型匹配
            2. 发送失败会在日志中记录详细错误信息
            3. 所有连接的槽函数都会按照连接顺序被调用
            4. 如果某个槽函数执行出错，不会影响其他槽函数的执行
        """
        try:
            if signal_name in self._signals:
                signal_instance = self._signals[signal_name]["instance"]
                signal_type = self._signals[signal_name]["type"]

                # 修复OBJECT类型信号没有参数的问题
                if signal_type == SignalType.OBJECT and len(
                        args) == 0 and not kwargs:
                    self._logger.debug(
                        f"提供默认None参数给OBJECT类型信号: '{signal_name}'")
                    signal_instance.emit(None)
                # 检查其他需要参数的信号类型
                elif signal_type != SignalType.OBJECT and len(
                        args) == 0 and not kwargs:
                    # 为每种信号类型提供适当的默认值
                    defaults = {
                        SignalType.STRING: "",
                        SignalType.INT: 0,
                        SignalType.FLOAT: 0.0,
                        SignalType.BOOL: False,
                        SignalType.LIST: [],
                        SignalType.DICT: {},
                    }
                    if signal_type in defaults:
                        self._logger.debug(
                            f"提供默认参数给{signal_type}类型信号: '{signal_name}'")
                        signal_instance.emit(defaults[signal_type])
                    else:
                        signal_instance.emit(*args, **kwargs)
                else:
                    signal_instance.emit(*args, **kwargs)

                self._logger.info(
                    f"Signal '{signal_name}' emitted successfully")
                return True
            return False
        except Exception as e:
            self._logger.error(f"Error emitting signal: {str(e)}")
            self._logger.debug(traceback.format_exc())
            return False

    def get_signal_info(self, signal_name: str) -> Optional[Dict]:
        """
        获取信号的详细信息。

        Args:
            signal_name: 信号名称，必须是已经注册的信号

        Returns:
            Optional[Dict]: 信号信息字典，包含以下字段：
                - type: 信号类型
                - instance: 信号实例
                - slots_count: 连接的槽函数数量
                - connections: 连接状态列表
                如果信号不存在，返回 None

        示例:
            ```python
            # 获取信号信息
            info = manager.get_signal_info("message")
            if info:
                print(f"信号类型: {info['type']}")
                print(f"连接数量: {info['slots_count']}")
                print(f"连接状态: {info['connections']}")
            ```

        注意:
            1. 可以用于调试和监控信号连接状态
            2. 信号实例是 PyQt 的原生信号对象
            3. 连接状态列表与槽函数列表一一对应
        """
        if signal_name in self._signals:
            return {
                "type": self._signals[signal_name]["type"],
                "instance": self._signals[signal_name]["instance"],
                "slots_count": len(self._slots[signal_name]),
                "connections": self._connections[signal_name],
            }
        return None

    def clear_all_connections(self) -> bool:
        """
        清除所有信号槽连接。通常在对象销毁时调用，以防止内存泄漏。

        Returns:
            bool: 清除是否成功
                - True: 清除成功
                - False: 清除失败

        示例:
            ```python
            # 在对象销毁时清除所有连接
            def __del__(self):
                self.signal_manager.clear_all_connections()

            # 手动清除所有连接
            manager.clear_all_connections()
            ```

        注意:
            1. 建议在不再需要信号通信时调用此方法
            2. 清除后所有槽函数都将断开连接
            3. 信号本身仍然存在，可以重新连接槽函数
            4. 清除失败会在日志中记录详细错误信息
        """
        try:
            for signal_name in self._signals:
                for slot_func in self._slots[signal_name][:]:
                    self.disconnect_slot(signal_name, slot_func)
            self._logger.info("All connections cleared successfully")
            return True
        except Exception as e:
            self._logger.error(f"Error clearing connections: {str(e)}")
            self._logger.debug(traceback.format_exc())
            return False


class SignalSlotThread(QThread):
    """线程安全的信号槽管理类

    使用说明：
    1. 基本使用：
       ```python
       # 创建线程管理器
       thread_manager = SignalSlotThread()

       # 定义槽函数
       def my_slot(value):
           print(f"收到值: {value}")

       # 启动线程
       thread_manager.start()

       # 注册信号
       thread_manager.manager.register_signal("test_signal", SignalType.STRING)

       # 连接槽函数
       thread_manager.manager.connect_slot("test_signal", my_slot)

       # 发送信号
       thread_manager.manager.emit_signal("test_signal", "Hello World!")

       # 停止线程
       thread_manager.terminate()
       thread_manager.wait()
       ```

    2. 多线程通信示例：
       ```python
       # 创建线程管理器
       thread_manager = SignalSlotThread()

       # 定义后台处理函数
       def background_process():
           # 模拟耗时操作
           import time
           time.sleep(2)
           # 发送处理结果
           thread_manager.manager.emit_signal("process_complete", "处理完成")

       # 定义结果处理函数
       def handle_result(value):
           print(f"处理结果: {value}")

       # 启动线程
       thread_manager.start()

       # 注册和连接信号
       thread_manager.manager.register_signal("process_complete", SignalType.STRING)
       thread_manager.manager.connect_slot("process_complete", handle_result)

       # 在新线程中执行后台处理
       import threading
       process_thread = threading.Thread(target=background_process)
       process_thread.start()
       ```

    3. 错误处理示例：
       ```python
       # 创建线程管理器
       thread_manager = SignalSlotThread()

       # 定义错误处理函数
       def error_handler(value):
           print(f"发生错误: {value}")

       # 启动线程
       thread_manager.start()

       try:
           # 注册和连接信号
           thread_manager.manager.register_signal("error_signal", SignalType.STRING)
           thread_manager.manager.connect_slot("error_signal", error_handler)

           # 模拟错误情况
           raise Exception("测试错误")

       except Exception as e:
           # 发送错误信号
           thread_manager.manager.emit_signal("error_signal", str(e))

       finally:
           # 清理资源
           thread_manager.terminate()
           thread_manager.wait()
       ```

    4. 多信号类型处理：
       ```python
       # 创建线程管理器
       thread_manager = SignalSlotThread()

       # 定义不同类型的槽函数
       def string_handler(value):
           print(f"字符串数据: {value}")

       def int_handler(value):
           print(f"整数数据: {value}")

       def list_handler(value):
           print(f"列表数据: {value}")

       # 启动线程
       thread_manager.start()

       # 注册不同类型的信号
       thread_manager.manager.register_signal("string_signal", SignalType.STRING)
       thread_manager.manager.register_signal("int_signal", SignalType.INT)
       thread_manager.manager.register_signal("list_signal", SignalType.LIST)

       # 连接对应的槽函数
       thread_manager.manager.connect_slot("string_signal", string_handler)
       thread_manager.manager.connect_slot("int_signal", int_handler)
       thread_manager.manager.connect_slot("list_signal", list_handler)

       # 发送不同类型的信号
       thread_manager.manager.emit_signal("string_signal", "Hello")
       thread_manager.manager.emit_signal("int_signal", 42)
       thread_manager.manager.emit_signal("list_signal", [1, 2, 3])
       ```

    5. 线程安全的数据处理：
       ```python
       # 创建线程管理器
       thread_manager = SignalSlotThread()

       # 定义数据处理函数
       def process_data(data):
           # 模拟数据处理
           result = [x * 2 for x in data]
           # 发送处理结果
           thread_manager.manager.emit_signal("data_processed", result)

       # 定义结果处理函数
       def handle_processed_data(data):
           print(f"处理后的数据: {data}")

       # 启动线程
       thread_manager.start()

       # 注册和连接信号
       thread_manager.manager.register_signal("data_processed", SignalType.LIST)
       thread_manager.manager.connect_slot("data_processed", handle_processed_data)

       # 在新线程中处理数据
       import threading
       data = [1, 2, 3, 4, 5]
       process_thread = threading.Thread(target=process_data, args=(data,))
       process_thread.start()
       ```

    注意事项：
    1. 线程管理：
       - 使用 start() 启动线程
       - 使用 terminate() 终止线程
       - 使用 wait() 等待线程结束

    2. 资源清理：
       - 程序退出前要调用 terminate() 和 wait()
       - 使用 try-finally 确保资源正确释放

    3. 线程安全：
       - 避免在线程中直接操作 UI 元素
       - 使用信号槽机制进行线程间通信
       - 注意数据类型的线程安全性

    4. 性能优化：
       - 合理设置 msleep 时间间隔
       - 避免频繁的信号发送
       - 及时清理不需要的信号槽连接

    5. 错误处理：
       - 使用 try-except 捕获异常
       - 通过信号机制通知错误
       - 记录详细的错误日志
    """

    def __init__(self):
        super().__init__()
        self.manager = SignalSlotManager()

    def run(self):
        """线程运行函数"""
        try:
            while True:
                # 在这里处理信号槽的线程安全操作
                self.msleep(100)  # 避免CPU占用过高
        except Exception as e:
            self.manager._logger.error(f"Thread error: {str(e)}")
            self.manager._logger.debug(traceback.format_exc())


class LogOutput:
    """日志输出类

    使用说明：
    1. 基本使用：
       ```python
       # 创建日志输出对象
       log_output = LogOutput(text_edit_widget)

       # 输出普通日志
       log_output.append("这是一条普通日志")

       # 输出带颜色的日志
       log_output.append("这是一条红色日志", color="red")

       # 输出带样式的日志
       log_output.append_with_style("这是一条大号加粗日志",
           color="red",
           size=14,
           bold=True)

       # 输出多颜色带样式的日志
       log_output.append_multi_style([
           ("错误", {"color": "red", "size": 14, "bold": True}),
           (": ", {"color": "black"}),
           ("文件不存在", {"color": "blue", "bold": True})
       ])

       # 清除日志
       log_output.clear()
       ```

    2. 信号槽使用：
       ```python
       # 创建日志输出对象
       log_output = LogOutput(text_edit_widget)

       # 发送普通日志
       log_output.append("这是一条通过信号发送的日志")

       # 发送带样式的日志
       log_output.append_with_style("这是一条带样式的日志",
           color="red",
           size=14,
           bold=True)

       # 发送多样式日志
       log_output.append_multi_style([
           ("[系统]", {"color": "purple", "size": 12, "bold": True}),
           (" ", {"color": "black"}),
           ("警告", {"color": "orange", "size": 14, "bold": True})
       ])

       # 在线程中使用
       def background_logging():
           for i in range(10):
               log_output.append(f"后台日志 {i}")
               time.sleep(0.5)

       import threading
       log_thread = threading.Thread(target=background_logging)
       log_thread.start()
       ```

    3. 高级使用场景：
       ```python
       # 创建日志输出对象
       log_output = LogOutput(text_edit_widget)

       # 设置默认样式
       log_output.set_default_color("blue")
       log_output.set_default_size(12)
       log_output.set_default_bold(True)
       log_output.set_timestamp_color("#666666")

       # 禁用自动滚动
       log_output.set_auto_scroll(False)

       # 输出系统状态日志
       def log_system_status(cpu_usage, memory_usage):
           status_parts = [
               ("[系统状态]", {"color": "purple", "size": 12, "bold": True}),
               ("\nCPU使用率: ", {"color": "black"}),
               (f"{cpu_usage}%", {"color": "red" if cpu_usage > 80 else "green"}),
               ("\n内存使用率: ", {"color": "black"}),
               (f"{memory_usage}%", {"color": "red" if memory_usage > 80 else "green"})
           ]
           log_output.append_multi_style(status_parts)

       # 输出错误日志
       def log_error(error_type, message, details=None):
           error_parts = [
               ("[错误]", {"color": "red", "size": 14, "bold": True}),
               (f" {error_type}: ", {"color": "black"}),
               (message, {"color": "red"}),
           ]
           if details:
               error_parts.extend([
                   ("\n详细信息: ", {"color": "black"}),
                   (details, {"color": "red"})
               ])
           log_output.append_multi_style(error_parts)

       # 输出警告日志
       def log_warning(message, level="warning"):
           color = "orange" if level == "warning" else "red"
           log_output.append_with_style(f"[警告] {message}",
               style={"color": color, "size": 12, "bold": True})

       # 输出成功日志
       def log_success(message):
           log_output.append_with_style(f"[成功] {message}",
               style={"color": "green", "size": 12, "bold": True})
       ```

    4. 性能优化建议：
       - 对于频繁的日志输出，建议使用信号槽机制
       - 大量日志时，考虑使用批处理方式
       - 适当设置自动滚动，避免频繁更新UI
       - 合理使用样式，避免过度使用加粗等样式

    5. 注意事项：
       - 确保在主线程中创建 QTextEdit 控件
       - 大量日志时注意内存使用
       - 样式设置要合理，避免影响可读性
       - 时间戳颜色要与背景色形成对比

    6. 常见问题：
       - 如果日志不显示，检查 QTextEdit 控件是否正确初始化
       - 如果样式不生效，检查样式参数是否正确
       - 如果自动滚动不工作，检查 set_auto_scroll 设置
       - 如果信号槽不工作，检查信号注册和连接是否正确
    """

    def __init__(self, log_output_widget: QTextEdit):
        """
        初始化日志输出类

        Args:
            log_output_widget: QTextEdit 控件，用于显示日志
        """
        self.log_output = log_output_widget
        self.default_color = "black"  # 默认颜色
        self.timestamp_color = "#808080"  # 时间戳颜色（淡灰色）
        self.default_size = 18  # 默认字体大小
        self.default_bold = False  # 默认是否加粗
        self.auto_scroll = True  # 是否自动滚动
        self.indent_width = 20  # 默认缩进宽度（像素）
        self.indent_str = "\t"  # 默认缩进字符串

        # 设置为只读模式，禁止编辑
        self.set_read_only(True)

        # 创建信号槽管理器
        self.signal_manager = SignalSlotManager()

        # 注册信号，使用字符串类型
        self.signal_manager.register_signal("log_message", "str")
        self.signal_manager.register_signal("log_style", "dict")
        self.signal_manager.register_signal("log_multi_style", "list")

        # 连接槽函数
        self.signal_manager.connect_slot("log_message", self._append)
        self.signal_manager.connect_slot("log_style", self._append_with_style)
        self.signal_manager.connect_slot("log_multi_style",
                                         self._append_multi_style)

    def _scroll_to_bottom(self) -> None:
        """
        滚动到底部
        """
        try:
            if self.auto_scroll:
                # 获取滚动条
                scrollbar = self.log_output.verticalScrollBar()
                # 设置滚动条位置为最大值
                scrollbar.setValue(scrollbar.maximum())
                # 强制更新
                self.log_output.viewport().update()
        except Exception as e:
            print(f"滚动错误: {str(e)}")

    def set_auto_scroll(self, enable: bool) -> None:
        """
        设置是否自动滚动

        Args:
            enable: 是否启用自动滚动
        """
        self.auto_scroll = enable

    def _get_timestamp(self) -> str:
        """
        获取当前时间戳

        Returns:
            str: 格式化的时间戳字符串
        """
        from datetime import datetime

        return datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")

    def _format_text(self, text: str, style: Dict = None) -> str:
        """
        格式化文本，添加样式

        Args:
            text: 要格式化的文本
            style: 样式字典，包含 color、size、bold 等属性

        Returns:
            str: 格式化后的HTML文本
        """
        if style is None:
            style = {}

        # 获取样式属性
        color = style.get("color", self.default_color)
        size = style.get("size", self.default_size)
        bold = style.get("bold", self.default_bold)

        # 构建样式字符串
        style_str = f"color: {color}; font-size: {size}px;"
        if bold:
            style_str += " font-weight: bold;"

        # 处理换行和缩进
        # 将 \n 替换为 <br>
        # 将 \t 替换为 4个空格的HTML实体
        html_text = ""
        for line in text.split("\n"):
            if html_text:  # 不是第一行，添加换行
                html_text += "<br>"
            # 处理每行开头的缩进
            if line.startswith("\t"):
                html_text += "&nbsp;&nbsp;&nbsp;&nbsp;" + line[1:]
            else:
                html_text += line

        return f'<span style="{style_str}">{html_text}</span>'

    def _wrap_text(self, text: str, max_width: int) -> str:
        """
        根据最大宽度自动换行文本

        Args:
            text: 要换行的文本
            max_width: 最大宽度

        Returns:
            str: 换行后的文本
        """
        if not text:
            return text

        # 获取字体度量
        font_metrics = self.log_output.fontMetrics()

        # 计算缩进宽度
        indent_width = self.indent_width or font_metrics.width(self.indent_str)

        # 如果文本已经包含换行符，先按换行符分割处理
        paragraphs = text.split("\n")
        result_lines = []

        for paragraph in paragraphs:
            if not paragraph.strip():
                result_lines.append("")  # 保留空行
                continue

            # 当前行宽度和当前行文本
            current_width = 0
            current_line = ""

            # 逐字符处理文本
            i = 0
            while i < len(paragraph):
                char = paragraph[i]

                # 获取当前字符宽度
                char_width = font_metrics.width(char)

                # 检查是否需要换行
                if current_width + char_width > max_width and current_line:
                    # 添加当前行到结果
                    result_lines.append(current_line)
                    # 开始新行，添加缩进
                    current_line = self.indent_str
                    current_width = indent_width

                # 添加字符到当前行
                current_line += char
                current_width += char_width
                i += 1

            # 添加最后一行
            if current_line:
                result_lines.append(current_line)

        return "\n".join(result_lines)

    def append(self, message: str, color: str = None) -> None:
        """
        添加单颜色日志消息

        Args:
            message: 日志消息
            color: 消息颜色（可选）
        """
        style = {"color": color} if color else None
        self.append_with_style(message, style)

    def append_with_style(self, message: str, style: Dict = None) -> None:
        """
        添加带样式的日志消息

        Args:
            message: 日志消息
            style: 样式字典，包含 color、size、bold 等属性
        """
        # 发送信号，将消息和样式合并为一个字典
        data = {"message": message, "style": style or {}}
        self.signal_manager.emit_signal("log_style", data)

    def append_multi_style(self, text_parts: List[Tuple[str, Dict]]) -> None:
        """
        添加多样式日志消息

        Args:
            text_parts: 文本部分列表，每个部分是一个(文本, 样式字典)元组
        """
        # 发送信号
        self.signal_manager.emit_signal("log_multi_style", text_parts)

    def _append(self, message: str, color: str = None) -> None:
        """
        内部方法：添加单颜色日志消息

        Args:
            message: 日志消息
            color: 消息颜色（可选）
        """
        style = {"color": color} if color else {}
        data = {"message": message, "style": style}
        self._append_with_style(data)

    def _append_with_style(self, data: Dict) -> None:
        """
        内部方法：添加带样式的日志消息

        Args:
            data: 包含消息和样式的字典
        """
        try:
            message = data["message"]
            style = data["style"]

            # 获取时间戳
            timestamp = self._get_timestamp()

            # 创建文本格式
            timestamp_format = self.log_output.currentCharFormat()
            timestamp_format.setForeground(QColor(self.timestamp_color))

            message_format = self.log_output.currentCharFormat()
            if style:
                if "color" in style:
                    message_format.setForeground(QColor(style["color"]))
                if "size" in style:
                    message_format.setFontPointSize(style["size"])
                if "bold" in style:
                    message_format.setFontWeight(
                        QFont.Bold if style["bold"] else QFont.Normal)

            # 获取文本光标
            cursor = self.log_output.textCursor()
            cursor.movePosition(QTextCursor.End)

            # 获取 QTextEdit 的宽度
            text_width = self.log_output.viewport().width() - 40  # 留出边距

            # 计算缩进宽度（以空格数计算）
            indent_spaces = "    "  # 4个空格作为缩进

            # 计算时间戳的宽度
            timestamp_width = self.log_output.fontMetrics().width(timestamp +
                                                                  ": ")

            # 当前行文本和宽度
            current_line = ""
            current_width = 0
            is_first_line = True

            # 处理每个字符
            i = 0
            while i < len(message):
                char = message[i]
                char_width = self.log_output.fontMetrics().width(char)

                # 如果是英文字母，尝试收集完整单词
                if char.isalpha() and ord(char) < 128:
                    word = char
                    word_width = char_width
                    j = i + 1
                    while (j < len(message) and message[j].isalpha()
                           and ord(message[j]) < 128):
                        word += message[j]
                        word_width += self.log_output.fontMetrics().width(
                            message[j])
                        j += 1

                    # 检查是否需要换行
                    total_width = current_width + word_width
                    if is_first_line:
                        total_width += timestamp_width

                    if total_width > text_width:
                        if current_line:
                            # 插入当前行
                            if is_first_line:
                                cursor.insertText(timestamp, timestamp_format)
                                cursor.insertText(": ", timestamp_format)
                            cursor.insertText(current_line, message_format)
                            cursor.insertBlock()
                            # 新行添加缩进
                            current_line = indent_spaces + word
                            current_width = self.log_output.fontMetrics(
                            ).width(indent_spaces + word)
                            is_first_line = False
                        else:
                            current_line = word
                            current_width = word_width
                    else:
                        current_line += word
                        current_width += word_width
                    i = j
                else:
                    # 处理中文字符或其他字符
                    total_width = current_width + char_width
                    if is_first_line:
                        total_width += timestamp_width

                    if total_width > text_width:
                        if current_line:
                            # 插入当前行
                            if is_first_line:
                                cursor.insertText(timestamp, timestamp_format)
                                cursor.insertText(": ", timestamp_format)
                            cursor.insertText(current_line, message_format)
                            cursor.insertBlock()
                            # 新行添加缩进
                            current_line = indent_spaces + char
                            current_width = self.log_output.fontMetrics(
                            ).width(indent_spaces + char)
                            is_first_line = False
                        else:
                            current_line = char
                            current_width = char_width
                    else:
                        current_line += char
                        current_width += char_width
                    i += 1

                # 跳过空格
                while i < len(message) and message[i].isspace():
                    i += 1

            # 插入最后一行
            if current_line:
                if is_first_line:
                    cursor.insertText(timestamp, timestamp_format)
                    cursor.insertText(": ", timestamp_format)
                cursor.insertText(current_line, message_format)

            # 插入额外的换行符，为下一条日志做准备
            cursor.insertBlock()

            # 更新光标位置
            self.log_output.setTextCursor(cursor)
            self._scroll_to_bottom()

        except Exception as e:
            print(f"日志输出错误: {str(e)}")
            traceback.print_exc()

    def _append_multi_style(self, text_parts: List[Tuple[str, Dict]]) -> None:
        """
        内部方法：添加多样式日志消息

        Args:
            text_parts: 文本部分列表，每个部分是一个(文本, 样式字典)元组
        """
        try:
            # 获取时间戳
            timestamp = self._get_timestamp()

            # 创建文本格式
            timestamp_format = self.log_output.currentCharFormat()
            timestamp_format.setForeground(QColor(self.timestamp_color))

            # 获取文本光标
            cursor = self.log_output.textCursor()
            cursor.movePosition(QTextCursor.End)

            # 获取 QTextEdit 的宽度
            text_width = self.log_output.viewport().width() - 40  # 留出边距

            # 计算缩进宽度（以空格数计算）
            indent_spaces = "    "  # 4个空格作为缩进

            # 计算时间戳的宽度
            timestamp_width = self.log_output.fontMetrics().width(timestamp +
                                                                  ": ")

            # 当前行文本和宽度
            current_line = ""
            current_width = 0
            current_format = None
            is_first_line = True

            # 处理每个文本部分
            for text, style in text_parts:
                # 创建当前部分的格式
                part_format = self.log_output.currentCharFormat()
                if style:
                    if "color" in style:
                        part_format.setForeground(QColor(style["color"]))
                    if "size" in style:
                        part_format.setFontPointSize(style["size"])
                    if "bold" in style:
                        part_format.setFontWeight(
                            QFont.Bold if style["bold"] else QFont.Normal)

                # 处理每个字符
                i = 0
                while i < len(text):
                    char = text[i]
                    char_width = self.log_output.fontMetrics().width(char)

                    # 如果是英文字母，尝试收集完整单词
                    if char.isalpha() and ord(char) < 128:
                        word = char
                        word_width = char_width
                        j = i + 1
                        while (j < len(text) and text[j].isalpha()
                               and ord(text[j]) < 128):
                            word += text[j]
                            word_width += self.log_output.fontMetrics().width(
                                text[j])
                            j += 1

                        # 检查是否需要换行
                        total_width = current_width + word_width
                        if is_first_line:
                            total_width += timestamp_width

                        if total_width > text_width:
                            if current_line:
                                # 插入当前行
                                if is_first_line:
                                    cursor.insertText(timestamp,
                                                      timestamp_format)
                                    cursor.insertText(": ", timestamp_format)
                                cursor.insertText(
                                    current_line, current_format
                                    or part_format)
                                cursor.insertBlock()
                                # 新行添加缩进
                                current_line = indent_spaces + word
                                current_width = self.log_output.fontMetrics(
                                ).width(indent_spaces + word)
                                is_first_line = False
                            else:
                                current_line = word
                                current_width = word_width
                        else:
                            current_line += word
                            current_width += word_width
                        i = j
                    else:
                        # 处理中文字符或其他字符
                        total_width = current_width + char_width
                        if is_first_line:
                            total_width += timestamp_width

                        if total_width > text_width:
                            if current_line:
                                # 插入当前行
                                if is_first_line:
                                    cursor.insertText(timestamp,
                                                      timestamp_format)
                                    cursor.insertText(": ", timestamp_format)
                                cursor.insertText(
                                    current_line, current_format
                                    or part_format)
                                cursor.insertBlock()
                                # 新行添加缩进
                                current_line = indent_spaces + char
                                current_width = self.log_output.fontMetrics(
                                ).width(indent_spaces + char)
                                is_first_line = False
                            else:
                                current_line = char
                                current_width = char_width
                        else:
                            current_line += char
                            current_width += char_width
                        i += 1

                    # 跳过空格
                    while i < len(text) and text[i].isspace():
                        i += 1

                    # 更新当前格式
                    current_format = part_format

            # 插入最后一行
            if current_line:
                if is_first_line:
                    cursor.insertText(timestamp, timestamp_format)
                    cursor.insertText(": ", timestamp_format)
                cursor.insertText(current_line, current_format)

            # 插入额外的换行符，为下一条日志做准备
            cursor.insertBlock()

            # 更新光标位置
            self.log_output.setTextCursor(cursor)
            self._scroll_to_bottom()

        except Exception as e:
            print(f"多样式日志输出错误: {str(e)}")
            traceback.print_exc()

    def clear(self) -> None:
        """
        清除所有日志
        """
        try:
            self.log_output.clear()
        except Exception as e:
            print(f"清除日志错误: {str(e)}")
            traceback.print_exc()

    def set_default_color(self, color: str) -> None:
        """
        设置默认文本颜色

        Args:
            color: 颜色值
        """
        self.default_color = color

    def set_timestamp_color(self, color: str) -> None:
        """
        设置时间戳颜色

        Args:
            color: 颜色值
        """
        self.timestamp_color = color

    def set_default_size(self, size: int) -> None:
        """
        设置默认字体大小

        Args:
            size: 字体大小（像素）
        """
        self.default_size = size

    def set_default_bold(self, bold: bool) -> None:
        """
        设置默认是否加粗

        Args:
            bold: 是否加粗
        """
        self.default_bold = bold

    def emit_log(self, message: str, color: str = None) -> None:
        """
        通过信号发送普通日志消息

        Args:
            message: 日志消息
            color: 消息颜色（可选）
        """
        style = {"color": color} if color else None
        data = {"message": message, "style": style or {}}
        self.signal_manager.emit_signal("log_style", data)

    def emit_log_style(self, message: str, **style) -> None:
        """
        通过信号发送带样式的日志消息

        Args:
            message: 日志消息
            **style: 样式参数，如 color、size、bold 等
        """
        data = {"message": message, "style": style}
        self.signal_manager.emit_signal("log_style", data)

    def emit_multi_style(self, text_parts: List[Tuple[str, Dict]]) -> None:
        """
        通过信号发送多样式日志消息

        Args:
            text_parts: 文本部分列表，每个部分是一个(文本, 样式字典)元组
        """
        self.signal_manager.emit_signal("log_multi_style", text_parts)

    def set_read_only(self, read_only: bool = True) -> None:
        """
        设置日志控件是否为只读模式

        Args:
            read_only: 是否为只读模式，默认为 True
        """
        try:
            self.log_output.setReadOnly(read_only)
        except Exception as e:
            print(f"设置只读模式错误: {str(e)}")

    def is_read_only(self) -> bool:
        """
        获取日志控件是否为只读模式

        Returns:
            bool: 是否为只读模式
        """
        try:
            return self.log_output.isReadOnly()
        except Exception as e:
            print(f"获取只读模式错误: {str(e)}")
            return True  # 默认返回只读模式

    def set_indent_width(self, width: int) -> None:
        """
        设置缩进宽度

        Args:
            width: 缩进宽度（像素）
        """
        self.indent_width = width

    def set_indent_str(self, indent_str: str) -> None:
        """
        设置缩进字符串

        Args:
            indent_str: 缩进字符串，例如 "\t" 或 "    "
        """
        self.indent_str = indent_str


class QPushButtonHelper(QObject):
    """
    QPushButtonHelper 是一个强大的工具类，用于增强 QPushButton 的功能和交互体验。
    提供了信号连接、样式切换、动画效果等丰富功能。

    使用示例集锦：

    1. 基本初始化和使用:
    ```python
    from PyQt5.QtWidgets import QPushButton
    from GUI.utils.completer import QPushButtonHelper

    # 创建一个按钮
    button = QPushButton("点击我")
    # 创建按钮助手，可选择启用自动样式
    helper = QPushButtonHelper(button, auto_style=True)

    # 监听按钮点击信号
    helper.button_clicked.connect(lambda checked: print(f"按钮被点击了: {checked}"))
    ```

    2. 按钮状态和样式管理:
    ```python
    # 禁用按钮(会自动应用禁用样式，如果启用了auto_style)
    helper.setEnabled(False)

    # 设置自定义禁用样式
    helper.setDisabledStyle('''
        QPushButton:disabled {
            color: #666666;
            background-color: #EEEEEE;
            border: 1px solid #AAAAAA;
        }
    ''')

    # 启用自动样式切换功能
    helper.enableAutoStyle(True)

    # 临时重置为原始样式
    helper.resetToOriginalStyle()
    ```

    3. 按钮外观设置:
    ```python
    # 设置按钮文本
    helper.setText("新按钮文本")

    # 设置工具提示
    helper.setTooltip("这是一个按钮提示")

    # 设置图标
    from PyQt5.QtGui import QIcon
    from PyQt5.QtCore import QSize
    icon = QIcon("path/to/icon.png")
    helper.setIcon(icon, QSize(16, 16))
    ```

    4. 信号连接管理:
    ```python
    # 定义按钮点击处理函数
    def on_button_clicked():
        print("按钮被点击")

    # 连接按钮点击信号(Qt风格)
    helper.connectClickedSignal(on_button_clicked)

    # 断开特定函数的连接
    helper.disconnectClickedSignal(on_button_clicked)

    # 断开所有连接
    helper.disconnectClickedSignal()
    ```

    5. 高级效果:
    ```python
    # 添加悬停透明度动画效果
    helper.addHoverOpacityEffect(
        start_opacity=1.0,  # 正常状态下不透明
        end_opacity=0.8,    # 悬停时轻微透明
        duration=200        # 200毫秒完成过渡
    )

    # 添加点击防抖动(避免短时间内多次点击触发多次事件)
    helper.enableDebounce(interval_ms=300)
    ```

    6. 检查状态:
    ```python
    # 检查按钮是否启用
    if helper.isEnabled():
        print("按钮当前已启用")

    # 检查是否启用了自动样式
    if helper.isAutoStyleEnabled():
        print("按钮已启用自动样式")
    ```

    7. 直接访问原始按钮:
    ```python
    # 获取原始QPushButton对象进行高级操作
    original_button = helper.getButton()
    original_button.setShortcut("Ctrl+B")
    ```
    """

    # --- 自定义信号 ---
    # 当按钮被点击时发出，参数为按钮的 checked 状态
    buttonClicked = pyqtSignal(bool)
    # 保留原始名称作为别名，以保持兼容性
    button_clicked = buttonClicked

    # 当按钮的启用状态变化时发出，参数为新的启用状态
    enabledChanged = pyqtSignal(bool)
    # 保留原始名称作为别名，以保持兼容性
    enabled_changed = enabledChanged

    # 当按钮的可见性变化时发出，参数为新的可见性状态
    visibilityChanged = pyqtSignal(bool)
    # 保留原始名称作为别名，以保持兼容性
    visibility_changed = visibilityChanged

    # --- 私有成员变量 ---
    __button: QPushButton  # 存储关联的 QPushButton 实例
    __opacity_effect: Optional[QGraphicsOpacityEffect] = (
        None  # 用于悬停效果的图形效果对象
    )
    __opacity_animation_in: Optional[QPropertyAnimation] = (
        None  # 鼠标进入时的透明度动画
    )
    __opacity_animation_out: Optional[QPropertyAnimation] = (
        None  # 鼠标离开时的透明度动画
    )
    __debounce_timer: Optional[QTimer] = None  # 用于防抖动的定时器
    __debounce_interval: int = 0  # 防抖动间隔 (毫秒)
    __last_emit_time: int = 0  # 上次信号发出时间，用于防抖动
    __logger: logging.Logger = None  # 类内部的日志记录器
    __original_stylesheet: str = ""  # 原始样式表，用于在启用按钮时恢复
    __disabled_stylesheet: str = ""  # 禁用状态的样式表
    __auto_style_enabled: bool = False  # 是否启用自动样式切换
    __is_watching_enabled: bool = False  # 是否监听按钮启用状态变化

    @classmethod
    def __get_logger(cls) -> logging.Logger:
        """
        获取或创建类的内部日志记录器。
        这是一个类方法，确保所有实例共享同一个日志记录器。

        Returns:
            logging.Logger: 配置好的日志记录器实例
        """
        if cls.__logger is None:
            # 创建并配置日志记录器
            cls.__logger = logging.getLogger("QPushButtonHelper")

            # 如果需要特定配置且没有已存在的处理器，可以添加处理器
            if not cls.__logger.handlers:
                # 这里不再设置 basicConfig，因为它已经在全局设置过了
                # 如果需要特殊配置，可以在这里添加
                pass

        return cls.__logger

    def __init__(
        self,
        button: QPushButton,
        parent: Optional[QObject] = None,
        auto_style: bool = False,
    ):
        """
        初始化 QPushButtonHelper。

        Args:
            button (QPushButton): 需要进行管理的 QPushButton 实例。
            parent (Optional[QObject]): Qt 的父对象，默认为 None。
            auto_style (bool): 是否自动启用样式切换功能，默认为 False。启用后，
                             按钮在禁用状态下会自动应用禁用样式，启用状态下恢复原始样式。
        """
        super().__init__(parent)
        # 初始化日志记录器
        self.__logger = self.__get_logger()

        # 类型检查，确保传入的是 QPushButton
        if not isinstance(button, QPushButton):
            error_msg = "初始化失败：传入的对象不是 QPushButton 实例。"
            self.__logger.error(error_msg)
            raise TypeError(error_msg)

        self.__button = button
        # 保存原始样式表
        self.__original_stylesheet = self.__button.styleSheet()
        # 设置默认的禁用样式表
        self.__disabled_stylesheet = """
            QPushButton:disabled {
                color: #888888;
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
            }
        """
        # 默认不启用自动样式和监听
        self.__auto_style_enabled = False
        self.__is_watching_enabled = False

        # 记录初始化日志，尝试获取按钮的对象名称或文本作为标识
        button_id = self.__button.objectName() or self.__button.text()
        self.__logger.debug(f"QPushButtonHelper 已为按钮 '{button_id}' 初始化")

        # 连接按钮原生信号到我们的自定义信号
        self.__setup_internal_connections()

        # 如果指定了自动启用样式切换，则启用它
        if auto_style:
            self.enableAutoStyle(True)
            self.__logger.debug(f"按钮 '{button_id}' 在初始化时启用了自动样式功能")

    def __setup_internal_connections(self) -> None:
        """
        设置内部信号连接，包括按钮状态监听和点击事件转发。
        这是一个私有方法，仅在初始化时调用。
        """
        # 连接点击信号
        self.__button.clicked.connect(self.__on_button_clicked)

        # 监听按钮启用状态变化（通过事件过滤器实现，因为 QPushButton 没有直接的 enabledChanged 信号）
        self.__button.installEventFilter(self)

        self.__logger.debug(
            f"已设置按钮 '{self.__button.objectName() or self.__button.text()}' 的内部信号连接"
        )

    def __del__(self) -> None:
        """
        析构函数，确保资源被正确清理。
        移除事件过滤器，停止并清理所有动画和定时器。

        注意: 此方法在对象被 Python 垃圾回收时调用，此时底层的 Qt 对象可能已被销毁。
        因此需要谨慎检查对象状态，避免访问已销毁的对象。
        """
        try:
            self.__logger.debug(f"QPushButtonHelper 开始清理资源")

            # 检查按钮是否已被删除
            if self.__button is not None:
                if not sip.isdeleted(self.__button):
                    self.__logger.debug(
                        f"按钮 '{self.__button.objectName() or self.__button.text()}' 仍然有效，移除事件过滤器"
                    )
                    # 清理事件过滤器
                    self.__button.removeEventFilter(self)

                    # 清理效果
                    if self.__opacity_effect is not None:
                        self.__logger.debug(f"清理按钮的透明度效果")
                        self.__button.setGraphicsEffect(None)
                else:
                    self.__logger.debug(f"按钮对象已被删除，跳过事件过滤器和图形效果的清理")
            else:
                self.__logger.debug(f"按钮对象为 None，跳过事件过滤器和图形效果的清理")

            # 清理动画 (即使按钮已删除，动画对象可能仍然存在)
            if self.__opacity_animation_in is not None:
                self.__logger.debug(f"停止进入动画")
                self.__opacity_animation_in.stop()

            if self.__opacity_animation_out is not None:
                self.__logger.debug(f"停止离开动画")
                self.__opacity_animation_out.stop()

            # 清理定时器
            if self.__debounce_timer is not None:
                self.__logger.debug(f"停止防抖动定时器")
                self.__debounce_timer.stop()

            self.__logger.debug(f"QPushButtonHelper 资源清理完成")
        except Exception as e:
            self.__logger.error(f"QPushButtonHelper 清理资源时发生错误: {e}",
                                exc_info=True)

    # --- 私有槽函数 ---

    @pyqtSlot(bool)
    def __on_button_clicked(self, checked: bool) -> None:
        """
        内部槽函数，处理按钮点击事件。
        如果启用了防抖动，将进行防抖处理。

        Args:
            checked (bool): 按钮的选中状态。
        """
        button_id = self.__button.objectName() or self.__button.text()

        # 如果启用了防抖动功能
        if self.__debounce_interval > 0:
            current_time = int(QTime.currentTime().msecsSinceStartOfDay())
            if current_time - self.__last_emit_time < self.__debounce_interval:
                self.__logger.debug(f"按钮 '{button_id}' 点击被防抖动机制过滤")
                return

            self.__last_emit_time = current_time

            # 如果定时器存在且活跃，重启它
            if self.__debounce_timer and self.__debounce_timer.isActive():
                self.__debounce_timer.stop()
                self.__debounce_timer.start(self.__debounce_interval)

        # 发出自定义信号
        self.__logger.debug(
            f"按钮 '{button_id}' 被点击，发出 buttonClicked 信号，checked={checked}")
        self.buttonClicked.emit(checked)  # 使用新的信号名称

    # --- 公有方法 ---

    def connect_click(
        self,
        func: Callable[[], Any],
        connection_type: Qt.ConnectionType = Qt.AutoConnection,
    ) -> None:
        """
        将按钮的 `clicked` 信号连接到指定的目标函数或方法。

        注意: 此方法使用延迟连接机制，与标准的Qt信号连接略有不同。
        这种设计用于解决某些特定场景下的信号连接时序问题。

        在大多数情况下，推荐使用标准Qt连接方式或我们的自定义信号:
        ```python
        # 标准Qt连接方式
        button.clicked.connect(func)

        # 使用QPushButtonHelper的自定义信号（推荐）
        button_helper.button_clicked.connect(func)
        ```

        Args:
            func (Callable[[], Any]): 当按钮被点击时需要调用的函数或方法。
            connection_type (Qt.ConnectionType): 信号连接类型，默认为 Qt.AutoConnection。
                常用值: Qt.DirectConnection (同步调用), Qt.QueuedConnection (异步调用)。
        """
        button_id = self.__button.objectName() or self.__button.text()
        # 使用 `getattr` 安全地获取函数名，防止 `func` 没有 `__name__` 属性
        func_name = getattr(func, "__name__", repr(func))
        self.__logger.debug(
            f"按钮 '{button_id}' 请求连接 clicked 信号到 '{func_name}' (将延迟执行连接操作)")

        # 定义实际执行连接的内部函数
        def __do_connect():
            # 使用 nonlocal 显式声明，虽然在这个简单场景下可能不是必须的，但更清晰
            # nonlocal button_id, func, func_name
            try:
                # 尝试连接信号
                self.__button.clicked.connect(func, connection_type)
                self.__logger.debug(
                    f"按钮 '{button_id}' 的 clicked 信号已成功延迟连接到函数/方法 '{func_name}'"
                )
            except TypeError as te:
                # 处理连接时可能发生的类型错误（例如槽函数签名不匹配）
                self.__logger.error(
                    f"延迟连接按钮 '{button_id}' 的 clicked 信号时发生类型错误: {te}",
                    exc_info=True,
                )
            except Exception as e:
                # 捕获其他潜在异常
                self.__logger.error(
                    f"延迟连接按钮 '{button_id}' 的 clicked 信号时发生未知错误: {e}",
                    exc_info=True,
                )

        # 使用 QTimer.singleShot 将 __do_connect 的执行推迟到事件循环下次空闲时
        try:
            QTimer.singleShot(0, __do_connect)
            # 注意：由于是异步执行，这里无法直接返回连接是否成功的状态
        except Exception as e:
            # singleShot 本身也可能失败（极少见），需要处理
            self.__logger.error(f"为按钮 '{button_id}' 调度延迟连接时发生错误: {e}",
                                exc_info=True)

    # --- 添加更符合Qt风格的连接方法 ---

    def connectClickedSignal(
        self,
        slot: Callable[[], Any],
        connection_type: Qt.ConnectionType = Qt.AutoConnection,
    ) -> bool:
        """
        将按钮的clicked信号连接到指定的槽函数，使用标准Qt连接方式。

        这是connect_click()的替代方法，更符合Qt的标准连接风格，并立即执行连接操作。

        Args:
            slot (Callable[[], Any]): 当按钮被点击时需要调用的槽函数或方法。
            connection_type (Qt.ConnectionType): 信号连接类型，默认为Qt.AutoConnection。

        Returns:
            bool: 连接成功返回True，失败返回False。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 连接按钮点击信号到槽函数
            def onButtonClicked():
                print("按钮被点击")

            # 方式1: 使用标准Qt风格的连接方法（推荐）
            helper.connectClickedSignal(onButtonClicked)

            # 方式2: 使用自定义信号（支持更多功能）
            helper.button_clicked.connect(lambda checked: print(f"按钮被点击: {checked}"))
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()
        func_name = getattr(slot, "__name__", repr(slot))

        try:
            self.__button.clicked.connect(slot, connection_type)
            self.__logger.debug(
                f"按钮 '{button_id}' 的clicked信号已成功连接到槽函数 '{func_name}'")
            return True
        except TypeError as te:
            self.__logger.error(
                f"连接按钮 '{button_id}' 的clicked信号时发生类型错误: {te}",
                exc_info=True,
            )
            return False
        except Exception as e:
            self.__logger.error(
                f"连接按钮 '{button_id}' 的clicked信号时发生未知错误: {e}",
                exc_info=True,
            )
            return False

    def disconnectClickedSignal(self,
                                slot: Optional[Callable[[],
                                                        Any]] = None) -> bool:
        """
        断开按钮的clicked信号与指定槽函数的连接。

        Args:
            slot (Optional[Callable[[], Any]]): 要断开的槽函数。
                如果为None，则断开所有connected到clicked信号的槽。

        Returns:
            bool: 断开连接成功返回True，失败返回False。

        示例:
            ```python
            # 断开特定槽函数
            helper.disconnectClickedSignal(onButtonClicked)

            # 断开所有连接到clicked信号的槽
            helper.disconnectClickedSignal()
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()

        try:
            if slot is None:
                # 断开所有连接
                success = self.__button.clicked.disconnect()
                if success:
                    self.__logger.debug(f"已断开按钮 '{button_id}' 的所有clicked信号连接")
                return success
            else:
                # 断开特定槽函数
                self.__button.clicked.disconnect(slot)
                self.__logger.debug(f"已断开按钮 '{button_id}' 的clicked信号与特定槽函数的连接")
                return True
        except TypeError as te:
            self.__logger.error(
                f"断开按钮 '{button_id}' 的clicked信号连接时发生类型错误: {te}",
                exc_info=True,
            )
            return False
        except RuntimeError as re:
            # 断开不存在的连接时会抛出RuntimeError
            self.__logger.warning(f"断开按钮 '{button_id}' 的clicked信号连接失败: {re}")
            return False
        except Exception as e:
            self.__logger.error(
                f"断开按钮 '{button_id}' 的clicked信号连接时发生未知错误: {e}",
                exc_info=True,
            )
            return False

    def setEnabled(self, enabled: bool) -> None:
        """
        设置按钮的启用或禁用状态。
        状态变化时会发出 enabledChanged 信号。
        如果启用了自动样式功能，还会自动应用相应的样式。

        Args:
            enabled (bool): True 表示启用按钮，False 表示禁用按钮。

        示例:
            ```python
            # 创建按钮和辅助类
            button = QPushButton("测试按钮")
            button_helper = QPushButtonHelper(button, auto_style=True)  # 初始化时启用自动样式

            # 禁用按钮（会自动应用禁用样式）
            button_helper.setEnabled(False)

            # 启用按钮（会自动恢复原始样式）
            button_helper.setEnabled(True)
            ```
        """
        if self.__button.isEnabled() != enabled:
            button_id = self.__button.objectName() or self.__button.text()
            try:
                # 如果启用了自动样式，在设置启用状态前应用相应的样式
                if self.__auto_style_enabled and self.__is_watching_enabled:
                    self.__apply_state_style(enabled)

                # 设置按钮状态
                self.__button.setEnabled(enabled)
                state = "启用" if enabled else "禁用"
                self.__logger.debug(f"按钮 '{button_id}' 的状态已设置为: {state}")

                # 手动发出自定义信号，因为我们不能直接拦截 QPushButton 的内部状态变化
                self.enabledChanged.emit(enabled)
            except Exception as e:
                self.__logger.error(f"设置按钮 '{button_id}' 启用状态时发生错误: {e}",
                                    exc_info=True)
            # 如果状态没有变化，则不执行任何操作

    def setTooltip(self, text: str) -> None:
        """
        设置按钮的工具提示（鼠标悬停时显示的文本）。

        Args:
            text (str): 要显示的工具提示内容。如果为空字符串，则移除提示。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 设置悬停提示文本
            helper.setTooltip("点击此按钮执行操作")
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()
        try:
            self.__button.setToolTip(text)
            if text:
                self.__logger.debug(f"按钮 '{button_id}' 的工具提示已设置为: '{text}'")
            else:
                self.__logger.debug(f"按钮 '{button_id}' 的工具提示已被移除")
        except Exception as e:
            self.__logger.error(f"设置按钮 '{button_id}' 工具提示时发生错误: {e}",
                                exc_info=True)

    # 保留原始方法名以保持兼容性
    set_tooltip = setTooltip

    def setText(self, text: str) -> None:
        """
        设置按钮上显示的文本标签。

        Args:
            text (str): 要在按钮上显示的文本。

        示例:
            ```python
            button = QPushButton("原始文本")
            helper = QPushButtonHelper(button)

            # 修改按钮文本
            helper.setText("新的按钮文本")
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()
        try:
            self.__button.setText(text)
            self.__logger.debug(f"按钮 '{button_id}' 的文本已设置为: '{text}'")
        except Exception as e:
            self.__logger.error(f"设置按钮 '{button_id}' 文本时发生错误: {e}",
                                exc_info=True)

    # 保留原始方法名以保持兼容性
    set_text = setText

    def setIcon(self, icon: QIcon, size: Optional[QSize] = None) -> None:
        """
        设置按钮的图标。

        Args:
            icon (QIcon): 要设置的 QIcon 图标对象。
            size (Optional[QSize]): 可选参数，用于指定图标在按钮上显示的尺寸。如果为 None，则使用默认尺寸。

        示例:
            ```python
            from PyQt5.QtGui import QIcon
            from PyQt5.QtCore import QSize

            button = QPushButton()
            helper = QPushButtonHelper(button)

            # 设置图标
            icon = QIcon("path/to/icon.png")
            helper.setIcon(icon, QSize(24, 24))
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()
        try:
            self.__button.setIcon(icon)
            log_message = f"按钮 '{button_id}' 的图标已设置。"
            if size is not None and isinstance(size, QSize):
                self.__button.setIconSize(size)
                log_message += f" 图标尺寸设置为 {size.width()}x{size.height()}."
            elif size is not None:
                self.__logger.warning(
                    f"为按钮 '{button_id}' 设置图标大小时提供了无效的 size 参数（类型应为 QSize），已忽略。"
                )

            self.__logger.debug(log_message)
        except Exception as e:
            self.__logger.error(f"设置按钮 '{button_id}' 图标时发生错误: {e}",
                                exc_info=True)

    # 保留原始方法名以保持兼容性
    set_icon = setIcon

    def isEnabled(self) -> bool:
        """
        获取按钮当前的启用状态。

        Returns:
            bool: 如果按钮当前处于启用状态，则返回 True；否则返回 False。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 检查按钮是否启用
            if helper.isEnabled():
                print("按钮已启用")
            else:
                print("按钮已禁用")
            ```
        """
        return self.__button.isEnabled()

    # 保留原始方法名以保持兼容性
    is_enabled = isEnabled

    def getButton(self) -> QPushButton:
        """
        获取被管理的原始 QPushButton 对象。

        这允许在需要时直接访问原始按钮以进行高级操作。
        应谨慎使用此方法，优先考虑使用 QPushButtonHelper 提供的方法。

        Returns:
            QPushButton: 被管理的原始按钮对象。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 获取原始按钮对象执行更高级的操作
            original_button = helper.getButton()
            original_button.setShortcut("Ctrl+B")  # 设置快捷键
            ```
        """
        return self.__button

    # 保留原始方法名以保持兼容性
    get_button = getButton

    # --- 新增方法 ---

    def addHoverOpacityEffect(self,
                              start_opacity: float = 1.0,
                              end_opacity: float = 0.7,
                              duration: int = 150) -> bool:
        """
        为按钮添加一个当鼠标悬停和离开时改变其透明度的视觉效果。

        如果效果已存在，则此方法不执行任何操作并返回 False。

        Args:
            start_opacity (float): 鼠标未悬停时的不透明度 (范围 0.0 到 1.0)。默认为 1.0 (完全不透明)。
            end_opacity (float): 鼠标悬停时的不透明度 (范围 0.0 到 1.0)。默认为 0.7。
            duration (int): 透明度变化的动画持续时间（单位：毫秒）。默认为 150ms。

        Returns:
            bool: 如果成功添加了效果（或效果已初始化）则返回 True，如果效果已存在则返回 False。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 添加鼠标悬停透明度效果
            helper.addHoverOpacityEffect(
                start_opacity=1.0,  # 正常状态下完全不透明
                end_opacity=0.8,    # 悬停时轻微透明
                duration=200        # 200毫秒内完成过渡动画
            )
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()
        # 检查效果是否已经添加
        if self.__opacity_effect is not None:
            self.__logger.warning(f"按钮 '{button_id}' 已经具有悬停透明度效果，本次操作忽略。")
            return False

        # 参数校验
        if not (0.0 <= start_opacity <= 1.0 and 0.0 <= end_opacity <= 1.0):
            self.__logger.error(
                f"为按钮 '{button_id}' 添加悬停效果失败：透明度值必须在 0.0 和 1.0 之间。")
            return False
        if duration < 0:
            self.__logger.error(f"为按钮 '{button_id}' 添加悬停效果失败：动画持续时间不能为负数。")
            return False

        try:
            # 1. 创建 QGraphicsOpacityEffect 并应用到按钮
            self.__opacity_effect = QGraphicsOpacityEffect(self.__button)
            self.__button.setGraphicsEffect(self.__opacity_effect)
            self.__opacity_effect.setOpacity(start_opacity)  # 设置初始透明度

            # 2. 创建进入动画 (透明度从 start_opacity 到 end_opacity)
            self.__opacity_animation_in = QPropertyAnimation(
                self.__opacity_effect, b"opacity", self)  # 父对象设为 self
            self.__opacity_animation_in.setDuration(duration)
            self.__opacity_animation_in.setStartValue(start_opacity)
            self.__opacity_animation_in.setEndValue(end_opacity)
            self.__opacity_animation_in.setEasingCurve(
                QEasingCurve.InOutQuad)  # 设置缓动曲线

            # 3. 创建离开动画 (透明度从 end_opacity 回到 start_opacity)
            self.__opacity_animation_out = QPropertyAnimation(
                self.__opacity_effect, b"opacity", self)  # 父对象设为 self
            self.__opacity_animation_out.setDuration(duration)
            self.__opacity_animation_out.setStartValue(end_opacity)
            self.__opacity_animation_out.setEndValue(start_opacity)
            self.__opacity_animation_out.setEasingCurve(
                QEasingCurve.InOutQuad)  # 设置缓动曲线

            # 4. 安装事件过滤器到按钮上，以便 Helper 能捕获悬停事件
            # 注意：eventFilter 已在 __setup_internal_connections 中安装，这里不需重复安装
            self.__logger.debug(
                f"为按钮 '{button_id}' 成功添加了悬停透明度效果 (start={start_opacity}, end={end_opacity}, duration={duration}ms)"
            )
            return True
        except Exception as e:
            self.__logger.error(f"为按钮 '{button_id}' 添加悬停效果时发生未知错误: {e}",
                                exc_info=True)
            # 清理可能部分创建的对象
            if self.__opacity_effect:
                self.__button.setGraphicsEffect(None)  # 移除效果
                self.__opacity_effect = None
            self.__opacity_animation_in = None
            self.__opacity_animation_out = None
            return False

    def enableDebounce(self, interval_ms: int = 200) -> None:
        """
        启用点击防抖动功能，防止短时间内多次点击触发多次事件。

        设置防抖间隔后，在指定的间隔时间内，重复点击只会触发一次 buttonClicked 信号。
        注意：此设置不影响原始按钮的 clicked 信号，只影响 QPushButtonHelper 的 buttonClicked 信号。

        Args:
            interval_ms (int): 防抖动间隔，单位毫秒。设置为 0 表示禁用防抖动。默认为 200ms。

        示例:
            ```python
            button = QPushButton("提交")
            helper = QPushButtonHelper(button)

            # 启用300毫秒的防抖动，防止用户短时间内多次点击
            helper.enableDebounce(interval_ms=300)

            # 禁用防抖动功能
            # helper.enableDebounce(interval_ms=0)
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()

        if interval_ms < 0:
            self.__logger.warning(
                f"按钮 '{button_id}' 设置防抖动间隔无效（不能为负数），已使用默认值 200ms")
            interval_ms = 200

        self.__debounce_interval = interval_ms

        # 如果禁用防抖动，清理资源
        if interval_ms == 0:
            if self.__debounce_timer:
                self.__debounce_timer.stop()
                self.__debounce_timer = None
            self.__logger.debug(f"按钮 '{button_id}' 已禁用防抖动功能")
            return

        # 创建防抖定时器，如果不存在
        if not self.__debounce_timer:
            self.__debounce_timer = QTimer(self)
            self.__debounce_timer.setSingleShot(True)

        self.__logger.debug(f"按钮 '{button_id}' 已启用防抖动功能，间隔设置为 {interval_ms}ms")

    def isAutoStyleEnabled(self) -> bool:
        """
        检查按钮是否启用了自动样式功能。

        Returns:
            bool: 如果启用了自动样式功能，则返回 True；否则返回 False。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button)

            # 检查是否启用了自动样式
            if not helper.isAutoStyleEnabled():
                helper.enableAutoStyle()
            ```
        """
        return self.__auto_style_enabled

    # 保留原始方法名以保持兼容性
    is_auto_style_enabled = isAutoStyleEnabled

    def setDisabledStyle(self, style_sheet: str) -> None:
        """
        设置按钮禁用状态的样式表。
        只有启用了自动样式功能 (enableAutoStyle) 后，此样式才会在按钮禁用时自动应用。

        样式表应该使用 "QPushButton:disabled { ... }" 选择器来定义禁用状态的样式。

        Args:
            style_sheet (str): CSS 样式表字符串，定义按钮禁用状态的样式。
                               例如: "QPushButton:disabled { color: gray; }"

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button, auto_style=True)

            # 设置自定义禁用样式
            helper.setDisabledStyle('''
                QPushButton:disabled {
                    color: #666666;
                    background-color: #EEEEEE;
                    border: 1px solid #AAAAAA;
                    border-radius: 4px;
                }
            ''')

            # 禁用按钮时会自动应用上述样式
            helper.setEnabled(False)
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()

        # 保存新的禁用样式表
        self.__disabled_stylesheet = style_sheet
        self.__logger.debug(f"按钮 '{button_id}' 的禁用样式表已更新")

        # 如果已启用自动样式且按钮当前处于禁用状态，立即应用新样式
        if self.__auto_style_enabled and not self.__button.isEnabled():
            self.__apply_state_style(False)

    # 保留原始方法名以保持兼容性
    set_disabled_style = setDisabledStyle

    def resetToOriginalStyle(self) -> None:
        """
        重置按钮样式到初始状态。
        无论当前按钮是否启用或禁用，都会恢复到原始样式。

        注意：此操作不会关闭自动样式功能，如果按钮之后状态改变，仍会应用相应样式。
        如需完全禁用自动样式，请使用 enableAutoStyle(False)。

        示例:
            ```python
            button = QPushButton("测试按钮")
            helper = QPushButtonHelper(button, auto_style=True)

            # 临时重置为原始样式，不影响自动样式功能
            helper.resetToOriginalStyle()
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()

        try:
            self.__button.setStyleSheet(self.__original_stylesheet)
            self.__logger.debug(f"按钮 '{button_id}' 样式已重置为原始状态")
        except Exception as e:
            self.__logger.error(f"重置按钮 '{button_id}' 样式时发生错误: {e}",
                                exc_info=True)

    # 保留原始方法名以保持兼容性
    reset_to_original_style = resetToOriginalStyle

    def __start_watching_enabled_state(self) -> None:
        """
        开始监听按钮的启用状态变化。
        当按钮状态变化时，会自动应用相应的样式。
        这是一个私有方法，仅在内部调用。
        """
        if self.__is_watching_enabled:
            return  # 如果已经在监听，则不再重复设置

        self.__is_watching_enabled = True
        button_id = self.__button.objectName() or self.__button.text()

        # 立即应用当前状态的样式
        current_enabled = self.__button.isEnabled()
        self.__apply_state_style(current_enabled)

        self.__logger.debug(
            f"开始监听按钮 '{button_id}' 的启用状态变化，当前状态: {'启用' if current_enabled else '禁用'}"
        )

    def __stop_watching_enabled_state(self) -> None:
        """
        停止监听按钮的启用状态变化。
        停止监听后，按钮的样式不会随状态自动变化。
        这是一个私有方法，仅在内部调用。
        """
        if not self.__is_watching_enabled:
            return  # 如果没有在监听，则不执行操作

        self.__is_watching_enabled = False
        button_id = self.__button.objectName() or self.__button.text()
        self.__logger.debug(f"停止监听按钮 '{button_id}' 的启用状态变化")

    def __apply_state_style(self, enabled: bool) -> None:
        """
        根据按钮启用状态应用相应的样式。
        这是一个私有方法，仅在内部调用。

        Args:
            enabled (bool): True 表示应用启用状态样式，False 表示应用禁用状态样式。
        """
        button_id = self.__button.objectName() or self.__button.text()
        try:
            if enabled:
                # 恢复原始样式表
                self.__button.setStyleSheet(self.__original_stylesheet)
                self.__logger.debug(f"按钮 '{button_id}' 已应用原始样式表（启用状态）")
            else:
                # 应用组合样式表 (原始样式 + 禁用样式)
                combined_style = self.__original_stylesheet + self.__disabled_stylesheet
                self.__button.setStyleSheet(combined_style)
                self.__logger.debug(f"按钮 '{button_id}' 已应用禁用样式表（禁用状态）")
        except Exception as e:
            self.__logger.error(
                f"为按钮 '{button_id}' 应用{'启用' if enabled else '禁用'}样式时发生错误: {e}",
                exc_info=True,
            )

    def enableAutoStyle(self, enable: bool = True) -> None:
        """
        启用或禁用按钮状态的自动样式变化。
        启用后，按钮在禁用状态下会自动应用禁用样式，启用状态下恢复原始样式。

        Args:
            enable (bool): True 表示启用自动样式功能，False 表示禁用。默认为 True。

        示例:
            ```python
            # 创建按钮和辅助类
            button = QPushButton("测试按钮")
            button_helper = QPushButtonHelper(button)

            # 启用自动样式
            button_helper.enableAutoStyle()

            # 当按钮状态改变时，样式会自动更新
            button.setEnabled(False)  # 按钮会自动应用禁用样式
            button.setEnabled(True)   # 按钮会自动恢复原始样式
            ```
        """
        button_id = self.__button.objectName() or self.__button.text()

        # 如果状态无变化，则不操作
        if self.__auto_style_enabled == enable:
            return

        self.__auto_style_enabled = enable

        if enable:
            self.__logger.debug(f"按钮 '{button_id}' 启用了自动样式功能")
            # 开始监听按钮状态变化
            self.__start_watching_enabled_state()
        else:
            self.__logger.debug(f"按钮 '{button_id}' 禁用了自动样式功能")
            # 停止监听按钮状态变化
            self.__stop_watching_enabled_state()
            # 恢复原始样式
            self.__button.setStyleSheet(self.__original_stylesheet)

    # 保留原始方法名以保持兼容性
    enable_auto_style = enableAutoStyle


class NestedScrollAreaFixer(QObject):
    """
    修复 PyQt5 中嵌套 QScrollArea 滚动传播问题的辅助类。

    当内层的 QScrollArea 滚动到其边界（顶部/底部/左侧/右侧）时，此类会阻止滚轮事件
    继续传播到外层的 QScrollArea，从而实现更自然的滚动行为。
    此类通过在其自身内部实现事件过滤器逻辑来工作。

    它现在可以处理垂直和水平滚动。
    还提供了一个 `rescan()` 方法来应对动态添加/删除的 ScrollArea。

    使用示例:
    --------
    ```python
    # 假设您有一个包含嵌套 QScrollArea 的 UI
    main_window = QMainWindow()
    root_scroll_area = QScrollArea() # 最外层的 ScrollArea
    # ... 在 root_scroll_area 中添加内容，包括其他的 QScrollArea ...

    # 创建修复器实例
    fixer = NestedScrollAreaFixer(root_scroll_area, parent=main_window)

    # 如果 UI 动态添加了新的 ScrollArea
    # new_scroll_area = QScrollArea(...)
    # root_layout.addWidget(new_scroll_area)
    # 调用 rescan() 来查找并应用过滤器到新控件
    # fixer.rescan()

    main_window.setCentralWidget(root_scroll_area)
    main_window.show()

    # 如果需要手动清理（例如，如果未设置父对象或需要在特定时间移除）:
    # fixer.cleanup()
    ```
    """

    def __init__(self,
                 root_scroll_area: QScrollArea,
                 parent: Optional[QObject] = None):
        """
        初始化嵌套滚动区域修复器。

        Args:
            root_scroll_area (QScrollArea): 应用修复的最外层 QScrollArea 控件。
                                          修复器将从此控件开始递归查找所有嵌套的 QScrollArea。
            parent (Optional[QObject], optional): 父对象。 默认为 None。
                                          建议设置父对象（如主窗口）来自动管理修复器的生命周期。
        """
        super().__init__(parent)
        self.__root_scroll_area = root_scroll_area
        self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__installed_viewports: Dict[QObject, QScrollArea] = {}

        if not isinstance(root_scroll_area, QScrollArea):
            self.__logger.error("提供的根控件不是 QScrollArea 类型。")
            raise TypeError("root_scroll_area 必须是 QScrollArea 类型")

        self.__logger.debug(
            f"初始化修复器，根 ScrollArea: {root_scroll_area.objectName() or 'Unnamed'}"
        )
        self.__find_and_install_filters(self.__root_scroll_area)
        self.__logger.debug(
            f"共在 {len(self.__installed_viewports)} 个 viewport 上安装了事件过滤器。")

    def __find_and_install_filters(self, widget: QWidget) -> None:
        """
        递归查找 QWidget 内的所有 QScrollArea 并为其 viewport 安装事件过滤器 (self)。

        Args:
            widget (QWidget): 开始搜索的 QWidget 控件。
        """
        # 检查 widget 是否仍然有效
        if sip.isdeleted(widget):
            self.__logger.warning(f"尝试查找的控件 {widget} 已被删除，跳过。")
            return

        # 如果当前 widget 是 QScrollArea
        if isinstance(widget, QScrollArea):
            scroll_area = widget
            viewport = scroll_area.viewport()
            if viewport:
                # 检查是否已为此 viewport 安装过滤器 (self)
                if viewport not in self.__installed_viewports:
                    self.__logger.debug(
                        f"为 ScrollArea '{scroll_area.objectName() or 'Unnamed'}' 的 viewport 安装事件过滤器 (self)。"
                    )
                    viewport.installEventFilter(self)
                    self.__installed_viewports[viewport] = scroll_area
                else:
                    self.__logger.debug(
                        f"过滤器(self)已安装在 ScrollArea '{scroll_area.objectName() or 'Unnamed'}' 的 viewport 上，跳过重复安装。"
                    )

                # 继续递归查找 ScrollArea 内部的控件 (如果它有子 widget)
                scroll_area_content_widget = scroll_area.widget()
                if scroll_area_content_widget and scroll_area_content_widget != widget:
                    self.__find_and_install_filters(scroll_area_content_widget)
            else:
                self.__logger.warning(
                    f"ScrollArea '{scroll_area.objectName() or 'Unnamed'}' 没有 viewport，无法安装过滤器。"
                )

        # 递归查找子控件 (直接子项)
        for child_widget in widget.findChildren(
                QWidget, options=Qt.FindDirectChildrenOnly):
            # 简化：findChildren 已经保证是 QWidget，只需要检查是否是已处理的 viewport
            # 注意：检查 child_widget not in self.__installed_viewports 可能不足够，
            # 因为 child_widget 可能是 scroll area 的 content widget 而非 viewport 本身。
            # 但由于我们先处理 scroll area 再处理其子项，并且递归调用时检查了
            # scroll_area_content_widget != widget，应该能避免大部分问题。
            # 更严格的检查可能需要存储 content widgets，但会增加复杂性。
            if (child_widget not in self.__installed_viewports.keys()
                    ):  # 检查是否是已安装过滤器的 viewport
                self.__find_and_install_filters(child_widget)  # 递归调用

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        """
        处理已安装过滤器的 viewport 的事件，现在同时处理垂直和水平滚动。

        Args:
            watched (QObject): 接收事件的对象 (即 viewport)。
            event (QEvent): 发生的事件。

        Returns:
            bool: 如果事件已被处理且不应进一步传播，则返回 True；否则返回 False。
        """
        if not (event.type() == QEvent.Wheel
                and watched in self.__installed_viewports):
            return False  # 非滚轮事件或非目标 viewport，不处理

        scroll_area = self.__installed_viewports[watched]

        if sip.isdeleted(scroll_area) or sip.isdeleted(watched):
            self.__logger.warning("事件过滤器检测到 viewport 或关联的 ScrollArea 已被删除。")
            if watched in self.__installed_viewports:
                del self.__installed_viewports[watched]
            return False

        wheel_event: QWheelEvent = event
        delta = wheel_event.angleDelta()  # QPoint(delta_x, delta_y)
        delta_x = delta.x()
        delta_y = delta.y()

        consumed = False  # 标记事件是否被消耗

        # --- 处理垂直滚动 --- (如果 delta_y != 0)
        if delta_y != 0:
            v_bar = scroll_area.verticalScrollBar()
            if v_bar and v_bar.isVisible():
                current_val = v_bar.value()
                min_val = v_bar.minimum()
                max_val = v_bar.maximum()
                self.__logger.debug(
                    f"VScroll: delta_y={delta_y}, val={current_val}, min={min_val}, max={max_val}"
                )

                # 向上滚动 (delta_y > 0) 且已在顶部
                if delta_y > 0 and current_val == min_val:
                    self.__logger.debug("垂直: 尝试向上滚动但已在顶部。消耗事件。")
                    consumed = True
                # 向下滚动 (delta_y < 0) 且已在底部
                elif delta_y < 0 and current_val == max_val:
                    self.__logger.debug("垂直: 尝试向下滚动但已在底部。消耗事件。")
                    consumed = True
            else:
                self.__logger.debug("垂直滚动条不可用或不可见。")

        # --- 处理水平滚动 --- (如果 delta_x != 0 并且垂直滚动未消耗事件)
        # 注意：通常滚轮主要触发垂直滚动，水平滚动可能由 Shift+滚轮 或 触控板触发
        if delta_x != 0 and not consumed:
            h_bar = scroll_area.horizontalScrollBar()
            if h_bar and h_bar.isVisible():
                current_val = h_bar.value()
                min_val = h_bar.minimum()
                max_val = h_bar.maximum()
                self.__logger.debug(
                    f"HScroll: delta_x={delta_x}, val={current_val}, min={min_val}, max={max_val}"
                )

                # 向左滚动 (delta_x > 0 in some conventions, check angleDelta sign)
                # Qt 的 angleDelta().x() 通常在向右滚动时为正
                if delta_x > 0 and current_val == max_val:  # 尝试向右滚动但已在最右侧
                    self.__logger.debug("水平: 尝试向右滚动但已在最右侧。消耗事件。")
                    consumed = True
                # 向左滚动 (delta_x < 0) 且已在最左侧
                elif delta_x < 0 and current_val == min_val:
                    self.__logger.debug("水平: 尝试向左滚动但已在最左侧。消耗事件。")
                    consumed = True
            else:
                self.__logger.debug("水平滚动条不可用或不可见。")

        # 根据 consumed 标志决定是否 accept 事件
        if consumed:
            event.accept()
            return True  # 事件已被处理
        else:
            self.__logger.debug("滚动事件未达边界或方向允许，允许传播。")
            return False  # 未处理，允许传播给父级或由 ScrollArea 处理

    def cleanup(self) -> None:
        """
        卸载所有已安装的事件过滤器 (self)。
        在不再需要修复器时调用此方法（例如，在关闭窗口或销毁相关 UI 时），
        特别是如果未设置父对象来管理生命周期。
        """
        self.__logger.debug("开始卸载所有事件过滤器 (self)...")
        viewports_to_remove = list(self.__installed_viewports.keys())
        for viewport in viewports_to_remove:
            if not sip.isdeleted(viewport):
                try:
                    viewport.removeEventFilter(self)
                    self.__logger.debug(
                        f"已从 viewport '{viewport.objectName() or 'Unnamed'}' 卸载过滤器 (self)。"
                    )
                except Exception as e:
                    self.__logger.error(
                        f"从 viewport '{viewport.objectName() or 'Unnamed'}' 卸载过滤器时出错: {e}",
                        exc_info=True,
                    )
            else:
                self.__logger.debug(
                    f"尝试卸载过滤器时，viewport '{viewport.objectName() or 'Unnamed'}' 已被删除。"
                )

        self.__installed_viewports.clear()
        self.__logger.debug("所有事件过滤器已卸载。")

    def rescan(self) -> None:
        """
        重新扫描 root_scroll_area 下的所有 QScrollArea 并安装/更新事件过滤器。
        用于处理在初始扫描后动态添加或删除的 QScrollArea。
        注意：此方法会先执行清理操作。
        """
        self.__logger.debug("开始重新扫描并安装过滤器...")
        # 1. 清理当前安装的过滤器
        self.cleanup()
        # 2. 重新从根节点开始查找并安装
        if not sip.isdeleted(self.__root_scroll_area):
            self.__find_and_install_filters(self.__root_scroll_area)
            self.__logger.debug(
                f"重新扫描完成，共在 {len(self.__installed_viewports)} 个 viewport 上安装了事件过滤器。"
            )
        else:
            self.__logger.warning("根 ScrollArea 已被删除，无法执行重新扫描。")


class LabelManager(QObject):
    """
    管理一个或多个 QLabel 控件，提供便捷的交互接口和信号通知。
    现在通过手动调用 `start_fade_animation` 方法来触发标签的淡入淡出动画。
    `set_text` 方法将直接设置文本，不再自动触发动画。

    此类允许通过 QLabel 的 objectName 来精确地查找、修改和查询 QLabel 的各种属性，
    如文本、可见性、样式表和对齐方式。当相关属性发生变化时，会发出相应的信号。

    核心要求:
    - 所有被管理的 QLabel 必须设置一个唯一的 objectName。
    - 不会自动查找 QLabel，需要在初始化时显式传入。

    信号:
    - text_changed(object_name: str, new_text: str): 当标签文本通过 set_text 成功改变时发出。
    - visibility_changed(object_name: str, is_visible: bool): 当标签可见性通过 set_visible 成功改变时发出。
    - stylesheet_changed(object_name: str, new_stylesheet: str): 当标签样式表通过 set_stylesheet 成功改变时发出。
    - alignment_changed(object_name: str, new_alignment: Qt.Alignment): 当标签对齐方式通过 set_alignment 成功改变时发出。
    # 可以考虑添加动画开始/结束的信号，如果需要的话
    # - fade_animation_started(object_name: str)
    # - fade_animation_finished(object_name: str)

    使用示例:
    ---------
    ```python
    # 假设已有 label1, label2, label3 三个 QLabel 实例
    label1.setObjectName("statusLabel")
    label2.setObjectName("infoLabel")
    label3.setObjectName("errorLabel") # label3 没有传入管理器

    # 创建管理器，可以传入单个标签或列表
    manager = LabelManager(label1, [label2], parent=main_window) # 推荐设置父对象

    # 1. 直接设置文本 (无动画)
    manager.set_text("statusLabel", "直接设置的文本")

    # 2. 手动触发一次淡入淡出动画 (不改变文本内容)
    manager.start_fade_animation("statusLabel") # 使用默认时长 50ms

    # 3. 设置文本后，再手动触发动画
    manager.set_text("infoLabel", "新信息来了!")
    manager.start_fade_animation("infoLabel", duration_ms=300) # 指定时长

    # 4. 获取文本
    status = manager.get_text("statusLabel") # status = "程序已启动"
    error_text = manager.get_text("errorLabel") # error_text = None

    # 5. 设置可见性
    manager.set_visible("infoLabel", False)

    # 6. 设置样式表
    manager.set_stylesheet("statusLabel", "color: green; font-weight: bold;")

    # 7. 设置对齐方式
    manager.set_alignment("statusLabel", Qt.AlignCenter)

    # 8. 清空文本
    manager.clear_text("infoLabel") # infoLabel 的文本变为空字符串
    manager.clear_all_texts() # statusLabel 的文本也变为空字符串

    # 9. 获取所有管理的标签名称
    names = manager.get_all_label_names() # names = ["statusLabel", "infoLabel"] (顺序可能不同)

    # 10. 连接信号
    def on_text_changed(name, text):
        print(f"标签 '{name}' 的文本变为: {text}")
    manager.text_changed.connect(on_text_changed)

    manager.set_text("statusLabel", "正在处理...") # 会触发 on_text_changed

    # 11. 获取 QLabel 对象本身 (如果需要直接操作)
    label_obj = manager.get_label("statusLabel")
    if label_obj:
        # 可以直接调用 QLabel 的其他方法
        label_obj.setToolTip("显示当前状态")

    # 设置文本时将有淡入淡出效果
    manager.set_text("statusLabel", "新的状态信息")

    ```
    """
    # 定义信号
    text_changed = pyqtSignal(str, str)
    visibility_changed = pyqtSignal(str, bool)
    stylesheet_changed = pyqtSignal(str, str)
    alignment_changed = pyqtSignal(str, Qt.Alignment)

    # 定义动画参数 (可配置)
    FADE_DURATION_MS = 50  # 默认手动动画时长
    FADE_EASING_CURVE = QEasingCurve.InOutQuad

    def __init__(self, *labels: Union[QLabel, List[QLabel]], parent: Optional[QObject] = None):
        """
        初始化 LabelManager。

        Args:
            *labels: 一个或多个 QLabel 实例，或者包含 QLabel 实例的列表。
                     所有传入的 QLabel 都必须设置了唯一的 objectName。
            parent (Optional[QObject], optional): 父对象。 默认为 None。
        """
        super().__init__(parent)
        self.__labels: Dict[str, QLabel] = {}
        self.__animation_data: Dict[str, Tuple[QGraphicsOpacityEffect, QPropertyAnimation, QPropertyAnimation]] = {}
        self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__logger.debug("初始化 LabelManager...")

        # 处理传入的标签
        for item in labels:
            if isinstance(item, QLabel):
                self.__add_label(item)
            elif isinstance(item, list):
                for label_in_list in item:
                    if isinstance(label_in_list, QLabel):
                        self.__add_label(label_in_list)
                    else:
                        self.__logger.warning(f"列表中的项目不是 QLabel 类型，已跳过: {type(label_in_list)}")
            else:
                self.__logger.warning(f"传入的参数不是 QLabel 或 QLabel 列表，已跳过: {type(item)}")

        self.__logger.debug(f"LabelManager 初始化完成，共管理 {len(self.__labels)} 个标签: {list(self.__labels.keys())}")

    def __add_label(self, label: QLabel) -> None:
        """ (私有) 验证并添加单个 QLabel 到管理器中。"""
        if sip.isdeleted(label):
            self.__logger.warning(f"尝试添加一个已被删除的 QLabel，已跳过。")
            return

        object_name = label.objectName()

        if not object_name:
            self.__logger.error(f"尝试添加一个未设置 objectName 的 QLabel，已跳过。请为所有管理的 QLabel 设置唯一的 objectName。 Label: {label}")
            return

        if object_name in self.__labels:
            self.__logger.warning(f"尝试添加具有重复 objectName ('{object_name}') 的 QLabel，已跳过。确保 objectName 唯一。")
            return

        self.__labels[object_name] = label
        self.__logger.debug(f"成功添加 QLabel '{object_name}' 到管理器。")

    def get_label(self, name: str) -> Optional[QLabel]:
        """
        通过 objectName 获取 QLabel 实例。
        如果发现标签已被删除，会清理相关的动画数据。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            Optional[QLabel]: 找到的 QLabel 实例，如果未找到或已被删除则返回 None。
        """
        self.__logger.debug(f"尝试获取名称为 '{name}' 的 QLabel 实例。")
        label = self.__labels.get(name)
        if label is None:
            self.__logger.warning(f"未在管理器中找到名称为 '{name}' 的 QLabel。")
            if name in self.__animation_data:
                self.__cleanup_animation_data(name)
            return None
        if sip.isdeleted(label):
            self.__logger.warning(f"名称为 '{name}' 的 QLabel已被删除，将从管理器中移除。")
            del self.__labels[name]
            if name in self.__animation_data:
                self.__cleanup_animation_data(name)
            return None
        return label

    def __cleanup_animation_data(self, name: str) -> None:
        """ (私有) 清理指定名称标签的动画效果和动画对象。"""
        if name in self.__animation_data:
            self.__logger.debug(f"清理标签 '{name}' 的动画数据。")
            effect, fade_out_anim, fade_in_anim = self.__animation_data[name]
            if fade_out_anim.state() == QPropertyAnimation.Running:
                fade_out_anim.stop()
            if fade_in_anim.state() == QPropertyAnimation.Running:
                fade_in_anim.stop()
            del self.__animation_data[name]

    def set_text(self, name: str, text: str) -> bool:
        """
        通过 objectName 直接设置 QLabel 的文本 (无动画)。

        Args:
            name (str): QLabel 的 objectName。
            text (str): 要设置的新文本。

        Returns:
            bool: 如果成功找到标签并设置文本（或文本无需更改），则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试直接设置标签 '{name}' 的文本为: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        label = self.get_label(name)
        if label:
            original_text = label.text()
            if original_text != text:
                try:
                    label.setText(text)
                    self.__logger.debug(f"成功直接设置标签 '{name}' 的文本。")
                    self.text_changed.emit(name, text)  # 发射信号
                    return True
                except Exception as e:
                    self.__logger.error(f"直接设置标签 '{name}' 文本时发生错误: {e}", exc_info=True)
                    return False
            else:
                self.__logger.debug(f"标签 '{name}' 的文本已经是目标文本，无需更改。")
                return True  # 视为成功
        else:
            return False  # 标签未找到

    def __ensure_label_visible(self, label: QLabel) -> None:
        """ (私有) 确保标签的效果透明度为 1.0。"""
        name = label.objectName()
        if name in self.__animation_data:
            effect, _, _ = self.__animation_data[name]
            if not sip.isdeleted(effect) and effect.opacity() != 1.0:
                _, fade_out_anim, fade_in_anim = self.__animation_data[name]
                if fade_out_anim.state() == QPropertyAnimation.Running:
                    fade_out_anim.stop()
                if fade_in_anim.state() == QPropertyAnimation.Running:
                    fade_in_anim.stop()
                effect.setOpacity(1.0)
                self.__logger.debug(f"确保标签 '{name}' 透明度重置为 1.0")
        elif label.graphicsEffect() is not None and isinstance(label.graphicsEffect(), QGraphicsOpacityEffect):
            try:
                label.graphicsEffect().setOpacity(1.0)
            except Exception as e:
                self.__logger.warning(f"尝试重置外部图形效果透明度时出错 for label '{name}': {e}")

    def start_fade_animation(self, name: str, duration_ms: Optional[int] = None) -> bool:
        """
        为指定的 QLabel 手动启动一次淡出再淡入的动画。
        此动画仅改变透明度，不改变标签的实际文本内容。

        Args:
            name (str): QLabel 的 objectName。
            duration_ms (Optional[int], optional): 动画单程时长（毫秒）。
                                                 如果为 None，则使用类定义的 FADE_DURATION_MS (默认50ms)。
                                                 如果为 0 或负数，将直接确保标签可见（透明度为1），不执行动画。

        Returns:
            bool: 如果成功找到标签并启动动画（或因duration<=0而跳过动画），则返回 True，否则返回 False。
        """
        if duration_ms is None:
            duration = self.FADE_DURATION_MS
        else:
            duration = int(duration_ms)

        self.__logger.debug(f"请求为标签 '{name}' 启动手动淡入淡出动画，时长: {duration}ms")
        label = self.get_label(name)
        if not label:
            return False  # 标签未找到

        # 如果时长无效，则仅确保可见，不执行动画
        if duration <= 0:
            self.__logger.debug(f"动画时长 ({duration}ms) 无效或为0，跳过动画，仅确保标签 '{name}' 可见。")
            self.__ensure_label_visible(label)
            return True

        try:
            # 获取或创建动画组件
            effect, fade_out_anim, fade_in_anim = self.__get_or_create_animation_data(label)

            # 停止当前动画
            if fade_out_anim.state() == QPropertyAnimation.Running:
                fade_out_anim.stop()
            if fade_in_anim.state() == QPropertyAnimation.Running:
                fade_in_anim.stop()

            # 设置动画时长
            fade_out_anim.setDuration(duration)
            fade_in_anim.setDuration(duration)

            # 设置淡出动画的起始值（从当前透明度开始）
            current_opacity = effect.opacity()
            fade_out_anim.setStartValue(current_opacity)
            # 结束值固定为 0.0 (在 __get_or_create 中设置或已设置)
            # fade_out_anim.setEndValue(0.0)
            self.__logger.debug(f"'{name}' 手动淡出: start_opacity={current_opacity}, duration={duration}")

            # --- 连接信号 (仅用于启动淡入) --- #
            try:
                fade_out_anim.finished.disconnect()  # 断开旧连接
            except TypeError:
                pass
            fade_out_anim.finished.connect(partial(self.__on_manual_fade_out_finished, name))

            # --- 启动淡出 --- #
            fade_out_anim.start()
            self.__logger.debug(f"'{name}' 手动淡出动画已启动。")
            return True

        except Exception as e:
            self.__logger.error(f"为标签 '{name}' 启动手动动画时出错: {e}", exc_info=True)
            return False

    def __get_or_create_animation_data(self, label: QLabel) -> Tuple[QGraphicsOpacityEffect, QPropertyAnimation, QPropertyAnimation]:
        """ (私有) 获取或创建标签的动画效果和动画对象。 """
        name = label.objectName()
        if name in self.__animation_data:
            effect, fade_out_anim, fade_in_anim = self.__animation_data[name]
            # 健壮性检查: 效果是否还在标签上，对象是否已被删除？
            if not sip.isdeleted(effect) and not sip.isdeleted(fade_out_anim) and not sip.isdeleted(fade_in_anim) and label.graphicsEffect() == effect:
                self.__logger.debug(f"复用 '{name}' 的现有动画数据。")
                return effect, fade_out_anim, fade_in_anim
            else:
                self.__logger.warning(f"标签 '{name}' 的动画数据无效或丢失，将重新创建。")
                self.__cleanup_animation_data(name)  # 清理可能残留的部分数据

        # 创建新的动画数据
        self.__logger.debug(f"为 '{name}' 创建新的动画效果和对象。")
        effect = QGraphicsOpacityEffect(label)
        label.setGraphicsEffect(effect)

        # 淡出动画 (结束值设为0)
        fade_out_anim = QPropertyAnimation(effect, b"opacity", label)
        fade_out_anim.setEndValue(0.0)
        fade_out_anim.setEasingCurve(self.FADE_EASING_CURVE)
        # duration 在 start_fade_animation 中设置

        # 淡入动画 (始终从0到1)
        fade_in_anim = QPropertyAnimation(effect, b"opacity", label)
        fade_in_anim.setStartValue(0.0)
        fade_in_anim.setEndValue(1.0)
        fade_in_anim.setEasingCurve(self.FADE_EASING_CURVE)
        # duration 在 start_fade_animation 中设置

        self.__animation_data[name] = (effect, fade_out_anim, fade_in_anim)
        return effect, fade_out_anim, fade_in_anim

    def __on_manual_fade_out_finished(self, name: str) -> None:
        """ (私有) 手动淡出动画完成后的处理：启动淡入。"""
        self.__logger.debug(f"'{name}' 手动淡出动画完成，准备启动淡入。")
        if name in self.__animation_data:
            effect, fade_out_anim, fade_in_anim = self.__animation_data[name]

            # 断开 finished 连接
            try:
                fade_out_anim.finished.disconnect()  # 断开 fade_out 的信号
            except TypeError:
                pass

            # 确保效果和动画对象有效
            if not sip.isdeleted(effect) and not sip.isdeleted(fade_in_anim):
                # 启动淡入动画
                fade_in_anim.start()
                self.__logger.debug(f"'{name}' 手动淡入动画已启动。")
            else:
                self.__logger.warning(f"'{name}' 的效果或淡入动画对象在尝试启动时已被删除。")
        else:
            self.__logger.warning(f"无法找到标签 '{name}' 的动画数据，无法启动淡入动画。")

    def get_text(self, name: str) -> Optional[str]:
        """
        通过 objectName 获取 QLabel 的文本。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            Optional[str]: 标签的当前文本，如果找不到标签则返回 None。
        """
        self.__logger.debug(f"尝试获取标签 '{name}' 的文本。")
        label = self.get_label(name)
        if label:
            return label.text()
        else:
            return None

    def set_visible(self, name: str, visible: bool) -> bool:
        """
        通过 objectName 设置 QLabel 的可见性。

        Args:
            name (str): QLabel 的 objectName。
            visible (bool): True 表示可见，False 表示隐藏。

        Returns:
            bool: 如果成功找到标签并设置可见性，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置标签 '{name}' 的可见性为: {visible}")
        label = self.get_label(name)
        if label:
            try:
                original_visibility = label.isVisible()
                if original_visibility != visible:
                    label.setVisible(visible)
                    self.__logger.debug(f"成功设置标签 '{name}' 的可见性。")
                    self.visibility_changed.emit(name, visible)  # 发射信号
                    if visible:
                        self.__ensure_label_visible(label)
                    return True
                else:
                    self.__logger.debug(f"标签 '{name}' 的可见性已经是 {visible}，无需更改。")
                    if visible:
                        self.__ensure_label_visible(label)
                    return True
            except Exception as e:
                self.__logger.error(f"设置标签 '{name}' 可见性时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def is_visible(self, name: str) -> Optional[bool]:
        """
        通过 objectName 获取 QLabel 的可见性状态。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            Optional[bool]: 如果标签可见返回 True，隐藏返回 False，找不到标签返回 None。
        """
        self.__logger.debug(f"尝试获取标签 '{name}' 的可见性。")
        label = self.get_label(name)
        if label:
            return label.isVisible()
        else:
            return None

    def set_stylesheet(self, name: str, stylesheet: str) -> bool:
        """
        通过 objectName 设置 QLabel 的样式表。

        Args:
            name (str): QLabel 的 objectName。
            stylesheet (str): 要设置的样式表字符串。

        Returns:
            bool: 如果成功找到标签并设置样式表，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置标签 '{name}' 的样式表为: '{stylesheet[:100]}{'...' if len(stylesheet) > 100 else ''}'")
        label = self.get_label(name)
        if label:
            try:
                original_stylesheet = label.styleSheet()
                if original_stylesheet != stylesheet:
                    label.setStyleSheet(stylesheet)
                    self.__logger.debug(f"成功设置标签 '{name}' 的样式表。")
                    self.stylesheet_changed.emit(name, stylesheet)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"标签 '{name}' 的样式表已是目标样式，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置标签 '{name}' 样式表时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def get_stylesheet(self, name: str) -> Optional[str]:
        """
        通过 objectName 获取 QLabel 的样式表。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            Optional[str]: 标签的当前样式表字符串，如果找不到标签则返回 None。
        """
        self.__logger.debug(f"尝试获取标签 '{name}' 的样式表。")
        label = self.get_label(name)
        if label:
            return label.styleSheet()
        else:
            return None

    def set_alignment(self, name: str, alignment: Qt.Alignment) -> bool:
        """
        通过 objectName 设置 QLabel 的文本对齐方式。

        Args:
            name (str): QLabel 的 objectName。
            alignment (Qt.Alignment): 要设置的对齐方式 (例如 Qt.AlignCenter)。

        Returns:
            bool: 如果成功找到标签并设置对齐方式，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置标签 '{name}' 的对齐方式为: {alignment}")
        label = self.get_label(name)
        if label:
            try:
                original_alignment = label.alignment()
                if original_alignment != alignment:
                    label.setAlignment(alignment)
                    self.__logger.debug(f"成功设置标签 '{name}' 的对齐方式。")
                    self.alignment_changed.emit(name, alignment)
                    return True
                else:
                    self.__logger.debug(f"标签 '{name}' 的对齐方式已是 {alignment}，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置标签 '{name}' 对齐方式时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def get_alignment(self, name: str) -> Optional[Qt.Alignment]:
        """
        通过 objectName 获取 QLabel 的文本对齐方式。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            Optional[Qt.Alignment]: 标签的当前对齐方式，如果找不到标签则返回 None。
        """
        self.__logger.debug(f"尝试获取标签 '{name}' 的对齐方式。")
        label = self.get_label(name)
        if label:
            return label.alignment()
        else:
            return None

    def clear_text(self, name: str) -> bool:
        """
        通过 objectName 清空 QLabel 的文本（设置为空字符串）。
        此操作无动画效果。

        Args:
            name (str): QLabel 的 objectName。

        Returns:
            bool: 操作是否成功 (取决于 set_text 的返回值)。
        """
        self.__logger.debug(f"尝试直接清空标签 '{name}' 的文本。")
        # 直接调用修改后的 set_text (无动画)
        return self.set_text(name, "")

    def clear_all_texts(self) -> None:
        """ 清空管理器中所有 QLabel 的文本（无动画）。"""
        self.__logger.debug("尝试直接清空所有管理标签的文本。")
        names = list(self.__labels.keys())
        count = 0
        for name in names:
            if self.clear_text(name):
                count += 1
        self.__logger.debug(f"直接清空文本操作完成，成功清空 {count} 个标签。")

    def get_all_label_names(self) -> List[str]:
        """
        获取当前管理器中所有 QLabel 的 objectName 列表。

        Returns:
            List[str]: 包含所有 objectName 的列表。
        """
        self.__logger.debug("获取所有管理的标签名称列表。")
        valid_names = [name for name in self.__labels if not sip.isdeleted(self.__labels[name])]
        active_anim_names = set(self.__animation_data.keys())
        valid_label_names = set(valid_names)
        names_to_cleanup = active_anim_names - valid_label_names
        for name in names_to_cleanup:
            self.__logger.warning(f"发现标签 '{name}' 已不在管理器中，但仍有动画数据，进行清理。")
            self.__cleanup_animation_data(name)
        return valid_names

    def cleanup(self) -> None:
        """
        清理 LabelManager，停止所有动画并清除内部引用。
        建议在父对象销毁前调用，或者依赖父子关系自动清理。
        """
        self.__logger.debug("开始清理 LabelManager...")
        names_to_cleanup = list(self.__animation_data.keys())
        for name in names_to_cleanup:
            self.__cleanup_animation_data(name)
        self.__animation_data.clear()

        self.__labels.clear()
        self.__logger.debug("LabelManager 清理完成。")


class ProgressBarManager(QObject):
    """
    管理一个或多个 QProgressBar 控件，提供便捷的交互接口、动画效果和信号通知。

    此类允许通过 QProgressBar 的 objectName 来精确地查找、修改和查询 QProgressBar 的各种属性，
    如值、范围、格式等，并能在值改变时应用平滑的动画效果。

    核心要求:
    - 所有被管理的 QProgressBar 必须设置一个唯一的 objectName。
    - 不会自动查找 QProgressBar，需要在初始化时显式传入。

    信号:
    - value_changed(object_name: str, new_value: int): 当进度条值通过 set_value 成功改变时发出。
    - range_changed(object_name: str, min_value: int, max_value: int): 当进度条范围通过 set_range 成功改变时发出。
    - format_changed(object_name: str, new_format: str): 当进度条格式通过 set_format 成功改变时发出。
    - text_visibility_changed(object_name: str, is_visible: bool): 当文本可见性通过 set_text_visible 成功改变时发出。
    - orientation_changed(object_name: str, orientation: Qt.Orientation): 当方向通过 set_orientation 成功改变时发出。
    - animation_started(object_name: str): 当值动画开始时发出。
    - animation_finished(object_name: str): 当值动画完成时发出。

    使用示例:
    ---------
    ```python
    # 假设已有 progressBar1, progressBar2 两个 QProgressBar 实例
    progressBar1.setObjectName("taskProgress")
    progressBar2.setObjectName("overallProgress")

    # 创建管理器
    manager = ProgressBarManager(progressBar1, [progressBar2], parent=main_window) # 推荐设置父对象

    # 1. 设置值 (默认带动画)
    manager.set_value("taskProgress", 50)

    # 2. 设置值 (不带动画)
    manager.set_value("overallProgress", 20, animate=False)

    # 3. 设置值 (自定义动画)
    manager.set_value("taskProgress", 75, duration_ms=1000, easing_curve=QEasingCurve.OutBounce)

    # 4. 获取值
    current_task_value = manager.get_value("taskProgress") # current_task_value = 75 (动画结束后)

    # 5. 设置范围
    manager.set_range("overallProgress", 0, 200)

    # 6. 设置格式
    manager.set_format("taskProgress", "当前任务: %p%")

    # 7. 设置文本可见性
    manager.set_text_visible("overallProgress", False)

    # 8. 重置进度
    manager.reset("taskProgress") # taskProgress 的值变为其最小值

    # 9. 获取所有管理的进度条名称
    names = manager.get_all_progress_bar_names() # names = ["taskProgress", "overallProgress"]

    # 10. 连接信号
    def on_value_changed(name, value):
        print(f"进度条 '{name}' 的值变为: {value}")
    manager.value_changed.connect(on_value_changed)

    def on_animation_finished(name):
        print(f"进度条 '{name}' 的动画完成。")
    manager.animation_finished.connect(on_animation_finished)

    manager.set_value("overallProgress", 100) # 会触发 on_value_changed 和 on_animation_finished

    # 11. 获取 QProgressBar 对象本身 (如果需要直接操作)
    pbar_obj = manager.get_progress_bar("taskProgress")
    if pbar_obj:
        # 可以直接调用 QProgressBar 的其他方法
        pbar_obj.setInvertedAppearance(True)

    # 12. 清理 (在不再需要管理器时)
    # manager.cleanup()
    ```
    """
    # --- 信号定义 ---
    value_changed = pyqtSignal(str, int)
    range_changed = pyqtSignal(str, int, int)
    format_changed = pyqtSignal(str, str)
    text_visibility_changed = pyqtSignal(str, bool)
    orientation_changed = pyqtSignal(str, Qt.Orientation)
    animation_started = pyqtSignal(str)
    animation_finished = pyqtSignal(str)

    # --- 默认动画参数 ---
    __DEFAULT_ANIMATION_DURATION = 300  # 默认动画时长 (毫秒)
    __DEFAULT_EASING_CURVE = QEasingCurve.InOutQuad  # 默认缓动曲线

    def __init__(self, *progress_bars: Union[QProgressBar, List[QProgressBar]], parent: Optional[QObject] = None):
        """
        初始化 ProgressBarManager。

        Args:
            *progress_bars: 一个或多个 QProgressBar 实例，或者包含 QProgressBar 实例的列表。
                             所有传入的 QProgressBar 都必须设置了唯一的 objectName。
            parent (Optional[QObject], optional): 父对象。 默认为 None。
        """
        super().__init__(parent)
        self.__progress_bars: Dict[str, QProgressBar] = {}
        self.__animations: Dict[str, QPropertyAnimation] = {}
        self.__logger = logging.getLogger(f"{self.__class__.__name__}")
        self.__logger.debug("初始化 ProgressBarManager...")

        # 处理传入的进度条
        for item in progress_bars:
            if isinstance(item, QProgressBar):
                self.__add_progress_bar(item)
            elif isinstance(item, list):
                for pbar_in_list in item:
                    if isinstance(pbar_in_list, QProgressBar):
                        self.__add_progress_bar(pbar_in_list)
                    else:
                        self.__logger.warning(f"列表中的项目不是 QProgressBar 类型，已跳过: {type(pbar_in_list)}")
            else:
                self.__logger.warning(f"传入的参数不是 QProgressBar 或 QProgressBar 列表，已跳过: {type(item)}")

        self.__logger.debug(f"ProgressBarManager 初始化完成，共管理 {len(self.__progress_bars)} 个进度条: {list(self.__progress_bars.keys())}")

    def __add_progress_bar(self, pbar: QProgressBar) -> None:
        """ (私有) 验证并添加单个 QProgressBar 到管理器中。"""
        if sip.isdeleted(pbar):
            self.__logger.warning(f"尝试添加一个已被删除的 QProgressBar，已跳过。")
            return

        object_name = pbar.objectName()

        if not object_name:
            self.__logger.error(f"尝试添加一个未设置 objectName 的 QProgressBar，已跳过。请为所有管理的 QProgressBar 设置唯一的 objectName。 ProgressBar: {pbar}")
            return

        if object_name in self.__progress_bars:
            self.__logger.warning(f"尝试添加具有重复 objectName ('{object_name}') 的 QProgressBar，已跳过。确保 objectName 唯一。")
            return

        self.__progress_bars[object_name] = pbar
        self.__logger.debug(f"成功添加 QProgressBar '{object_name}' 到管理器。")

    def __get_progress_bar(self, name: str) -> Optional[QProgressBar]:
        """
        (私有) 通过 objectName 获取 QProgressBar 实例。
        如果发现进度条已被删除，会清理相关的动画数据并将其从管理器中移除。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[QProgressBar]: 找到的 QProgressBar 实例，如果未找到或已被删除则返回 None。
        """
        self.__logger.debug(f"尝试获取名称为 '{name}' 的 QProgressBar 实例。")
        pbar = self.__progress_bars.get(name)
        if pbar is None:
            self.__logger.warning(f"未在管理器中找到名称为 '{name}' 的 QProgressBar。")
            # 确保如果进度条不在字典中，也没有对应的动画残留
            if name in self.__animations:
                self.__stop_animation(name)  # 会处理删除
            return None
        if sip.isdeleted(pbar):
            self.__logger.warning(f"名称为 '{name}' 的 QProgressBar 已被删除，将从管理器中移除。")
            del self.__progress_bars[name]
            # 确保清理动画
            if name in self.__animations:
                self.__stop_animation(name)  # 会处理删除
            return None
        return pbar

    def __stop_animation(self, name: str) -> None:
        """ (私有) 停止并清理指定名称进度条的当前动画。"""
        if name in self.__animations:
            animation = self.__animations[name]
            if not sip.isdeleted(animation):
                if animation.state() == QPropertyAnimation.Running:
                    self.__logger.debug(f"停止进度条 '{name}' 的当前动画。")
                    animation.stop()
                # 安全地断开连接并删除动画对象
                try:
                    animation.finished.disconnect()
                except TypeError:
                    pass  # 没有连接，忽略
                # 不需要手动删除，Qt的父子关系或Python垃圾回收会处理
                # del animation # 或者 animation.deleteLater() 如果设置了父对象
            # 从字典中移除引用
            del self.__animations[name]
            self.__logger.debug(f"已清理进度条 '{name}' 的动画引用。")

    # --- 公共接口方法 ---

    def get_progress_bar(self, name: str) -> Optional[QProgressBar]:
        """
        通过 objectName 获取 QProgressBar 实例。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[QProgressBar]: 找到的 QProgressBar 实例，如果未找到或已被删除则返回 None。
        """
        return self.__get_progress_bar(name)

    def get_all_progress_bar_names(self) -> List[str]:
        """
        获取当前管理器中所有 QProgressBar 的 objectName 列表。
        会过滤掉已被删除的进度条。

        Returns:
            List[str]: 包含所有有效 objectName 的列表。
        """
        self.__logger.debug("获取所有管理的进度条名称列表。")
        valid_names = []
        # 使用 list(self.__progress_bars.keys()) 复制键列表以允许在迭代期间删除
        for name in list(self.__progress_bars.keys()):
            pbar = self.__progress_bars.get(name)
            if pbar and not sip.isdeleted(pbar):
                valid_names.append(name)
            elif pbar:  # 存在但已被删除
                self.__logger.warning(f"发现受管进度条 '{name}' 已被删除，将从管理器移除。")
                del self.__progress_bars[name]
                self.__stop_animation(name)  # 清理动画
        return valid_names

    def set_value(self, name: str, value: int, animate: bool = True,
                  duration_ms: Optional[int] = None,
                  easing_curve: Optional[QEasingCurve] = None) -> bool:
        """
        设置指定 QProgressBar 的值，可选动画效果。

        Args:
            name (str): QProgressBar 的 objectName。
            value (int): 要设置的新值。
            animate (bool, optional): 是否使用动画。默认为 True。
            duration_ms (Optional[int], optional): 动画时长（毫秒）。
                                                 如果为 None 或 animate=False，则使用默认值或不使用。
                                                 默认为 None (使用 __DEFAULT_ANIMATION_DURATION)。
            easing_curve (Optional[QEasingCurve], optional): 动画缓动曲线。
                                                          如果为 None 或 animate=False，则使用默认值或不使用。
                                                          默认为 None (使用 __DEFAULT_EASING_CURVE)。

        Returns:
            bool: 如果成功找到进度条并设置值（或启动动画），则返回 True，否则返回 False。
        """
        self.__logger.debug(f"请求设置进度条 '{name}' 的值为 {value} (动画: {animate})")
        pbar = self.__get_progress_bar(name)
        if not pbar:
            return False

        # 验证值范围
        min_val = pbar.minimum()
        max_val = pbar.maximum()
        clamped_value = max(min_val, min(value, max_val))
        if clamped_value != value:
            self.__logger.warning(f"请求的值 {value} 超出范围 [{min_val}, {max_val}]，已修正为 {clamped_value}。")
            value = clamped_value

        original_value = pbar.value()

        # 总是先停止旧动画
        self.__stop_animation(name)

        try:
            if animate:
                # 如果值没有变化，则不执行动画
                if original_value == value:
                    self.__logger.debug(f"进度条 '{name}' 的值已经是 {value}，跳过动画。")
                    # 确保信号被触发（如果之前的值不同但现在相同） - 或者不触发？决定不触发
                    # self.value_changed.emit(name, value)
                    return True  # 视为成功，因为值已经是目标值

                # --- 设置动画 ---
                anim = QPropertyAnimation(pbar, b"value", self)  # 使用 self 作为父对象方便管理
                current_duration = duration_ms if duration_ms is not None else self.__DEFAULT_ANIMATION_DURATION
                current_curve = easing_curve if easing_curve is not None else self.__DEFAULT_EASING_CURVE

                anim.setDuration(current_duration)
                anim.setEasingCurve(current_curve)
                anim.setStartValue(original_value)  # 从当前实际值开始
                anim.setEndValue(value)

                # --- 连接信号 ---
                # 使用 lambda 捕获 name
                anim.finished.connect(lambda: self.__on_animation_finished(name))

                # --- 存储和启动 ---
                self.__animations[name] = anim
                anim.start(QPropertyAnimation.DeletionPolicy.DeleteWhenStopped)  # 动画结束后自动删除
                self.__logger.debug(f"进度条 '{name}' 的值动画已启动: {original_value} -> {value}, 时长: {current_duration}ms, 曲线: {current_curve.name}")
                self.animation_started.emit(name)  # 发射动画开始信号
                # 注意：value_changed 信号将在动画过程中由 Qt 内部的属性变化触发，或者我们可以在这里或动画结束时手动触发
                # 为了保持一致性，我们在值确实改变时就触发，即使是通过动画
                if original_value != value:
                    self.value_changed.emit(name, value)  # 触发值改变信号（目标值）

            else:
                # --- 直接设置值 ---
                if original_value != value:
                    pbar.setValue(value)
                    self.__logger.debug(f"已直接设置进度条 '{name}' 的值为 {value}。")
                    self.value_changed.emit(name, value)  # 发射信号
                else:
                    self.__logger.debug(f"进度条 '{name}' 的值已经是 {value}，无需直接设置。")
                    # self.value_changed.emit(name, value) # 同样，值未变不触发

            return True

        except Exception as e:
            self.__logger.error(f"设置进度条 '{name}' 值时发生错误: {e}", exc_info=True)
            return False

    def get_value(self, name: str) -> Optional[int]:
        """
        获取指定 QProgressBar 的当前值。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[int]: 当前值，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的当前值。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.value()
        else:
            return None

    def set_range(self, name: str, minimum: int, maximum: int) -> bool:
        """
        设置指定 QProgressBar 的范围（最小值和最大值）。

        Args:
            name (str): QProgressBar 的 objectName。
            minimum (int): 新的最小值。
            maximum (int): 新的最大值。

        Returns:
            bool: 如果成功找到进度条并设置范围，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的范围为 [{minimum}, {maximum}]。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_min = pbar.minimum()
                original_max = pbar.maximum()
                if original_min != minimum or original_max != maximum:
                    pbar.setRange(minimum, maximum)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的范围。")
                    self.range_changed.emit(name, minimum, maximum)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的范围已经是 [{minimum}, {maximum}]，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 范围时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def get_range(self, name: str) -> Optional[Tuple[int, int]]:
        """
        获取指定 QProgressBar 的范围（最小值和最大值）。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[Tuple[int, int]]: 包含 (最小值, 最大值) 的元组，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的范围。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return (pbar.minimum(), pbar.maximum())
        else:
            return None

    def get_minimum(self, name: str) -> Optional[int]:
        """ 获取最小值 """
        pbar = self.__get_progress_bar(name)
        return pbar.minimum() if pbar else None

    def get_maximum(self, name: str) -> Optional[int]:
        """ 获取最大值 """
        pbar = self.__get_progress_bar(name)
        return pbar.maximum() if pbar else None

    def set_format(self, name: str, format_str: str) -> bool:
        """
        设置指定 QProgressBar 的显示格式字符串。

        Args:
            name (str): QProgressBar 的 objectName。
            format_str (str): 新的格式字符串 (例如 "%p%", "%v/%m")。

        Returns:
            bool: 如果成功找到进度条并设置格式，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的格式为: '{format_str}'")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_format = pbar.format()
                if original_format != format_str:
                    pbar.setFormat(format_str)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的格式。")
                    self.format_changed.emit(name, format_str)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的格式已经是 '{format_str}'，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 格式时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def get_format(self, name: str) -> Optional[str]:
        """
        获取指定 QProgressBar 的当前显示格式字符串。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[str]: 当前格式字符串，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的格式。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.format()
        else:
            return None

    def set_text_visible(self, name: str, visible: bool) -> bool:
        """
        设置指定 QProgressBar 的文本是否可见。

        Args:
            name (str): QProgressBar 的 objectName。
            visible (bool): True 表示可见，False 表示隐藏。

        Returns:
            bool: 如果成功找到进度条并设置可见性，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的文本可见性为: {visible}")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_visible = pbar.isTextVisible()
                if original_visible != visible:
                    pbar.setTextVisible(visible)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的文本可见性。")
                    self.text_visibility_changed.emit(name, visible)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的文本可见性已经是 {visible}，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 文本可见性时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def is_text_visible(self, name: str) -> Optional[bool]:
        """
        检查指定 QProgressBar 的文本是否可见。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[bool]: 如果文本可见返回 True，否则返回 False。如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的文本可见性。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.isTextVisible()
        else:
            return None

    def set_orientation(self, name: str, orientation: Qt.Orientation) -> bool:
        """
        设置指定 QProgressBar 的方向。

        Args:
            name (str): QProgressBar 的 objectName。
            orientation (Qt.Orientation): 新的方向 (Qt.Horizontal 或 Qt.Vertical)。

        Returns:
            bool: 如果成功找到进度条并设置方向，则返回 True，否则返回 False。
        """
        self.__logger.debug(f"尝试设置进度条 '{name}' 的方向为: {orientation}")
        pbar = self.__get_progress_bar(name)
        if pbar:
            try:
                original_orientation = pbar.orientation()
                if original_orientation != orientation:
                    pbar.setOrientation(orientation)
                    self.__logger.debug(f"成功设置进度条 '{name}' 的方向。")
                    self.orientation_changed.emit(name, orientation)  # 发射信号
                    return True
                else:
                    self.__logger.debug(f"进度条 '{name}' 的方向已经是 {orientation}，无需更改。")
                    return True
            except Exception as e:
                self.__logger.error(f"设置进度条 '{name}' 方向时发生错误: {e}", exc_info=True)
                return False
        else:
            return False

    def get_orientation(self, name: str) -> Optional[Qt.Orientation]:
        """
        获取指定 QProgressBar 的当前方向。

        Args:
            name (str): QProgressBar 的 objectName。

        Returns:
            Optional[Qt.Orientation]: 当前方向，如果找不到进度条则返回 None。
        """
        self.__logger.debug(f"尝试获取进度条 '{name}' 的方向。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            return pbar.orientation()
        else:
            return None

    def reset(self, name: str, animate: bool = False, **kwargs) -> bool:
        """
        将指定 QProgressBar 的值重置为其最小值。

        Args:
            name (str): QProgressBar 的 objectName。
            animate (bool, optional): 是否使用动画重置。默认为 False。
            **kwargs: 传递给 set_value 的其他动画参数 (duration_ms, easing_curve)。

        Returns:
            bool: 操作是否成功 (取决于 set_value 的返回值)。
        """
        self.__logger.debug(f"尝试重置进度条 '{name}' 的值 (动画: {animate})。")
        pbar = self.__get_progress_bar(name)
        if pbar:
            min_val = pbar.minimum()
            return self.set_value(name, min_val, animate=animate, **kwargs)
        else:
            return False

    def stop_animation(self, name: str) -> None:
        """
        停止指定 QProgressBar 上当前正在进行的动画。

        Args:
            name (str): QProgressBar 的 objectName。
        """
        self.__logger.debug(f"请求停止进度条 '{name}' 的动画。")
        self.__stop_animation(name)

    def cleanup(self) -> None:
        """
        清理 ProgressBarManager，停止所有动画并清除内部引用。
        建议在父对象销毁前调用，或者依赖父子关系自动清理。
        """
        self.__logger.debug("开始清理 ProgressBarManager...")
        # 停止所有动画
        # 使用 list 复制键，因为 __stop_animation 会修改字典
        names_to_stop = list(self.__animations.keys())
        for name in names_to_stop:
            self.__stop_animation(name)
        self.__animations.clear()

        # 清除进度条引用
        self.__progress_bars.clear()
        self.__logger.debug("ProgressBarManager 清理完成。")
