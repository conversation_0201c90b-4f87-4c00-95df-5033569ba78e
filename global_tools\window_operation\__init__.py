from .__operation import (
    capture_window_handle,
    WindowCapture,
    GetSpecifiedWindowHandle,
    WindowHandleManager,
    DrawUtils
)

from .helper import (
    user32, gdi32,
    WindowCaptureException, WindowCaptureError, WindowHandleException, 
    WindowNotFoundError, WindowOperationError,
    random_point_in_obb
)

from .utils import (
    get_random_point_in_polygon,
)

from .wgc_live_recorder.live_recorder import LiveRecorder

__all__ = [
    # ------- 窗口句柄获取与管理 -------
    "capture_window_handle",     # 函数: 捕获指定窗口的句柄，用于后续窗口操作
    "GetSpecifiedWindowHandle",  # 类: 用于获取指定窗口句柄的工具类，支持通过窗口名称、类名等多种方式查找
    "WindowHandleManager",       # 类: 窗口句柄管理器，提供对窗口句柄的缓存、验证和管理功能
    "random_point_in_obb",       # 函数: 在指定窗口的OBB区域内随机生成一个点

    # ------- 窗口图像捕获与处理 -------
    "WindowCapture",             # 类: 窗口截图捕获工具，能够获取指定窗口的屏幕图像
    "DrawUtils",                 # 类: 绘图工具集，提供在捕获的窗口图像上绘制辅助线、矩形等功能

    # ------- Windows系统API -------
    "user32",                    # 模块: Windows用户界面相关API的DLL导入，包含窗口操作、消息处理等功能
    "gdi32",                     # 模块: Windows图形设备接口API的DLL导入，用于处理图形绘制相关功能

    # ------- 异常类 -------
    "WindowCaptureException",    # 异常类: 窗口捕获过程中的一般性异常基类
    "WindowCaptureError",        # 异常类: 窗口捕获失败时抛出的具体错误
    "WindowHandleException",     # 异常类: 窗口句柄操作过程中的一般性异常基类
    "WindowNotFoundError",       # 异常类: 找不到指定窗口时抛出的错误
    "WindowOperationError",      # 异常类: 执行窗口操作时出现的错误

    # ------- 工具函数 -------
    "get_random_point_in_polygon",  # 函数: 在指定多边形内部生成一个随机点
    "LiveRecorder",                # 类: 使用WGC.dll进行窗口捕获的工具类
]


# Windows Graphics Capture API
# DXGI Desktop Duplication