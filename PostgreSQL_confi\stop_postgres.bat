@echo off
chcp 936 >nul
echo 正在停止 PostgreSQL 服务...

sc query PostgreSQL_17 | findstr "RUNNING" > nul
if %errorlevel% equ 0 (
    sc stop PostgreSQL_17
    echo 正在等待服务停止...
    ping 127.0.0.1 -n 5 > nul
    sc query PostgreSQL_17 | findstr "STOPPED" > nul
    if %errorlevel% equ 0 (
        echo PostgreSQL 服务已成功停止！
    ) else (
        echo PostgreSQL 服务停止过程中遇到问题，请手动检查服务状态。
    )
) else (
    echo PostgreSQL 服务当前未运行。
)

echo.
echo 当前PostgreSQL服务状态:
sc query PostgreSQL_17

echo.
echo 按任意键退出...
pause > nul 