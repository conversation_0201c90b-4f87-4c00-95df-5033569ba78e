# PostgreSQL 服务管理命令指南

## 基本信息

- **服务名称**: PostgreSQL_17
- **安装目录**: C:\Program Files\PostgreSQL\17
- **数据目录**: H:\PostgreSQL_data
- **监听端口**: 9001（非默认的5432）
- **配置文件**: H:\PostgreSQL_data\postgresql.conf
- **访问控制配置**: H:\PostgreSQL_data\pg_hba.conf

## 服务管理命令

### 1. 查看服务状态

```cmd
sc query PostgreSQL_17
```

### 2. 启动服务

```cmd
sc start PostgreSQL_17
```

### 3. 停止服务

```cmd
sc stop PostgreSQL_17
```

### 4. 重启服务

```cmd
sc stop PostgreSQL_17 && sc start PostgreSQL_17
```

### 5. 服务启动类型配置

#### 查看服务配置
```cmd
sc qc PostgreSQL_17
```

#### 设置自动启动
```cmd
sc config PostgreSQL_17 start= auto
```

#### 设置手动启动
```cmd
sc config PostgreSQL_17 start= demand
```

#### 设置禁止启动
```cmd
sc config PostgreSQL_17 start= disabled
```

注意: 在 `start=` 后必须有一个空格，这是 Windows 命令的特殊要求。

## 使用 pg_ctl 管理服务

除了 Windows 服务命令外，也可以使用 PostgreSQL 自带的 `pg_ctl` 工具：

### 1. 使用 pg_ctl 查看状态
```cmd
"C:\Program Files\PostgreSQL\17\bin\pg_ctl.exe" status -D "H:\PostgreSQL_data"
```

### 2. 使用 pg_ctl 启动数据库
```cmd
"C:\Program Files\PostgreSQL\17\bin\pg_ctl.exe" start -D "H:\PostgreSQL_data" -o "-p 9001"
```

### 3. 使用 pg_ctl 停止数据库
```cmd
"C:\Program Files\PostgreSQL\17\bin\pg_ctl.exe" stop -D "H:\PostgreSQL_data" -m fast
```

### 4. 使用 pg_ctl 重启数据库
```cmd
"C:\Program Files\PostgreSQL\17\bin\pg_ctl.exe" restart -D "H:\PostgreSQL_data" -m fast -o "-p 9001"
```

## 常见问题排查

### 1. 服务无法启动

检查事件查看器中的日志：
```cmd
eventvwr
```

检查 PostgreSQL 日志（位于数据目录的 log 子目录）：
```cmd
type "H:\PostgreSQL_data\log\postgresql-当前日期.log"
```

### 2. 无法连接到数据库

确认服务正在运行：
```cmd
sc query PostgreSQL_17
```

检查端口是否正确（本例中为9001）：
```cmd
netstat -ano | findstr :9001
```

测试连接：
```cmd
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -p 9001
```

### 3. 重置PostgreSQL密码

当忘记管理员密码时，可以修改 `pg_hba.conf` 文件，临时将认证方式改为 `trust`，然后重置密码。

#### 修改 pg_hba.conf
将以下行：
```
local   all             all                                     scram-sha-256
host    all             all             127.0.0.1/32            scram-sha-256
host    all             all             ::1/128                 scram-sha-256
```

临时改为：
```
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust
```

#### 重启服务
```cmd
sc stop PostgreSQL_17 && sc start PostgreSQL_17
```

#### 重设密码
```cmd
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -p 9001 -c "ALTER USER postgres WITH PASSWORD 'new_password';"
```

#### 恢复 pg_hba.conf 并重启服务
```cmd
sc stop PostgreSQL_17 && sc start PostgreSQL_17
```

## 开机自启动验证

PostgreSQL_17 服务已配置为自动启动（START_TYPE: 2 AUTO_START）。系统重启后，服务应自动启动，无需手动干预。

如需验证，可以在系统重启后运行：
```cmd
sc query PostgreSQL_17
```
状态应该显示为 "4 RUNNING"。 