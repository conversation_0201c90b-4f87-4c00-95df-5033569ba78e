import os
import sys
import time
import logging
import numpy as np
import cv2
import ctypes
from ctypes import wintypes
from ctypes import POINTER, byref, windll, Structure, WINFUNCTYPE, c_void_p, c_int, c_uint, c_bool, c_float
from typing import Tuple, Optional, Union, Dict, Any, List

# 配置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WindowsGraphicsCapture")

# Windows API 常量
user32 = ctypes.windll.user32
gdi32 = ctypes.windll.gdi32
kernel32 = ctypes.windll.kernel32

# 多显示器支持的 Windows API 常量
SM_CXVIRTUALSCREEN = 78  # 虚拟屏幕的宽度
SM_CYVIRTUALSCREEN = 79  # 虚拟屏幕的高度
SM_XVIRTUALSCREEN = 76   # 虚拟屏幕的左坐标
SM_YVIRTUALSCREEN = 77   # 虚拟屏幕的上坐标

# 定义必要的 Windows API 结构
class RECT(Structure):
    _fields_ = [("left", c_int),
                ("top", c_int),
                ("right", c_int),
                ("bottom", c_int)]

class MONITORINFO(Structure):
    _fields_ = [
        ("cbSize", c_uint),
        ("rcMonitor", RECT),
        ("rcWork", RECT),
        ("dwFlags", c_uint)
    ]

class WindowsGraphicsCapture:
    """
    一个使用 DXcam 进行屏幕捕获的高性能截图捕获类。
    该类提供方法从指定的窗口句柄捕获屏幕截图，
    坐标相对于窗口的左上角。
    
    DXcam 利用 Desktop Duplication API，它在 Windows 中
    提供高性能的屏幕捕获功能。
    
    属性:
        __dpi_scale (float): 当前的 DPI 缩放因子。
        __last_capture_time (float): 上次捕获操作的时间戳。
    """

    def __init__(self):
        """
        初始化 WindowsGraphicsCapture 类。
        设置 DXcam 进行屏幕捕获。
        """
        self.__dpi_scale = self.__get_dpi_scale()
        self.__last_capture_time = 0.0
        self.__dxcam_instance = None
        self.__current_device_idx = None
        # 存储屏幕尺寸用于区域验证
        self.__screen_width = user32.GetSystemMetrics(0)  # SM_CXSCREEN
        self.__screen_height = user32.GetSystemMetrics(1)  # SM_CYSCREEN
        self.__initialize_dxcam()
        logger.info("WindowsGraphicsCapture 初始化完成，使用 DXcam 后端")
        logger.info(f"检测到的屏幕分辨率: {self.__screen_width}x{self.__screen_height}")
        # 捕获尝试的最大次数
        self.__max_retries = 3
        # 捕获之间的最小延迟，避免使 DXcam 过载
        self.__min_delay_between_captures = 0.01  # 10ms

    def __get_real_monitor_count(self) -> int:
        """
        获取系统中真实的显示器数量。
        
        返回:
            int: 系统中的显示器数量。
        """
        monitor_count = 0
        
        def monitor_enum_proc(hMonitor, hdcMonitor, lprcMonitor, dwData):
            nonlocal monitor_count
            monitor_count += 1
            return True
        
        monitor_enum_proc_type = WINFUNCTYPE(c_bool, c_void_p, c_void_p, POINTER(RECT), c_void_p)
        monitor_enum_proc_callback = monitor_enum_proc_type(monitor_enum_proc)
        user32.EnumDisplayMonitors(None, None, monitor_enum_proc_callback, 0)
        
        return monitor_count
    
    def get_monitor_info(self, monitor_index: int = None) -> Dict[str, Any]:
        """
        获取指定显示器的信息，包括名称和分辨率。
        
        参数:
            monitor_index (int, optional): 要获取信息的显示器索引。如果为None，返回所有显示器的信息。
        
        返回:
            Dict[str, Any]: 包含显示器信息的字典。
            
        示例:
            ```python
            capture = WindowsGraphicsCapture()
            
            # 获取所有显示器的信息
            all_monitors = capture.get_monitor_info()
            
            # 获取特定显示器的信息
            monitor_info = capture.get_monitor_info(1)
            print(f"显示器名称: {monitor_info['name']}")
            print(f"显示器分辨率: {monitor_info['width']}x{monitor_info['height']}")
            ```
        """
        monitors = []
        
        def monitor_enum_proc(hMonitor, hdcMonitor, lprcMonitor, dwData):
            # 获取显示器信息
            info = MONITORINFO()
            info.cbSize = ctypes.sizeof(MONITORINFO)
            if user32.GetMonitorInfoW(hMonitor, byref(info)):
                width = info.rcMonitor.right - info.rcMonitor.left
                height = info.rcMonitor.bottom - info.rcMonitor.top
                
                # 尝试获取显示器设备名称
                display_name = f"显示器 {len(monitors) + 1}"
                
                # 结合位置信息，提供更有意义的名称
                position_info = ""
                if info.dwFlags & 1:  # MONITORINFOF_PRIMARY
                    position_info = "主显示器"
                elif info.rcMonitor.left < 0:
                    position_info = "左侧显示器"
                elif info.rcMonitor.left > 0:
                    position_info = "右侧显示器"
                
                if position_info:
                    display_name = f"{display_name} ({position_info})"
                
                monitors.append({
                    'handle': hMonitor,
                    'index': len(monitors),
                    'name': display_name,
                    'x': info.rcMonitor.left,
                    'y': info.rcMonitor.top,
                    'width': width,
                    'height': height,
                    'is_primary': (info.dwFlags & 1) == 1  # MONITORINFOF_PRIMARY = 1
                })
            return True
        
        monitor_enum_proc_type = WINFUNCTYPE(c_bool, c_void_p, c_void_p, POINTER(RECT), c_void_p)
        monitor_enum_proc_callback = monitor_enum_proc_type(monitor_enum_proc)
        user32.EnumDisplayMonitors(None, None, monitor_enum_proc_callback, 0)
        
        if monitor_index is not None:
            if 0 <= monitor_index < len(monitors):
                return monitors[monitor_index]
            else:
                logger.warning(f"显示器索引 {monitor_index} 超出范围，可用显示器数量: {len(monitors)}")
                return monitors[0] if monitors else {}
        
        return monitors

    def __initialize_dxcam(self, device_idx: int = 0) -> None:
        """
        初始化 DXcam 进行屏幕捕获。

        参数:
            device_idx (int): 要使用的显示器索引，默认为0（主显示器）。
                              注意：DXcam 通常只支持设备索引 0，不同显示器的捕获是通过调整捕获区域实现的。
        """
        try:
            # 获取系统的真实显示器数量
            monitor_count = self.__get_real_monitor_count()
            logger.info(f"系统中的真实显示器数量: {monitor_count}")
            
            # 确保设备索引有效
            if device_idx >= monitor_count:
                logger.warning(f"显示器索引 {device_idx} 超出范围，可用显示器数量: {monitor_count}。回退到主显示器 (0)")
                device_idx = 0
            
            # 获取显示器信息
            monitor_info = self.get_monitor_info(device_idx)
            if monitor_info:
                logger.info(f"使用显示器: {monitor_info['name']} "
                          f"分辨率: {monitor_info['width']}x{monitor_info['height']} "
                          f"位置: ({monitor_info['x']},{monitor_info['y']}) "
                          f"{'[主显示器]' if monitor_info.get('is_primary') else ''}")
            
            # 动态导入 dxcam 以避免直接依赖
            import importlib.util
            spec = importlib.util.find_spec("dxcam")
            if spec is not None:
                dxcam = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(dxcam)
                
                # 创建 DXcam 实例前的安全检查
                try:
                    # 创建输出为 RGB 颜色的 DXcam 实例，始终使用设备索引0
                    # DXcam 通常只支持设备索引 0，不同显示器的捕获是通过调整捕获区域实现的
                    logger.info(f"尝试创建 DXcam 实例，使用默认设备索引 0")
                    self.__dxcam_instance = dxcam.create(output_color="RGB", device_idx=0)
                    
                    # 验证实例是否成功创建
                    if self.__dxcam_instance is None:
                        raise RuntimeError(f"DXcam 实例创建失败，返回了 None")
                    
                    # 保存当前使用的设备索引（系统显示器索引，非 DXcam 设备索引）
                    # 我们仍然保存系统显示器索引，以便在捕获时调整区域
                    self.__current_device_idx = device_idx
                    
                    logger.info(f"DXcam 初始化成功，使用显示器索引 {device_idx} (DXcam 设备索引 0)")
                except Exception as e:
                    logger.error(f"创建 DXcam 实例时出错: {str(e)}")
                    raise RuntimeError(f"无法初始化 DXcam: {str(e)}")
            else:
                raise ImportError("未找到 DXcam 模块")
        except ImportError as e:
            logger.error(f"导入 DXcam 时出错: {str(e)}")
            logger.error("请使用以下命令安装 DXcam: pip install dxcam")
            raise ImportError("WindowsGraphicsCapture 需要 DXcam")
        except Exception as e:
            logger.error(f"初始化 DXcam 时出错: {str(e)}")
            self.__dxcam_instance = None
            self.__current_device_idx = None
            raise RuntimeError(f"无法初始化 DXcam: {str(e)}")

    def __get_dpi_scale(self) -> float:
        """
        获取当前的 DPI 缩放因子。

        返回:
            float: DPI 缩放因子。
        """
        try:
            # 检查是否为 Windows 8.1 或更高版本
            if hasattr(windll, "shcore"):
                awareness = ctypes.c_int()
                windll.shcore.GetProcessDpiAwareness(0, ctypes.byref(awareness))
                
                # 如果进程是 DPI 感知的，获取 DPI 缩放
                if awareness.value != 0:  # 0 表示不感知 DPI
                    dpi = ctypes.c_uint()
                    windll.shcore.GetDpiForSystem(ctypes.byref(dpi))
                    return dpi.value / 96.0
            return 1.0
        except Exception:
            # 如果无法确定，则回退到默认缩放
            return 1.0

    def __get_window_rect(self, hwnd: int) -> Tuple[int, int, int, int]:
        """
        获取窗口的矩形坐标。

        参数:
            hwnd (int): 窗口句柄。

        返回:
            Tuple[int, int, int, int]: 包含 (left, top, right, bottom) 坐标的元组。
        """
        # 获取窗口的客户区域
        rect = RECT()
        if not user32.GetClientRect(hwnd, byref(rect)):
            error_code = ctypes.GetLastError()
            raise ctypes.WinError(error_code)
        
        logger.debug(f"窗口客户区大小: 宽={rect.right - rect.left}, 高={rect.bottom - rect.top}")
        
        # 将客户区坐标转换为屏幕坐标
        pt_left_top = wintypes.POINT(rect.left, rect.top)
        user32.ClientToScreen(hwnd, byref(pt_left_top))
        
        pt_right_bottom = wintypes.POINT(rect.right, rect.bottom)
        user32.ClientToScreen(hwnd, byref(pt_right_bottom))
        
        # 构建屏幕坐标下的矩形
        screen_rect = (pt_left_top.x, pt_left_top.y, pt_right_bottom.x, pt_right_bottom.y)
        logger.debug(f"窗口客户区屏幕坐标: {screen_rect}")
        
        return screen_rect

    def __validate_window(self, hwnd: int) -> bool:
        """
        验证窗口句柄是否有效且可见。

        参数:
            hwnd (int): 窗口句柄。

        返回:
            bool: 如果窗口有效且可见，则为 True，否则为 False。
        """
        if not user32.IsWindow(hwnd):
            logger.warning(f"无效的窗口句柄: {hwnd}")
            return False
        
        if not user32.IsWindowVisible(hwnd):
            logger.warning(f"窗口 {hwnd} 不可见")
            return False
        
        # 检查窗口是否最小化
        if user32.IsIconic(hwnd):
            logger.warning(f"窗口 {hwnd} 已最小化")
            return False
            
        return True

    def __validate_and_adjust_region(self, 
                                    window_rect: Tuple[int, int, int, int], 
                                    region: Optional[Tuple[int, int, int, int]] = None) -> Tuple[int, int, int, int]:
        """
        验证并调整捕获区域，确保其在屏幕边界内。

        参数:
            window_rect (Tuple[int, int, int, int]): 窗口矩形 (left, top, right, bottom)。
            region (Optional[Tuple[int, int, int, int]]): 要验证的区域 (x, y, right, bottom)。

        返回:
            Tuple[int, int, int, int]: 调整后的区域 (left, top, right, bottom)。
        """
        # 获取虚拟屏幕的尺寸（所有显示器的总区域）
        virtual_screen_left = user32.GetSystemMetrics(SM_XVIRTUALSCREEN)
        virtual_screen_top = user32.GetSystemMetrics(SM_YVIRTUALSCREEN)
        virtual_screen_width = user32.GetSystemMetrics(SM_CXVIRTUALSCREEN)
        virtual_screen_height = user32.GetSystemMetrics(SM_CYVIRTUALSCREEN)
        
        logger.debug(f"虚拟屏幕区域: 左={virtual_screen_left}, 上={virtual_screen_top}, "
                    f"宽={virtual_screen_width}, 高={virtual_screen_height}")
        logger.debug(f"窗口矩形: {window_rect}")
        
        if region is None:
            # 使用整个窗口
            left, top, right, bottom = window_rect
        else:
            # 根据窗口相对区域计算绝对屏幕坐标
            left = window_rect[0] + region[0]
            top = window_rect[1] + region[1]
            right = window_rect[0] + region[2] 
            bottom = window_rect[1] + region[3]
            
        logger.debug(f"原始捕获区域: ({left}, {top}, {right}, {bottom})")
        
        # 确保区域在虚拟屏幕边界内
        left = max(virtual_screen_left, min(left, virtual_screen_left + virtual_screen_width - 1))
        top = max(virtual_screen_top, min(top, virtual_screen_top + virtual_screen_height - 1))
        right = max(left + 1, min(right, virtual_screen_left + virtual_screen_width))
        bottom = max(top + 1, min(bottom, virtual_screen_top + virtual_screen_height))
        
        # 确保最小尺寸（DXcam 需要至少 1x1 区域）
        if right - left < 1:
            right = left + 1
        if bottom - top < 1:
            bottom = top + 1
            
        logger.debug(f"调整后的捕获区域: ({left}, {top}, {right}, {bottom})")
        return (left, top, right, bottom)

    def get_monitor_index_for_window(self, hwnd: int) -> int:
        """
        获取指定窗口所在的显示器索引。

        参数:
            hwnd (int): 窗口句柄。

        返回:
            int: 窗口所在显示器的索引，如果找不到则返回 0（主显示器）。

        示例:
            ```python
            # 创建 WindowsGraphicsCapture 实例
            capture = WindowsGraphicsCapture()
            
            # 获取记事本窗口句柄
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("记事本")
            
            # 获取窗口所在的显示器索引
            monitor_index = capture.get_monitor_index_for_window(hwnd)
            print(f"窗口位于显示器 {monitor_index}")
            ```
        """
        # 验证窗口句柄是否有效
        if not self.__validate_window(hwnd):
            logger.warning(f"窗口句柄 {hwnd} 无效或不可见")
            return 0  # 返回主显示器索引

        try:
            # 获取窗口矩形
            window_rect = self.__get_window_rect(hwnd)
            
            # 计算窗口中心点
            window_center_x = (window_rect[0] + window_rect[2]) // 2
            window_center_y = (window_rect[1] + window_rect[3]) // 2
            
            # 使用 MonitorFromPoint 函数找到包含窗口中心点的显示器
            MONITOR_DEFAULTTONEAREST = 0x00000002  # 如果点不在任何显示器上，返回最近的显示器
            monitor_handle = user32.MonitorFromPoint(
                wintypes.POINT(window_center_x, window_center_y), 
                MONITOR_DEFAULTTONEAREST
            )
            
            # 获取所有显示器
            monitors = []
            
            def monitor_enum_proc(hMonitor, hdcMonitor, lprcMonitor, dwData):
                monitors.append(hMonitor)
                return True
            
            monitor_enum_proc_type = WINFUNCTYPE(c_bool, c_void_p, c_void_p, POINTER(RECT), c_void_p)
            monitor_enum_proc_callback = monitor_enum_proc_type(monitor_enum_proc)
            user32.EnumDisplayMonitors(None, None, monitor_enum_proc_callback, 0)
            
            # 找到匹配的显示器索引
            for i, monitor in enumerate(monitors):
                if monitor == monitor_handle:
                    logger.debug(f"找到窗口 {hwnd} 位于显示器索引 {i}")
                    return i
            
            logger.warning(f"无法确定窗口 {hwnd} 所在的显示器，返回主显示器 (0)")
            return 0
        
        except Exception as e:
            logger.error(f"获取窗口 {hwnd} 的显示器索引时出错: {str(e)}")
            return 0  # 发生错误时返回主显示器索引

    def capture(self, 
                hwnd: int, 
                region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        使用 DXcam 从指定的窗口句柄捕获屏幕截图。
        
        参数:
            hwnd (int): 要捕获的窗口句柄。
            region (Optional[Tuple[int, int, int, int]]): 要捕获的可选区域 (x, y, right, bottom)，
                                                         相对于窗口的左上角。

        返回:
            np.ndarray: 作为 RGB 格式的 numpy 数组的捕获图像。
            
        示例:
            ```python
            # 创建 WindowsGraphicsCapture 实例
            capture = WindowsGraphicsCapture()
            
            # 获取窗口句柄（示例使用窗口标题）
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("记事本")
            
            # 捕获整个窗口
            img = capture.capture(hwnd)
            
            # 捕获特定区域（从左上角开始的 100x100 像素）
            region_img = capture.capture(hwnd, region=(0, 0, 100, 100))
            
            # 显示捕获的图像
            cv2.imshow("捕获", img)
            cv2.waitKey(0)
            ```
        """
        start_time = time.time()
        last_error = None
        
        # 检查 DXcam 是否可用
        if self.__dxcam_instance is None:
            try:
                logger.warning("DXcam 未初始化，尝试重新初始化")
                self.__initialize_dxcam()
                if self.__dxcam_instance is None:
                    raise RuntimeError("DXcam 初始化失败")
            except Exception as e:
                logger.error(f"重新初始化 DXcam 失败: {str(e)}")
                raise RuntimeError(f"DXcam 未初始化且无法重新初始化: {str(e)}")
        
        # 验证窗口句柄
        if not self.__validate_window(hwnd):
            raise ValueError(f"窗口句柄 {hwnd} 无效或不可见")
        
        # 获取窗口所在的显示器索引
        monitor_index = self.get_monitor_index_for_window(hwnd)
        
        # 获取并打印窗口所在的显示器信息
        monitor_info = self.get_monitor_info(monitor_index)
        if monitor_info:
            logger.info(f"窗口位于显示器 {monitor_index}: {monitor_info['name']}, "
                      f"分辨率: {monitor_info['width']}x{monitor_info['height']}, "
                      f"位置: ({monitor_info['x']},{monitor_info['y']})")
        
        # 获取主显示器信息
        primary_monitor = None
        for m in self.get_monitor_info():
            if m.get('is_primary'):
                primary_monitor = m
                break
        
        if not primary_monitor:
            logger.warning("无法获取主显示器信息，使用系统默认值")
            primary_monitor = {
                'x': 0,
                'y': 0,
                'width': self.__screen_width,
                'height': self.__screen_height
            }
        
        # 如果捕获发生得太快，添加小延迟
        elapsed_since_last = time.time() - self.__last_capture_time / 1000.0
        if elapsed_since_last < self.__min_delay_between_captures:
            time.sleep(self.__min_delay_between_captures - elapsed_since_last)
        
        # 获取窗口坐标
        window_rect = self.__get_window_rect(hwnd)
        
        # 根据窗口相对区域计算绝对屏幕坐标
        if region is None:
            # 使用整个窗口
            abs_left, abs_top, abs_right, abs_bottom = window_rect
        else:
            # 根据窗口相对区域计算绝对屏幕坐标
            abs_left = window_rect[0] + region[0]
            abs_top = window_rect[1] + region[1]
            abs_right = window_rect[0] + region[2]
            abs_bottom = window_rect[1] + region[3]
        
        logger.debug(f"绝对屏幕坐标: ({abs_left}, {abs_top}, {abs_right}, {abs_bottom})")
        
        # 将非主显示器上的坐标转换为主显示器上的相对坐标
        primary_left = int(primary_monitor['x'])
        primary_top = int(primary_monitor['y'])
        primary_width = int(primary_monitor['width'])
        primary_height = int(primary_monitor['height'])
        
        # 检查窗口是否在主显示器上
        is_on_primary = (
            abs_left >= primary_left and
            abs_top >= primary_top and
            abs_right <= primary_left + primary_width and
            abs_bottom <= primary_top + primary_height
        )
        
        if not is_on_primary:
            logger.warning(f"窗口不在主显示器上，DXcam 可能无法正确捕获")
            logger.info(f"尝试将窗口移动到主显示器上进行捕获")
            
            # 计算窗口在主显示器上的位置
            # 这里我们简单地将窗口放在主显示器的中心
            window_width = abs_right - abs_left
            window_height = abs_bottom - abs_top
            
            # 确保窗口尺寸不超过主显示器
            window_width = min(window_width, primary_width)
            window_height = min(window_height, primary_height)
            
            # 计算居中位置
            center_x = primary_left + (primary_width - window_width) // 2
            center_y = primary_top + (primary_height - window_height) // 2
            
            # 设置新的捕获区域（在主显示器上）
            dxcam_region = (
                max(primary_left, center_x),
                max(primary_top, center_y),
                min(primary_left + primary_width, center_x + window_width),
                min(primary_top + primary_height, center_y + window_height)
            )
            
            logger.warning(f"原始窗口区域 ({abs_left}, {abs_top}, {abs_right}, {abs_bottom}) 不在主显示器上")
            logger.warning(f"改为捕获主显示器上的区域 {dxcam_region}")
        else:
            # 使用原始区域，但确保它在主显示器边界内
            dxcam_region = (
                max(primary_left, abs_left),
                max(primary_top, abs_top),
                min(primary_left + primary_width, abs_right),
                min(primary_top + primary_height, abs_bottom)
            )
        
        # 确保最小尺寸（DXcam 需要至少 1x1 区域）
        if dxcam_region[2] - dxcam_region[0] < 1:
            dxcam_region = (dxcam_region[0], dxcam_region[1], dxcam_region[0] + 1, dxcam_region[3])
        if dxcam_region[3] - dxcam_region[1] < 1:
            dxcam_region = (dxcam_region[0], dxcam_region[1], dxcam_region[2], dxcam_region[1] + 1)
        
        logger.debug(f"调整后的 DXcam 捕获区域: {dxcam_region}")
        
        # 更强健的捕获机制
        for retry in range(self.__max_retries):
            try:
                # 使用 DXcam 执行捕获
                if self.__dxcam_instance is None:
                    raise RuntimeError("DXcam 实例无效，可能初始化失败")
                
                logger.debug(f"尝试使用 DXcam 捕获区域: {dxcam_region}")
                frame = self.__dxcam_instance.grab(region=dxcam_region)
                
                if frame is None:
                    logger.warning(f"DXcam 在尝试 {retry+1}/{self.__max_retries} 时返回 None")
                    last_error = RuntimeError("使用 DXcam 捕获帧失败，返回了 None")
                    # 在重试前等待一段时间
                    time.sleep(0.05 * (retry + 1))
                    # 如果是最后一次尝试前，尝试重新初始化 DXcam
                    if retry == self.__max_retries - 2:
                        logger.info("重新初始化 DXcam 实例")
                        if hasattr(self.__dxcam_instance, 'release'):
                            self.__dxcam_instance.release()
                        self.__initialize_dxcam(device_idx=0)
                    continue
                
                # 记录指标
                elapsed_time = (time.time() - start_time) * 1000  # 转换为毫秒
                self.__last_capture_time = elapsed_time
                
                # 记录性能数据
                logger.debug(f"使用 DXcam 完成捕获，耗时 {elapsed_time:.2f} ms（尝试 {retry+1}）")
                
                # DXcam 返回 BGR 格式，尽管设置了 output_color="RGB"，因此需要转换
                # 这确保颜色正确显示（黄色显示为黄色，而不是蓝色）
                if frame.shape[2] >= 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 如果窗口不在主显示器上，我们捕获的是主显示器上的替代区域
                # 在这种情况下，我们应该在日志中添加警告
                if not is_on_primary:
                    logger.warning(f"窗口不在主显示器上，返回的是主显示器上的替代区域")
                
                return frame
                
            except ValueError as e:
                # 特殊处理区域无效的错误
                if "Invalid Region" in str(e):
                    logger.error(f"DXcam 区域无效: {str(e)}")
                    logger.error(f"尝试捕获区域: {dxcam_region}")
                    logger.error(f"主显示器区域: ({primary_left}, {primary_top}, {primary_left + primary_width}, {primary_top + primary_height})")
                    
                    # 尝试使用更小的区域
                    smaller_region = (
                        primary_left,
                        primary_top,
                        min(primary_left + 100, primary_left + primary_width),
                        min(primary_top + 100, primary_top + primary_height)
                    )
                    
                    logger.info(f"尝试使用更小的区域: {smaller_region}")
                    try:
                        frame = self.__dxcam_instance.grab(region=smaller_region)
                        if frame is not None:
                            logger.info(f"使用更小的区域捕获成功，图像大小: {frame.shape}")
                            
                            # 记录指标
                            elapsed_time = (time.time() - start_time) * 1000
                            self.__last_capture_time = elapsed_time
                            
                            # 转换颜色格式
                            if frame.shape[2] >= 3:
                                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            
                            logger.warning("返回的不是请求的窗口区域，而是主显示器上的一个小区域")
                            return frame
                    except Exception as small_error:
                        logger.error(f"使用更小的区域捕获失败: {str(small_error)}")
                
                last_error = e
                logger.warning(f"捕获尝试 {retry+1}/{self.__max_retries} 失败: {str(e)}")
                if retry < self.__max_retries - 1:
                    # 使用指数退避在重试之间等待更长时间
                    time.sleep(0.05 * (2 ** retry))
            except Exception as e:
                last_error = e
                logger.warning(f"捕获尝试 {retry+1}/{self.__max_retries} 失败: {str(e)}")
                if retry < self.__max_retries - 1:
                    # 使用指数退避在重试之间等待更长时间
                    time.sleep(0.05 * (2 ** retry))
        
        # 如果所有捕获尝试都失败
        logger.error(f"所有 {self.__max_retries} 次捕获尝试均失败")
        if last_error:
            raise last_error
        else:
            raise RuntimeError("多次尝试后捕获屏幕截图失败")

    def get_last_capture_time(self) -> float:
        """
        返回上次捕获操作所花费的时间（毫秒）。
        
        返回:
            float: 上次捕获操作的时间（毫秒）。
            
        示例:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            print(f"捕获耗时 {capture.get_last_capture_time():.2f} ms")
            ```
        """
        return self.__last_capture_time
    
    def get_screen_size(self) -> Tuple[int, int]:
        """
        返回主屏幕的分辨率。
        
        返回:
            Tuple[int, int]: 主屏幕的宽度和高度。
        """
        return (self.__screen_width, self.__screen_height)
    
    @staticmethod
    def get_window_handle_by_title(title: str) -> int:
        """
        通过标题获取窗口句柄。
        
        参数:
            title (str): 要搜索的窗口标题。
            
        返回:
            int: 如果找到窗口句柄，则返回该句柄，否则返回 0。
            
        示例:
            ```python
            capture = WindowsGraphicsCapture()
            hwnd = WindowsGraphicsCapture.get_window_handle_by_title("记事本")
            if hwnd:
                img = capture.capture(hwnd)
            ```
        """
        return user32.FindWindowW(None, title)
    
    @staticmethod
    def list_windows() -> List[Dict[str, Any]]:
        """
        列出系统中所有可见的窗口。
        
        返回:
            List[Dict[str, Any]]: 包含窗口信息的字典列表。
            
        示例:
            ```python
            windows = WindowsGraphicsCapture.list_windows()
            for window in windows:
                print(f"窗口: {window['title']} (句柄: {window['hwnd']})")
            ```
        """
        windows = []
        
        def enum_windows_proc(hwnd, lparam):
            if user32.IsWindowVisible(hwnd):
                length = user32.GetWindowTextLengthW(hwnd)
                if length > 0:
                    buffer = ctypes.create_unicode_buffer(length + 1)
                    user32.GetWindowTextW(hwnd, buffer, length + 1)
                    rect = RECT()
                    user32.GetWindowRect(hwnd, byref(rect))
                    
                    windows.append({
                        "hwnd": hwnd,
                        "title": buffer.value,
                        "rect": (rect.left, rect.top, rect.right, rect.bottom),
                        "width": rect.right - rect.left,
                        "height": rect.bottom - rect.top
                    })
            return True
        
        enum_windows_proc_type = WINFUNCTYPE(c_bool, c_int, c_int)
        enum_windows_proc_callback = enum_windows_proc_type(enum_windows_proc)
        user32.EnumWindows(enum_windows_proc_callback, 0)
        
        return windows
    
    def release(self) -> None:
        """
        释放捕获实例使用的资源。
        
        示例:
            ```python
            capture = WindowsGraphicsCapture()
            img = capture.capture(hwnd)
            capture.release()
            ```
        """
        if self.__dxcam_instance is not None:
            if hasattr(self.__dxcam_instance, 'release'):
                self.__dxcam_instance.release()
            
            self.__dxcam_instance = None
            self.__current_device_idx = None
            
        logger.info("资源已释放")

# 使用示例
if __name__ == "__main__":
    try:
        # 设置日志级别为DEBUG以显示更多信息
        logging.basicConfig(level=logging.DEBUG)
        
        # 创建 WindowsGraphicsCapture 实例
        capture = WindowsGraphicsCapture()
        
        # 列出所有显示器
        monitors = capture.get_monitor_info()
        print(f"\n系统中检测到 {len(monitors)} 个显示器:")
        for i, monitor in enumerate(monitors):
            print(f"显示器 {i}: {monitor['name']}, 分辨率: {monitor['width']}x{monitor['height']}, "
                  f"位置: ({monitor['x']},{monitor['y']}), "
                  f"{'[主显示器]' if monitor.get('is_primary') else ''}")
        
        # 列出所有窗口
        windows = WindowsGraphicsCapture.list_windows()
        print(f"\n系统中检测到 {len(windows)} 个窗口:")
        for i, window in enumerate(windows[:10]):  # 只显示前10个
            print(f"{i+1}. {window['title']} (句柄: {window['hwnd']})")
        
        # 如果有窗口，尝试捕获第一个
        if windows:
            # 选择一个有意义的窗口（避免系统窗口）
            selected_window = None
            for window in windows:
                if len(window['title']) > 5 and window['width'] > 100 and window['height'] > 100:
                    selected_window = window
                    break
            
            if selected_window is None:
                selected_window = windows[0]
            
            hwnd = selected_window['hwnd']
            print(f"\n尝试捕获窗口: {selected_window['title']}")
            print(f"窗口尺寸: {selected_window['width']}x{selected_window['height']}")
            
            # 获取窗口所在的显示器索引
            monitor_index = capture.get_monitor_index_for_window(hwnd)
            print(f"窗口位于显示器 {monitor_index}")
            
            # 获取显示器信息
            monitor_info = capture.get_monitor_info(monitor_index)
            if monitor_info:
                print(f"显示器信息: {monitor_info['name']}, "
                      f"分辨率: {monitor_info['width']}x{monitor_info['height']}, "
                      f"位置: ({monitor_info['x']},{monitor_info['y']}), "
                      f"{'[主显示器]' if monitor_info.get('is_primary') else ''}")
            
            # 捕获窗口
            try:
                print("开始捕获整个窗口...")
                img = capture.capture(hwnd)
                print(f"捕获整个窗口成功，图像大小: {img.shape}")
                
                # 保存捕获的图像
                output_file = "window_capture_full.png"
                cv2.imwrite(output_file, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
                print(f"整个窗口图像已保存为 {output_file}")
                
                # 捕获窗口的一部分区域
                print("开始捕获窗口的一部分区域 (0,0,500,300)...")
                region_img = capture.capture(hwnd, region=(0, 0, 500, 300))
                print(f"捕获窗口区域成功，图像大小: {region_img.shape}")
                
                # 保存捕获的区域图像
                region_output_file = "window_capture_region.png"
                cv2.imwrite(region_output_file, cv2.cvtColor(region_img, cv2.COLOR_RGB2BGR))
                print(f"窗口区域图像已保存为 {region_output_file}")
                
            except Exception as e:
                print(f"捕获失败: {str(e)}")
        else:
            print("未找到可捕获的窗口")
        
        # 释放资源
        capture.release()
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

