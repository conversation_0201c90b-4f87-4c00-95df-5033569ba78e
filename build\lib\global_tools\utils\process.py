#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义进程类模块 - 提供增强的进程控制功能

此模块实现了一个扩展的进程类，支持回调函数执行、结果获取、优雅停止等功能，
同时具有强大的错误处理能力和详细的日志记录。
"""

import multiprocessing
import os
import pickle
import signal
import sys
import tempfile
import threading
import time
import traceback
import logging
import inspect
import weakref
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, Set


class ProcessError(Exception):
    """自定义进程错误基类"""
    pass


class ProcessTimeoutError(ProcessError):
    """进程操作超时错误"""
    pass


class ProcessTerminatedError(ProcessError):
    """进程被终止错误"""
    pass


class ProcessCallbackError(ProcessError):
    """回调函数错误"""
    pass


class CustomProcess(multiprocessing.Process):
    """
    增强的进程类，提供更多功能和更好的错误处理
    
    特性:
    - 支持在进程中执行自定义回调函数
    - 可以向回调函数传递任意参数
    - 提供优雅停止进程的机制
    - 捕获并记录详细的错误信息
    - 保存并提供获取回调函数返回值的方法
    - 线程安全的结果获取
    - 资源管理和自动清理
    - 心跳机制确保进程健康
    """
    
    # 类级别配置
    DEFAULT_TIMEOUT = 5.0         # 默认超时时间(秒)
    MAX_RESULT_SIZE = 100 * 1024 * 1024  # 默认最大结果大小(100MB)
    HEARTBEAT_INTERVAL = 1.0      # 心跳间隔(秒)
    MIN_POLL_INTERVAL = 0.01      # 最小轮询间隔(秒)
    MAX_POLL_INTERVAL = 0.5       # 最大轮询间隔(秒)
    
    # 进程状态常量
    STATUS_NOT_STARTED = 'not_started'
    STATUS_RUNNING = 'running'
    STATUS_COMPLETED = 'completed'
    STATUS_FAILED = 'failed'
    STATUS_STOPPED = 'stopped'
    STATUS_UNKNOWN = 'unknown'
    
    # 类级别的跟踪实例集合 - 使用list而不是set
    # 因为set不能在多进程间安全共享
    _instances = []
    
    def __init__(
        self, 
        name: str = None, 
        daemon: bool = None,
        callback: Callable = None, 
        *args, 
        **kwargs
    ) -> None:
        """
        初始化自定义进程
        
        Args:
            name: 进程名称
            daemon: 是否为守护进程
            callback: 进程中要执行的回调函数
            *args: 传递给回调函数的位置参数
            **kwargs: 传递给回调函数的关键字参数
        
        Raises:
            TypeError: 如果回调函数不是可调用对象
            ValueError: 如果参数无效
        """
        # 验证回调函数
        if callback is not None and not callable(callback):
            raise TypeError("callback 必须是可调用对象")
        
        # 从kwargs中提取传递给Process的参数
        process_kwargs = {}
        if name is not None:
            process_kwargs['name'] = name
        if daemon is not None:
            process_kwargs['daemon'] = daemon
        
        # 提取回调函数相关参数
        callback_kwargs = {}
        process_params = {'name', 'daemon', 'group', 'target', 'args'}
        
        # 处理特殊参数
        param_mapping = {}
        
        # 检查是否需要重命名参数 (比如 callback_name -> name)
        if hasattr(callback, '__code__'):
            try:
                callback_params = set(inspect.signature(callback).parameters.keys())
                # 第一个参数预留给进程对象
                if callback_params and 'callback_name' in kwargs and 'name' in callback_params:
                    param_mapping['callback_name'] = 'name'
            except (ValueError, TypeError):
                # 某些可调用对象可能无法获取签名
                pass
        
        # 分离进程参数和回调参数
        for key, value in kwargs.items():
            if key in process_params:
                process_kwargs[key] = value
            elif key in param_mapping:
                callback_kwargs[param_mapping[key]] = value
            else:
                callback_kwargs[key] = value
        
        # 调用父类初始化
        super().__init__(**process_kwargs)
        
        # 存储回调函数和参数
        self._callback = callback
        self._args = args
        self._kwargs = callback_kwargs
        
        # 用于进程间通信的共享对象
        self._result = None
        self._temp_files = []  # 存储临时文件路径
        
        # 用于控制进程优雅停止的事件
        # 使用Value替代Event，因为Event可能包含不可序列化的对象
        self._should_stop_flag = multiprocessing.Value('i', 0)
        
        # 标记进程是否已启动
        self._has_started = False
        
        # 用于在主进程保存子进程的结果
        self._local_result = {
            'value': None,
            'error': None,
            'error_type': None,
            'traceback': None,
            'status': self.STATUS_NOT_STARTED
        }
        
        # 创建管道，用于子进程向主进程传递结果
        self._pipe_parent, self._pipe_child = multiprocessing.Pipe()
        
        # 心跳相关(只存储最后一次心跳时间，不存储线程和事件对象)
        self._last_heartbeat = time.time()
        
        # 添加到实例列表
        # 使用普通值而不是弱引用，避免序列化问题
        self.__class__._instances.append(self)
    
    @classmethod
    def cleanup_all(cls):
        """清理所有存活的自定义进程实例"""
        for instance in list(cls._instances):
            try:
                if instance.is_alive():
                    instance.stop(timeout=1.0)
            except:
                pass
        # 清空列表
        cls._instances.clear()
    
    def set_callback(self, callback: Callable, *args, **kwargs) -> None:
        """
        设置或更新回调函数及其参数
        
        Args:
            callback: 进程中要执行的回调函数
            *args: 传递给回调函数的位置参数
            **kwargs: 传递给回调函数的关键字参数
            
        Raises:
            RuntimeError: 如果进程已经启动，则无法更改回调函数
            TypeError: 如果回调函数不是可调用对象
        """
        if self._has_started:
            raise RuntimeError("无法更改已启动进程的回调函数")
        
        if not callable(callback):
            raise TypeError("callback 必须是可调用对象")
        
        self._callback = callback
        self._args = args
        self._kwargs = kwargs
    
    def _start_heartbeat_monitor(self):
        """启动心跳监控线程 - 只在子进程中调用"""
        def monitor_heartbeat():
            # 这个线程在主进程中运行，监听来自子进程的心跳
            while True:
                if self._pipe_parent.poll():
                    try:
                        msg = self._pipe_parent.recv()
                        if isinstance(msg, dict) and msg.get('type') == 'heartbeat':
                            # 记录心跳时间
                            self._last_heartbeat = time.time()
                    except (EOFError, BrokenPipeError):
                        break
                time.sleep(0.1)  # 低频率检查，减少CPU占用
        
        # 启动监控线程
        heartbeat_thread = threading.Thread(
            target=monitor_heartbeat, 
            daemon=True
        )
        heartbeat_thread.start()
        return heartbeat_thread
    
    def _send_heartbeat(self, pipe, stop_event):
        """在子进程中发送心跳 - 只在子进程中调用"""
        while not stop_event.is_set():
            try:
                # 发送心跳消息
                pipe.send({'type': 'heartbeat'})
            except (IOError, EOFError, BrokenPipeError):
                # 管道关闭，退出循环
                break
            except Exception as e:
                # 其他异常，仅记录但继续尝试
                pass
            
            # 等待一段时间
            time.sleep(self.HEARTBEAT_INTERVAL)
    
    def run(self) -> None:
        """
        进程启动时执行的方法，会调用设置的回调函数并处理结果和异常
        
        此方法重写了 multiprocessing.Process 的 run 方法
        """
        # 配置子进程日志
        self._logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # 标记进程已启动
        self._has_started = True
        
        # 创建心跳停止事件(只在子进程中创建)
        stop_heartbeat = threading.Event()
        heartbeat_thread = None
        
        # 在子进程中创建结果字典
        result_dict = {
            'value': None,
            'error': None,
            'error_type': None,
            'traceback': None,
            'status': self.STATUS_RUNNING
        }
        
        try:
            # 启动心跳线程(只在子进程中创建)
            heartbeat_thread = threading.Thread(
                target=self._send_heartbeat,
                args=(self._pipe_child, stop_heartbeat),
                daemon=True
            )
            heartbeat_thread.start()
            
            # 设置信号处理器，用于优雅地处理终止信号
            self._setup_signal_handlers()
            
            # 检查是否设置了回调函数
            if self._callback is None:
                raise ValueError("未设置回调函数")
            
            # 将当前进程对象作为第一个参数传递给回调函数
            # 这样回调函数就可以访问进程对象的方法，如should_stop
            modified_args = (self,) + self._args
            
            # 执行回调函数并捕获返回值
            result_value = self._callback(*modified_args, **self._kwargs)
            
            # 处理可能的大结果数据
            processed_result = self._handle_large_result(result_value)
            
            # 存储返回值和状态
            result_dict['value'] = processed_result
            result_dict['status'] = self.STATUS_COMPLETED
            
        except Exception as e:
            # 捕获并记录异常信息
            error_type = type(e).__name__
            error_msg = str(e)
            tb = traceback.format_exc()
            
            result_dict['error'] = error_msg
            result_dict['error_type'] = error_type
            result_dict['traceback'] = tb
            result_dict['status'] = self.STATUS_FAILED
            
            # 记录错误信息
            self._logger.error(f"进程 {self.name} 发生错误: {error_type}: {error_msg}")
            self._logger.debug(f"错误堆栈: {tb}")
            
            # 打印详细的错误堆栈
            print(f"进程 {self.name} 发生错误 ({error_type}): {error_msg}", file=sys.stderr)
            traceback.print_exc()
        finally:
            # 停止心跳线程
            stop_heartbeat.set()
            if heartbeat_thread and heartbeat_thread.is_alive():
                heartbeat_thread.join(timeout=1.0)
            
            # 通过管道将结果发送给父进程
            try:
                self._pipe_child.send(result_dict)
            except (EOFError, BrokenPipeError):
                # 管道可能已关闭，忽略错误
                pass
            except Exception as e:
                print(f"发送结果时出错: {e}", file=sys.stderr)
            
            # 确保资源被正确释放
            self._cleanup_resources()
    
    def _handle_large_result(self, result_value):
        """
        处理大型结果数据，如果太大则存储到临时文件
        
        Args:
            result_value: 回调函数返回的结果
            
        Returns:
            处理后的结果或引用
        """
        try:
            # 尝试估计结果大小
            result_size = sys.getsizeof(result_value)
            if result_size > self.MAX_RESULT_SIZE:
                # 结果太大，保存到临时文件
                fd, path = tempfile.mkstemp(prefix=f"proc_{self.pid}_result_")
                os.close(fd)  # 关闭文件描述符，由pickle操作文件
                
                with open(path, 'wb') as f:
                    pickle.dump(result_value, f)
                
                # 添加到临时文件列表用于清理
                self._temp_files.append(path)
                
                # 返回文件引用而不是实际内容
                return {'_type': 'file_result', 'path': path}
        except Exception as e:
            if hasattr(self, '_logger'):
                self._logger.warning(f"处理大结果时出错: {e}")
            else:
                print(f"处理大结果时出错: {e}", file=sys.stderr)
        
        # 如果上述过程失败或结果不大，返回原始值
        return result_value
    
    def _load_result_from_file(self, result_ref):
        """
        从临时文件加载大型结果
        
        Args:
            result_ref: 包含文件路径的引用字典
            
        Returns:
            加载的结果对象
        
        Raises:
            IOError: 如果文件无法访问或读取失败
            pickle.PickleError: 如果反序列化失败
        """
        path = result_ref['path']
        if not os.path.exists(path):
            raise IOError(f"结果文件不存在: {path}")
        
        with open(path, 'rb') as f:
            return pickle.load(f)
    
    def _setup_signal_handlers(self) -> None:
        """
        设置信号处理器，用于捕获终止信号
        """
        def signal_handler(signum, frame):
            """信号处理函数，收到信号时触发停止"""
            self._handle_stop_signal()
        
        # 注册 SIGTERM 和 SIGINT 信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
    
    def _handle_stop_signal(self) -> None:
        """
        处理停止信号
        """
        # 将停止标志设为1
        with self._should_stop_flag.get_lock():
            self._should_stop_flag.value = 1
    
    def _cleanup_resources(self) -> None:
        """
        清理资源，在进程结束前调用
        """
        # 清理临时文件
        for path in self._temp_files:
            try:
                if os.path.exists(path):
                    os.unlink(path)
            except Exception as e:
                if hasattr(self, '_logger'):
                    self._logger.warning(f"清理临时文件 {path} 失败: {e}")
                else:
                    print(f"清理临时文件 {path} 失败: {e}", file=sys.stderr)
        
        # 关闭子进程端的管道
        try:
            if hasattr(self, '_pipe_child'):
                self._pipe_child.close()
        except Exception as e:
            if hasattr(self, '_logger'):
                self._logger.warning(f"关闭管道时出错: {e}")
            else:
                print(f"关闭管道时出错: {e}", file=sys.stderr)
    
    def stop(self, timeout: float = DEFAULT_TIMEOUT) -> bool:
        """
        优雅地停止进程
        
        此方法设置停止标志并等待进程结束，如果超时则强制终止。
        
        Args:
            timeout: 等待进程自行终止的最大时间（秒）
            
        Returns:
            bool: 成功停止返回 True，否则返回 False
            
        Raises:
            ValueError: 如果timeout参数小于或等于0
        """
        if timeout <= 0:
            raise ValueError("超时时间必须大于0")
        
        if not self.is_alive():
            return True
        
        # 设置停止标志
        with self._should_stop_flag.get_lock():
            self._should_stop_flag.value = 1
        
        # 等待进程结束，使用自适应轮询间隔
        start_time = time.time()
        poll_interval = self.MIN_POLL_INTERVAL
        
        while self.is_alive() and (time.time() - start_time) < timeout:
            time.sleep(poll_interval)
            
            # 尝试从管道接收结果
            self._receive_result()
            
            # 增加轮询间隔，但不超过最大值
            poll_interval = min(poll_interval * 1.5, self.MAX_POLL_INTERVAL)
        
        # 如果进程仍然存活，强制终止
        if self.is_alive():
            self.terminate()
            self.join(timeout=1.0)
            
            # 仍然存活，则返回失败
            if self.is_alive():
                print(f"无法终止进程 {self.name} (PID {self.pid})", file=sys.stderr)
                return False
        
        # 尝试最后一次接收结果
        self._receive_result()
        
        # 如果状态仍然是运行中，则更新为已停止
        if self._local_result['status'] in (self.STATUS_NOT_STARTED, self.STATUS_RUNNING):
            self._local_result['status'] = self.STATUS_STOPPED
            
        return True
    
    def _receive_result(self) -> None:
        """
        从子进程接收结果（非阻塞方式）
        """
        if self._pipe_parent.poll():
            try:
                result = self._pipe_parent.recv()
                # 处理心跳消息
                if isinstance(result, dict) and result.get('type') == 'heartbeat':
                    self._last_heartbeat = time.time()
                    return
                
                # 更新结果
                self._local_result.update(result)
            except (EOFError, BrokenPipeError):
                # 管道可能已关闭，忽略错误
                pass
            except Exception as e:
                print(f"接收结果时出错: {e}", file=sys.stderr)
    
    def should_stop(self) -> bool:
        """
        检查进程是否应该停止
        
        回调函数应定期调用此方法以检查是否应该停止执行
        
        Returns:
            bool: 如果进程应该停止，则返回 True
        """
        with self._should_stop_flag.get_lock():
            return self._should_stop_flag.value == 1
    
    def get_result(self) -> Dict[str, Any]:
        """
        获取回调函数的执行结果和状态
        
        如果进程正在运行，会尝试从子进程获取最新结果
        
        Returns:
            Dict[str, Any]: 包含以下键的字典:
                - value: 回调函数的返回值
                - error: 如果发生错误，则为错误消息
                - error_type: 错误类型
                - traceback: 如果发生错误，则为错误堆栈
                - status: 进程状态
        """
        # 如果进程还活着，尝试接收最新结果
        if self.is_alive():
            self._receive_result()
        
        # 复制结果，避免修改原始数据
        result = self._local_result.copy()
        
        # 如果结果是文件引用，尝试加载真实数据
        if isinstance(result.get('value'), dict) and result.get('value', {}).get('_type') == 'file_result':
            try:
                result['value'] = self._load_result_from_file(result['value'])
            except Exception as e:
                # 如果加载失败，记录错误并返回None
                print(f"加载结果文件失败: {e}", file=sys.stderr)
                result['value'] = None
        
        return result
    
    def get_return_value(self) -> Any:
        """
        获取回调函数的返回值
        
        先尝试从子进程获取最新结果
        
        Returns:
            Any: 回调函数的返回值，如果函数尚未完成或发生错误则返回 None
        """
        result = self.get_result()
        return result.get('value')
    
    def get_error(self) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        获取错误信息（如果有）
        
        先尝试从子进程获取最新结果
        
        Returns:
            Tuple[Optional[str], Optional[str], Optional[str]]: 
                (错误类型, 错误消息, 错误堆栈)
                如果没有错误则所有值都为 None
        """
        # 更新结果
        if self.is_alive():
            self._receive_result()
        
        return (
            self._local_result.get('error_type'),
            self._local_result.get('error'),
            self._local_result.get('traceback')
        )
    
    def get_status(self) -> str:
        """
        获取进程状态
        
        先尝试从子进程获取最新结果
        
        Returns:
            str: 进程状态，可能的值:
                - 'not_started': 进程尚未启动
                - 'running': 进程正在运行
                - 'completed': 进程已成功完成
                - 'failed': 进程遇到错误
                - 'stopped': 进程被手动停止
                - 'unknown': 状态未知
        """
        # 更新结果
        if self.is_alive():
            self._receive_result()
        
        # 获取状态，添加默认返回值
        status = self._local_result.get('status')
        return status if status is not None else self.STATUS_UNKNOWN
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """
        等待进程完成
        
        Args:
            timeout: 等待的最大时间（秒），None 表示无限等待
            
        Returns:
            bool: 如果进程成功完成则返回 True，否则返回 False
        """
        # 等待进程结束
        self.join(timeout)
        
        # 获取最终结果
        if not self.is_alive():
            self._receive_result()
        
        return not self.is_alive() and self._local_result.get('status') == self.STATUS_COMPLETED
    
    def is_healthy(self) -> bool:
        """
        检查子进程是否处于健康状态
        
        该方法通过两个维度判断进程健康状态：
        1. 进程存活状态：通过 is_alive() 检查进程是否仍在运行
        2. 心跳机制：检查上次收到心跳的时间是否在合理范围内
        
        心跳机制工作原理：
        - 子进程每隔 HEARTBEAT_INTERVAL 秒(默认1秒)通过管道发送一个心跳消息
        - 父进程接收到心跳消息后会更新 _last_heartbeat 时间戳
        - 如果父进程长时间未收到心跳(超过间隔的3倍)，则认为子进程可能已卡死
        
        使用场景：
        - 检测进程是否卡死或陷入无限循环
        - 监控长时间运行的任务进程状态
        - 实现自动故障恢复机制
        - 作为优雅停止长时间运行进程的前置检查
        
        Returns:
            bool: 如果进程正常运行并能及时响应则返回 True，否则返回 False
        
        示例:
            >>> if not process.is_healthy():
            >>>     print("进程可能已卡死，尝试重启")
            >>>     process.stop()
            >>>     # 重启进程逻辑...
        """
        # 首先判断进程是否还在运行
        # 如果进程已终止，无需进一步检查，直接返回不健康
        if not self.is_alive():
           
            return False
        
        # 计算从上次接收到心跳到现在经过的时间(秒)
        heartbeat_age = time.time() - self._last_heartbeat
        
        
        # 判断心跳是否超时
        # 通常允许一定程度的心跳延迟，这里使用心跳间隔的3倍作为阈值
        # 如果超过这个阈值，说明子进程可能已卡死或无法正常通信
        # HEARTBEAT_INTERVAL默认为1秒，因此默认阈值为3秒
        return heartbeat_age < (self.HEARTBEAT_INTERVAL * 3)
    
    def wait_until(self, condition_func, timeout=None, check_interval=0.1):
        """
        等待直到条件满足或超时
        
        Args:
            condition_func: 接受进程对象并返回布尔值的条件函数
            timeout: 超时时间(秒)，None表示无限等待
            check_interval: 检查条件的间隔时间(秒)
            
        Returns:
            bool: 如果条件满足返回True，超时返回False
            
        Examples:
            >>> process.wait_until(lambda p: p.get_status() == 'completed', timeout=10)
        """
        start_time = time.time()
        while timeout is None or (time.time() - start_time < timeout):
            if condition_func(self):
                return True
            time.sleep(check_interval)
        return False
    
    @classmethod
    def create_and_start(cls, callback, *args, **kwargs):
        """
        创建进程并立即启动（工厂方法）
        
        Args:
            callback: 要执行的回调函数
            *args: 传递给回调函数的位置参数
            **kwargs: 传递给回调函数和进程的关键字参数
            
        Returns:
            CustomProcess: 已启动的进程对象
        """
        process = cls(callback=callback, *args, **kwargs)
        process.start()
        return process


# 注册在程序退出时清理所有进程
import atexit
atexit.register(CustomProcess.cleanup_all)


# 定义一个可以被子进程找到的全局回调函数
def example_callback(process, callback_name, count=3, delay=1):
    """
    示例回调函数 - 注意第一个参数是进程对象本身
    
    Args:
        process: 运行此回调的进程对象
        callback_name: 进程名称
        count: 执行步骤数
        delay: 每步延迟时间（秒）
    
    Returns:
        list: 执行结果列表
    """
    print(f"进程 {callback_name} 开始执行")
    result = []
    
    for i in range(count):
        # 检查是否应该停止 - 使用传入的进程对象
        if process.should_stop():
            print(f"进程 {callback_name} 收到停止信号")
            break
        
        print(f"进程 {callback_name}: 步骤 {i+1}/{count}")
        result.append(f"步骤 {i+1} 完成")
        time.sleep(delay)
    
    print(f"进程 {callback_name} 执行完成")
    return result


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建自定义进程 - 使用工厂方法
    process = CustomProcess.create_and_start(
        callback=example_callback,
        name="ExampleProcess",  # 这是进程名称
        callback_name="示例进程",  # 这是传递给回调函数的name参数
        count=20,
        delay=0.5
    )
    
    try:
        print(f"主进程: 已启动 {process.name}")
        
        # 等待进程运行一段时间
        time.sleep(2.5)
        
        # 检查进程是否健康
        print(f"进程健康状态: {process.is_healthy()}")
        
        # 等待直到特定条件满足
        # completed = process.wait_until(
        #     lambda p: len(p.get_return_value() or []) >= 5,
        #     timeout=5
        # )
        
        # if completed:
        #     print("已完成至少5个步骤，现在停止进程")
        
        # 优雅地停止进程
        print("主进程: 正在停止进程...")
        process.stop()
        
        # 获取结果
        result = process.get_result()
        print(f"进程状态: {result['status']}")
        print(f"返回值: {result['value']}")
        
        if result['error']:
            error_type = result.get('error_type', '未知错误类型')
            print(f"错误 ({error_type}): {result['error']}")
            print(f"错误堆栈: {result['traceback']}")
    
    except KeyboardInterrupt:
        print("主进程: 收到键盘中断")
        process.stop()
    
    finally:
        # 清理确保所有资源释放
        if process.is_alive():
            process.stop(timeout=1.0)

